// 定义语言枚举
export enum Languages {
  English = 1,
  Chinese = 2,
  Arabic = 3
}

// 定义会话类型枚举
export enum TaskTypes {
  casualChat = 1,
  generateJD = 2
}

// 定义聊天来源枚举
export enum Source {
  JDGenerator = 1,
  JDOptimizer = 2,
  skillTagsGenerator = 3
}

// 定义聊天类型枚举
export enum ChatTypes {
  jd = 1,
  skillTags = 2,
  pk = 3
}

// 定义文件类型枚举
export enum FileTypes {
  docx = 0,
  pdf = 1,
  md = 2
}

// 定义JD发布状态枚举
export enum JDPublishStatus {
  draft = 1,
  publish = 2
}

// 定义技能标签生成Level 2枚举
export enum SkillTagsLevel1 {
  'Soft Skill' = 1,
  'Hard Skill' = 2,
  'HSE' = 3
}

// Boc Required 和 HSE 都使用值 3
export const BOC_REQUIRED_VALUE = 3;

// 定义技能标签生成Level 2枚举
export enum SkillTagsLevel2 {
  'Problem Solving & Critical Thinking' = 1,
  'Communication & Interpersonal Skills' = 2,
  'Leadership & Management' = 3,
  'Industry-Specific Skills' = 4,
  'Domain-Specific Skills' = 5,
  'HSE' = 6
}