export enum CourseChapterType {
  // Video = 1,
  // Audio = 2,
  // Document = 3,
  // Scorm = 4,
  // Exam = 5,
  Video = 1,
  Audio = 2,
  Document = 3,
  Scorm = 4,
  ZIP = 5,
  Exam = 6,
  Aicc = 7
}
// 当前课程的解压状态
export enum ScormFileStatus {
  /** 解压中 */
  Unziping = 0,
  /** 解压成功 */
  Success = 1,
  /** 解压失败 */
  Failure = 2,
  /** 非Scorm文件 */
  NotScorm = 3,
}
/** 当前章节学习状态 */
export enum ChapterStatus {
  NotStart = 0,
  InProgress = 1,
  Failed = 2,
  Completed = 3,
}
/** 邮件发送状态 */
export enum SendStatus {
  /** 发送中 */
  Sending = 0,
  /** 发送失败 */
  Failed = 2,
  /** 发送成功 */
  Succeeded = 1,
}
/** Exam 试卷类型 */
export enum ExamChapterType {
  CustomPaper = 0,
  AutoPaper = 1,
}
