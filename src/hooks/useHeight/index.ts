import type { VisibleDomRect } from '@/typings/global'
import { getElementVisibleRect } from '@/utils/dom'
import { useDebounceFn } from '@vueuse/core'
export function useHeight() {
  let resizeObserver: null | ResizeObserver = null
  const contentElement = ref<HTMLDivElement | null>(null)
  const visibleDomRect = ref<null | VisibleDomRect>(null)
  // const contentHeight = useCssVar(CSS_VARIABLE_CONTENT_HEIGHT)
  // const contentWidth = useCssVar(CSS_VARIABLE_CONTENT_WIDTH)
  const debouncedCalcHeight = useDebounceFn(
    (_entries: ResizeObserverEntry[]) => {
      visibleDomRect.value = getElementVisibleRect(contentElement.value)
      // contentHeight.value = `${visibleDomRect.value?.height}px`
      // contentWidth.value = `${visibleDomRect.value?.width}px`
    },
    16,
  )
  function setResizeObserver() {
    if (contentElement.value && !resizeObserver) {
      resizeObserver = new ResizeObserver(debouncedCalcHeight)
      resizeObserver.observe(contentElement.value)
    }
  }
  // onMounted(() => {
  //   nextTick(() => {
  //     console.log(contentElement.value)

  //     if (contentElement.value && !resizeObserver) {
  //       resizeObserver = new ResizeObserver(debouncedCalcHeight)
  //       resizeObserver.observe(contentElement.value)
  //     }
  //   })
  // })

  onUnmounted(() => {
    resizeObserver?.disconnect()
    resizeObserver = null
  })
  return { setResizeObserver, contentElement, visibleDomRect }
}
