import request from '@/config/axios'

/** ----- ENUM ----- */
/** 定义阶段类型 */
export enum PhaseEnum {
  PHASE_1 = 1,
  PHASE_2 = 2,
  PHASE_3 = 3
}

/** 定义课程类型 */
export enum LearningContentEnum {
  ONLINE_COURSE = 10,
  MLC_TRAINING = 50,
  KNOWLEDGE = 60
}

/** 定义学习内容状态枚举 */
export enum LearningContentStatusEnum {
  DRAFT = 0, // 草稿
  PUBLISHED = 1 // 已发布
}

// 学习内容快照 VO
export interface LearningContentSnapshotVO {
  id: number // 主键ID
  type: number // 内容类型
  typeId: number // 内容ID
  level: number // 难度等级(Level，0：Suitable for all（默认），1：Beginner，2：Intermediate，3：Advanced)
  title: string // 内容标题
  keywords: string // 关键词
  introduction: string // 介绍
}

/** 添加学习内容VO */
export interface AddLearningContentVO {
  recommendIds: number[] // 推荐ID列表
  positionId: number // 职位ID
  phase: LearningContentEnum // 阶段
}

/** 根据Phase重新生成学习内容VO */
export interface RegenerateByPhaseReqVO {
  phase?: PhaseEnum
  positionId: number
  matchDegree: number
}

/** 查询不同分类的课程分页VO */
export interface getContentPageVO {
  bizType?: number
  phase: number
  positionId: number
  matchDegree: number
  pageNo: number
  pageSize: number
}

// 学习内容快照 API
export const LearningContentSnapshotApi = {
  // 查询学习内容分页
  getLearningContentSnapshotPage: async (params: any) => {
    return await request.get({ url: `/edp/position-learning-map/page`, params })
  },

  // 查询不同分类的课程分页
  getContentPage: async (params: getContentPageVO) => {
    return await request.get({ url: `/edp/position-learning-map/content/page`, params })
  },

  // 新增学习内容
  addLearningContent: async (data: AddLearningContentVO) => {
    return await request.post({ url: `/edp/position-learning-map/add`, data })
  },

  // 根据Phase重新生成学习内容
  regenrateByPhase: async (data: RegenerateByPhaseReqVO) => {
    return await request.post({ url: `/edp/position-learning-map/phase-regenerate`, data })
  },

  // 批量删除学习内容
  batchDeleteContentByIds: async (id: number) => {
    return await request.delete({
      url: `edp/position-learning-map/batch-delete?id=${id}`
    })
  }
}
