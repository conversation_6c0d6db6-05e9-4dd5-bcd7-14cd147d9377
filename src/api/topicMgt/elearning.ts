import request from '@/config/axios'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getAccessToken } from '@/utils/auth'
import { config } from '@/config/axios/config'

export interface CourseScopeVO {
  pageNum: number
  pageSize: number
  createId: number
  createBy: string
  createTime: string // You may also want to use Date if you're planning to convert this to a Date object.
  updateId: number | null
  updateBy: string | null
  updateTime: string // Same here, consider using Date.
  remark: string | null
  id: number
  courseId: number
  relevanceId: number
  relevanceName: string
  scope: any
  type: number
  effectiveDay: number
  autoAssign: boolean | null // Assuming autoAssign can be a boolean or null based on your data.
}

export interface CourseReqVO {
  pageNo: number
  pageSize: number
  name: string
  deptId: number
  duration: number
  isSubtitle: string
  source: number
  language: number
  handDuration: number
  topicId: string[]
}

export interface CourseSaveVO {
  id?: number
  cover: string
  createBy: string
  createId: number
  deptId: number
  duration: number
  exam: number
  examNum: number
  introduction: string
  isNew: boolean
  isRecommend: boolean
  keywords: string
  lang: string
  language: number
  level: number
  name: string
  source: number
  status: number
  studyStatus: number
  subtitle: string
  topic: string
  topicId: string[]
  type: number
  isNew: boolean
}

export interface CourseInfoReqVO {
  id?: number
  email: string
  pageNo: number
  pageSize: number
  status: number
  studentName: string
  type: string
}

export interface CourseInfoRespVO {
  userId: number
  assignmentDetailId: number
  studentName: string
  badgeNo: string
  email: string
  department: string
  section: string
  position: string
  type: string
  onSchedule: boolean
  star: number
  operator: string
  messageStatus: string
  score: string
  examTimes: string
  examStatus: string
  status: number
  createTime: Date
  company: string
}

export interface CourseInfoExportReqVO {
  id: number
  email: string
  type: number
  pageNo: number
  pageSize: number
  status: number
  studentName: string
}
export interface TaskStatisticsDetailReqVO {
  pageNo: number
  pageSize: number
  courseId: number
  courseChapterId: number
  username: string
  badgeNo: string
  email: string
}
export interface TaskStatisticsDetailRespVO {
  id: number
  userId: number
  courseId: number
  userName: string
  badgeNo: string
  email: string
  companyName: string
  departmentName: string
  sectionName: string
  positionName: string
  status: number
  score: number
  examTimes: number
  examStatus: string
}
export interface CourseChapterSaveVO {
  id?: number
  courseId: number
  title: string
  sort: number
  status: number
  type: number
  origin: number
  lang: string
  size: number
  duration: number
  aiccParseStatus: number
  scormParseStatus: number
  scormVersion: string
  scormRunPath: string
  resourceId: number
  fileId: number
  fileName: string
  fileUrl: string
  contentType: string
  contentExamId: number
}
export interface ImportCourseReqVO {
  pageNo: number
  pageSize: number
  taskId: number
  contentTitle
  category
  area
  subject
  channel
  language
  courseSource
  status
}

export interface ImportCourseRespVO {
  taskId: string
  contentTitle: string
  contentld: string
  assetUuid: string
  category: string
  area: string
  subject: string
  channel: string
  language: string
  estimatedDuratio: string
  level: string
  exam: string
  subtitle: string
  courseSource: string
  imageUrI: string
  keywords: string
  description: string
  courseFormat: string
  courseFileName: string
  fileLocation: string
  id: number
  status: number
  statusDesc: string
  importTime: Date
  errorMessage: string
  createTime: Date
  updateTime: Date
  creator: string
  updater: string
}
/**
 * 获取课程基本信息详情
 * @param courseId 课程id
 * @returns 课程详情
 */
export const getCourse = (id: number) => {
  return request.get({ url: `/learning/course/get?id=` + id })
}

// 查询专题课程列表
export const listCourse = (params: CourseReqVO) => {
  return request.get<PageResult<CourseInfoRespVO[]>>({ url: '/learning/course/page', params })
  // return request.get({ url: '/adapter/v1/course', params })
}

// 新增专题课程
export const addCourse = (data: CourseSaveVO) => {
  return request.post({ url: '/learning/course/create', data })
}

// 修改专题课程
export const updateCourse = (data: CourseSaveVO) => {
  return request.put({ url: '/learning/course/update', data })
}

// 删除专题课程
export const delCourse = (id: number) => {
  return request.delete({ url: `/learning/course/delete?id=` + id })
}
// 查询导入中的课程列表
export const importingCourse = (params: ImportCourseReqVO) => {
  return request.get<PageResult<ImportCourseRespVO[]>>({
    url: '/learning/course-import-info/page',
    params
  })
}
// 导入专题课程
export const importCourse = (data: any) => {
  return request.post({ url: '/learning/course-import-info/batch-import', data })
}
// 重新导入课程
export const restartImportCourse = (id: number) => {
  return request.put({ url: `/learning/course-import-info/reimport?id=` + id })
}

// 删除正在导入的课程
export const delImportCourse = (ids: string[]) => {
  return request.delete({ url: `/learning/course-import-info/delete?ids=` + ids })
}

// 导出课程
export const exportCourse = () => {
  return request.download({ url: '/learning/course-import-info/export-template' })
}

// 上架课程
export const putOnCourse = (ids: string[]) => {
  return request.put({ url: `/learning/course/put_on?courseIds=${ids}` })
}
// 下架课程
export const putOffCourse = (ids: string[]) => {
  return request.put({ url: `/learning/course/put_off?courseIds=${ids}` })
}
/** 推荐或取消推荐课程 */
export const recommendCourse = (data: { id: number; isRecommend: boolean }) => {
  return request.put({
    url: `/learning/course/updateIsRecommend?id=${data.id}&isRecommend=${data.isRecommend}`
  })
}
/** 是否新课程 */
export const isNewCourse = (data: { id: number; isNew: boolean }) => {
  return request.put({ url: `/learning/course/updateIsNew?id=${data.id}&isNew=${data.isNew}` })
}
// 查询专题课程详情
export const InfoCourse = (id: number) => {
  return request.get({ url: `/adapter/v1/adapter/v1/course/${id}` })
}
// 新增课程章节
export const addChapter = (data: CourseChapterSaveVO) => {
  return request.post({ url: '/learning/course-chapter/chapter', data })
}
// 编辑课程章节
export const editChapter = (data: CourseChapterSaveVO) => {
  return request.put({ url: '/learning/course-chapter/chapter', data })
}
// 查询课程章节
export const listChapterData = (params: { courseId: number }) => {
  return request.get({ url: '/learning/course/chapter', params })
}
// 查询课程章节
export const InfoChapter = (id: number) => {
  return request.get({ url: `/learning/course-chapter/get?id=` + id })
}
// 删除课程章节
export const delChaptere = (ids: string[]) => {
  return request.delete({ url: `/learning/course-chapter/chapter/${ids}` })
}
// 课程章节排序
export const sortChapter = (data: { id: number; sort: number }) => {
  return request.put({ url: '/learning/course-chapter/chapter/sort', data })
}
// 检查大文件是否存在
export const checkFile = (params: any) => {
  return request.get({ url: '/adapter/v1/file/check', params })
}
// 检查大文件是否存在 Todo
export const initFile = (data: any) => {
  return request.post({ url: '/adapter/v1/file/init', data })
}

export const uploadUrl = (urls: any, data: any) => {
  return request.put({ url: urls, headers: { 'Content-Type': 'multipart/form-data' }, data })

  // return request({
  //   url: urls,
  //   method: 'put',
  //   headers: {
  //     'ContentType-Type': 'multipart/form-data',
  //   },
  //   data,
  // })
}
// 解析上传的scrom(该接口项目中未使用)
export const unscormFile = (params: any) => {
  return request.get({ url: '/adapter/v1/file/parseScorm', params })
}
/**
 * 给当前选中的数据分配可成
 * @param id 课程ID
 * @param scope 范围（公司，部门，员工）
 * @param type 课程类型（选修，必修）
 * @param data scope数据
 * @returns 分配结果
 */
export const AssignFun = (id: number, scope: any, type: any, data: string[]) => {
  return request.post({
    url: `/learning/course/assign?courseId=${id}&scope=${scope}&type=${type}`,
    data
  })
}
/**
 * 查询已分配的课程
 */
export const AssignedList = (params: { courseId: number; type: number }) => {
  return request.get({ url: '/learning/course/assignedList', params })
}
/**
 * 获取当前课程学习情况列表
 * @param params 课程id
 * @returns 学习情况列表
 */
export const listStatistics = (params: CourseInfoReqVO) => {
  return request.get<PageResult<CourseInfoRespVO[]>>({ url: '/learning/course/info', params })
}
/**
 * 获取当前课程统计信息
 * @param id 课程ID
 * @returns 课程的学习统计
 */
export const getStatistics = (id: number) => {
  return request.get({ url: '/learning/course/statics', params: { id } })
}

export const delAssign = (ids: string[]) => {
  return request.delete({ url: `/learning/course/assign/${ids}` })
}
/**
 * 获取学生课程学习信息
 * @param courseId 课程ID
 * @param userId 学员ID
 * @returns 学员本课程学习统计信息
 */
export const getEmployeeStatistics = (courseId: number, userId: number) => {
  return request.get({ url: '/learning/course-chapter/chapter/info', params: { courseId, userId } })
}

/**
 * 获取章节学习信息
 * @param courseId 课程ID
 * @returns 课程章节学习进度信息
 */
export const getTasksStatistics = (courseId: number) => {
  return request.get({ url: '/learning/course-chapter/chapter/list', params: { courseId } })
}
/**
 * 重新开始解析scorm课程
 * @param chapterId 章节id
 * @returns 解析请求结果
 */
export const unzipScorm = (chapterId: number) => {
  return request.post({
    url: '/learning/course-chapter/chapter/parse-scorm',
    params: { chapterId }
  })
}
/**
 * 获取章节下所有用户学习进度信息
 * @param params 查询参数
 * @returns 章节下所有用户学习进度信息
 */
export const getTasksStatisticsDetail = (params: TaskStatisticsDetailReqVO) => {
  return request.get<PageResult<TaskStatisticsDetailRespVO[]>>({
    url: '/learning/course-study-chapter/study/chapter/list',
    params
  })
}

// 导出课程数据
export const exportCourseInfo = (params: CourseInfoExportReqVO) => {
  return request.download({ url: '/learning/course/info/export', params })
}

// 获取标签内容和总结
export const getGeneratedContent = async (
  data: any,
  ctrl: AbortController,
  onMessage: (event: any) => void,
  onError: (event: any) => void,
  onClose: () => void
) => {
  const token = getAccessToken()
  return fetchEventSource(
    `${config.base_url}/learning/course/getTagsAndSummary`,
    // `http://10.248.18.22:48080/admin-api/edp/chat-history/conv/getStreamAnswer`,
    {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
      openWhenHidden: true,
      onmessage: onMessage,
      onerror: onError,
      onclose: onClose,
      signal: ctrl.signal
    }
  )
}
