import request from '@/config/axios'

export interface OnboardingCategoryReqVO {
  pageNo: number
  pageSize: number
  title: string
}
export interface OnboardingCategorySaveVO extends OnboardingCategoryReqVO{
  id?: number
  sort: number
}

export interface OnboardingSaveVO {
  id?: number
  title?: string | undefined
  cover: string
  departmentId: number
  categoryId?: string | undefined
  file?: string | undefined
  isMandatory: boolean
  sort?: string | undefined
  estimateTime: string
  estimateTimeTemp: string
  description?: string | undefined
  content?: string | undefined
  type?: number | undefined
  fileList?: [] | undefined
  attachmentList?: [] | undefined
  keywords: string | []
}

export interface OnboardingAssignReqVO {
  companyName: string
  departmentName: string
  duration: number
  email: string
  id?: number
  pageNo: number
  pageSize: number
  positionName: string
  sectionName: string
  status: number
  userId: number
  userName: string
}

export interface OnboardingScopeReqVO {
  createBy: string
  createId: number
  createTime: string
  id?: number
  onboardingId: number
  pageNo: number
  pageSize: number
  remark: string
  scope: number
}

export interface CompanyPolicyRecordExportReqVO extends OnboardingAssignReqVO {
}

// 新增onboarding
export const addOnboarding = (data: OnboardingSaveVO) => {
  return request.post({ url: '/learning/onboarding', data})
}

// 查询onboarding
export const detailOnboarding = (id: number) => {
  return request.get({ url: `/learning/onboarding/${id}` })
}

// 修改onboarding
export const editOnboarding = (data: OnboardingSaveVO) => {
  return request.put({ url: '/learning/onboarding', data})
}

// 获取category列表
export const getCategory = (params: OnboardingCategoryReqVO) => {
  return request.get({ url: '/learning/onboarding', params })
}

// 添加category
export const addCategory = (data: OnboardingCategorySaveVO) => {
  return request.post({ url: '/learning/onboarding/category', data})
}

// 查询category
export const detailCategory = (id: number) => {
  return request.get({ url: `/learning/onboarding/category/${id}` })
}
// 修改category
export const EditCategory = (data: OnboardingCategorySaveVO) => {
  return request.put({ url: '/learning/onboarding/category', data})
}
// 删除category
export const deleteCategory = (ids: string[]) => {
  return request.delete({ url: `/learning/onboarding/category/${ids}` })
}
// 查询onboarding列表没有分页
export const getOnboardingCategory = () => {
  return request.get({ url: '/learning/onboarding/category/simple-list' })
}

// 查询学习记录
export const getLearning = (params: OnboardingAssignReqVO) => {
  return request.get({ url: '/learning/onboarding/assignment', params })
}

export const AssignFun = (id: number, scope: number, data: []) => {
  return request.post({ url: `/learning/onboarding/scope/assign?onboardingId=${id}&scope=${scope}`, data})
}

export const AssignedList = (params: OnboardingScopeReqVO) => {
  return request.get({ url: '/learning/onboarding/scope/list', params })
}

// 删除onboarding分配的数据
export const delAssign = (ids: string[]) => {
  return request.delete({ url: `/learning/onboarding/scope/${ids}` })
}

// 导出
export const exportOnboarding = (params: CompanyPolicyRecordExportReqVO) => {
  return request.download({ url: '/learning/onboarding/assignment/export', params, })
}

export const exportPolicy = (params: CompanyPolicyRecordExportReqVO) => {
  return request.download({ url: '/learning/company-policy/record/export', params, })
}
