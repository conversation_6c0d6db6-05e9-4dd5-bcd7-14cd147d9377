import request from '@/config/axios'

export interface CompanyPolicyReqVO {
  title: string
  ack: boolean
  duration: number
  pageNo: number
  pageSize:  number
  mediaType: number
}
export interface CompanyPolicyAssignReqVO {
  userName: string
  badgeNo: string
  email: string
  status: number
  pageNo: number
  pageSize: number
  onBoardingId?: number
  companyPolicyId?: number
}
export interface AssignedCompanyReqVO {
  companyPolicyId: number
  type?: string | number
}
export interface FileListData {
  type?: string | undefined
  fileId?: string | undefined
  fileUrl?: string | undefined
  name?: string | undefined
  fileName?: string | undefined
  orientationId?: string | undefined
  duration?: number | undefined
}

export interface CompanyPolicySaveVO {
  title?: string | undefined
  cover?: string | undefined
  file?: string | undefined
  ack: boolean
  sort?: number | undefined
  declaration?: string | undefined
  content?: string | undefined
  attachmentList?: [] | undefined
  muploader?: FileListData[] | undefined
  keywords: string | []
  fileId?: string | undefined
  fileUrl?: string | undefined
  fileName?: string | undefined
  duration?: number | undefined
  origin?: string | number | undefined
  lang?: string | undefined
  size?: number | undefined
  format?: string | undefined
  mediaType?: string | undefined
}
export interface CompanyPolicyUpdateIsAckVO {
  id: number
  isAck: number
}
// 查询company policy
export const getCompanyPolicy = (params: CompanyPolicyReqVO) => {
  return request.get({ url: '/learning/company-policy', params })
}

// 删除company policy
export const deleteCompanyPolicy = (ids: number[]) => {
  return request.delete({ url: `/learning/company-policy/${ids}` })
}

// 根据id查询company policy
export const detailCompanyPolicy = (id: number) => {
  return request.get({ url: `/learning/company-policy/${id}` })
}

// 新增公司政策
export const addCompanyPolicy = (data: CompanyPolicySaveVO) => {
  return request.post({ url: '/learning/company-policy', data })
}

// 修改公司政策
export const editCompanyPolicy = (data: CompanyPolicySaveVO) => {
  return request.put({ url: '/learning/company-policy', data })
}

// 查询公司政策的学习记录列表
export const getCompanyPolicyAssignment = (params: CompanyPolicyAssignReqVO) => {
  return request.get({ url: '/learning/company-policy/record', params })
}
// company分配课程
export const AssignFun = (id: number, scope: any, data: []) => {
  return request.post({ url: `/learning/company-policy/scope/assign?companyPolicyId=${id}&scope=${scope}`, data })
}
export const AssignCompanyFun = (id: number, scope: []) => {
  return request.post({ url: `/learning/company-policy/scope/assign?companyPolicyId=${id}`, data: scope })

}
// 查询company分配的课程
export const AssignedList = (params: AssignedCompanyReqVO) => {
  return request.get({ url: '/learning/company-policy/scope/list', params })
}
export const AssignedCompanyList = (params: AssignedCompanyReqVO) => {
  return request.get({ url: '/learning/company-policy/scope/list', params })
}
// 删除company分配的数据
export const delAssign = (ids: number[]) => {
  return request.delete({ url: `/learning/company-policy/scope/${ids}` })
}
export const deleteCompanyAssign = (ids: number) => {
  return request.delete({ url: `/learning/company-policy/scope/${ids}` })
}
/** 是否Ack */
export const updateCompanyPolicy = (data: CompanyPolicyUpdateIsAckVO) => {
  return request.put({ url: `/learning/company-policy/updateIsAck?id=${data.id}&isAck=${data.isAck}` })
}
