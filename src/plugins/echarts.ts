// 引入 echarts 核心模块。
import * as echarts from 'echarts/core'

// 引入柱状图和折线图组件。
import { Bar<PERSON>hart, LineChart, PictorialBarChart, PieChart } from 'echarts/charts'

// 引入标题、提示框、网格、数据集和数据转换器组件。
import {
  DataZoomComponent,
  DatasetComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  TransformComponent,
} from 'echarts/components'

// 引入标签布局和通用过渡动画特性。
import { LabelLayout, UniversalTransition } from 'echarts/features'

// 引入 Canvas 渲染器。
import { CanvasRenderer } from 'echarts/renderers'

import type {
  // 系列类型的定义后缀都为 SeriesOption
  BarSeriesOption,
  LineSeriesOption,
  PictorialBarSeriesOption,
} from 'echarts/charts'

import type {
  // 组件类型的定义后缀都为 ComponentOption
  DatasetComponentOption,
  GridComponentOption,
  TitleComponentOption,
} from 'echarts/components'
import type {
  ComposeOption,
} from 'echarts/core'
import type { PieSeriesOption } from 'echarts'

// 通过 ComposeOption 来组合出一个只有必须组件和图表的 Option 类型
export type ECOption = ComposeOption<
  | TitleComponentOption
  | GridComponentOption
  | DatasetComponentOption
  | BarSeriesOption
  | PictorialBarSeriesOption
  | PieSeriesOption
  | LineSeriesOption
>

/**
    注册必须的组件，包括标题、提示框、网格、数据集、数据转换器，
    以及柱状图、折线图、标签布局、通用过渡动画和 Canvas 渲染器。
 */
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  DataZoomComponent,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  PieChart,
  LegendComponent,
  BarChart,
  PictorialBarChart,
  LineChart,
])
// 导出
export default echarts
