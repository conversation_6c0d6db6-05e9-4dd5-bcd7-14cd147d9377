<script setup lang='ts'>
import { sumBy } from 'lodash-es'
import { ChartFactory } from '@/views/dashboard/admin/scripts/chart'
import type { ECOption } from '@/plugins/echarts'
import type { AdminUserRecord } from '@/typings/dashboard/admin'
import { getUserRecord } from '@/api/dashboard/admin'

const loading = ref(true)
const chartRef = ref()
const userPieList = ref<AdminUserRecord[]>()
let chart: ChartFactory
const totalUserNum = computed(() =>
  userPieList.value?.reduce((prev, cur) => {
    return cur.totalNum + prev
  }, 0),
)
const colorArr = ref()
colorArr.value = ['#007943', '#17A987', '#E8B321', '#1FBD9F', '#2AA77F', '#009751', '#006F3B', '#F3C04C', '#DAB12E']
function initChart() {
  userPieList.value.map(((item, index) => {
    colorArr.value.map(((itt, ind) => {
      if (index === ind) {
        userPieList.value[index].colors = colorArr.value[ind]
      }
    }))
  }))
  const data = userPieList.value?.map(item => ({
    name: item.companyShortName,
    value: item.totalNum,
  }))
  const options: ECOption = {
    color: ['#007943', '#17A987', '#E8B321', '#1FBD9F', '#2AA77F', '#009751', '#006F3B', '#F3C04C', '#DAB12E'],
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: 'User',
        type: 'pie',
        radius: ['50%', '70%'],
        // avoidLabelOverlap: false, // 是否启用防止标签重叠策略，默认开启

        data,
      },
    ],
  }
  chart = new ChartFactory(chartRef.value, options)
}

const getCourseBarInfo = async () => {
  loading.value = true
  try {
    userPieList.value = await getUserRecord()
    // 排序
    userPieList.value.sort((a, b) => b.totalNum - a.totalNum)
    initChart()
  } finally {
    loading.value = false
  }
}

function resize() {
  chart.resize()
}
onMounted(() => {
  getCourseBarInfo()
})

window.addEventListener('resize', resize)
onDeactivated(() => {
  window.removeEventListener('resize', resize)
})
onActivated(() => {
  window.addEventListener('resize', resize)
})
</script>

<template>
  <div v-loading="loading" class="h-[400px] bg-white rounded-[10px] flex flex-col p-5">
    <div class="flex items-center gap-2.5 mb-5">
      <svg-icon icon-class="DashboardUser" />
      <span class="text-lg text-[#233a35]">{{ t('dashboard.total.user') }}</span>
    </div>
    <el-scrollbar>
      <div class="flex h-full w-full">
        <!-- 左侧环形图 -->
        <div ref="chartRef" class="shrink-0  min-[1920px]:w-[450px] md:w-[300px]" />
        <!-- 右侧进度条统计图 -->
        <div class="w-[230px] min-h-[300px] flex flex-col flex-1  justify-center mx-10">
          <template
            v-for="(item, index) in userPieList"
            :key="index"
          >
            <span class="text-sm mt-6">
              {{ item.companyShortName }}
            </span>
            <el-progress
              class="w-full"
              :percentage="(item.totalNum / totalUserNum * 100).toFixed(2)"
              :color="item.colors"
            />
          </template>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped lang='scss'>
:deep .el-progress__text{
  position: absolute;
  right: 0;
  bottom: 10px;
}
</style>
