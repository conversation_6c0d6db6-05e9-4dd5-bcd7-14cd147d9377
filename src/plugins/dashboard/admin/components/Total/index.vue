<script setup lang='ts'>
import { getTotal } from '@/api/dashboard/admin'
import type { AdminTotal } from '@/typings/dashboard/admin'
const { t } = useI18n()
const totalInfo = ref<AdminTotal>()

const cards = computed(() => [
  {
    text: t('dashboard.total.totalNumberOfCourse'),
    icon: 'DashboardCourse',
    num: totalInfo.value?.courseNum || '--',
  },
  {
    text: t('dashboard.total.totalNumberOfExam'),
    icon: 'DashboardExam',
    num: totalInfo.value?.examNum || '--',
  },
  {
    text: t('dashboard.total.totalNumberOfOnboarding'),
    icon: 'DashboardOnboarding',
    num: totalInfo.value?.onboardingNum || '--',
  },
  {
    text: t('dashboard.total.totalNumberOfOrientation'),
    icon: 'DashboardOrientation',
    num: totalInfo.value?.orientationNum || '--',
  },
  {
    text: t('dashboard.total.totalNumberOfCompanyPolicy'),
    icon: 'DashboardPolicy',
    num: totalInfo.value?.companyPolicyNum || '--',
  },
])

const getTotalInfo = async () => {
  totalInfo.value = await getTotal()
}
onMounted(() => {
  getTotalInfo()
})
</script>

<template>
  <div class="w-full flex gap-[20px]">
    <div v-for="(item, index) in cards" :key="index" class="basis-[313px] h-[90px] bg-white rounded-[10px] flex items-center gap-2.5 ps-3.5 pe-3.5" :class="index !== 0 ? 'flex-1' : 'shrink-0'">
      <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center shrink-0">
        <svg-icon :icon-class="item.icon" class="text-2xl" />
      </div>
      <div class="flex flex-col gap-2">
        <span class="text-sm text-[#777777] line-clamp-1" :title="item.text">{{ item.text }}</span>
        <span class="font-bold text-2xl text-[#222222]">{{ item.num }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang='scss'>

</style>
