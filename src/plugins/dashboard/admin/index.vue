<script setup lang='ts'>
import Total from './components/Total/index.vue'
import Quick from './components/Quick/index.vue'
import CourseBarChart from './components/CourseBarChart/index.vue'
import Statistics from './components/Statistics/index.vue'
import UserPieChart from './components/UserPieChart/index.vue'
</script>

<template>
  <div class="grid grid-rows-[auto_minmax(0,1fr)_minmax(0,1fr)] grid-cols-[313px_440px_auto] p-7 gap-x-5">
    <Total :data="totalInfo" class="row-span-1 col-span-3" />
    <Quick class="row-span-2 col-span-1 mt-5" />
    <!-- <div class="row-span-2 border border-red-600">
      1
    </div> -->
    <CourseBarChart :data="courseBarInfo" class="mt-5 col-span-2" />
    <Statistics class="row-span-1 col-span-1 mt-5" />
    <UserPieChart class="row-span-1 col-span-1 mt-5" />
  </div>
</template>

<style scoped lang='scss'>

</style>
