import type { EChartsType } from 'echarts'
import type { ECOption } from '@/plugins/echarts'
import echarts from '@/plugins/echarts'

export class ChartFactory {
  echarts: echarts.ECharts
  constructor(public container: any, public options: ECOption) {
    this.echarts = echarts.init(container)
    this.setOption()
  }

  private setOption() {
    this.echarts.setOption(this.options)
  }

  resize() {
    this.echarts.resize()
  }
}
