/* ACTIONS */
.actions {
  position: relative; // 使用相对定位使按钮组与Tab两端对齐在同一行
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--app-content-gap);
}
/* SINGLE BUTTON */
.ai-toolkit {
  &.single-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;

    // 修改Hover状态样式
    &:hover,
    &.lock-icon {
      color: var(--cs-color-primary);
      border-color: var(--cs-color-border);
      background-color: var(--cs-color-bgcolor);
    }

    // 修改Active状态样式
    &:active {
      color: white;
      border-color: transparent;
      background: var(--cs-color-primary);
    }
  }
  &.select {
    .el-select__wrapper.is-focused {
      box-shadow: 0 0 0 1px var(--cs-color-primary) inset;
    }

    .isSelected {
      color: var(--cs-color-primary);
    }
  }

  &.input {
    .el-input__wrapper.is-focus {
      box-shadow: 0 0 0 1px var(--cs-color-primary) inset;
    }
  }

  &.normal-button {
    &:hover {
      color: var(--cs-color-primary);
      border-color: var(--cs-color-border);
      background-color: var(--cs-color-bgcolor);
    }
  }

  &.close-icon-button {
    color: #fff;
    // background-color: rgba(0, 0, 0, 0.2);
    padding: 5px;
    border-radius: 100px;
    &.el-button.is-text:not(.is-disabled):hover {
      background-color: #656465;
    }
  }
  &.close-icon-button-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 20px;
    cursor: pointer;
    &:hover {
      background-color: #f7f8f8;
      border-radius: 50%; /* Make the background circular */
      transition: background-color 0.3s ease; /* Transition for smooth effect */
    }
  }
  &.tab-bar {
    // 修改Tab按钮样式对齐UI图
    :deep(.n-tabs-rail) {
      width: auto;
      padding: 4px 4.5px 4px 4px; // 右侧Active状态边距不对称,调整数据对齐视觉效果
      border-radius: 12px; // 修改背景圆角,圆角值大于胶囊圆角值:视觉等边
      background-color: var(--cs-color-bgcolor);

      // 修改胶囊圆角
      .n-tabs-capsule {
        border-radius: 8px;
      }

      // 调整Tab内边距,提升显示效果
      .n-tabs-tab__label {
        padding: 0 var(--app-content-padding);
      }
    }
    // 修改Active状态下文本颜色
    :deep(.n-tabs-tab--active .n-tabs-tab__label) {
      color: var(--cs-color-primary) !important;
    }
  }
}
