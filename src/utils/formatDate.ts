export const formatDate = (date,type?: number) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // JavaScript月份从0开始
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  // 用，type: 1.返回年月日 2.直播统计按时间查询专后端需要的格式是2025-06-01T00:00:00(中间带个T)
  // 如果不传返回年月日时分秒
  if (type === 1) {
    return `${year}-${month}-${day}`;
  } else if(type === 2) {
    return `${year}-${month}-${day}T${hour}:${minute}:${second}`;
  } else {
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }
}

// 获取当前天的的星期一到星期天的开始时间和结束时间(type传1返回年月日，如果不传返回年月日时分秒)
export const getWeekStartAndEnd = (type?: number) => {
  // 获取当前日期时间
  const now = new Date();

  // 计算本周一的日期，假设一周从周一（weekday 1）开始到周日（weekday 0）
  const dayOfWeek = now.getDay(); // getDay() 方法返回 0（周日）到 6（周六）
  const diff = now.getDate() - dayOfWeek + (dayOfWeek == 0 ? -6 : 1); // 如果是周日，则调整为上周一

  // 设置为本周一的开始时间
  const startOfWeek = new Date(now.setDate(diff));
  startOfWeek.setHours(0, 0, 0, 0);

  // 设置为本周日的结束时间
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(endOfWeek.getDate() + 6);
  endOfWeek.setHours(23, 59, 59, 999);
  return [formatDate(startOfWeek,type), formatDate(endOfWeek,type)]
}
// 获取当前天的月的开始时间和结束时间
// 用，type: 1.返回年月日 2.直播统计按时间查询专后端需要的格式是2025-06-01T00:00:00(中间带个T)
// 如果不传返回年月日时分秒
export const getMonthStartAndEnd = (type?: number | undefined) => {
  const now = new Date();

  // 设置为本月第一天的开始时间
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

  // 计算下个月的第一天，然后回退一天得到本月最后一天的结束时间
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  const endOfMonth = new Date(nextMonth - 1);
  endOfMonth.setHours(23, 59, 59, 999); // 设置为当天的最后一秒

  return [formatDate(startOfMonth,type), formatDate(endOfMonth,type)]
}

// 获取当前天的月的开始时间和结束时间(直播统计按时间查询专用，后端需要的格式是2025-06-01T00:00:00(中间带个T) type传1返回年月日，如果不传返回年月日时分秒)
export const getLiveMonthStartAndEnd = (type?: number | undefined) => {
  const now = new Date();

  // 设置为本月第一天的开始时间
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

  // 计算下个月的第一天，然后回退一天得到本月最后一天的结束时间
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  const endOfMonth = new Date(nextMonth - 1);
  endOfMonth.setHours(23, 59, 59, 999); // 设置为当天的最后一秒

  return [formatDate(startOfMonth,type), formatDate(endOfMonth,type)]
}

// 获取当前天的季度的开始时间和结束时间
export const getQuarterStartAndEnd = (type?: number) => {
  const now = new Date();
  const quarter = Math.floor((now.getMonth() / 3)) + 1; // 计算当前是第几个季度

  // 设置为本季度第一天的开始时间
  const startOfQuarter = new Date(now.getFullYear(), (quarter - 1) * 3, 1, 0, 0, 0, 0);

  // 计算下个季度的第一天，然后回退一天得到本季度最后一天的结束时间
  const endOfQuarter = new Date(now.getFullYear(), quarter * 3, 1);
  endOfQuarter.setHours(0, 0, 0, 0); // 先设置为下个季度第一天的开始时间
  endOfQuarter.setTime(endOfQuarter.getTime() - 1); // 回退1毫秒到本季度最后一天的最后一秒
  endOfQuarter.setHours(23, 59, 59, 999); // 设置为当天的最后一秒

  return [formatDate(startOfQuarter,type), formatDate(endOfQuarter,type)]
}

// 根据要求的季度，返回该季度的开始时间和结束时间(quarterNumber: 代表是第几季度，type:1 代表返回的是年月日格式，不传默认返回年月日时分秒格式 )
export const getQuarterTime = (quarterNumber: number, type?: number) => {
  const now = new Date();
  const year = now.getFullYear();

  // 计算季度起始月份
  const startMonth = (quarterNumber - 1) * 3

  // 季度开始时间
  const start = new Date(year, startMonth, 1)

  // 季度结束时间：下个季度第一天减一毫秒
  const end = new Date(year, startMonth + 3, 1)
  end.setTime(end.getTime() - 1); // 前一天的最后一秒

  // 格式化函数
  const format = (d: Date) => {
    const pad = (n: number) => String(n).padStart(2, '0');
    const dateStr = `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`

    if (type === 1) {
      return dateStr
    }

    const timeStr = `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`
    return `${dateStr} ${timeStr}`
  }
  return [format(start), format(end)]
}

// 获取当前年的开始时间和结束时间(type传1返回年月日，如果不传返回年月日时分秒)
export const getYearStartAndEnd = (type?: number) => {
  const now = new Date();

  // 设置为今年第一天的开始时间
  const startOfYear = new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0);

  // 设置为今年最后一天的结束时间
  const endOfYear = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999); // 12月是索引11

  return [formatDate(startOfYear,type), formatDate(endOfYear,type)]
}
// 获取当前天的下个月开始时间和结束时间
export const getNextMonthStartAndEnd = (type?: number) => {
  const now = new Date();

  // 设置为下个月第一天的开始时间
  const startOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1, 0, 0, 0, 0);

  // 计算下下个月的第一天，然后回退一天得到下个月最后一天的结束时间
  const endOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 2, 1);
  endOfNextMonth.setHours(0, 0, 0, 0); // 先设置为下下个月第一天的开始时间
  endOfNextMonth.setTime(endOfNextMonth.getTime() - 1); // 回退1毫秒到下个月最后一天的最后一秒
  endOfNextMonth.setHours(23, 59, 59, 999); // 设置为当天的最后一秒

  return [formatDate(startOfNextMonth,type), formatDate(endOfNextMonth,type)]
}

// 根据开始时间和结束时间计算出训练天数
export const calculateTrainingDays = (startStr, endStr,  thresholdHours = 12) => {
  const today = new Date().toISOString().split('T')[0];
  const start = new Date(`${today}T${startStr}`);
  const end = new Date(`${today}T${endStr}`);

  if (end < start) end.setDate(end.getDate() + 1);

  const diffHours = (end - start) / (1000 * 60 * 60);
  const days = diffHours / 24;

  return Math.ceil(days * 2) / 2; // 向上取 0.5 的倍数
}

// 获取指定年的开始时间和结束时间
export const getYearStartAndEndByYear = (year) => {
  const startOfYear = new Date(year, 0, 1, 0, 0, 0, 0); // 年-01-01 00:00:00

  // 设置为指定年份最后一天的结束时间
  const endOfYear = new Date(year, 11, 31, 23, 59, 59, 999); // 年-12-31 23:59:59.999

  return [formatDate(startOfYear), formatDate(endOfYear)];
}
// 获取指定月的开始时间和结束时间
export const getMonthStartAndEndByMonth = (year, month) => {
  const startOfMonth = new Date(year, month - 1, 1, 0, 0, 0, 0); // 年-月-01 00:00:00

  // 获取指定月份的最后一天
  const endOfMonth = new Date(year, month, 0); // 年-月-最后一天

  return [formatDate(startOfMonth), formatDate(endOfMonth)];
}

// 获取当前年月日 格式样例 20250515
export const getCurrentDateFormatted = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0') // 获取月份，并确保是两位数
  const day = now.getDate().toString().padStart(2, '0') // 获取日期，并确保是两位数

  return `${year}${month}${day}`
}

// 数组格式转换为时分秒 HH:mm:ss
export const formatTime = (timeArray) => {
  return timeArray.map((v, i) => (i === 0 ? v : String(v).padStart(2, '0'))).join(':');
}

// yyyy-mm-dd格式 转换为 yyyy/mm/dd
export const formatDateArray = (dateArray) => {
  const [year, month, day] = dateArray

  // 使用 String.prototype.padStart() 补零
  const formattedMonth = String(month).padStart(2, '0')
  const formattedDay = String(day).padStart(2, '0')

  return dateArray.replace(/-/g, '/');
}

// [yyyy,mm,dd]格式 转换为 yyyy/mm/dd
export const formatDateTimeArray = (dateArray) => {
  const [year, month, day] = dateArray
  return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
}

// 将时间戳格式转换为 yyyy-mm-dd
export const timestampToDate = (timestamp: number) => {
// 创建日期对象
  const date = new Date(timestamp);

  // 获取年、月、日
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要+1
  const day = String(date.getDate()).padStart(2, '0');

  // 返回格式化后的字符串
  return `${year}-${month}-${day}`;
}


