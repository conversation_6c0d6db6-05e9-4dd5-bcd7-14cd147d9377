export const langMenuMap = [
  { lang: 'zh-CN', labelKey: 'nameCn' },
  { lang: 'en', labelKey: 'nameEn' },
  { lang: 'ar', labelKey: 'nameAr' }
]
export const langLabelMap = [
  { lang: 'zh-CN', labelKey: 'labelCn' },
  { lang: 'en', labelKey: 'labelEn' },
  { lang: 'ar', labelKey: 'labelAr' }
]
// 部分未从后台数据中获取国际化翻译,直接从项目中路由文件中获取,比如新增修改
export const validTitles = [
  'router.home',
  'router.dictName',
  'common.profile',
  'router.addBanner',
  'router.editBanner',
  'router.contentCourse',
  'router.importList',
  'router.addJourney',
  'router.addOnBoarding',
  'router.learningRecords',
  'router.addCompanyPolicy',
  'router.addOrientation',
  'router.createExam',
  'router.editExam',
  'router.viewExam',
  'router.examRecord',
  'router.addCustomizedPaper',
  'router.addAutoPaper',
  'router.editPaper',
  'router.questionMgt',
  'router.addQuestion',
  'router.editQuestion',
  'router.courseDetail',
  'router.liveDetail',
  'router.onboardingDetail',
  'router.companyPolicyDetail',
  'router.examDetail',
  'router.studentDetail',
  'router.manage',
  'router.processDetail'
]
export const dictNameList = [
  { routerName: 'router.home'},
  { routerName: 'router.dictName'},
  { routerName: 'common.profile'},
  { routerName: 'router.addBanner'},
  { routerName: 'router.editBanner'},
  { routerName: 'router.contentCourse'},
  { routerName: 'router.importList'},
  { routerName: 'router.addJourney'},
  { routerName: 'router.addOnBoarding'},
  { routerName: 'router.learningRecords'},
  { routerName: 'router.addCompanyPolicy'},
  { routerName: 'router.addOrientation'},
  { routerName: 'router.createExam'},
  { routerName: 'router.editExam'},
  { routerName: 'router.viewExam'},
  { routerName: 'router.examRecord'},
  { routerName: 'router.addCustomizedPaper'},
  { routerName: 'router.addAutoPaper'},
  { routerName: 'router.editPaper'},
  { routerName: 'router.questionMgt'},
  { routerName: 'router.addQuestion'},
  { routerName: 'router.editQuestion'},
  { routerName: 'router.courseDetail'},
  { routerName: 'router.liveDetail'},
  { routerName: 'router.onboardingDetail'},
  { routerName: 'router.companyPolicyDetail'},
  { routerName: 'router.examDetail'},
  { routerName: 'router.studentDetail'},
  { routerName: 'router.manage'},
  { routerName: 'router.processDetail'},
]
