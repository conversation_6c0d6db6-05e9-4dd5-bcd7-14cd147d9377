/**
 * 通用js方法封装处理
 * Copyright (c) 2019 ruoyi
 */
// 日期格式化
export function parseTime(time: any, pattern?: string) {
  if (arguments.length === 0 || !time)
    return null

  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  }
  else {
    if ((typeof time === 'string') && (/^\d+$/.test(time)))
      time = Number.parseInt(time)
    else if (typeof time === 'string')
      // eslint-disable-next-line prefer-regex-literals
      time = time.replace(new RegExp(/-/g), '/').replace('T', ' ').replace(new RegExp(/\.\d{3}/g), '')

    if ((typeof time === 'number') && (time.toString().length === 10))
      time = time * 1000

    date = new Date(time)
  }
  const formatObj: Record<string, any> = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_str = format.replace(/\{([ymdhisa])+\}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a')
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    if (result.length > 0 && value < 10)
      value = `0${value}`

    return value || 0
  })
  return time_str
}

// 表单重置
export function resetForm(refName: string) {
  // @ts-expect-error no error
  if (this.$refs[refName])
  // @ts-expect-error no error
    this.$refs[refName].resetFields()
}

// 添加日期范围
export function addDateRange(params: any, dateRange: any[], propName?: string) {
  const search = params
  search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {}
  dateRange = Array.isArray(dateRange) ? dateRange : []
  if (typeof (propName) === 'undefined') {
    search.params.beginTime = dateRange[0]
    search.params.endTime = dateRange[1]
  }
  else {
    search.params[`begin${propName}`] = dateRange[0]
    search.params[`end${propName}`] = dateRange[1]
  }
  return search
}

// 回显数据字典
export function selectDictLabel(datas: any, value: any) {
  if (value === undefined)
    return ''

  const actions = []
  // eslint-disable-next-line array-callback-return
  Object.keys(datas).some((key) => {
    if (datas[key].value === (`${value}`)) {
      actions.push(datas[key].label)
      return true
    }
  })
  if (actions.length === 0)
    actions.push(value)

  return actions.join('')
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas: any, value: any, separator: any) {
  if (value === undefined || value.length === 0)
    return ''

  if (Array.isArray(value))
    value = value.join(',')

  const actions: any[] = []
  const currentSeparator = undefined === separator ? ',' : separator
  const temp = value.split(currentSeparator)
  // eslint-disable-next-line array-callback-return
  Object.keys(value.split(currentSeparator)).some((val) => {
    let match = false
    // eslint-disable-next-line array-callback-return
    Object.keys(datas).some((key) => {
      if (datas[key].value === (`${temp[val]}`)) {
        actions.push(datas[key].label + currentSeparator)
        match = true
      }
    })
    if (!match)
      actions.push(temp[val] + currentSeparator)
  })
  return actions.join('').substring(0, actions.join('').length - 1)
}

// 字符串格式化(%s )
export function sprintf(str: any, ...args: any[]) {
  let flag = true; let i = 0
  str = str.replace(/%s/g, () => {
    const arg = args[i++]
    if (typeof arg === 'undefined') {
      flag = false
      return ''
    }
    return arg
  })
  return flag ? str : ''
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str: string) {
  if (!str || str === 'undefined' || str === 'null')
    return ''

  return str
}

// 数据合并
export function mergeRecursive(source: any, target: any) {
  for (const p in target) {
    try {
      if (target[p].constructor === Object)
        source[p] = mergeRecursive(source[p], target[p])
      else
        source[p] = target[p]
    }
    catch (e) {
      source[p] = target[p]
    }
  }
  return source
};

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data: any, id: any, parentId?: any, children?: any) {
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children',
  }

  const childrenListMap: any = {}
  const nodeIds: any = {}
  const tree = []

  for (const d of data) {
    const parentId = d[config.parentId]
    if (childrenListMap[parentId] == null)
      childrenListMap[parentId] = []

    nodeIds[d[config.id]] = d
    childrenListMap[parentId].push(d)
  }

  for (const d of data) {
    const parentId = d[config.parentId]
    if (nodeIds[parentId] == null)
      tree.push(d)
  }

  for (const t of tree)
    adaptToChildrenList(t, 1)

  function adaptToChildrenList(o: any, level: number) {
    o.level = level
    if (childrenListMap[o[config.id]] !== null)
      o[config.childrenList] = childrenListMap[o[config.id]]

    if (o[config.childrenList]) {
      for (const c of o[config.childrenList])
        adaptToChildrenList(c, level + 1)
    }
  }
  return tree
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params: any) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    const part = `${encodeURIComponent(propName)}=`
    if (value !== null && value !== '' && typeof (value) !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== '' && typeof (value[key]) !== 'undefined') {
            const params = `${propName}[${key}]`
            const subPart = `${encodeURIComponent(params)}=`
            result += `${subPart + encodeURIComponent(value[key])}&`
          }
        }
      }
      else {
        result += `${part + encodeURIComponent(value)}&`
      }
    }
  }
  return result
}

// 返回项目路径
export function getNormalPath(p: any) {
  if (p.length === 0 || !p || p === 'undefined')
    return p

  const res = p.replace('//', '/')
  if (res[res.length - 1] === '/')
    return res.slice(0, res.length - 1)

  return res
}

// 验证是否为blob格式
export function blobValidate(data: any) {
  return data.type !== 'application/json'
}
/**
 * 将秒转换为分钟格式
 * @param totalSeconds 秒
 * @returns 如果是-1返回"-"，否则返回分钟:秒格式(mm:ss)
 */
export function formatSecond(totalSeconds: number) {
  // 如果是-1，返回"-"
  if (totalSeconds === -1) {
    return '-'
  }
  
  // 计算分钟和秒
  const minutes: string | number = Math.floor(totalSeconds / 60) // 计算分钟
  const seconds: string | number = totalSeconds % 60 // 计算秒
  return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`
}

/**
 * 将秒转换为分秒
 */
export function convertToTime(totalSeconds: number) {
  const hours: number = Math.floor(totalSeconds / 3600) // 计算小时数
  const minutes: number = Math.floor((totalSeconds % 3600) / 60) // 计算分钟数
  const remainingSeconds: number = totalSeconds % 60 // 计算剩余的秒数

  // 使用 padStart() 方法确保每个单位都是两位数，不足的补零
  const formattedHours = String(hours).padStart(2, '0')
  const formattedMinutes = String(minutes).padStart(2, '0')
  const formattedSeconds = String(remainingSeconds).padStart(2, '0')

  // 返回格式化的时分秒
  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
}

export function formatDecimal(num: number) {
  // 判断是否为小数
  if (num % 1 !== 0) {
    const integerPart = Math.floor(num) // 取整数部分
    const decimalPart = (num % 1).toFixed(2).substring(2) // 取小数部分，保留两位小数（注意：去掉'0.'）
    return {
      integer: integerPart,
      decimal: decimalPart,
    }
  }
  return num
}
/**
 * 将字节数据格式化显示
 * @param bytes 字节数据
 */
export function formatBytes(bytes: number) {
  if (bytes === 0)
    return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / k ** i).toPrecision(3)} ${sizes[i]}`
  // toPrecision(3) 后面保留两位小数，如1.00GB
}