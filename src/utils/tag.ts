import type { RouteLocationNormalizedLoaded } from 'vue-router'

export function setupManualClosePage(view: RouteLocationNormalizedLoaded, callback: ((view: any) => void) | (undefined)) {
  // eslint-disable-next-line dot-notation
  view!.meta['manualClose'] = callback
}
export function setupManualRefreshPage(view: RouteLocationNormalizedLoaded, callback: ((view: any) => void) | (undefined)) {
  // eslint-disable-next-line dot-notation
  view!.meta['manualRefresh'] = callback
}
