import { fileTypeFromBlob } from 'file-type'

// 定义 FileExtension 类型，包含所有支持的扩展名
type FileExtension =
  | 'pdf'
  | 'jpg'
  | 'png'
  | 'xls'
  | 'xlsx'
  | 'doc'
  | 'docx'
  | 'ppt'
  | 'pptx'
  | 'txt'
  | 'mp3'
  | 'mp4'
  | 'wav'
  | 'avi'
  | 'mov'

// 定义 FileTypeMap 映射
export const FileTypeMap = new Map<FileExtension, string>([
  ['pdf', 'application/pdf'],
  ['jpg', 'image/jpeg'],
  ['png', 'image/png'],
  ['xls', 'application/vnd.ms-excel'],
  ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  ['doc', 'application/msword'],
  ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  ['ppt', 'application/vnd.ms-powerpoint'],
  ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],
  ['txt', 'text/plain'],
  ['mp3', 'audio/mpeg'],
  ['mp4', 'video/mp4'],
  ['wav', 'audio/wav'],
  ['avi', 'video/x-msvideo'],
  ['mov', 'video/quicktime'],
  ['cfb', 'application/x-cfb']
])

export const checkFile = async (file: File, fileTypes: string[]) => {
  let checkResult = { can: false, type: '' }
  const fileName = file.name

  // 检查文件名称长度
  if (fileName.length > 255) {
    useMessage().notifyWarning(`The file name: ${fileName}, cannot exceed 255 characters.`)
    return checkResult
  }

  // 检查文件 MIME 类型
  let mimetype = await fileTypeFromBlob(file)
  console.log('mimetype:', mimetype)
  // 如果无法获取 MIME 类型且文件是 .txt 文件，则手动设置 MIME 类型为 text/plain
  if (!mimetype) {
    if (fileName.toLowerCase().endsWith('.txt')) {
      mimetype = { ext: 'txt', mime: 'text/plain' }
    } else {
      useMessage().notifyWarning('Unsupported File Mimetype: ' + file.name)
      return checkResult
    }
  }
  // 检查文件类型是否在支持的文件类型列表中
  if (!fileTypes.includes(mimetype.ext.toUpperCase())) {
    useMessage().notifyWarning('Unsupported File Type: ' + mimetype.ext)
    return checkResult
  }
  // 检查 MIME 类型是否在 FileTypeMap 中（忽略大小写）
  if (!FileTypeMap.has(mimetype.ext.toLowerCase() as FileExtension)) {
    useMessage().notifyWarning('Unsupported File Type: ' + mimetype.ext)
    return checkResult
  }

  // 文件通过检查，返回文件类型
  checkResult = { can: true, type: FileTypeMap.get(mimetype.ext.toLowerCase() as FileExtension)! }
  return checkResult
}

// 获取文件扩展名，根据 MIME 类型查找
export function getFileExt(mimeType: string): string {
  const fileExt = Array.from(FileTypeMap.keys()).find((key) => FileTypeMap.get(key) === mimeType)
  return fileExt?.toLowerCase() || 'unknown'
}
// 文件预览
export const handlePreview = (fileUrl: string, filename: string) => {
  const baseUrl = `${window.location.origin}/preview`
  // 构建查询字符串
  const queryParams = new URLSearchParams({
    url: fileUrl,
    title: filename
  }).toString()
  const fullUrl = `${baseUrl}?${queryParams}`
  window.open(fullUrl, '_blank')
}
// file-type支持的文件类型
// 3g2- 3GPP2 为 3G CDMA2000 多媒体服务定义的多媒体容器格式
// 3gp- 第三代合作伙伴计划 (3GPP) 为 3G UMTS 多媒体服务定义的多媒体容器格式
// 3mf- 3D制造格式
// 7z- 7-Zip 档案
// Z- Unix 压缩文件
// aac- 高级音频编码
// ac3- ATSC A/52 音频文件
// ace- ACE 档案
// ai- Adob​​e Illustrator 图稿
// aif- 音频交换文件
// alias- macOS 别名文件
// amr- 自适应多速率音频编解码器
// ape- 猴子的音频
// apng- 动画便携式网络图形
// ar- 存档文件
// arj- 存档文件
// arrow- 数据表的列格式
// arw- Sony Alpha Raw 图像文件
// asar- 主要用于封装 Electron 应用程序的存档格式
// asf- 高级系统格式
// avi- 音频视频交错文件
// avif- AV1 图像文件格式
// avro- Apache Avro 开发的对象容器文件
// blend-Blender 项目
// bmp- 位图图像文件
// bpg- 更好的便携式图形文件
// bz2- 存档文件
// cab- 内阁文件
// cfb- 复合文件二进制格式
// chm- Microsoft 编译的 HTML 帮助
// class- Java 类文件
// cpio- Cpio 档案
// cr2- Canon Raw 图像文件 (v2)
// cr3- Canon Raw 图像文件 (v3)
// crx- Google Chrome 扩展程序
// cur- 图标文件
// dcm- DICOM 图像文件
// deb-Debian 软件包
// dmg- 苹果磁盘映像
// dng- Adob​​e 数字负片图像文件
// docx- Microsoft Word 文档
// dsf- 索尼 DSD 流文件 (DSF)
// dwg- Autodesk CAD 文件
// elf- Unix 可执行和可链接格式
// eot- 嵌入 OpenType 字体
// eps- 封装的 PostScript
// epub- 电子书文件
// exe- 可执行文件
// f4a- Adob​​e Flash Player 使用的纯音频 ISO 基础媒体文件格式
// f4b- Adob​​e Flash Player 使用的有声读物和播客 ISO 基础媒体文件格式
// f4p- ISO 基础媒体文件格式受 Adob​​e Flash Player 使用的 Adob​​e Access DRM 保护
// f4v- Adob​​e Flash Player 使用的 ISO 基础媒体文件格式
// fbx- Filmbox 是一种专有文件格式，用于提供数字内容创作应用程序之间的互操作性。
// flac- 免费无损音频编解码器
// flif- 免费无损图像格式
// flv- Flash 视频
// gif- 图形交换格式
// glb- GL 传输格式
// gz- 存档文件
// heic- 高效图像文件格式
// icc- ICC 配置文件
// icns- 苹果图标图像
// ico- Windows 图标文件
// ics- 电子日历
// indd- Adob​​e InDesign 文档
// it- 音频模块格式：Impulse Tracker
// j2cJPEG 2000
// jls- 连续色调图像的无损/近无损压缩标准
// jp2JPEG 2000
// jpg- 联合图像专家组图像
// jpmJPEG 2000
// jpxJPEG 2000
// jxl-JPEG XL 图像格式
// jxr- 联合图像专家组扩大范围
// ktx- OpenGL 和 OpenGL ES 纹理
// lnk- Microsoft Windows 文件快捷方式
// lz- 存档文件
// lzh- LZH 档案
// m4a- 仅音频的 MPEG-4 文件
// m4b- 有声读物和播客 MPEG-4 文件，其中还包含章节标记、图像和超链接等元数据
// m4p- 通过 iTunes Store 销售的 MPEG-4 文件，其音频流由 FairPlay 数字版权管理加密
// m4v- Apple 开发的视频容器格式，与 MP4 格式非常相似
// macho- Mach-O 二进制格式
// mid- 乐器数字接口文件
// mie- 专用元信息格式，支持存储二进制和文本元信息
// mj2运动 JPEG 2000
// mkv- Matroska 视频文件
// mobi- 摩比口袋
// mov- QuickTime 视频文件
// mp1- MPEG-1 音频层 I
// mp2- MPEG-1 音频层 II
// mp3- 音频文件
// mp4- MPEG-4 Part 14 视频文件
// mpc- 缪斯包 (SV7 和 SV8)
// mpg- MPEG-1 文件
// mts- MPEG-2 传输流，包括原始版本和蓝光光盘音频视频 (BDAV) 版本
// mxf- 素材交换格式
// nef- 尼康电子格式图像文件
// nes- 任天堂 NES ROM
// odp- 用于演示的开放文档
// ods- 电子表格的 OpenDocument
// odt- 用于文字处理的 OpenDocument
// oga- 音频文件
// ogg- 音频文件
// ogm- 音频文件
// ogv- 音频文件
// ogx- 音频文件
// opus- 音频文件
// orf- 奥林巴斯 Raw 图像文件
// otf- OpenType 字体
// parquet- 阿帕奇镶木地板
// pcap- Libpcap 文件格式
// pdf- 可移植文档格式
// pgp- 隐私性相当好
// png- 便携式网络图形
// pptx- Microsoft Powerpoint 文档
// ps- 后记
// psd- Adob​​e Photoshop 文档
// pst- 个人存储表文件
// qcp- 标记和分块数据
// raf- 富士 RAW 图像文件
// rar- 存档文件
// rpm- Red Hat 软件包管理器文件
// rtf- 富文本格式
// rw2- 松下 RAW 图像文件
// s3m- 音频模块格式：ScreamTracker 3
// shp- 地理空间矢量数据格式
// skp- SketchUp
// spx- 音频文件
// sqlite-SQLite 文件
// stl- 标准镶嵌几何文件格式（仅限 ASCII）
// swf- Adob​​e Flash Player 文件
// tar- Tarball 存档文件
// tif- 标记图像文件
// ttfTrueType 字体
// vcf- 电子名片
// voc- 创意语音文件
// wasm- WebAssembly 中间编译格式
// wav- 波形音频文件
// webm- 网络视频文件
// webp- 网页图片格式
// woff- Web 开放字体格式
// woff2- Web 开放字体格式
// wv- WavPack
// xcf- 实验计算设施
// xlsx- Microsoft Excel 文档
// xm- 音频模块格式：FastTracker 2
// xml- 可扩展标记语言
// xpi- XP安装文件
// xz- 压缩文件
// zip- 存档文件
// zst- 存档文件
//
// 添加上传队列任务
