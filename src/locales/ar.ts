export default {
  media: {
    noFileSelected: 'لم يتم اختيار ملف',
    fileSizeError: 'حجم الملف لا يمكن أن يتجاوز 50 ميجابايت',
    unsupportedFormat: 'تنسيق الملف غير مدعوم',
    uploadFile: 'يرجى تحميل ملف أولاً',
    transcriptionFailed: 'فشل النسخ، يرجى المحاولة مرة أخرى',
    errorOccurred: 'حدث خطأ، يرجى المحاولة لاحقًا',
    startTranscription: 'ابدأ النسخ',
    dragAndDrop: 'اسحب وأفلت',
    selectFile: 'أو اختر ملفًا',
    supportedFormats: 'أنواع الملفات المدعومة',
    browseFile: 'تصفح الملفات',
    maxFileSize: 'أقصى حجم للملف: 500 ميجابايت'
  },
  translation: {
    mediaTranscription: 'نسخ الوسائط',
    textTranslation: 'ترجمة النص',
    documentTranslation: 'ترجمة المستند',
    fileName: 'اسم المستند',
    maxFileSize: 'أقصى حجم للملف: 20 ميجابايت',
    copySuccess: 'تم النسخ بنجاح',
    copyFailed: 'فشل النسخ',
    failed: 'فشل الترجمة',
    formatError: 'تنسيق الملف غير صحيح',
    sizeError: 'حجم الملف لا يمكن أن يتجاوز 20 ميجابايت',
    emptyText: 'النص لا يمكن أن يكون فارغًا',
    sameLanguage: 'لا يمكن أن تكون اللغات المصدر والهدف نفسها',
    cannotSwap: 'نتيجة الترجمة فارغة'
  },
  tooltip: {
    clearText: 'مسح النص',
    copy: 'نسخ'
  },
  placeholder: {
    translationResult: 'نتيجة الترجمة'
  },
  button: {
    translate: 'ترجم'
  },
  common: {
    inputText: 'يرجى إدخال',
    selectText: 'يرجى اختيار',
    chooseText: 'الرجاء الاختيار',
    choose: 'اختر',
    noData: 'لا توجد بيانات',
    startTimeText: 'وقت البدء',
    endTimeText: 'وقت الانتهاء',
    login: 'تسجيل الدخول',
    required: 'هذا مطلوب',
    loginOut: 'تسجيل الخروج',
    profile: 'مركز المستخدم',
    reminder: 'تذكير',
    loginOutMessage: 'هل تريد الخروج من النظام؟',
    back: 'رجوع',
    ok: 'موافق',
    save: 'حفظ',
    cancel: 'إلغاء',
    close: 'إغلاق',
    reload: 'إعادة تحميل الحالي',
    success: 'نجاح',
    closeTab: 'إغلاق الحالي',
    closeTheLeftTab: 'إغلاق اليسار',
    closeTheRightTab: 'إغلاق اليمين',
    closeOther: 'إغلاق الأخرى',
    closeAll: 'إغلاق الكل',
    prevLabel: 'السابق',
    nextLabel: 'التالي',
    skipLabel: 'تخطي',
    doneLabel: 'انتهى',
    menu: 'القائمة',
    menuDes: 'شريط القوائم المعروض في الهيكل الموجه',
    collapse: 'طي',
    collapseDes: 'توسيع وتصغير شريط القوائم',
    tagsView: 'عرض التاج',
    tagsViewDes: 'يستخدم لتسجيل تاريخ التوجيه',
    tool: 'أداة',
    toolDes: 'تستخدم لإعداد الأنظمة المخصصة',
    query: 'استعلام',
    reset: 'إعادة تعيين',
    shrink: 'إخفاء',
    expand: 'توسيع',
    confirmTitle: 'تلميح النظام',
    exportMessage: 'هل تؤكد تصدير بيانات العنصر؟',
    importMessage: 'هل تؤكد استيراد بيانات العنصر؟',
    createSuccess: 'تم الإنشاء بنجاح',
    updateSuccess: 'تم التحديث بنجاح',
    delMessage: 'هل تريد حذف البيانات المحددة؟',
    delAllMessage: 'هل تريد حذف جميع البيانات؟',
    delDataMessage: 'هل تريد حذف البيانات؟',
    delNoData: 'يرجى اختيار البيانات للحذف',
    delSuccess: 'تم الحذف بنجاح',
    delOption: 'هل أنت متأكد من حذف الخيار المحدد؟',
    keyWords: 'لا يمكن تكرار الكلمات المفتاحية.',
    keyWordsLength: 'يمكنك تعيين ما يصل إلى خمس كلمات مفتاحية، وكل كلمة مفتاحية يمكن أن تحتوي على 300 حرفًا إنجليزيًا كحد أقصى.',
    isRecommend: 'التوصية:',
    uploadMessage: 'جاري تحميل الملف، يرجى الانتظار...',
    uploadFormat: 'الصيغ المدعومة:',
    uploadError: 'فشل التحميل',
    noScormLink: 'لم يتم الحصول على رابط تشغيل Scorm',
    uploadLoadingError: 'فشل التحميل، يرجى التحقق مما إذا كان الاتصال بالشبكة يعمل بشكل صحيح.',
    fromRepository: 'من المستودع',
    source: 'المصدر:',
    language: 'اللغة:',
    duration: 'المدة:',
    format: 'الصيغة:',
    size: 'الحجم:',
    subject: 'الموضوع:',
    type: 'النوع:',
    uploadFormatMessage: 'الصيغ المدعومة: Zip، صوت، فيديو، PDF، PPT، Word',
    uploadFormatMessage2: 'PDF',
    uploadFormatMessage3: 'الصيغ المدعومة: doc/txt/pdf/csv/xls/ppt',
    noExist: 'ملاحظة: المورد موجود بالفعل ولا حاجة لتحميله مرة أخرى.',
    inputNumberText: 'يرجى إدخال رقم',
    selectTeachingForm: 'اختر شكل التعليم',
    selectTeachingContent: 'اختر محتوى التعليم',
    learningSetting: 'إعدادات التعلم',
    noSubject: 'لا يوجد موضوع',
    unzip: 'جاري فك الضغط',
    failure: 'فشل',
    nonScorm: 'غير Scorm',
    updateFailure: 'للالتحاق بالتحديث، يرجى إيقاف التشغيل أولاً.',
    incompatible: 'غير متوافق',
    index: 'الفهرس',
    status: 'الحالة',
    createTime: 'وقت الإنشاء',
    updateTime: 'وقت التحديث',
    copy: 'نسخ',
    copySuccess: 'تم النسخ بنجاح',
    copyError: 'خطأ في النسخ',
    upload: 'يرجى تحميل الملف',
    uploadSuccessLength: 'عدد النجاحات في التحميل:',
    updateSuccessLength: 'عدد النجاحات في التحديث:',
    updateFailLength: 'عدد الفشل في التحديث:',
    selectAll: 'تحديد الكل / إلغاء تحديد الكل',
    expandAll: 'فتح الكل / طي الكل',
    yes: 'نعم',
    no: 'لا',
    expandSwitch: 'فتح',
    collapseSwitch: 'طي',
    loading: 'جارٍ التحميل، يرجى الانتظار',
    linkage: 'الارتباط بين الأب والابن (عند تحديد العقدة الأبوية، يتم اختيار العقد الفرعية تلقائيًا):',
    message: 'عنوان المسار الذي تم الوصول إليه، مثل: `user`. إذا كان هناك عنوان خارجي، فإنه يجب أن يبدأ بـ `http(s)://`',
    menuPath: 'عنوان المسار',
    menuMessage: "رمز الأذونات على طريقة Controller، مثل: @PreAuthorize(`@ss.hasPermission('system:user:list')`)",
    menuShowStatusMessage: 'عند اختيار الإخفاء، لن يكون المسار مرئيًا في شريط الجهة، ولكن يمكن الوصول إليه',
    menuAlwaysShowMessage: 'عند اختيار "ليس" فإن القائمة تُعرض فقط عند وجود أكثر من قائمة فرعية واحدة',
    menuCacheStatusMessage: 'عند اختيار التخزين المؤقت، سيتم تخزينه بواسطة `keep-alive`، ويجب ملء حقل "اسم المكون"',
    data: 'البيانات',
    default: 'افتراضي',
    primary: 'رئيسي',
    warning: 'تحذير',
    info: 'معلومات',
    danger: 'خطر',
    catalog: 'الفهرس',
    button: 'زر',
    pleaseInput: 'الرجاء الإدخال',
    parameter: 'المعلمة',
    cannotBeEmpty: 'لا يمكن أن يكون فارغًا',
    searchMenuContent: 'الرجاء إدخال محتويات القائمة',
    adminMenuName: 'إدارة نهاية',
    studentMenuName: 'العميل',
    scan: 'مسح',
    result: 'نتيجة',
    batchAction: 'إجراء مجمع',
    ddtPermitNo: 'رقم تصريح DDT',
    dateOfBirth: 'تاريخ الميلاد',
    projectAsset: 'المشروع/الأصل',
    workType: 'نوع العمل',
    workTerm: 'مدة العمل',
    drivingLicenceNumber: 'رقم رخصة القيادة',
    issuingDate: 'تاريخ الإصدار',
    expiryDate: 'تاريخ الانتهاء',
    vehicle: 'المركبة',
    eyeTest: 'فحص النظر',
    testResult: 'نتيجة الاختبار',
    reject: 'رفض',
    pass: 'نجح',
    fail: 'فشل',
    postpone: 'تأجيل',
    batchReject: 'رفض مجمع',
    batchPass: 'نجح مجمع',
    batchFail: 'فشل مجمع',
    batchPostpone: 'تأجيل مجمع',
    successfulOperation: 'تمت العملية بنجاح',
    projectQRCode: 'عرض رمز الاستجابة السريعة',
    downloadQRCode: 'تحميل رمز الاستجابة السريعة',
    launch: 'إطلاق',
    uploadFile: 'رفع',
    materialName: 'اسم المادة',
    creationTime: 'وقت الإنشاء',
    uploadMaterials: 'رفع المواد',
    video: 'فيديو',
    pdf: 'PDF',
    pocEmail: 'بريد POC الإلكتروني',
    pocPhoneNumber: 'رقم هاتف POC',
    contractHolder: 'حامل العقد',
    ePassportNo: 'رقم جواز السفر الإلكتروني',
    bookingMode: 'وضع الحجز',
    batchRemove: 'إزالة مجمعة',
    cantQueryProcessInfo: 'لا يمكن الاستعلام عن معلومات العملية!',
    preferredDate: 'التاريخ المفضل',
    receivedDate: 'تاريخ الاستلام',
    courseTitle: 'عنوان الدورة',
    workTerms: 'شروط العمل',
    assign: 'تعيين',
    batchAssign: 'تعيين مجمع',
    pleaseSelectSameCourse: 'يرجى اختيار الطلاب من نفس الدورة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    publishStatus: 'حالة النشر',
    classTitle: 'عنوان الفصل',
    classType: 'نوع الفصل',
    trainer: 'المدرب',
    classroom: 'الفصل الدراسي',
    date: 'التاريخ',
    bookingNumber: 'رقم الحجز',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    nextWeek: 'الأسبوع القادم',
    thisYear: 'هذا العام',
    allocationSuccessful: 'تم التخصيص بنجاح',
    start: 'بداية',
    end: 'نهاية',
    advancedScreening: 'تصفية متقدمة',
    clear: 'مسح',
    uploadSuccess: 'تحميل الملف بنجاح'
  },
  lock: {
    lockScreen: 'قفل الشاشة',
    lock: 'قفل',
    lockPassword: 'كلمة مرور قفل الشاشة',
    unlock: 'انقر لإلغاء القفل',
    backToLogin: 'الرجوع إلى تسجيل الدخول',
    entrySystem: 'دخول النظام',
    placeholder: 'يرجى إدخال كلمة مرور قفل الشاشة',
    message: 'خطأ في كلمة مرور قفل الشاشة'
  },
  error: {
    noPermission: 'عذرًا، ليس لديك إذن للوصول إلى هذه الصفحة.',
    pageError: 'عذرًا، الصفحة التي زرتها غير موجودة.',
    construction: 'عذرًا، الصفحة قيد الإنشاء.',
    networkError: 'عذرًا، أبلغ الخادم عن خطأ.',
    returnToHome: 'الرجوع إلى الصفحة الرئيسية',
    uploadError: 'Falhat al-taḥmīl, يرجى إعادة التحميل!',
    uploadErrorLength: 'لا يمكن تحميل أكثر من ملف واحد فقط!',
    pathError: 'يجب أن يبدأ المسار بـ /',
    pathErrorStart: 'لا يمكن أن يبدأ المسار بـ /',
    fileFormatError: 'صيغة الملف غير صحيحة، يرجى تحميل الملف مرة أخرى',
    imageFormatFile: `ملف بصيغة الصورة!`,
    imageSizeError: 'لا يمكن أن يتجاوز حجم صورة الأفاتار المرفوعة',
    imageSizeErrorLength: 'الرجاء تحميل الصور وفقًا للحجم المحدد',
    imageLoading: 'فشل تحميل الصورة',
    fileLengthError: 'لا يمكن أن يتجاوز عدد الملفات المرفوعة',
    uploadErrorMessage: 'فشل رفع الصور',
    pleaseSetDuration: 'يرجى تعيين المدة',
    fileTypeError: 'نوع الملف الذي تم تحميله لا يتطابق مع النوع المحدد',
    fileSizeError: 'الحجم ليس أكثر من',
    m: 'ميجابايت!',
    failedToUploadFile: 'فشل تحميل الملف',
    dropFileHereOr: 'أفلت الملف هنا أو',
    pleaseChooseExamPaper: 'يرجى اختيار ورقة الامتحان',
    fileFormatIsNotCorrect: 'تنسيق الملف غير صحيح، يرجى تحميل',
    formatFile: 'ملف بالتنسيق الصحيح!',
    fileSizeIsNotCorrect: 'خطأ في تنسيق الصورة',
    fileSizeIsNotCorrectLength: 'لا يمكن أن يتجاوز حجم الملف المرفوع',
    imageInsertionFailed: 'فشل إدراج الصورة',
    uploadTo: 'يمكنك تحميل ما يصل إلى',
    resourcesAtATime: ' مورد في المرة الواحدة!'


  },
  loading: {
    upLoading: 'تحميل الصور ، الرجاء الانتظار . . .'
  },
  permission: {
    hasPermission: 'يرجى تعيين قيمة تصنيف إذن العملية',
    hasRole: 'يرجى تعيين قيمة تصنيف إذن الدور'
  },
  setting: {
    projectSetting: 'إعداد المشروع',
    theme: 'السمة',
    layout: 'التخطيط',
    systemTheme: 'سمة النظام',
    menuTheme: 'سمة القائمة',
    interfaceDisplay: 'عرض الواجهة',
    breadcrumb: 'سلسلة التنقل',
    breadcrumbIcon: 'أيقونة سلسلة التنقل',
    collapseMenu: 'طي القائمة',
    hamburgerIcon: 'أيقونة الهامبرغر',
    screenfullIcon: 'أيقونة الشاشة الكاملة',
    sizeIcon: 'أيقونة الحجم',
    localeIcon: 'أيقونة اللغة',
    messageIcon: 'أيقونة الرسائل',
    tagsView: 'عرض التاج',
    logo: 'الشعار',
    greyMode: 'وضع رمادي',
    fixedHeader: 'رأس ثابت',
    headerTheme: 'سمة الرأس',
    cutMenu: 'قص القائمة',
    copy: 'نسخ',
    clearAndReset: 'مسح الكاش وإعادة التعيين',
    copySuccess: 'تم نسخ الترجمة',
    copyFailed: 'فشل النسخ',
    footer: 'التذييل',
    uniqueOpened: 'فتح واحد فقط',
    tagsViewIcon: 'أيقونة عرض التاج',
    reExperienced: 'يرجى الخروج من التجربة مرة أخرى',
    fixedMenu: 'قائمة ثابتة',
    myInternalMessage: 'رسالتي',
    viewAll: 'عرض جميع',
    // 轮播图
    banner: {
      status: 'حالة الرف',
      title: 'العنوان',
      no: 'رقم',
      updateTime: 'وقت التحديث',
      basicInfo: 'المعلومات الأساسية',
      maxSize: '（الحد الأقصى لحالة الرف هو 4 مدخلات）',
      titlePH: 'الرجاء إدخال عنوان الخبر، أقل من 50 كلمة',
      cover: 'الغلاف',
      coverPH: 'تنسيق PNG、JPG、GIF، أقل من 500 كيلوبايت',
      summary: 'ملخص',
      summaryPH: 'الرجاء إدخال ملخص الخبر، أقل من 5000 كلمة',
      detail: 'التفاصيل',
      titleRule: 'لا يمكن أن يكون العنوان فارغًا',
      coverRule: 'لا يمكن أن يكون الغلاف فارغًا',
      sort: 'ترتيب',
      onShelfTime: 'وقت الرف'
    },
  },
  size: {
    default: 'افتراضي',
    large: 'كبير',
    small: 'صغير'
  },
  login: {
    welcome: 'مرحبًا بك في النظام',
    message: 'نظام إدارة الواجهة الخلفية',
    tenantname: 'اسم المستأجر',
    username: 'اسم المستخدم',
    password: 'كلمة المرور',
    code: 'رمز التحقق',
    login: 'تسجيل الدخول',
    relogin: 'تسجيل الدخول مرة أخرى',
    otherLogin: 'تسجيل الدخول باستخدام',
    register: 'تسجيل',
    checkPassword: 'تأكيد كلمة المرور',
    remember: 'تذكرني',
    hasUser: 'هل لديك حساب؟ تسجيل الدخول',
    forgetPassword: 'نسيت كلمة المرور؟',
    tenantNamePlaceholder: 'يرجى إدخال اسم المستأجر',
    usernamePlaceholder: 'يرجى إدخال اسم المستخدم',
    passwordPlaceholder: 'يرجى إدخال كلمة المرور',
    codePlaceholder: 'يرجى إدخال رمز التحقق',
    mobileTitle: 'تسجيل الدخول عبر الهاتف',
    mobileNumber: 'رقم الهاتف المحمول',
    mobileNumberPlaceholder: 'يرجى إدخال رقم الهاتف المحمول',
    backLogin: 'الرجوع',
    getSmsCode: 'احصل على رمز SMS',
    btnMobile: 'تسجيل الدخول عبر الهاتف',
    btnQRCode: 'تسجيل الدخول عبر QR',
    qrcode: 'مسح رمز الاستجابة السريعة لتسجيل الدخول',
    btnRegister: 'التسجيل',
    btnAD: 'تسجيل الدخول باستخدام Active Directory',
    btnMFA: 'تسجيل الدخول باستخدام MFA',
    SmsSendMsg: 'تم إرسال الرمز'
  },
  captcha: {
    verification: 'يرجى إتمام التحقق الأمني',
    slide: 'اسحب إلى اليمين لإتمام التحقق',
    point: 'يرجى النقر',
    success: 'تم التحقق بنجاح',
    fail: 'فشل التحقق'
  },
  router: {
    login: 'تسجيل الدخول',
    home: 'لوحة أجهزة القياس',
    dictName: 'قاموس البيانات',
    analysis: 'التحليل',
    workplace: 'مكان العمل',
    addBanner: 'تحت الرف',
    editBanner: 'تحرير لافتات',
    contentCourse: 'محتوى الدورة',
    importList: 'قائمة الاستيراد',
    addJourney: 'إضافة/تعديل الرحلة',
    addOnBoarding: 'إضافة/تعديل تدريب التوظيف',
    learningRecords: 'سجلات التعلم',
    addCompanyPolicy: 'إضافة / تحرير سياسة الشركة',
    addOrientation: 'إضافة / تحرير الاتجاه',
    createExam: 'إنشاء امتحان',
    editExam: 'تحرير الامتحan',
    viewExam: 'عرض الامتحان',
    examRecord: 'سجل الامتحان',
    addCustomizedPaper: 'إضافة ورقة مخصصة',
    addAutoPaper: 'إضافة ورقة تلقائية',
    editPaper: 'تحرير الورقة',
    questionMgt: 'إدارة الأسئلة',
    addQuestion: 'إضافة سؤال',
    editQuestion: 'تحرير السؤال',
    courseDetail: 'تفاصيل الدورة',
    onboardingDetail: 'تفاصيل التهيئة الوظيفية',
    companyPolicyDetail: 'تفاصيل سياسة الشركة',
    examDetail: 'تفاصيل الفحص',
    studentDetail: 'تفاصيل الطالب',
    manage: 'إدارة'
  },
  analysis: {
    newUser: 'مستخدم جديد',
    unreadInformation: 'معلومات غير مقروءة',
    transactionAmount: 'مقدار المعاملة',
    totalShopping: 'إجمالي التسوق',
    monthlySales: 'المبيعات الشهرية',
    userAccessSource: 'مصدر وصول المستخدم',
    january: 'يناير',
    february: 'فبراير',
    march: 'مارس',
    april: 'أبريل',
    may: 'مايو',
    june: 'يونيو',
    july: 'يوليو',
    august: 'أغسطس',
    september: 'سبتمبر',
    october: 'أكتوبر',
    november: 'نوفمبر',
    december: 'ديسمبر',
    estimate: 'تقدير',
    actual: 'فعلي',
    directAccess: 'الوصول المباشر',
    mailMarketing: 'التسويق عبر البريد الإلكتروني',
    allianceAdvertising: 'إعلانات التحالف',
    videoAdvertising: 'الإعلانات عبر الفيديو',
    searchEngines: 'محركات البحث',
    weeklyUserActivity: 'نشاط المستخدم الأسبوعي',
    activeQuantity: 'الكمية النشطة',
    monday: 'الإثنين',
    tuesday: 'الثلاثاء',
    wednesday: 'الأربعاء',
    thursday: 'الخميس',
    friday: 'الجمعة',
    saturday: 'السبت',
    sunday: 'الأحد'
  },
  workplace: {
    welcome: 'مرحبًا',
    happyDay: 'نتمنى لك يومًا سعيدًا!',
    toady: 'اليوم مشمس',
    notice: 'إشعار',
    project: 'المشروع',
    access: 'دخول المشروع',
    toDo: 'المهام',
    introduction: 'مقدمة جادة',
    shortcutOperation: 'دخول سريع',
    operation: 'عملية',
    index: 'الفهرس',
    personal: 'شخصي',
    team: 'الفريق',
    quote: 'اقتباس',
    contribution: 'مساهمة',
    hot: 'شائع',
    yield: 'العائد',
    dynamic: 'ديناميكي',
    push: 'دفع',
    follow: 'اتبع'
  },
  form: {
    input: 'حقل إدخال',
    inputNumber: 'حقل إدخال رقمي',
    default: 'افتراضي',
    icon: 'أيقونة',
    mixed: 'مختلط',
    textarea: 'نص متعدد الأسطر',
    slot: 'فتحة',
    position: 'الموقع',
    autocomplete: 'إكمال تلقائي',
    select: 'منتقي',
    selectGroup: 'مجموعة خيارات',
    selectV2: 'منتقي قائمة افتراضية',
    cascader: 'منتقي متسلسل',
    switch: 'تبديل',
    rate: 'تقييم',
    colorPicker: 'منتقي اللون',
    transfer: 'نقل بين القوائم',
    render: 'محرك عرض',
    radio: 'زر اختيار واحد',
    button: 'زر',
    checkbox: 'مربع اختيار',
    slider: 'شريط تمرير',
    datePicker: 'منتقي التاريخ',
    shortcuts: 'اختصارات',
    today: 'اليوم',
    yesterday: 'الأمس',
    aWeekAgo: 'منذ أسبوع',
    week: 'أسبوع',
    year: 'سنة',
    month: 'شهر',
    dates: 'تواريخ',
    daterange: 'نطاق التواريخ',
    monthrange: 'نطاق الشهور',
    dateTimePicker: 'منتقي التاريخ والوقت',
    dateTimerange: 'نطاق التاريخ والوقت',
    timePicker: 'منتقي الوقت',
    timeSelect: 'اختيار الوقت',
    inputPassword: 'حقل إدخال كلمة المرور',
    passwordStrength: 'قوة كلمة المرور',
    operate: 'عملية',
    change: 'تغيير',
    restore: 'استعادة',
    disabled: 'معطل',
    disablement: 'إلغاء التعطيل',
    delete: 'حذف',
    add: 'إضافة',
    setValue: 'إعداد القيمة',
    resetValue: 'إعادة تعيين القيمة',
    set: 'إعداد',
    subitem: 'عنصر فرعي',
    formValidation: 'تحقق من النموذج',
    verifyReset: 'إعادة التحقق',
    remark: 'ملاحظات',

    // --------مستخدم----------
    userNickName: 'اسم المستخدم المستعار',
    belongingDept: 'القسم التابع له',
    phoneNumber: 'رقم الهاتف',
    userName: 'اسم المستخدم',
    userPassword: 'كلمة مرور المستخدم',
    email: 'البريد الإلكتروني',
    userSex: 'جنس المستخدم',
    posts: 'المواقع',

    // --------دور----------
    roles: 'الأدوار',
    roleName: 'اسم الدور',
    roleType: 'نوع الدور',
    roleKey: 'معرف الدور',
    showSort: 'ترتيب العرض',
    status: 'الحالة',

    // --------قائمة----------
    menuPermission: 'أذونات القائمة',
    permissionScope: 'نطاق الأذونات',
    menuCode: 'رمز القائمة',
    menu: 'اسم القائمة',
    menuName: 'اسم القائمة الصينية',
    menuNameEn: 'اسم القائمة الإنجليزية',
    menuNameAr: 'اسم القائمة العربية',
    previousMenu: 'القائمة السابقة',
    menuType: 'نوع القائمة',
    menuIcon: 'أيقونة القائمة',
    menuPath: 'عنوان المسار',
    menuComponent: 'مسار المكون',
    menuComponentName: 'اسم المكون',
    menuPermissionKey: 'معرف الأذونات',
    menuSort: 'ترتيب العرض',
    menuStatus: 'حالة القائمة',
    menuShowStatus: 'حالة العرض',
    show: 'عرض',
    hide: 'إخفاء',
    alwaysShow: 'عرض دائمًا',
    always: 'دائماً',
    not: 'ليس',
    cacheStatus: 'حالة التخزين المؤقت',
    cache: 'تخزين مؤقت',
    noCache: 'لا تخزين مؤقت',

    // --------قسم----------
    deptName: 'اسم القسم',
    deptStatus: 'حالة القسم',
    parentDept: 'القسم الرئيسي',
    deptShowSort: 'ترتيب العرض',
    deptNickName: 'المسؤول',
    deptPhone: 'رقم الهاتف',
    deptEmail: 'البريد الإلكتروني',

    // --------موقع----------
    postName: 'اسم الموقع',
    postCode: 'رمز الموقع',
    postSort: 'ترتيب الموقع',

    // --------قاموس----------
    dictName: 'اسم القاموس',
    dictType: 'نوع القاموس',
    createTime: 'وقت الإنشاء',
    dictLabel: 'علامة القاموس',
    dictLabelCn: 'القاموس الصيني العلامات',
    dictLabelEn: 'قاموس اللغة الإنجليزية التسمية',
    dictLabelAr: 'قاموس العلامات',
    dictValue: 'قيمة القاموس',
    dictShowSort: 'ترتيب العرض',
    dictColor: 'نوع اللون',
    cssClass: 'CSS Class',

    // --------مركز الرسائل----------
    // إدارة الرسائل القصيرة (قناة الرسائل)
    signature: 'توقيع الرسالة القصيرة',
    enableStatus: 'حالة التفعيل',
    channelCode: 'رمز القناة',
    channelStatus: 'حالة التفعيل',
    channelApiKey: 'حساب API للرسائل القصيرة',
    channelApiSecret: 'مفتاح سري API للرسائل القصيرة',
    channelCallbackUrl: 'URL استدعاء الإرسال الخاص بالرسائل القصيرة',

    // إدارة الرسائل القصيرة (قالب الرسائل)
    smsType: 'نوع الرسالة القصيرة',
    openStatus: 'حالة الفتح',
    templateCode: 'رمز القالب',
    templateId: 'رقم القالب من API الرسائل القصيرة',
    templateContent: 'قالب الرسالة القصيرة',
    templateChannel: 'قناة الرسائل القصيرة',
    templateChannelCode: 'رمز قناة الرسائل القصيرة',
    templateName: 'اسم القالب',
    templateSubstance: 'محتوى القالب',
    mobile: 'رقم الهاتف',

    // إدارة الرسائل القصيرة (سجل الرسائل)
    logTemplateCode: 'رمز القالب',
    sendStatus: 'حالة الإرسال',
    sendTime: 'وقت الإرسال',
    receiveStatus: 'حالة الاستقبال',
    receiveTime: 'وقت الاستقبال',

    // إدارة البريد الإلكتروني (قالب البريد)
    receiveEmail: 'عنوان البريد الإلكتروني المستقبل',

    // --------إدارة الرسائل الداخلية----------
    // إدارة الرسائل الداخلية ===> إدارة القوالب
    senderName: 'اسم المرسل',
    type: 'النوع',
    emailUserType: 'نوع المستخدم',
    recipientID: 'معرف المستقبل',
    recipient: 'المستقبل',

    // إدارة الرسائل الداخلية ===> سجل الرسائل
    userId: 'معرف المستخدم',
    templateType: 'نوع القالب',

    // --------إشعارات وإعلانات----------
    noticeTitle: 'عنوان الإشعار',
    noticeContent: 'محتوى الإشعار',
    noticeStatus: 'حالة الإشعار',
    noticeType: 'نوع الإشعار',

    // --------قائمة المستأجر----------
    tenantName: 'اسم المستأجر',
    contactName: 'اسم جهة الاتصال',
    contactPhone: 'هاتف جهة الاتصال',
    tenantStatus: 'حالة المستأجر',
    tenantPackage: 'باقة المستأجر',
    tenantQuota: 'حصة الحساب',
    expireTime: 'وقت الانتهاء',
    domain: 'اسم النطاق المرتبط',

    // --------باقة المستأجر----------
    packageName: 'اسم الباقة',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramName: 'اسم المعلمة',
    paramKey: 'مفتاح المعلمة',
    paramType: 'مضمن في النظام',
    paramCategory: 'تصنيف المعلمة',
    paramValue: 'قيمة المعلمة',
    paramVisible: 'هل هو مرئي',
    // --------我的站内信----------
    readStatus: 'هل قرأت',
  },
  watermark: {
    watermark: 'علامة مائية'
  },
  table: {
    table: 'جدول',
    index: 'فهرس',
    title: 'عنوان',
    author: 'المؤلف',
    createTime: 'وقت الإنشاء',
    action: 'الإجراء',
    pagination: 'التقسيم إلى صفحات',
    reserveIndex: 'حجز الفهرس',
    restoreIndex: 'استعادة الفهرس',
    showSelections: 'عرض الاختيارات',
    hiddenSelections: 'إخفاء الاختيارات',
    showExpandedRows: 'عرض الصفوف الموسعة',
    hiddenExpandedRows: 'إخفاء الصفوف الموسعة',
    header: 'الرأس',
    // --------ContentType Module----------
    userNumber: 'رقم المستخدم',
    userName: 'اسم المستخدم',
    userNickName: 'اسم المستخدم المستعار',
    deptName: 'القسم',
    phoneNumber: 'رقم الهاتف',
    status: 'الحالة',

// --------Role Module----------
    roleNumber: 'رقم الدور',
    roleName: 'اسم الدور',
    roleType: 'نوع الدور',
    roleKey: 'معرف الدور',
    showSort: 'ترتيب العرض',
    remark: 'ملاحظات',

// --------Menu Module----------
    menuName: 'اسم القائمة',
    menuIcon: 'أيقونة القائمة',
    menuSort: 'الترتيب',
    menuPermission: 'معرف الأذونات',
    menuComponent: 'مسار المكون',
    menuComment: 'اسم المكون',
    menuStatus: 'حالة القائمة',

// --------Department Module----------
    nickName: 'المسؤول',
    deptSort: 'الترتيب',

// --------Post Module----------
    postNumber: 'رقم الوظيفة',
    postName: 'اسم الوظيفة',
    postCode: 'رمز الوظيفة',
    postSort: 'ترتيب الوظيفة',
    postRemark: 'ملاحظات الوظيفة',

// --------Dictionary Module----------
    dictNumber: 'رقم القاموس',
    dictName: 'اسم القاموس',
    dictType: 'نوع القاموس',
    dictLabel: 'علامة القاموس',
    dictLabelCn: 'القاموس الصيني العلامات',
    dictLabelEn: 'قاموس اللغة الإنجليزية التسمية',
    dictLabelAr: 'قاموس العلامات',
    dictValue: 'قيمة القاموس',
    dictSort: 'ترتيب القاموس',
    dictColor: 'نوع اللون',
    cssClass: 'CSS Class',

// --------Message Center----------
// SMS Management Module (SMS Channel)
    channelId: 'الرقم التعريفي',
    channelCode: 'رمز القناة',
    smsSign: 'توقيع الرسالة القصيرة',
    channelStatus: 'حالة التفعيل',
    channelApiKey: 'حساب API للرسائل القصيرة',
    channelApiSecret: 'مفتاح سري API للرسائل القصيرة',
    channelCallbackUrl: 'URL استدعاء الإرسال الخاص بالرسائل القصيرة',

// SMS Management Module (SMS Template)
    templateName: 'اسم القالب',
    templateCode: 'رمز القالب',
    templateContent: 'محتوى القالب',
    templateTitle: 'عنوان القالب',
    templateType: 'نوع القالب',
    templateApiTemplateCode: 'رمز القالب من API الرسائل القصيرة',
    templateChannel: 'قناة الرسائل القصيرة',

// SMS Management Module (SMS Log)
    templateNumber: 'الرقم التعريفي',
    logSmsContent: 'محتوى الرسالة القصيرة',
    logSendStatus: 'حالة الإرسال',
    logReceiveStatus: 'حالة الاستقبال',
    logTemplateCode: 'رمز القالب',

// Email Management Module (Email Account)
    emailAccount: 'حساب البريد الإلكتروني',
    email: 'البريد الإلكتروني',
    emailUsername: 'اسم المستخدم',
    emailPassword: 'كلمة مرور البريد الإلكتروني',
    emailSmtpHost: 'اسم المجال لخادم SMTP',
    emailSmtpPort: 'منفذ خادم SMTP',
    emailSsl: 'هل سيتم تفعيل SSL؟',
    emailStarttlsEnable: 'هل سيتم تفعيل STARTTLS؟',

// Email Management Module (Email Template)
    emailFromName: 'اسم المرسل',

// Email Management Module (Email Record)
    emailUserType: 'نوع المستخدم',
    emailTitle: 'عنوان البريد الإلكتروني',
    emailContent: 'محتوى البريد الإلكتروني',
    emailParams: 'معلمات البريد الإلكتروني',
    emailAddress: 'عنوان البريد المرسل',
    emailTemplateFromName: 'اسم المرسل من القالب',
    emailSendNumber: 'رقم الرسالة المرسلة',
    emailSendException: 'استثناء الإرسال',
    sendStatus: 'حالة الإرسال',
    receiveEmail: 'البريد الإلكتروني المستقبل',

// --------Internal Message Management----------
// Internal Message Management ===> Template Management
    type: 'النوع',
    senderName: 'اسم المرسل',
    openStatus: 'حالة الفتح',

// Internal Message Management ===> Message Records
    templateParams: 'معلمات القالب',
    readStatus: 'هل تم قراءته؟',
    readTime: 'وقت القراءة',

// --------Notice Announcement----------
    noticeNumber: 'رقم الإشعار',
    noticeTitle: 'عنوان الإشعار',
    noticeType: 'نوع الإشعار',

// --------Tenant List----------
    tenantNumber: 'رقم المستأجر',
    tenantName: 'اسم المستأجر',
    tenantPackage: 'باقة المستأجر',
    sysTenant: 'مستأجر النظام',
    contactName: 'اسم جهة الاتصال',
    contactPhone: 'هاتف جهة الاتصال',
    tenantStatus: 'حالة المستأجر',
    tenantQuota: 'حصة الحساب',
    domain: 'اسم النطاق المرتبط',

// --------Tenant Package----------
    tenantPackageNumber: 'رقم الباقة',
    tenantPackageName: 'اسم الباقة',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramId: 'مفتاح المعلمة الرئيسي',
    paramCategory: 'تصنيف المعلمة',
    paramName: 'اسم المعلمة',
    paramKey: 'مفتاح المعلمة',
    paramValue: 'قيمة المعلمة',
    paramVisible: 'هل هو مرئي',
    paramClass: 'مضمن في النظام',
    // --------我的站内信----------
    sendName: 'مرسل',
    sendTime: 'وقت الإرسال',
    messageContent: 'محتوى الرسالة',
    detail: 'تفاصيل',
    read: 'قراءة',
  },
  action: {
    search: 'البحث',
    create: 'إضافة جديدة',
    add: 'إضافة جديدة',
    del: 'حذف',
    delete: 'حذف',
    edit: 'تحرير',
    update: 'تحرير',
    reset: 'إعادة تعيين',
    preview: 'معاينة',
    more: 'المزيد',
    sync: 'مزامنة',
    save: 'حفظ',
    detail: 'التفاصيل',
    export: 'تصدير',
    import: 'استيراد',
    userImport: 'استيراد المستخدم',
    generate: 'إنشاء',
    logout: 'الخروج القسري',
    test: 'اختبار',
    typeCreate: 'إضافة نوع قاموس جديد',
    typeUpdate: 'تحرير نوع القاموس',
    dataCreate: 'إضافة بيانات قاموس جديدة',
    dataUpdate: 'تحرير بيانات القاموس',
    resetPassword: 'إعادة تعيين كلمة المرور',
    assignRole: 'تخصيص دور',
    confirm: 'تأكيد',
    cancel: 'إلغاء',
    drag: 'اسحب الملف هنا، أو',
    upload: 'انقر لتحميل',
    updateExistingUsers: 'هل تريد تحديث بيانات المستخدمين الموجودة مسبقًا؟',
    fileFormat: 'يُسمح فقط باستيراد ملفات بصيغة xls، xlsx.',
    downloadTemplate: 'تنزيل القالب',
    assign: 'تخصيص دور',
    menuPermission: 'أذونات القائمة',
    dataPermission: 'أذونات البيانات',
    expand: 'توسيع/طي',
    refreshMenuCache: 'تحديث ذاكرة التخزين المؤقت للقائمة',
    refreshConfigCache: 'تحديث ذاكرة التخزين المؤقت',
    searchIcon: 'بحث عن أيقونة',
    push: 'دفع',
    fileUpload: 'تحميل الملف',
    markRead: 'علامة القراءة',
    markAllRead: 'قراءة جميع',
    putOnShelf: 'على الرف',
    removeOffShelf: 'تحت الرف',
    addKeyWord: 'مفتاح جديد',
    uploadFormat: 'ملفات xls/xlsx بحجم أقل من 10 ميجابايت',
    moveOffShelf: 'نقل إلى غير متاح',
    importingList: 'قائمة الاستيراد',
    assignCourse: 'تعيين الدورة',
    statistics: 'الإحصائيات',
    back: 'رجوع',
    offShelf: 'غير متاح',
    onShelf: 'متاح',
    clickRetry: 'انقر هنا لإعادة المحاولة',
    chooseExamPaper: 'اختر ورقة الامتحان',
    uploading: 'جاري التحميل',
    previous: 'السابق',
    unzip: 'فك الضغط',
    moveUp: 'تحريك للأعلى',
    moveDown: 'تحريك للأسفل',
    uploadFile: 'تحميل',
    questionManagement: 'إدارة الأسئلة',
    addChoice: '+ إضافة خيار',
    close: 'إغلاق',
    view: 'عرض',
    chooseAll: 'اختر الكل',
    remove: 'إزالة',
    reAssign: 'إعادة توزيع',
    answerDetails: 'تفاصيل الإجابة',
    uploadInBatch: 'تحميل دفعة',
    parsingList: 'تحليل القائمة',
    record: 'سجلات',
    restart: 'إعادة تشغيل',
    report: 'تقرير',
    batchDelete: 'حذف دفعة',
    settingInBatch: 'إعدادات الدفعة',
    submit: 'إرسال',
    refresh: 'تحديث',
    resissue: 'إعادة إطلاق'
  },
  dialog: {
    dialog: 'حوار',
    open: 'فتح',
    close: 'إغلاق',
    menuPermission: 'أذونات القائمة',
    dataPermission: 'إذن البيانات',
    test: 'اختبار',
    detail: 'التفاصيل',
    searchMenu: 'قائمة البحث',
    // --------我的站内信----------
    messageDetails: 'تفاصيل الرسالة',
    import: 'استيراد',
    resourceSelection: 'اختيار الموارد',
    selectEmployees: 'اختيار الموظفين',
    selectCompany: 'اختيار الشركة',
    selectDepartment: 'اختيار القسم',
    select: 'اختيار',
    edit: 'تحرير',
    resourceModificationLog: 'سجل تعديل الموارد',
    selectClassroom: 'اختيار الفصل الدراسي',
    confirm: 'تأكيد',
    venue: 'المكان',
    companyDetail: 'تفاصيل الشركة'
  },
  sys: {
    api: {
      operationFailed: 'فشلت العملية',
      errorTip: 'نصيحة الخطأ',
      errorMessage: 'فشلت العملية، النظام غير طبيعي!',
      timeoutMessage: 'انتهت صلاحية تسجيل الدخول، يرجى تسجيل الدخول مرة أخرى!',
      apiTimeoutMessage: 'انتهت صلاحية طلب الواجهة، يرجى تحديث الصفحة والمحاولة مرة أخرى!',
      apiRequestFailed: 'فشل طلب الواجهة، يرجى المحاولة مرة أخرى لاحقًا!',
      networkException: 'استثناء الشبكة',
      networkExceptionMsg: 'يرجى التحقق مما إذا كانت اتصالك بالشبكة طبيعيًا! الشبكة غير طبيعية',

      errMsg401: 'لا يمتلك المستخدم إذن (خطأ في الرمز المميز، اسم المستخدم، كلمة المرور)!',
      errMsg403: 'تم الترخيص للمستخدم، ولكن الوصول محظور!',
      errMsg404: 'خطأ في طلب الشبكة، لم يتم العثور على المورد!',
      errMsg405: 'خطأ في طلب الشبكة، طريقة الطلب غير مسموح بها!',
      errMsg408: 'انتهت مهلة طلب الشبكة!',
      errMsg500: 'خطأ في الخادم، يرجى الاتصال بالمسؤول!',
      errMsg501: 'الشبكة غير مدعومة!',
      errMsg502: 'خطأ في الشبكة!',
      errMsg503: 'الخدمة غير متوفرة، الخادم مؤقتًا محمل أو في الصيانة!',
      errMsg504: 'انتهت مهلة الشبكة!',
      errMsg505: 'إصدار HTTP لا يدعم الطلب!',
      errMsg901: 'وضع العرض فقط، لا يمكن إجراء عمليات كتابة!'
    },
    app: {
      logoutTip: 'تذكير',
      logoutMessage: 'هل أنت متأكد من أنك تريد الخروج من النظام؟',
      menuLoading: 'تحميل القائمة...'
    },
    exception: {
      backLogin: 'العودة إلى تسجيل الدخول',
      backHome: 'العودة إلى الصفحة الرئيسية',
      subTitle403: 'عذرًا، ليس لديك حق الوصول إلى هذه الصفحة.',
      subTitle404: 'عذرًا، الصفحة التي زرتها غير موجودة.',
      subTitle500: 'عذرًا، يواجه الخادم خطأ.',
      noDataTitle: 'لا توجد بيانات في الصفحة الحالية.',
      networkErrorTitle: 'خطأ في الشبكة',
      networkErrorSubTitle: 'عذرًا، تم قطع الاتصال بالشبكة الخاصة بك، يرجى التحقق من الشبكة!'
    },
    lock: {
      unlock: 'اضغط لفتح القفل',
      alert: 'خطأ في كلمة مرور القفل',
      backToLogin: 'العودة إلى تسجيل الدخول',
      entry: 'دخول النظام',
      placeholder: 'يرجى إدخال كلمة مرور القفل أو كلمة مرور المستخدم'
    },
    login: {
      backSignIn: 'العودة إلى تسجيل الدخول',
      mobileSignInFormTitle: 'تسجيل الدخول عبر الهاتف المحمول',
      qrSignInFormTitle: 'تسجيل الدخول عبر رمز الاستجابة السريعة',
      signInFormTitle: 'تسجيل الدخول',
      adFormTitle: 'تسجيل الدخول عبر AD',
      signUpFormTitle: 'إنشاء حساب',
      forgetFormTitle: 'إعادة تعيين كلمة المرور',

      signInTitle: 'نظام إدارة الواجهة الخلفية',
      signInDesc: 'أدخل بياناتك الشخصية وابدأ!',
      policy: 'أوافق على سياسة الخصوصية الخاصة بـ xxx',
      scanSign: 'امسح الرمز لإتمام تسجيل الدخول',

      loginButton: 'تسجيل الدخول',
      registerButton: 'إنشاء حساب',
      rememberMe: 'تذكرني',
      forgetPassword: 'نسيت كلمة المرور؟',
      otherSignIn: 'تسجيل الدخول باستخدام',

      // notify
      loginSuccessTitle: 'تم تسجيل الدخول بنجاح',
      loginSuccessDesc: 'مرحبًا بعودتك',

      // placeholder
      accountPlaceholder: 'الرجاء إدخال اسم المستخدم',
      passwordPlaceholder: 'الرجاء إدخال كلمة المرور',
      smsPlaceholder: 'الرجاء إدخال رمز الرسائل القصيرة',
      mobilePlaceholder: 'الرجاء إدخال رقم الهاتف المحمول',
      policyPlaceholder: 'سجل بعد الموافقة',
      diffPwd: 'كلمتا المرور غير متطابقتين',

      userName: 'اسم المستخدم',
      password: 'كلمة المرور',
      confirmPassword: 'تأكيد كلمة المرور',
      email: 'البريد الإلكتروني',
      smsCode: 'رمز الرسائل القصيرة',
      mobile: 'الهاتف المحمول'
    },
    // 用户模块
    user: {
      userName: 'اسم المستخدم',
      nickName: 'اللقب',
      userNumber: 'رقم المستخدم',
      userNickname: 'كنية المستخدم',
      department: 'قسم',
      company: 'شركة',
      phoneNumber: 'رقم الهاتف',
      userNameRule: 'لا يمكن أن يكون اسم المستخدم فارغًا',
      userNameLengthRule: 'يجب أن يكون الطول بين 5 و 50 حرفًا',
      nickNameLengthRule: 'يجب أن يكون الطول بين 1 و 128 حرفًا',
      userNicknameRule: 'لا يمكن أن يكون الاسم فارغًا',
      passwordRule: 'لا يمكن أن تكون كلمة مرور المستخدم فارغة',
      passwordLengthRule: 'يجب أن تتراوح طول كلمة المرور بين 5 و 20 حرفًا',
      emailRule: 'الرجاء إدخال عنوان بريد إلكتروني صحيح',
      phoneNumberRule: 'الرجاء إدخال رقم هاتف صحيح',
      deleteTip: 'هل أنت متأكد أنك تريد حذف المستخدم؟',
      resetPasswordTip: 'الرجاء إدخال كلمة مرور جديدة لـ',
      resetPasswordConfirm: 'بعد إعادة التعيين، سيتم إعادة تعيين كلمة مرور تسجيل الدخول للموظف وإرسال بريد إلكتروني لإعلامه. هل تريد المتابعة أو الإلغاء؟',
      resetPwdSuccess: 'تم التحديث بنجاح',
      import: 'استيراد المستخدم',
      importRes: 'نتيجة الاستيراد',
      addUser: 'إضافة مستخدم',
      editUser: 'تحرير المستخدم',
      deptNamePH: 'الرجاء إدخال',
      userNamePH: 'الرجاء إدخال اسم المستخدم',
      nickNamePH: 'الرجاء إدخال اللقب',
      phoneNumberPH: 'الرجاء إدخال رقم الهاتف',
      resetPassword: 'إعادة تعيين كلمة المرور',
      assignRoles: 'تعيين الأدوار',
      departmentPH: 'اختر القسم',
      departmentRule: 'لا يمكن أن يكون القسم فارغًا',
      companyPH: 'اختر الشركة',
      companyRule: 'لا يمكن أن تكون الشركة فارغة',
      sectionPH: 'اختر القسم',
      sectionRule: 'لا يمكن أن يكون القسم فارغًا',
      positionPH: 'اختر الوظيفة',
      positionRule: 'لا يمكن أن تكون الوظيفة فارغة',
      userNicknamePH: 'الرجاء إدخال كنية المستخدم',
      email: 'البريد الإلكتروني',
      emailPH: 'الرجاء إدخال البريد الإلكتروني الخاص بك',
      password: 'كلمة المرور',
      passwordPH: 'الرجاء إدخال كلمة المرور',
      gender: 'الجنس',
      genderPH: 'اختر الجنس',
      lineManagerPH: 'اختر مدير الخط المباشر',
      nationality: 'الجنسية',
      workTerms: 'شروط العمل',
      nationalityPH: 'اختر الجنسية',
      workTermPH: 'اختر شروط العمل',
      position: 'الموقع',
      role: 'دور',
      rolePH: 'اختر دورًا',
      roleRule: 'لا يمكن أن يكون الدور فارغًا',
      updateData: 'هل تريد تحديث بيانات المستخدم الحالية؟',
      importTip: 'يمكن استيراد ملفات xls و xlsx فقط',
      downloadTemplate: 'تنزيل النموذج',
      section: 'الجزء',
      badgePH: 'الرجاء إدخال الرقم التعريفي',
      badgeNo: 'الرقم التعريفي',
      badgeNoPH: 'الرجاء إدخال الرقم التعريفي الخاص بك',
      badgeNoRule: 'لا يمكن أن يكون الرقم التعريفي فارغًا',
      lineManager: 'مدير الخط المباشر',
      status: 'الحالة',
      statusPH: 'اختر الحالة',
      statusRule: 'لا يمكن أن تكون الحالة فارغة',
      onboardingDate: 'تاريخ التوظيف',
      onboardingDatePH: 'اختر تاريخ التوظيف',
      createTime: 'وقت الإنشاء',
      userStatus: 'حالة المستخدم',
      userStatusRule: 'لا يمكن أن تكون حالة المستخدم فارغة',
      userStatusPH: 'اختر حالة المستخدم',
      workTypeRule: 'لا يمكن أن يكون نوع العمل فارغًا',
      workType: 'نوع العمل',
      workTypePH: 'اختر نوع العمل',
    },
    // 公司模块
    company: {
      companyName: 'اسم الشركة',
      abbreviation: 'اختصار',
      type: 'النوع (المقاول)',
      uniqueCode: 'الرمز الفريد',
      dataSource: 'مصدر البيانات',
      superiorCompany: 'الشركة الأم',
      sort: 'فرز',
      createTime: 'وقت الإنشاء',
      creator: 'المنشئ',
      status: 'مركز',
      yes: 'نعم .',
      no: 'لا .',
      orderNum: 'تصنيف الشركات',
      companyCode: 'رمز الشركة',
      companyNamePH: 'الرجاء إدخال اسم الشركة',
      typePH: 'الرجاء تحديد نوع المقاول',
      uniqueCodePH: 'الرجاء إدخال الرمز الفريد',
      dataSourcePH: 'الرجاء إدخال مصدر البيانات',
      superiorCompanyPH: 'الرجاء تحديد الشركة الأم',
      abbreviationPH: 'الرجاء إدخال اختصار',
      companyCodePH: 'الرجاء إدخال رمز الشركة',
      serviceCompanyIdRule: 'الرجاء تحديد الشركة الأم',
      deptNameRule: 'الرجاء إدخال اسم الشركة',
      shortNameRule: 'الرجاء إدخال الاختصار',
      orderNumRule: 'لا يمكن ترك ترتيب العرض فارغًا',
      deptCodeRule: 'الرجاء إدخال رمز الشركة',
      typeRule: 'الرجاء تحديد نوع المقاول',
    },
    // 部门模块
    dept: {
      addDept: 'إضافة قسم',
      editDept: 'تحرير القسم',
      expand: 'طي/نشر',
      totalTip: 'إجمالي عدد الأقسام',
      deptName: 'اسم القسم',
      superiorDept: 'القسم الأعلى',
      superiorDeptPH: 'الرجاء تحديد القسم الأعلى',
      deptSort: 'ترتيب القسم',
      deptCode: 'رمز القسم',
      deptCodePH: 'الرجاء إدخال رمز القسم',
      delTip: "هل أنت متأكد أنك تريد حذف البيانات الخاصة بالقسم المسمى",
      refuseDelTip: "القسم الحالي يحتوي على أقسام فرعية ولا يمكن حذفه",
      deptNamePH: "الرجاء إدخال اسم القسم",
      parentIdRule: "الرجاء تحديد القسم الأعلى",
      deptNameRule: "الرجاء إدخال اسم القسم",
      shortNameRule: "الرجاء إدخال الاختصار",
      orderNumRule: "لا يمكن ترك ترتيب العرض فارغًا",
      deptCodeRule: "الرجاء إدخال رمز القسم",
      typeRule: "الرجاء تحديد النوع",
      parentPH: "الرجاء تحديد القسم الأعلى",
      abbrPH: "الرجاء إدخال اختصار القسم",
    },
    // 部分模块
    section: {
      addSection: 'إضافة قسم',
      editSection: 'تحرير القسم',
      sectionName: 'اسم القسم',
      sectionNamePH: 'الرجاء إدخال اسم القسم',
      superiorSection: 'القسم الأعلى',
      superiorSectionRule: 'الرجاء تحديد القسم الأعلى',
      sectionSort: 'ترتيب القسم',
      sectionCode: 'رمز القسم',
      sectionCodePH: 'الرجاء إدخال رمز القسم',
      sectionNameRule: 'الرجاء إدخال اسم القسم',
      sectionCodeRule: 'الرجاء إدخال رمز القسم',
      orderNumRule: 'الرجاء إدخال ترتيب القسم',
    },
    // 岗位模块
    post: {
      addPost: 'إضافة منصب',
      editPost: 'تحرير المنصب',
      postName: 'اسم المنصب',
      deptName: 'توليد تلقائي',
      section: 'القسم',
      sectionPH: 'الرجاء تحديد القسم',
      parentPosition: 'المنصب الأعلى',
      parentPositionPH: 'الرجاء تحديد المنصب الأعلى',
      positionName: 'اسم المنصب',
      positionPH: 'الرجاء إدخال اسم المنصب',
      positionSort: 'ترتيب المنصب',
      positionCode: 'رمز المنصب',
      positionCodePH: 'الرجاء إدخال رمز المنصب',
      positionNameRule: 'الرجاء إدخال اسم المنصب',
      positionCodeRule: 'الرجاء إدخال رمز المنصب',
      orderNumRule: 'الرجاء إدخال ترتيب المنصب',
      totalTip: 'مجموع الوظائف'
    },
    // 配置管理(一期)
    config: {
      addConfig: 'إضافة تكوين',
      editConfig: 'تحرير التكوين',
      configName: 'اسم التكوين',
      configNamePH: 'الرجاء إدخال اسم التكوين',
      configKey: 'مفتاح التكوين',
      configKeyPH: 'الرجاء إدخال مفتاح التكوين',
      builtInSystem: 'مضمن في النظام',
      configId: 'هوية التكوين',
      configValue: 'قيمة التكوين',
      configValuePH: 'الرجاء إدخال قيمة التكوين',
      remark: 'ملاحظة',
      remarkPH: 'الرجاء إدخال ملاحظة',
      configNameRule: 'لا يمكن أن يكون اسم المعلمة فارغًا',
      configKeyRule: 'لا يمكن أن يكون مفتاح المعلمة فارغًا',
      configValueRule: 'لا يمكن أن تكون قيمة المعلمة فارغة',
    },
  },
  profile: {
    user: {
      title: 'معلومات شخصية',
      username: 'اسم المستخدم',
      nickname: 'الاسم المستعار',
      mobile: 'رقم الهاتف',
      email: 'البريد الإلكتروني',
      dept: 'القسم',
      posts: 'المنصب',
      roles: 'الدور',
      sex: 'الجنس',
      man: 'رجل',
      woman: 'امرأة',
      createTime: 'تاريخ الإنشاء'
    },
    info: {
      title: 'المعلومات الأساسية',
      basicInfo: 'المعلومات الأساسية',
      resetPwd: 'إعادة تعيين كلمة المرور',
      userSocial: 'المعلومات الاجتماعية'
    },
    rules: {
      nickname: 'يرجى إدخال الاسم المستعار للمستخدم',
      mail: 'يرجى إدخال عنوان البريد الإلكتروني',
      truemail: 'يرجى إدخال البريد الإلكتروني الصحيح',
      phone: 'يرجى إدخال رقم الهاتف',
      truephone: 'يرجى إدخال رقم الهاتف الصحيح'
    },
    password: {
      oldPassword: 'كلمة المرور القديمة',
      newPassword: 'كلمة المرور الجديدة',
      confirmPassword: 'تأكيد كلمة المرور',
      oldPwdMsg: 'يرجى إدخال كلمة المرور القديمة',
      newPwdMsg: 'يرجى إدخال كلمة المرور الجديدة',
      cfPwdMsg: 'يرجى إدخال تأكيد كلمة المرور',
      diffPwd: 'كلمات المرور المدخلة غير متطابقة'
    }
  },
  cropper: {
    selectImage: 'اختيار صورة',
    uploadSuccess: 'تم التحميل بنجاح!',
    modalTitle: 'تحميل الصورة الشخصية',
    okText: 'تأكيد ورفع الصورة',
    btn_reset: 'إعادة تعيين',
    btn_rotate_left: 'التدوير عكس اتجاه الساعة',
    btn_rotate_right: 'التدوير مع اتجاه الساعة',
    btn_scale_x: 'التقليب أفقيًا',
    btn_scale_y: 'التقليب عموديًا',
    btn_zoom_in: 'تكبير',
    btn_zoom_out: 'تصغير',
    preview: 'معاينة'
  },
  input: {
    // --------ContentType Module----------
    deptPlaceholder: 'الرجاء إدخال اسم القسم',
    userPlaceholder: 'الرجاء إدخال اسم المستخدم',
    userNickNamePlaceholder: 'الرجاء إدخال الاسم المستعار للمستخدم',
    phonePlaceholder: 'الرجاء إدخال رقم الهاتف',
    emailPlaceholder: 'الرجاء إدخال البريد الإلكتروني',
    passwordTitle: 'الرجاء إدخال كلمة مرور المستخدم',
    passwordPlaceholder: 'الرجاء إدخال كلمة المرور',
    startTime: 'وقت البدء',
    endTime: 'وقت النهاية',
    pleaseInput: 'الرجاء إدخال المحتوى',

    // --------Role Module----------
    roleNamePlaceholder: 'الرجاء إدخال اسم الدور',
    roleKeyPlaceholder: 'الرجاء إدخال معرف الدور',
    orderPlaceholder: 'الرجاء إدخال ترتيب العرض',

    // --------Menu Module----------
    menuCodePlaceholder: 'الرجاء إدخال رمز القائمة',
    menuPlaceholder: 'من فضلك أدخل اسم القائمة',
    menuNamePlaceholder: 'الرجاء إدخال اسم القائمة الصينية',
    menuNameEnPlaceholder: 'الرجاء إدخال اسم القائمة الإنجليزية',
    menuNameArPlaceholder: 'الرجاء إدخال اسم القائمة العربية',
    routeAddressPlaceholder: 'الرجاء إدخال عنوان المسار',
    menuComponentPlaceholder: 'على سبيل المثال: system/user/index',
    menuComponentNamePlaceholder: 'على سبيل المثال: SystemUser',
    menuPermissionPlaceholder: 'الرجاء إدخال معرف الأذونات',

    // --------Department Module----------
    deptNamePlaceholder: 'الرجاء إدخال اسم القسم',
    deptStatus: 'الرجاء اختيار حالة القسم',
    parentDept: 'الرجاء اختيار القسم الرئيسي',
    deptNickName: 'الرجاء إدخال المسؤول',
    deptPhone: 'الرجاء إدخال رقم الاتصال',
    deptEmail: 'الرجاء إدخال البريد الإلكتروني',

    // --------Post Module----------
    postNamePlaceholder: 'الرجاء إدخال اسم الوظيفة',
    postCodePlaceholder: 'الرجاء إدخال رمز الوظيفة',
    postSortPlaceholder: 'الرجاء إدخال ترتيب الوظيفة',
    remark: 'الرجاء إدخال ملاحظات',

    // --------Dictionary Module----------
    dictNamePlaceholder: 'الرجاء إدخال اسم القاموس',
    dictTypePlaceholder: 'الرجاء إدخال نوع القاموس',
    dictStatusPlaceholder: 'الرجاء إدخال حالة القاموس',
    dictLabelPlaceholder: 'من فضلك أدخل القاموس التسمية',
    dictLabelCnPlaceholder: 'من فضلك أدخل القاموس الصيني التسمية',
    dictLabelEnPlaceholder: 'من فضلك أدخل قاموس اللغة الإنجليزية التسمية',
    dictLabelArPlaceholder: 'من فضلك أدخل القاموس العربي التسمية',
    dictNameTypePlaceholder: 'الرجاء إدخال اسم المعلمة',
    dictValuePlaceholder: 'الرجاء إدخال بيانات العلامات',
    dictKeyPlaceholder: 'الرجاء إدخال قيمة مفتاح القاموس',
    dictClassPlaceholder: 'الرجاء إدخال CSS Class',
    dictRemarkPlaceholder: 'الرجاء إدخال ترتيب القاموس',

    // --------Message Center----------
    // SMS Management Module (SMS Channel)
    smsSignPlaceholder: 'الرجاء إدخال توقيع الرسالة القصيرة',
    channelCodePlaceholder: 'الرجاء اختيار رمز القناة',
    apiAccountPlaceholder: 'الرجاء إدخال حساب API للرسائل القصيرة',
    apiSecretPlaceholder: 'الرجاء إدخال مفتاح سري API للرسائل القصيرة',
    apiCallbackPlaceholder: 'الرجاء إدخال URL استدعاء API لإرسال الرسائل',

    // SMS Management Module (SMS Template)
    templateCodePlaceholder: 'الرجاء إدخال رمز القالب',
    templateNamePlaceholder: 'الرجاء إدخال اسم القالب',
    templateContentPlaceholder: 'الرجاء إدخال محتوى القالب',
    templateApiNoPlaceholder: 'الرجاء إدخال رمز القالب من API الرسائل القصيرة',

    // Email Management Module (Email Template)
    emailInputPlaceholder: 'الرجاء إدخال بريد المستلم',

    // --------Internal Message Management----------
    // Internal Message Management ===> Template Management
    templateCode: 'الرجاء إدخال رمز القالب',
    templateName: 'الرجاء إدخال اسم القالب',
    senderName: 'الرجاء إدخال اسم المرسل',
    userCodePlaceholder: 'الرجاء إدخال رمز المستخدم',

    // --------Notice Announcement----------
    noticeTitlePlaceholder: 'الرجاء إدخال عنوان الإشعار',

    // --------Tenant List----------
    tenantNamePlaceholder: 'الرجاء إدخال اسم المستأجر',
    contactName: 'الرجاء إدخال اسم جهة الاتصال',
    contactPhone: 'الرجاء إدخال رقم هاتف جهة الاتصال',
    tenantQuotaPlaceholder: 'الرجاء إدخال حصة الحساب',
    expireTime: 'الرجاء اختيار وقت الانتهاء',
    domainPlaceholder: 'الرجاء إدخال اسم المجال المرتبط',

    // --------Tenant Package----------
    packageNamePlaceholder: 'الرجاء إدخال اسم الباقة',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramNamePlaceholder: 'الرجاء إدخال اسم المعلمة',
    paramKeyPlaceholder: 'الرجاء إدخال مفتاح المعلمة',
    paramCategoryPlaceholder: 'الرجاء إدخال تصنيف المعلمة',
    paramValuePlaceholder: 'الرجاء إدخال قيمة المعلمة',
  },
  select: {
    userStatus: 'حالة المستخدم',
    userRole: 'الرجاء اختيار دور المعلومات',
    belongingDept: 'الرجاء اختيار القسم التابع له',
    pleaseSelect: 'الرجاء الاختيار',
    status: 'الرجاء اختيار الحالة',
    menuStatus: 'الرجاء اختيار حالة القائمة',
    smsStatusPlaceholder: 'الرجاء اختيار حالة التفعيل',
    templateChannelCode: 'الرجاء اختيار رمز قناة الرسائل القصيرة',
    templateType: 'الرجاء اختيار نوع الرسالة القصيرة',

    //短信管理模块(سجل الرسائل القصيرة)
    templateChannel: 'الرجاء اختيار قناة الرسائل القصيرة',
    templateChannelPlaceholder: 'الرجاء اختيار رمز قناة الرسائل القصيرة',
    templateStatusPlaceholder: 'الرجاء اختيار حالة الإرسال',
    templateSendStatusPlaceholder: 'الرجاء اختيار حالة الاستقبال',
    templateTypePlaceholder: 'الرجاء اختيار النوع',
    templateReceiveNamePlaceholder: 'الرجاء اختيار المستقبل',
    pleaseSelectStatus: 'الرجاء اختيار حالة التفعيل',
    userTypePlaceholder: 'الرجاء اختيار نوع المستخدم',
    notifyTemplateTypePlaceholder: 'الرجاء اختيار نوع القالب',

    // --------إشعارات وإعلانات----------
    noticeStatusPlaceholder: 'الرجاء اختيار حالة الإشعار',
    noticeTypePlaceholder: 'الرجاء اختيار نوع الإشعار',
    tenantStatusPlaceholder: 'الرجاء اختيار الحالة',
    tenantPackagePlaceholder: 'الرجاء اختيار باقة المستأجر',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramTypePlaceholder: 'الرجاء اختيار الخيار المضمن في النظام',
  },
  // 搜索区域内容标题
  search: {
    userName: 'اسم المستخدم',
    phoneNumber: 'رقم الهاتف',
    status: 'الحالة',
    createTime: 'وقت الإنشاء',
    search: 'بحث',
    roleName: 'اسم الدور',
    roleKey: 'معرف الدور',
  },
  // form表单中的检验提醒
  formValidate: {
    // --------ContentType Module----------
    userName: 'اسم المستخدم لا يمكن أن يكون فارغًا',
    userNickName: 'اسم المستخدم المستعار لا يمكن أن يكون فارغًا',
    userPassword: 'كلمة مرور المستخدم لا يمكن أن تكون فارغة',
    email: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
    phoneNumber: 'يرجى إدخال رقم هاتف صحيح',

    // --------Role Module----------
    roles: 'يرجى اختيار دور',
    roleName: 'اسم الدور لا يمكن أن يكون فارغًا',
    roleKey: 'دور الهوية لا يمكن أن تكون فارغة أو دخلت في شكل غير صحيح',
    showSort: 'ترتيب العرض لا يمكن أن يكون فارغًا',
    status: 'الحالة لا يمكن أن تكون فارغة',
    remark: 'الملاحظات لا يمكن أن تكون فارغة',

    // --------Menu Module----------
    menuName: 'اسم القائمة الصينية لا يمكن أن تكون فارغة',
    menuNameEn: 'اسم القائمة الإنجليزية لا يمكن أن تكون فارغة',
    menuNameAr: 'العربية اسم القائمة لا يمكن أن تكون فارغة',
    menuCode: 'رمز القائمة لا يمكن أن تكون فارغة',
    menuSort: 'ترتيب القائمة لا يمكن أن يكون فارغًا',
    menuPath: 'عنوان المسار لا يمكن أن يكون فارغًا',

    // --------Department Module----------
    parentDept: 'القسم الرئيسي لا يمكن أن يكون فارغًا',
    deptName: 'اسم القسم لا يمكن أن يكون فارغًا',
    deptShowSort: 'ترتيب العرض لا يمكن أن يكون فارغًا',
    deptEmail: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
    deptPhone: 'يرجى إدخال رقم هاتف صحيح',

    // --------Post Module----------
    postName: 'اسم الوظيفة لا يمكن أن يكون فارغًا',
    postCode: 'رمز الوظيفة لا يمكن أن يكون فارغًا',
    postStatus: 'حالة الوظيفة لا يمكن أن تكون فارغة',
    postRemark: 'محتوى الوظيفة لا يمكن أن يكون فارغًا',

    // --------Dictionary Module----------
    dictName: 'اسم القاموس لا يمكن أن يكون فارغًا',
    dictType: 'نوع القاموس لا يمكن أن يكون فارغًا',
    dictLabelCn: 'البيانات الصينية التسمية لا يمكن فارغة',
    dictLabelEn: 'بيانات اللغة الإنجليزية التسمية لا يمكن فارغة',
    dictLabelAr: 'العربية تسمية البيانات لا يمكن أن تكون فارغة',
    dictValue: 'قيمة البيانات لا يمكن أن تكون فارغة',
    dictShowSort: 'ترتيب البيانات لا يمكن أن يكون فارغًا',

    // --------Message Center----------
    // SMS Management Module (SMS Channel)
    signature: 'توقيع الرسالة القصيرة لا يمكن أن يكون فارغًا',
    channelCode: 'رمز القناة لا يمكن أن يكون فارغًا',
    enableStatus: 'حالة التفعيل لا يمكن أن تكون فارغة',
    channelApiKey: 'حساب API للرسائل القصيرة لا يمكن أن يكون فارغًا',

    // SMS Management Module (SMS Template)
    templateType: 'نوع الرسالة القصيرة لا يمكن أن يكون فارغًا',
    templateOpenStatus: 'حالة الفتح لا يمكن أن تكون فارغة',
    templateCode: 'رمز القالب لا يمكن أن يكون فارغًا',
    templateName: 'اسم القالب لا يمكن أن يكون فارغًا',
    templateContent: 'محتوى القالب لا يمكن أن يكون فارغًا',
    templateId: 'رقم القالب من API الرسائل القصيرة لا يمكن أن يكون فارغًا',
    templateChannelCode: 'رمز قناة الرسائل القصيرة لا يمكن أن يكون فارغًا',
    templateMobile: 'الهاتف لا يمكن أن يكون فارغًا',

    // Email Management Module (Email Template)
    templateEmail: 'البريد الإلكتروني لا يمكن أن يكون فارغًا',
    templateEmailContent: 'محتوى القالب لا يمكن أن يكون فارغًا',

    // --------Internal Message Management----------
    // Internal Message Management ===> Template Management
    messageType: 'نوع الرسالة لا يمكن أن يكون فارغًا',
    sendName: 'اسم المرسل لا يمكن أن يكون فارغًا',

    // --------Internal Message Management----------
    // Internal Message Management ===> Template Management
    userId: 'رقم المستخدم لا يمكن أن يكون فارغًا',
    templateNumber: 'رقم القالب لا يمكن أن يكون فارغًا',

    // --------Notice Announcement----------
    noticeTitle: 'عنوان الإشعار لا يمكن أن يكون فارغًا',
    noticeContent: 'محتوى الإشعار لا يمكن أن يكون فارغًا',
    noticeType: 'نوع الإشعار لا يمكن أن يكون فارغًا',

    // --------Tenant List----------
    tenantName: 'اسم المستأجر لا يمكن أن يكون فارغًا',
    tenantPackage: 'باقة المستأجر لا يمكن أن تكون فارغة',
    contactName: 'اسم جهة الاتصال لا يمكن أن يكون فارغًا',
    tenantStatus: 'حالة المستأجر لا يمكن أن تكون فارغة',
    tenantQuota: 'حصة الحساب لا يمكن أن تكون فارغة',
    expireTime: 'وقت الانتهاء لا يمكن أن يكون فارغًا',
    domain: 'اسم النطاق المرتبط لا يمكن أن يكون فارغًا',

    // --------Tenant Package----------
    packageName: 'اسم الباقة لا يمكن أن يكون فارغًا',
    packageMenu: 'رقم القائمة المرتبطة لا يمكن أن يكون فارغًا',
    // --------基础设施----------
    // 基础设施===========> 配置管理
    paramCategory: 'لا يمكن أن يكون تصنيف المعلمة فارغًا',
    paramName: 'لا يمكن أن يكون اسم المعلمة فارغًا',
    paramKey: 'لا يمكن أن يكون مفتاح المعلمة فارغًا',
    paramValue: 'لا يمكن أن تكون قيمة المعلمة فارغة',
    paramVisible: 'لا يمكن أن يكون خيار الرؤية فارغًا',
  },
  confirm: {
    // --------Menu Module----------
    refreshMenuCache: 'سيتم تحديث ذاكرة التخزين المؤقت وإعادة تحميل المتصفح قريباً! تحديث ذاكرة التخزين المؤقت للقائمة',
    sendMessage: 'هل تريد إرسال الإشعارات المختارة؟',
    isOffShelf: 'إذا تم سحب الدورة من الرف، لن يتمكن الطلاب من الدخول إليها مرة أخرى. هل تريد التأكيد؟',
    importCourse: 'هل أنت متأكد من أنك تريد استيراد هذه الدورة؟',
    reImportCourse: 'هل أنت متأكد من أنك تريد إعادة استيراد الدورة الحالية؟',
    deImportCourse: 'هل أنت متأكد من أنك تريد حذف الدورة الحالية؟',
    deleteChapter: 'هل أنت متأكد من أنك تريد حذف الفصل الحالي المسمى',
    deleteOption: 'تأكيد حذف الخيار المحدد؟',
    deleteAssignment: 'هل أنت متأكد من إلغاء المهمة ؟',
    deleteChoice: 'حذف هذا الخيار ، تأكيد أو عدم تأكيد',
    deleteAssignCourse: 'هل أنت متأكد أنك تريد إلغاء المهمة ؟',
    uploading: 'جارٍ الرفع، سيتم قطع الاتصال عند الإغلاق، هل أنت متأكد من أنك تريد الإغلاق؟',
    deleteScorm: 'هل أنت متأكد من أنك تريد حذف scorm الذي عنوانه " ',
    refreshSuccess: 'سيتم قطع رفع الملف عند تحديث الصفحة الحالية، هل أنت متأكد من أنك تريد الاستمرار في تحديث الصفحة الحالية؟',
    sureTo: 'هل أنت متأكد من أنك تريد',
    resourceOf: 'الموارد الخاصة بـ',
    success: 'نجاح',
    deleteResource: 'هل أنت متأكد من أنك تريد حذف المورد ذو المعرف (ID)'
  },
  success: {
    sendSuccess: 'تمت العملية بنجاح! نتائج الإرسال، شاهد سجل رقم الإرسال:',
    pushSuccess: 'تمت العملية بنجاح',
    // --------我的站内信----------
    readSuccess: 'قراءة كل النجاح!',
    // 批量已读成功
    readSuccessBatch: 'قراءة الكثير بنجاح!',
  },
  descriptions: {
    // --------Message Center Module----------
    // SMS Management ---> SMS Log (Details)
    logId: 'مفتاح السجل الرئيسي',
    channelId: 'قناة الرسائل القصيرة',
    templateId: 'قالب الرسالة القصيرة',
    templateCode: 'رمز القالب من API',
    userId: 'معلومات المستخدم',
    content: 'محتوى الرسالة القصيرة',
    params: 'معلمات الرسالة القصيرة',
    createTime: 'وقت الإنشاء',
    sendStatus: 'حالة الإرسال',
    sendTime: 'وقت الإرسال',
    sendResult: 'نتيجة الإرسال من API',
    sendLogId: 'رقم الرسالة القصيرة',
    requestId: 'رقم الطلب من API',
    receiveStatus: 'حالة الاستقبال من API',
    receiveResult: 'نتيجة الاستقبال من API',

    // Internal Message Management ===> Message Records
    id: 'الرقم التعريفي',
    userType: 'نوع المستخدم',
    userNumber: 'رقم المستخدم',
    templateNumber: 'رقم القالب',
    templateCodes: 'رموز القالب',
    senderName: 'اسم المرسل',
    templateContent: 'محتوى القالب',
    templateParams: 'معلمات القالب',
    templateType: 'نوع القالب',
    readStatus: 'هل تم قراءته؟',
    readTime: 'وقت القراءة',
    // --------我的站内信----------
    sendName: 'مرسل',
    messageType: 'نوع الرسالة',
    messageContent: 'المحتويات',
  },

  global: {
    status: 'الحالة',
    createTime: 'وقت الإنشاء',
    deleteSuccess: 'تم الحذف بنجاح',
    importSuccess: 'تم الاستيراد بنجاح',
    importRowLimit: 'تجاوز حد الصفوف المستوردة',
    notInFormat: 'لا يتوافق النموذج المرفوع مع التنسيق المطلوب',
    enable: 'تفعيل',
    disable: 'تعطيل',
    changeStatusFront: 'هل أنت متأكد أنك تريد',
    the: 'ال',
    user: 'المستخدم',
    successfully: 'بنجاح',
    tip: 'نصيحة',
    ok: 'موافق',
    cancel: 'إلغاء',
    editSuccess: 'تم التحرير بنجاح',
    addSuccess: 'تمت الإضافة بنجاح',
    pleaseSelectCourse: 'الرجاء اختيار دورة واحدة على الأقل',
    submitSuccess: 'تم الإرسال بنجاح',
    startDate: 'تاريخ البدء',
    endDate: 'تاريخ الانتهاء',
    unknown: 'غير معروف',
    search: 'بحث',
    reset: 'إعادة تعيين',
    add: 'إضافة',
    edit: 'تحرير',
    delete: 'حذف',
    deleteTip: 'هل أنت متأكد أنك تريد حذف ',
    deleteFile: 'جارٍ تحميل البيانات، هل تريد حذفها أم لا؟',
    deleteExam: 'ملاحظة: لن يتمكن الدارس من مراجعة الامتحان بعد الحذف. هل تريد الحذف؟',
    import: 'استيراد',
    export: 'تصدير',
    action: 'إجراء',
    selectPH: 'الرجاء الاختيار',
    remark: 'ملاحظة',
    inputPH: 'الرجاء إدخال المحتوى',
    confirm: 'تأكيد',
    fileDrag: 'قم بإسقاط الملفات هنا',
    or: 'أو',
    clickToUpload: 'انقر للتحميل',
    select: 'الرجاء تحديد البيانات',
    clearSelection: 'مسح التحديد',
    company: 'شركة',
    refresh: 'تحديث ذاكرة التخزين المؤقت بنجاح',
    video: 'فيديو',
    audio: 'صوت',
    file: 'ملف',
    scorm: 'Scorm',
    aicc: 'AICC',

  },
  hr: {
    section: {
      refuseDelTip: "توجد أقسام فرعية ولا يمكن حذفها",
      delTip: "هل أنت متأكد أنك تريد حذف القسم المسمى",
      des: "الوصف",
      totalTip: "إجمالي عدد الأقسام",
    }
  },
  // 一期关于分类国际化
  category: {
    topic: {
      addTopic: 'إضافة موضوع',
      editTopic: 'تحرير الموضوع',
      subjectName: 'اسم الموضوع',
      subjectNamePH: 'الرجاء الإدخال',
      keyWords: 'كلمات مفتاحية',
      introduction: 'مقدمة',
      sort: 'ترتيب',
      creationTime: 'وقت الإنشاء',
      parentSubject: 'الموضوع الأب',
      parentSubject0: 'الموضوع الأب0',
      parentSubject2: 'الموضوع الأب2',
      cover: 'غلاف',
      coverPH: '150*150 بكسل أو نسبة 1:1، صيغة PNG أو JPG أو GIF، أقل من 500 كيلوبايت',
      subjectNameRule: 'الرجاء إدخال اسم الموضوع',
      keyWordsRule: 'الرجاء إدخال الكلمات المفتاحية',
      introductionRule: 'الرجاء إدخال المقدمة'
    },
    journey: {
      addCategory: 'إضافة تصنيف',
      editCategory: 'تحرير التصنيف',
      titleRule: 'التصنيف مطلوب',
      sortRule: 'الترتيب مطلوب',
      categoryTitle: 'اسم التصنيف',
      categoryTitlePH: 'الرجاء الإدخال',
      title: 'العنوان',
      sort: 'ترتيب',
      creationTime: 'وقت الإنشاء',
      creator: 'المُنشئ'
    }
  },
  // 操作日志
  log: {
    operaLog: {
      logId: 'رقم السجل',
      operator: 'المشغل',
      operationModule: 'وحدة العملية',
      operationName: 'اسم العملية',
      operationContent: 'محتوى العملية',
      operationTime: 'وقت العملية',
      businessNo: 'رقم العمل',
      actionIP: 'عنوان IP للعملية',
      actionLog: 'سجل العملية.xls',
      traceId: 'معرّف التتبع',
      operatorId: 'هوية المشغل',
      operatorName: 'اسم المشغل',
      operatorUA: 'وحدة استشعار المشغل (UA)',
      operatorExt: 'المعلمات الاضافية للمشغل',
      requestURL: 'عنوان URL للطلب',
      operatorPH: 'الرجاء إدخال المشغل',
      operatorModulePH: 'الرجاء إدخال وحدة العملية',
      operatorNamePH: 'الرجاء إدخال اسم المشغل',
      operatorContentPH: 'الرجاء إدخال محتوى العملية',
      businessNoPH: 'الرجاء إدخال رقم العمل',
    },
    loginLog: {
      userName: 'اسم المستخدم',
      loginAddress: 'عنوان الدخول',
      loginTime: 'وقت الدخول',
      logId: 'رقم السجل',
      operationType: 'نوع العملية',
      browser: 'المتصفح',
      loginResult: 'نتيجة الدخول',
      actionLog: 'سجل الدخول.xls',
      userNamePH: 'الرجاء إدخال اسم المستخدم',
      loginAddressPH: 'الرجاء إدخال عنوان الدخول',
    }
  },
  // learning-center
  learningCenter: {
    course: {
      days: 'أيام .',
      employee: 'موظف',
      course: 'الدورة',
      courses: 'قائمة الدورات',
      id: 'المعرف',
      format: 'الصيغة',
      courseTemplate: 'قالب الدورة.xlsx',
      title: 'العنوان',
      onShelfStatus: 'حالة التوفر',
      newCourse: 'دورة جديدة',
      isRecommend: 'حالة التوصية',
      isAssigned: 'حالة التخصيص',
      level: 'المستوى',
      language: 'اللغة',
      duration: 'المدة',
      subTitle: 'العنوان الفرعي',
      courseSource: 'مصدر الدورة',
      uniqueId: 'المعرّف الفريد',
      tasks: 'المهام',
      exams: 'الاختبارات',
      assignedNumber: 'عدد التخصيصات',
      basicInfo: 'المعلومات الأساسية',
      courseCatalogue: 'فهرس الدورة',
      assignScope: 'نطاق التخصيص',
      courseStatistics: 'إحصائيات الدورة',
      addCourse: 'إضافة دورة',
      topicIdRule: 'يرجى الاختيار',
      courseNameRule: 'يرجى إدخال اسم الدورة، لا يزيد عن 200 حرف',
      courseName: 'اسم الدورة',
      coverPH: 'أبعاد 750*442 بكسل أو نسبة 16:9، صيغة PNG/JPG/GIF، أقل من 5 ميجابايت',
      timeliness: 'التوقيت',
      permanent: 'دائم',
      setExpirationDate: 'تعيين تاريخ الانتهاء',
      certificate: 'شهادة',
      autoAssign: 'التخصيص التلقائي',
      courseDescription: 'وصف الدورة',
      taskMgt: 'إدارة المهام',
      studentName: 'الاسم',
      badgeNo: 'رقم الشارة',
      email: 'البريد الإلكتروني',
      type: 'النوع',
      deptName: 'القسم',
      section: 'الجزء',
      company: 'الشركة',
      position: 'المنصب',
      star: 'التقييم بالنجوم',
      operator: 'المشغل',
      sendingStatus: 'حالة الإرسال',
      score: 'الدرجة',
      status: 'الحالة',
      electiveCourse: 'الطلاب في الدورات الاختيارية',
      mandatoryCourse: 'الدورات الإجبارية المكتملة',
      comprehensiveCourse: 'تقييم الدورات الشاملة',
      selectCourseRule: 'يرجى اختيار معلومات الدورة',
      chooseCourse: 'اختيار الدورات',
      taskId: 'معرّف المهمة',
      contentTitle: 'عنوان المحتوى',
      contentId: 'معرّف المحتوى',
      assetUUID: 'UUID الأصل',
      categoryL1: 'الفئة-L1',
      areaL2: 'المنطقة-L2',
      subjectL3: 'الموضوع-L3',
      channelL4: 'القناة-L4',
      estimatedDuration: 'المدة المتوقعة',
      isExam: 'امتحان (نعم/لا)',
      isSubtitle: 'الترجمة (نعم/لا)',
      isLocal: 'محلي/سحابي',
      imageUrl: 'رابط الصورة',
      keywords: 'الكلمات الرئيسية',
      courseFileName: 'اسم ملف الدورة',
      fileLocation: 'موقع الملف',
      importTime: 'وقت الاستيراد',
      mandatoryScope: 'نطاق إلزامي',
      languageRule: 'لا يمكن أن تكون اللغة فارغة',
      titleRule: 'لا يمكن أن يكون العنوان فارغًا',
      durationRule: 'لا يمكن أن تكون المدة فارغة',
      setTeachingContent: 'وضع محتوى التدريس',
    },
    exam: {
      examPaper: 'ورقة الامتحان',
      addExam: 'إضافة امتحان',
      editExam: 'تعديل الامتحان',
      customizedPaper: 'ورقة مخصصة',
      autoPaper: 'ورقة تلقائية',
      passScore: 'درجة النجاح',
      passScorePH: 'يرجى إدخال درجة الامتحان',
      points: 'نقاط',
      minutes: 'دقائق',
      examScorePH: 'يرجى إدخال درجة الامتحان',
      editExamTip: 'تعديل الامتحان لا يؤثر على نتائج ودرجات الطلاب الذين شاركوا في الامتحان مسبقًا، بل فقط على البيانات بعد التعديل.',
      examPaperName: 'اسم ورقة الامتحان',
      examDuration: 'مدة الامتحان',
      examAttempts: 'عدد محاولات الامتحان',
      taskName: 'اسم المهمة',
      appendix: 'المرفقات',
      examTimes: 'عدد المحاولات',
      passFail: 'نجاح/فشل',
    },
    paper: {
      paperName: 'اسم الورقة',
      type: 'النوع',
      itemNumber: 'رقم السؤال',
      fullScore: 'الدرجة الكاملة',
    },
    task: {
      addTask: 'إضافة مهمة',
      tasks: 'المهام',
      totalTasks: 'إجمالي المهام',
      taskNo: 'رقم المهمة',
      taskName: 'اسم المهمة',
      type: 'النوع',
      progress: 'التقدم',
    },
    journey: {
      addJourney: 'إضافة رحلة',
      editJourney: 'تعديل رحلة',
      title: 'العنوان',
      cover: 'الغلاف',
      categoryTitle: 'الفئة',
      uniqueId: 'المعرّف الفريد',
      creationTime: 'وقت الإنشاء',
      journeyTitleRule: 'يرجى إدخال اسم الرحلة، لا يزيد عن 200 حرف',
    },
    boarding: {
      addBoarding: 'إضافة تدريب التوظيف',
      editBoarding: 'تعديل تدريب التوظيف',
      boarding: 'تدريب التوظيف',
      category: 'الفئة',
      mandatoryRule: 'الحقل الإلزامي مطلوب',
      titleRule: 'العنوان مطلوب',
      coverRule: 'الغلاف مطلوب',
      fileRule: 'الملف مطلوب',
      durationRule: 'المدة الزمنية مطلوبة',
      mandatory: 'إلزامي',
      description: 'الوصف',
      name: 'الاسم',
      badgeNumber: 'رقم الشارة',
      title: 'العنوان',
      learningRecords: 'سجلات التعلم',
    },
    companyPolicy: {
      companyPolicy: 'سياسة الشركة',
      ackRule: 'التأكيد مطلوب',
      declarationRule: 'الإعلان مطلوب',
      titleRule: 'العنوان مطلوب',
      categoryIdRule: 'الفئة مطلوبة',
      coverRule: 'الغلاف مطلوب',
      muploaderRule: 'الملف مطلوب',
      durationRule: 'المدة الزمنية مطلوبة',
      addCompanyPolicy: 'إضافة سياسة الشركة',
      editCompanyPolicy: 'تحرير سياسة الشركة',
      ack: 'تأكيد',
      controlledScope: 'نطاق التحكم',
      acknowledgement: 'الإقرار',
    },
    orientation: {
      displayRule: 'العرض مطلوب',
      addOrientation: 'إضافة التوجه',
      editOrientation: 'تحرير التوجه',
      display: 'عرض',
      orientation: 'التوجيه'
    }
  },
  // 考试
  examMgt: {
    question: {
      poolTitle: 'عنوان المجموعة',
      contentRule: 'الرجاء إدخال المحتوى',
      questionContent: 'محتوى السؤال',
      questionType: 'نوع السؤال',
      choice: 'اختيار',
      correct: 'صحيح',
      itemTitleRule: 'الرجاء إدخال عنوان العنصر',
      choiceSubjectRule: 'الرجاء اختيار الموضوع',
      itemTitle: 'عنوان العنصر',
      correctAnswer: 'الإجابة الصحيحة:',
      questionAnalysis: 'تحليل السؤال',
      addNewQuestion: 'إضافة سؤال جديد',
      editQuestion: 'تحرير السؤال',
      questionPreview: 'معاينة السؤال',
      singleChoice: 'اختيار واحد',
      multipleChoice: 'اختيارات متعددة',
      trueOrFalse: 'صح أو خطأ',
      thisQuestion: 'هذا السؤال؟'
    },
    paper: {
      name: 'الاسم',
      itemName: 'اسم العنصر',
      questionNumber: 'رقم السؤال',
      content: 'المحتوى',
      type: 'النوع',
      selectedQuestion: 'السؤال المختار:',
      setScoreInBatch: 'تعيين الدرجة بالجملة',
      paperNameRule: 'الرجاء إدخال اسم الورقة',
      createAutoPaper: 'إنشاء ورقة تلقائية',
      namePH: 'الرجاء إدخال الاسم',
      chooseQuestion: 'اختر السؤال',
      questionPackage: 'حزمة الأسئلة',
      multipleChoiceType: 'نوع الأسئلة المتعددة الخيارات',
      fullScore: 'الدرجة الكاملة:',
      chooseQuestionPackage: 'اختر حزمة الأسئلة',
      createCustomizedPaper: 'إنشاء ورقة مخصصة',
      editAutoPaper: 'تحرير الورقة التلقائية',
      editCustomizedPaper: 'تحرير الورقة المخصصة',
      pleaseChooseTheQuestion: 'الرجاء اختيار السؤال، الأسئلة هي نفسها للموظفين',
      autoPaper: 'ورقة تلقائية',
      autoPaperPH: 'تم تحديد الاختيار من بنك أسئلة واحد أو أكثر. عند بدء الامتحان، سيختار النظام عشوائيًا من حزمة الأسئلة'
    },
    exam: {
      notAttend: 'لم يحضر',
      failed: 'راسب',
      fail: 'فشل',
      pass: 'ناجح',
      numberExam: 'عدد المشاركين في الامتحان',
      numberPass: 'عدد الموظفين الناجحين',
      numberFailed: 'عدد الموظفين الراسبين',
      examPassRate: 'نسبة نجاح الامتحان',
      onSchedule: 'وفقًا للجدول',
      submitTime: 'وقت التسليم',
      topScore: 'أعلى درجة',
      examNameRule: 'الرجاء إدخال اسم الامتحان',
      examTimeRule: 'الرجاء اختيار وقت الامتحان',
      examAttemptsRule: 'الرجاء إدخال محاولات الامتحان',
      passScoreRule: 'الرجاء إدخال درجة النجاح',
      examDurationRule: 'الرجاء إدخال مدة الامتحان',
      examPaperRule: 'الرجاء اختيار ورقة الامتحان',
      examName: 'اسم الامتحان',
      examTime: 'وقت الامتحان',
      selectExamPaper: 'اختر ورقة الامتحان',
      times: 'مرات',
      examDescription: 'وصف الامتحان',
      examDescriptionPH: 'الرجاء إدخال وصف الامتحان',
      examTaken: 'الاختبارات التي تم إجراؤها:',
      remain: 'متبقى:',
      namePH: 'الرجاء إدخال الاسم',
      paperRule: 'الرجاء اختيار الورقة',
      to: 'إلى',
      moment: 'لحظة',
      examType: 'نوع الامتحان',
      notStarted: 'لم يبدأ',
      inProcess: 'قيد الإجراء',
      expired: 'منتهي الصلاحية',
      examStatistics: 'إحصائيات الامتحان',
      score: 'الدرجة:',
      nonAnswered: 'غير مجاب',
      userList: 'قائمة المستخدمين',
    }
  },
  // 统计
  statistics: {
    course: {
      requiredUsers: 'المستخدمون المطلوبون',
      completedUsersOfRequired: 'المستخدمون الذين أكملوا المتطلبات',
      uncompletedUsersOfRequired: 'المستخدمون الذين لم يكملوا المتطلبات',
      electiveUsers: 'المستخدمون الاختياريون',
      subjectCourseStatistics: 'إحصائيات الدورة الدراسية',
      completed: 'مكتمل',
      sending: 'جار الإرسال',
      sendSuccess: 'تم الإرسال بنجاح',
      sendFail: 'فشل الإرسال',
      none: 'لا شيء',
      scorm: 'SCORM'
    },
    exam: {
      totalExams: 'إجمالي الامتحانات',
      passRates: 'نسب النجاح',
      scoreDetails: 'تفاصيل الدرجة',
    },
    companyPolicy: {
      allCompanyPolicy: 'جميع سياسات الشركة',
      acknowledgement: 'إقرار',
      optional: 'اختياري'
    },
    student: {
      student: 'طالب',
      active: 'نشط',
      inactive: 'غير نشط',
      badge: 'شارات',
      mandatoryCourse: 'دورة إلزامية',
      electiveCourse: 'دورة اختيارية',
      exam: 'امتحان',
      courseEvaluation: 'تقييم الدورة'
    },
    training: {
      // بطاقات الإحصائيات
      totalCourses: 'إجمالي الدورات',
      offlineClasses: 'الفصول الحضورية',
      virtualClasses: 'الفصول الافتراضية',
      hybridClasses: 'الفصول المختلطة',
      totalStudents: 'إجمالي الطلاب',
      hstDdtCourses: 'دورات HST/DDT',

      // التقارير والرسوم البيانية
      report: 'التقرير',
      exportReport: 'تصدير التقرير',

      // عناوين أعمدة الجدول
      courseTitle: 'عنوان الدورة',
      courseCode: 'رمز الدورة',
      category: 'الفئة',
      noOfClasses: 'عدد الفصول',
      plannedAttendance: 'الحضور المخطط',
      actualAttendance: 'الحضور الفعلي',
      noShow: 'عدم الحضور',
      attendance: 'نسبة الحضور',
      classEvaluationScore: 'درجة تقييم الفصل',
      trainerEvaluationScore: 'درجة تقييم المدرب',
      facilityEvaluationScore: 'درجة تقييم المرافق',

      // عناوين الرسوم البيانية
      percentageActualAttendanceNoShow: 'نسبة الحضور الفعلي وعدم الحضور',
      passportIssued: 'الشهادات الصادرة',
      bocAosKbr: 'BOC-AOS-KBR',
      noShowReport: 'تقرير عدم الحضور',
      coursesDelivered: 'الدورات المقدمة',
      noOfCourses: 'عدد الدورات',
      weeklyReport: 'التقرير الأسبوعي',
      mlcDdtWeeklyReport: 'التقرير الأسبوعي MLC-DDT',

      // صفحة التفاصيل
      student: 'الطالب',
      class: 'الفصل',
      company: 'الشركة',
      trainer: 'المدرب',
      details: 'التفاصيل',

      // البيانات الإحصائية
      total: 'المجموع',
      plannedAttendanceLabel: 'الحضور المخطط',
      actualAttendanceLabel: 'الحضور الفعلي',
      noShowLabel: 'عدم الحضور',

      // المدرب
      trainerType: 'نوع المدرب',
      trainerName: 'اسم المدرب',
      trainerDetail: 'تفاصيل المدرب',

      // الفصل
      classCode: 'رمز الفصل',
      classType: 'نوع الفصل',
      classroom: 'الفصل الدراسي',
      duration: 'المدة',

      // خيارات الوقت
      thisWeek: 'هذا الأسبوع',
      thisMonth: 'هذا الشهر',
      thisYear: 'هذا العام',
      time: 'الوقت',

      // الشركة
      studentNumber: 'عدد الطلاب',
      completionRate: 'معدل الإكمال',
      notCompleted: 'غير مكتمل',

      // الطالب
      testResult: 'نتيجة الاختبار',
      attendanceStatus: 'حالة الحضور',
      name: 'الاسم',
      badgeNumber: 'رقم الشارة',
      position: 'المنصب',

      // تفاصيل الفصل
      classDetail: 'تفاصيل الفصل',
      courseName: 'اسم الدورة',
      checkIn: 'تسجيل الدخول',
      checkOut: 'تسجيل الخروج',
      checkInOut: 'تسجيل الدخول/الخروج',
      department: 'القسم',
      badgeNo: 'رقم الشارة',

      // تفاصيل الشركة
      companyDetail: 'تفاصيل الشركة',
      companyName: 'اسم الشركة'
    }
  },
  dashboard: {
    quick: {
      quickEntrance: 'مدخل سريع'
    },
    statistics: {
      totalStudents: 'إجمالي الطلاب',
      distinctStudents: 'عدد الطلاب المميزين',
      totalAdmins: 'إجمالي المشرفين',
      distinctAdmins: 'عدد المشرفين المميزين',
      loginStatistics: 'إحصائيات تسجيل الدخول'
    },
    total: {
      totalNumberOfCourse: 'إجمالي عدد الدورات',
      totalNumberOfExam: 'إجمالي عدد الامتحانات',
      totalNumberOfOnboarding: 'إجمالي عدد برامج التهيئة الوظيفية',
      totalNumberOfOrientation: 'إجمالي عدد الجولات الإرشادية',
      totalNumberOfCompanyPolicy: 'إجمالي عدد سياسات الشركة',
      user: 'المستخدم'
    }
  },
  // 资源管理
  resource: {
    id: 'المعرف',
    size: 'الحجم',
    referenced: 'مُشارَك',
    dataPermissionsRule: 'لا يمكن أن تكون أذونات البيانات فارغة',
    fileUploadCompleted: 'اكتمل تحميل الملف',
    thereAreStillUnprocessedFiles: 'ما زالت هناك ملفات غير معالجة',
    allFilesError: 'تم تحميل جميع الملفات، ولكن هناك خطأ/أخطاء',
    supportResource: 'يدعم تحميل ما يصل إلى 5 موارد في المرة الواحدة. الدعم للتنسيقات: Zip, MP3, MP4, PPT, DOCX, PDF, Excel, TXT.',
    eventType: 'نوع الحدث',
    before: 'قبل',
    resourceCreated: 'تم إنشاء المورد',
    after: 'بعد',
    operateTime: 'وقت العملية',
    noResourceModificationRecord: 'لا يوجد سجل لتعديلات الموارد حتى الآن',
    languageRule: 'لا يمكن أن تكون اللغة فارغة',
    uploadIn: 'جارٍ الرفع إلى',
    batch: 'دفعة',
    waitingForUploads: 'في انتظار الرفع...',
    uploadFailed: 'فشل الرفع',
    fileBeingAnalyzed: 'يجري تحليل الملف، قد يستغرق ذلك بعض الوقت',
    fileParsing: 'يجري تحليل الملف...',
  },
  warning: {
    noResource: 'الدورات المحددة حاليًا لا تحتوي على موارد، يرجى التحقق',
    noResourceList: 'الدورة الحالية لا تحتوي على موارد، ويُمنع إدراجها',
    unsupportedCourseFileTypes: 'أنواع ملفات الدورة غير مدعومة، يرجى التحقق',
    canNotDelete: 'غير قادر على حذف',
    noResourcesByNow: 'لا توجد موارد تحتاج إلى تقديمها في الوقت الحالي',
    resourcesUploadedIncorrectly: 'هناك موارد تم تحميلها بشكل غير صحيح. يرجى التحقق',
    someFilesNotUploaded: 'بعض الملفات لم يتم تحميلها',
    noPreview: 'نوع الملف الحالي لا يدعم المعاينة',
    delOption: 'يرجى حذف الخيارات غير المكتملة',
    delContent: 'من فضلك املأ محتوى السؤال',
    answerOption: 'الرجاء تحديد الإجابة الصحيحة'
  },

  survey: {
    // عام
    category: 'الفئة',
    template: 'القالب',
    question: 'السؤال',
    instance: 'المثيل',
    response: 'الإجابة',
    statistics: 'الإحصائيات',

    // إدارة الفئات
    categoryManagement: 'إدارة الفئات',
    categoryName: 'اسم الفئة',
    parentCategory: 'الفئة الأصل',
    rootCategory: 'الفئة الجذر',
    sortOrder: 'ترتيب الفرز',
    status: 'الحالة',
    enabled: 'مفعل',
    disabled: 'معطل',
    description: 'الوصف',
    addCategory: 'إضافة فئة',
    editCategory: 'تحرير الفئة',
    addSubcategory: 'إضافة فئة فرعية',
    deleteCategory: 'حذف الفئة',
    expandAll: 'توسيع الكل',
    collapseAll: 'طي الكل',

    // إدارة القوالب
    templateManagement: 'إدارة القوالب',
    templateName: 'اسم القالب',
    templateCode: 'رمز القالب',
    templateEditor: 'محرر القالب',
    addTemplate: 'إضافة قالب',
    editTemplate: 'تحرير القالب',
    copyTemplate: 'نسخ القالب',
    deleteTemplate: 'حذف القالب',
    previewTemplate: 'معاينة القالب',
    publishTemplate: 'نشر القالب',
    archiveTemplate: 'أرشفة القالب',
    templateStatus: 'حالة القالب',
    draft: 'مسودة',
    published: 'منشور',
    archived: 'مؤرشف',
    unpublished: 'غير منشور',
    ongoing: 'جاري',
    ended: 'انتهى',
    questionCount: 'عدد الأسئلة',

    // إدارة الأسئلة
    questionManagement: 'إدارة الأسئلة',
    questionTitle: 'عنوان السؤال',
    questionType: 'نوع السؤال',
    questionEditor: 'محرر الأسئلة',
    addQuestion: 'إضافة سؤال',
    editQuestion: 'تحرير السؤال',
    copyQuestion: 'نسخ السؤال',
    deleteQuestion: 'حذف السؤال',
    required: 'مطلوب',
    optional: 'اختياري',
    singleChoice: 'اختيار واحد',
    multipleChoice: 'اختيار متعدد',
    trueFalse: 'صح/خطأ',
    rating: 'تقييم',
    fileUpload: 'رفع ملف',
    text: 'نص',
    singleChoiceDesc: 'سؤال اختيار واحد، يمكن للمستخدمين اختيار خيار واحد فقط',
    multipleChoiceDesc: 'سؤال اختيار متعدد، يمكن للمستخدمين اختيار خيارات متعددة',
    trueFalseDesc: 'سؤال صح/خطأ، يختار المستخدمون الصحيح أو الخطأ',
    ratingDesc: 'سؤال تقييم، يعطي المستخدمون درجة تقييم',
    fileUploadDesc: 'سؤال رفع ملف، يرفع المستخدمون الملفات',
    textDesc: 'سؤال نصي، يدخل المستخدمون محتوى نصي',

    // إدارة المثيلات
    instanceManagement: 'إدارة المثيلات',
    instanceName: 'اسم المثيل',
    instanceEditor: 'محرر المثيل',
    addInstance: 'إضافة مثيل',
    editInstance: 'تحرير المثيل',
    copyInstance: 'نسخ المثيل',
    deleteInstance: 'حذف المثيل',
    publishInstance: 'نشر المثيل',
    stopInstance: 'إيقاف المثيل',
    instanceStatus: 'حالة المثيل',
    stopped: 'متوقف',
    expired: 'منتهي الصلاحية',
    startTime: 'وقت البداية',
    endTime: 'وقت النهاية',
    department: 'القسم',
    anonymous: 'مجهول',
    scopeType: 'نوع النطاق',
    public: 'عام',
    internal: 'داخلي',
    submissionFrequency: 'تكرار التقديم',
    once: 'مرة واحدة',
    onceOnly: 'مرة واحدة فقط',
    multiple: 'متعدد',
    unlimited: 'غير محدود',
    submitFrequency: 'تكرار التقديم',
    searchNickname: 'البحث عن اللقب',
    searchEmail: 'البحث عن البريد الإلكتروني',
    searchLeader: 'البحث عن القائد',
    searchRoleCode: 'البحث عن رمز الدور',
    nickname: 'اللقب',
    email: 'البريد الإلكتروني',
    mobile: 'الهاتف المحمول',
    deptCode: 'رمز القسم',
    parentDept: 'القسم الأب',
    sort: 'الترتيب',
    leader: 'القائد',
    phone: 'الهاتف',
    roleCode: 'رمز الدور',
    roleType: 'نوع الدور',
    system: 'النظام',
    custom: 'مخصص',
    maxSubmissions: 'الحد الأقصى للتقديمات',
    maxResponses: 'الحد الأقصى للاستجابات',
    maxResponsesTip: 'سينتهي الاستطلاع تلقائياً عند الوصول للعدد المحدد من الاستجابات الصحيحة',
    submissionInterval: 'فترة التقديم (ساعات)',
    allowViewStatistics: 'السماح بعرض الإحصائيات',
    estimatedTime: 'الوقت المقدر (دقائق)',
    responseCount: 'عدد الإجابات',
    completionRate: 'معدل الإكمال',
    view: 'عرض',
    completed: 'مكتمل',
    inProgress: 'قيد التقدم',
    notStarted: 'لم يبدأ',
    confirmPublish: 'تأكيد نشر هذا المثيل للاستطلاع؟',
    confirmStop: 'تأكيد إيقاف هذا المثيل للاستطلاع؟',
    confirmCopy: 'تأكيد نسخ هذا المثيل للاستطلاع؟',
    confirmDelete: 'تأكيد حذف هذا المثيل للاستطلاع؟',
    publishSuccess: 'تم النشر بنجاح',
    stopSuccess: 'تم الإيقاف بنجاح',
    copySuccess: 'تم النسخ بنجاح',
    deleteSuccess: 'تم الحذف بنجاح',
    exportSuccess: 'تم التصدير بنجاح',
    instanceDetail: 'تفاصيل المثيل',
    basicInfo: 'المعلومات الأساسية',
    totalResponses: 'إجمالي الإجابات',
    averageTime: 'متوسط الوقت',
    todayResponses: 'إجابات اليوم',
    minutes: 'دقائق',
    scopeConfiguration: 'تكوين النطاق',
    targetType: 'نوع الهدف',
    targetName: 'اسم الهدف',
    user: 'المستخدم',
    role: 'الدور',
    templateInfo: 'معلومات القالب',
    questionList: 'قائمة الأسئلة',
    trueOrFalse: 'صح أو خطأ',
    unknown: 'غير معروف',
    addScope: 'إضافة نطاق',
    noScopeConfigured: 'لا يوجد نطاق مكون',
    target: 'الهدف',
    pleaseSelectTarget: 'يرجى اختيار الهدف',
    scopeAlreadyExists: 'النطاق موجود بالفعل',
    scopeConfigSuccess: 'تم تكوين النطاق بنجاح',
    scopeAddedSuccess: 'تم إضافة النطاق بنجاح، تم إضافة {count} عنصر',
    scopeConfig: 'تكوين النطاق',
    noScopeData: 'لا توجد بيانات نطاق',
    availableUsers: 'المستخدمون المتاحون',
    availableDepts: 'الأقسام المتاحة',
    selectedScopes: 'النطاقات المحددة',
    searchUserName: 'يرجى إدخال اسم المستخدم',
    searchMobile: 'يرجى إدخال رقم الهاتف',
    searchDeptName: 'يرجى إدخال اسم القسم',
    totalDepts: 'إجمالي {count} قسم',
    scopeRemovedSuccess: 'تم إزالة {count} نطاق بنجاح',
    selectionError: 'فشلت عملية التحديد، يرجى المحاولة مرة أخرى',
    loadUserDataError: 'فشل في تحميل بيانات المستخدم، يرجى المحاولة مرة أخرى',
    loadDeptDataError: 'فشل في تحميل بيانات القسم، يرجى المحاولة مرة أخرى',
    hours: 'ساعات',
    loadFailed: 'فشل التحميل',
    createSuccess: 'تم الإنشاء بنجاح',
    updateSuccess: 'تم التحديث بنجاح',
    pleaseSelectTemplate: 'يرجى اختيار قالب أولاً',
    pleaseCheckFormData: 'يرجى التحقق من بيانات النموذج',
    featureNotImplemented: 'الميزة غير مطبقة بعد',
    publicSurveyNoScope: 'الاستطلاعات العامة لا تتطلب تكوين النطاق، يمكن لجميع المستخدمين المشاركة',
    questionStatistics: 'إحصائيات الأسئلة',
    responseData: 'بيانات الإجابات',
    totalAnswers: 'إجمالي الإجابات',
    validResponses: 'الردود الصحيحة',
    pleaseEnterUserName: 'يرجى إدخال اسم المستخدم',
    to: 'إلى',
    displayRange: 'نطاق العرض',
    pleaseSelect: 'يرجى الاختيار',
    allRecords: 'جميع السجلات',
    validRecordsOnly: 'السجلات الصحيحة فقط',
    search: 'بحث',
    reset: 'إعادة تعيين',
    userInfo: 'معلومات المستخدم',
    completionTime: 'وقت الإكمال',
    seconds: 'ثانية',
    score: 'النقاط',
    valid: 'صحيح',
    invalid: 'غير صحيح',
    submitCount: 'عدد المرات المرسلة',
    submitCountFormat: 'المرة {count}',
    ipAddress: 'عنوان IP',
    operation: 'العملية',
    viewDetail: 'عرض التفاصيل',
    loadDataFailed: 'فشل تحميل البيانات',
    responseDetailTitle: 'تفاصيل الإجابة',
    deptName: 'اسم القسم',
    totalScore: 'النقاط الإجمالية',
    isValid: 'صحيح أم لا',
    validResponse: 'إجابة صحيحة',
    invalidResponse: 'إجابة غير صحيحة',
    responseStatistics: 'إحصائيات الإجابة',
    totalQuestions: 'إجمالي الأسئلة',
    answeredQuestions: 'الأسئلة المجابة',
    skippedQuestions: 'الأسئلة المتخطاة',
    answerDetails: 'تفاصيل الإجابة',
    answer: 'الإجابة',
    file: 'الملف',
    viewFile: 'عرض الملف',
    notAnswered: 'لم يتم الإجابة',
    close: 'إغلاق',
    loadDetailFailed: 'فشل تحميل التفاصيل',
    totalRecords: 'إجمالي {count} سجل',
    noData: 'لا توجد بيانات',
    answered: 'تم الإجابة',
    noFileUploaded: 'لم يتم رفع ملف',
    pleaseEnter: 'يرجى إدخال',
    loadStatistics: 'تحميل الإحصائيات',
    loadStatisticsFailed: 'فشل تحميل الإحصائيات',
    loadingTemplateData: 'جاري تحميل بيانات القالب...',
    points: 'نقاط',

    // صفحة الإحصائيات
    surveyStatistics: 'إحصائيات الاستطلاع',
    loadingStatistics: 'جاري تحميل بيانات الإحصائيات...',
    refreshData: 'تحديث البيانات',
    exportReport: 'تصدير التقرير',
    participationStatus: 'حالة المشاركة',
    validAnswers: 'الإجابات الصحيحة',
    averageScore: 'متوسط النقاط',
    highest: 'الأعلى',
    lowest: 'الأدنى',
    averageCompletionTime: 'متوسط وقت الإكمال',
    statisticsTime: 'وقت الإحصائيات',
    lastUpdated: 'آخر تحديث',
    submissionTimeDistribution: 'توزيع وقت التقديم',
    byDay: 'حسب اليوم',
    byWeek: 'حسب الأسبوع',
    byMonth: 'حسب الشهر',
    departmentParticipationDistribution: 'توزيع مشاركة الأقسام',
    questionDetailedStatistics: 'إحصائيات الأسئلة التفصيلية',
    searchQuestions: 'البحث في الأسئلة...',
    clear: 'مسح',
    noMatchingQuestions: 'لم يتم العثور على أسئلة مطابقة',
    noStatisticsData: 'لا توجد بيانات إحصائية',
    possibleReasons: 'الأسباب المحتملة:',
    noParticipants: 'لم يشارك أحد في الاستطلاع بعد',
    dataGenerating: 'يتم إنشاء البيانات',
    reload: 'إعادة تحميل',
    dataRefreshSuccess: 'تم تحديث البيانات بنجاح',
    dataRefreshFailed: 'فشل تحديث البيانات',
    exportFeatureInDevelopment: 'ميزة التصدير قيد التطوير...',
    pleaseEnterSearchKeyword: 'يرجى إدخال كلمة البحث',
    foundMatchingQuestions: 'تم العثور على {count} أسئلة مطابقة',
    searchConditionsCleared: 'تم مسح شروط البحث',
    dailySubmission: 'التقديم اليومي',
    cumulativeSubmission: 'التقديم التراكمي',
    participationRate: 'معدل المشاركة',
    participantCount: 'عدد المشاركين',

    // إحصائيات الأسئلة
    responseRate: 'معدل الاستجابة',
    people: 'شخص',
    noOptionData: 'لا توجد بيانات خيارات',
    yes: 'نعم',
    no: 'لا',
    star: 'نجمة',
    answerCount: 'عدد الإجابات',
    answerList: 'قائمة الإجابات',
    collapse: 'طي',
    viewAllAnswers: 'عرض جميع الإجابات {count}',
    uploadedFiles: 'الملفات المرفوعة',
    uploadedPeople: 'الأشخاص المرفوعون',
    notUploadedCount: 'عدد غير المرفوعة',
    fileList: 'قائمة الملفات',
    fileName: 'اسم الملف',
    uploadTime: 'وقت الرفع',
    fileSize: 'حجم الملف',
    download: 'تحميل',
    skipCount: 'عدد التخطي',
    optionDistribution: 'توزيع الخيارات',
    selectTemplate: 'اختيار القالب',
    changeTemplate: 'تغيير القالب',
    searchTemplateName: 'البحث في اسم القالب',
    templateSelectedSuccess: 'تم اختيار القالب بنجاح',
    select: 'اختيار',
    selectTargetType: 'اختيار نوع الهدف',
    searchRoleName: 'البحث في اسم الدور',
    pleaseSearch: 'يرجى إدخال محتوى البحث',
    userName: 'اسم المستخدم',
    roleName: 'اسم الدور',
    name: 'الاسم',
    statisticsOverview: 'نظرة عامة على الإحصائيات',
    loadTemplateFailed: 'فشل تحميل قائمة القوالب',
    templateSelectFailed: 'فشل اختيار القالب',
    pleaseSelectScope: 'يرجى اختيار النطاق',
    loadScopeListFailed: 'فشل تحميل قائمة النطاق',
    allScopeAlreadyExists: 'جميع النطاقات المحددة موجودة بالفعل',
    normal: 'عادي',
    instanceContent: 'محتوى المثيل',
    basicInfoDesc: 'تكوين المعلومات الأساسية',
    templateInfoDesc: 'عرض تفاصيل القالب',
    scopeConfigDesc: 'تكوين النطاق',
    statisticsDesc: 'عرض الإحصائيات',
    timeRange: 'النطاق الزمني',

    // إدارة الإجابات
    responseManagement: 'إدارة الإجابات',
    responseDetail: 'تفاصيل الإجابة',
    deleteResponse: 'حذف الإجابة',
    exportResponse: 'تصدير الإجابة',

    // تحليل الإحصائيات
    statisticsAnalysis: 'تحليل الإحصائيات',
    statisticsDetail: 'تفاصيل الإحصائيات',

    // العمليات
    add: 'إضافة',
    edit: 'تحرير',
    delete: 'حذف',
    copy: 'نسخ',
    preview: 'معاينة',
    export: 'تصدير',
    import: 'استيراد',
    save: 'حفظ',
    cancel: 'إلغاء',
    confirm: 'تأكيد',

    // رسائل التنبيه
    importSuccess: 'تم الاستيراد بنجاح',

    // التحقق من النموذج
    categoryNameRequired: 'اسم الفئة مطلوب',
    templateNameRequired: 'اسم القالب مطلوب',
    questionTitleRequired: 'عنوان السؤال مطلوب',

    // أخرى
    createTime: 'وقت الإنشاء',
    updateTime: 'وقت التحديث',
    actions: 'العمليات',
    loading: 'جاري التحميل...',
    refreshCategories: 'تحديث الفئات',
    searchCategoryName: 'البحث في اسم الفئة',
    autoGenerated: 'يتم إنشاؤه تلقائياً إذا كان فارغاً',
    dragOrClickToAdd: 'اسحب أو انقر لإضافة الأسئلة',
    dragQuestionHere: 'اسحب الأسئلة هنا',
    dragQuestionDesc: 'اسحب أنواع الأسئلة من اللوحة اليسرى أو انقر للإضافة',
    addOption: 'إضافة خيار',
    allTemplates: 'جميع القوالب',
    option: 'خيار',
    selectionRange: 'نطاق الاختيار',
    correct: 'صحيح',
    incorrect: 'خطأ',
    poor: 'ضعيف',
    excellent: 'ممتاز',
    scorePerPoint: 'النقاط لكل نقطة',
    allowedTypes: 'الأنواع المسموحة',
    maxFiles: 'الحد الأقصى للملفات',
    maxSize: 'الحد الأقصى للحجم',
    lengthRequirement: 'متطلبات الطول',
    saveTemplateFirst: 'يرجى حفظ القالب أولاً',
    previewOpened: 'تم فتح المعاينة',
    selectFile: 'اختر ملف',
    insertHere: 'أدرج هنا',
    questionConfig: 'إعداد السؤال',
    fullscreen: 'تحرير ملء الشاشة',
    exitFullscreen: 'الخروج من ملء الشاشة',
    options: 'الخيارات',
    new: 'جديد ',
    scrollToSeeMore: 'مرر لرؤية المزيد'
  },

  // -------------------------在线学院 ----------------------------------
  academy: {
    classroom: {
      name: 'الاسم',
      location: 'الموقع',
      roomNumber: 'رقم الغرفة',
      totalSeats: 'إجمالي المقاعد',
      description: 'الوصف',
      status: 'الحالة',
      time: 'الوقت',
      startDate: 'تاريخ البداية',
      endDate: 'تاريخ النهاية',
      pleaseSelect: 'يرجى الاختيار',
      pleaseInput: 'يرجى الإدخال',
      search: 'بحث',
      reset: 'إعادة تعيين',
      add: 'إضافة',
      edit: 'تحرير',
      view: 'عرض',
      delete: 'حذف',
      action: 'إجراء',
      classroomNumber: 'رقم الفصل الدراسي',
      pleaseEnterName: 'يرجى إدخال الاسم',
      cover: 'الغلاف',
      default: 'افتراضي',
      customize: 'تخصيص',
      pleaseInputDescription: 'يرجى إدخال الوصف',
      cancel: 'إلغاء',
      save: 'حفظ',
      nameRequired: 'اسم الفصل الدراسي لا يمكن أن يكون فارغاً',
      roomNumberRequired: 'رقم الفصل الدراسي لا يمكن أن يكون فارغاً',
      statusRequired: 'الحالة لا يمكن أن تكون فارغة',
      locationRequired: 'الموقع لا يمكن أن يكون فارغاً',
      totalSeatsRequired: 'سعة الفصل الدراسي لا يمكن أن تكون فارغة'
    },
    trainer: {
      trainerType: 'نوع المدرب',
      company: 'الشركة',
      department: 'القسم',
      position: 'المنصب',
      time: 'الوقت',
      startDate: 'تاريخ البداية',
      endDate: 'تاريخ النهاية',
      trainerName: 'اسم المدرب',
      pleaseSelect: 'يرجى الاختيار',
      pleaseInput: 'يرجى الإدخال',
      search: 'بحث',
      reset: 'إعادة تعيين',
      add: 'إضافة',
      edit: 'تحرير',
      delete: 'حذف',
      action: 'إجراء',
      badgeNo: 'رقم الشارة',
      // Form placeholders
      trainerNamePlaceholder: 'يرجى إدخال اسم المدرب',
      trainerTypePlaceholder: 'يرجى اختيار نوع المدرب',
      // Form validation messages
      trainerNameRequired: 'اسم المدرب لا يمكن أن يكون فارغاً',
      trainerTypeRequired: 'نوع المدرب لا يمكن أن يكون فارغاً',
      pleaseSelectUser: 'يرجى اختيار مستخدم',
      // Button labels
      cancel: 'إلغاء',
      save: 'حفظ',
      // Description labels
      badge: 'الشارة',
      email: 'البريد الإلكتروني'
    },
    course: {
      // Search form labels
      category: 'الفئة',
      categoryPlaceholder: 'يرجى اختيار الفئة',
      language: 'اللغة',
      languagePlaceholder: 'يرجى اختيار اللغة',
      trainerType: 'نوع المدرب',
      trainerTypePlaceholder: 'يرجى اختيار نوع المدرب',
      courseTitle: 'عنوان الدورة',
      courseTitlePlaceholder: 'يرجى إدخال عنوان الدورة',
      // Button labels
      search: 'بحث',
      reset: 'إعادة تعيين',
      add: 'إضافة',
      assign: 'تخصيص',
      edit: 'تحرير',
      copy: 'نسخ',
      delete: 'حذف',
      cancel: 'إلغاء',
      confirm: 'تأكيد',
      // Table column labels
      courseCode: 'رمز الدورة',
      approvals: 'الموافقات',
      validity: 'الصلاحية',
      action: 'إجراء',
      months: 'أشهر',
      yes: 'نعم',
      no: 'لا',
      // Dialog titles
      newCourseTitle: 'عنوان الدورة الجديدة',
      selectStudents: 'اختيار الطلاب',
      courseCategory: 'فئة الدورة',
      manage: 'إدارة',
      remove: 'إزالة',
      // Form validation
      courseNameRequired: 'اسم الدورة لا يمكن أن يكون فارغاً',
      courseCodeRequired: 'رمز الدورة لا يمكن أن يكون فارغاً',
      categoryRequired: 'فئة الدورة لا يمكن أن تكون فارغة',
      coverRequired: 'يرجى تحميل صورة الغلاف',
      languageRequired: 'اللغة لا يمكن أن تكون فارغة',
      trainerTypeRequired: 'نوع المدرب لا يمكن أن يكون فارغاً',
      approvalSettingRequired: 'إعداد الموافقة لا يمكن أن يكون فارغاً',
      validityRequired: 'فترة صلاحية الشهادة لا يمكن أن تكون فارغة',
      certificateRequired: 'الشهادة لا يمكن أن تكون فارغة',
      bookingTimeRequired: 'إعداد وقت الحجز لا يمكن أن يكون فارغاً',
      restrictionRequired: 'وقت الغياب ووقت التجميد لا يمكن أن يكونا فارغين',
      // Common labels
      pleaseSelect: 'يرجى الاختيار',
      pleaseInput: 'يرجى الإدخال',
      // CourseForm specific labels
      addCategory: 'إضافة فئة',
      cover: 'الغلاف',
      default: 'افتراضي',
      customize: 'تخصيص',
      level: 'المستوى',
      ifmsApproval: 'موافقة IFMS',
      contractorApproval: 'موافقة المقاول',
      certificate: 'الشهادة',
      exam: 'الامتحان',
      prerequisite: 'المتطلبات المسبقة',
      course: 'الدورة',
      attachment: 'المرفق',
      new: 'جديد',
      restriction: 'القيود',
      absent: 'غائب',
      absentTime: 'مرات',
      unableToSubscribe: '، غير قادر على الاشتراك خلال',
      feedback: 'التعليقات',
      bookingTimeSetting: 'إعداد وقت الحجز',
      bookingTimeDesc: 'حجز 24 ساعة قبل بدء الجدولة',
      description: 'الوصف',
      // Placeholders
      pleaseSelectCertificate: 'يرجى اختيار الشهادة',
      pleaseSelectExam: 'يرجى اختيار الامتحان',
      pleaseSelectCourse: 'يرجى اختيار الدورة',
      // Validity
      monthsUnit: 'شهر (أشهر)',
      noRestrictions: '0 يمثل عدم وجود قيود',
      // Dialog titles
      template: 'القالب',
      // Approval workflow
      student: 'الطالب',
      review: 'مراجعة',
      end: 'النهاية',
      // Approval descriptions
      noReviewNeeded: 'لا حاجة للمراجعة',
      submitBookingRequest: 'تقديم طلب الحجز',
      trainingAdmin: 'مدير التدريب',
      lineManager: 'المدير المباشر',
      eptwAdmin: 'مدير EPTW',
      contractorHolder: 'حامل العقد',
      // Category management
      newCategory: 'فئة جديدة',
      categoryName: 'اسم الفئة',
      categoryParent: 'الفئة الأصل',
      status: 'الحالة',
      categoryNamePlaceholder: 'يرجى إدخال اسم الفئة',
      selectParentCategory: 'يرجى اختيار الفئة الأصل',
      parentCategoryRequired: 'يرجى اختيار الفئة الأصل',
      // Certificate selection
      selectCertificate: 'اختيار الشهادة',
      name: 'الاسم',
      time: 'الوقت',
      creationTime: 'وقت الإنشاء',
      startDate: 'تاريخ البداية',
      endDate: 'تاريخ النهاية',
      pleaseInputName: 'يرجى إدخال الاسم',
      // Exam selection
      selectOnlineExam: 'اختيار الامتحان الإلكتروني',
      examName: 'اسم الامتحان',
      subject: 'الموضوع',
      type: 'النوع',
      examTime: 'وقت الامتحان',
      assignedNumber: 'العدد المخصص',
      creator: 'المنشئ',
      // Prerequisite course selection
      selectPrerequisitesCourse: 'اختيار الدورات المتطلبة مسبقاً'
    },
    class: {
      // Search form labels
      course: 'الدورة',
      type: 'النوع',
      language: 'اللغة',
      publishStatus: 'حالة النشر',
      status: 'الحالة',
      date: 'التاريخ',
      title: 'العنوان',
      start: 'البداية',
      end: 'النهاية',
      pleaseInputTitle: 'يرجى إدخال العنوان',
      // Button labels
      search: 'بحث',
      reset: 'إعادة تعيين',
      add: 'إضافة',
      publish: 'نشر',
      join: 'انضم',
      // Table column labels
      courseTitle: 'عنوان الدورة',
      classTitle: 'عنوان الفصل',
      classType: 'نوع الفصل',
      trainer: 'المدرب',
      classroom: 'الفصل الدراسي',
      duration: 'المدة',
      bookingNumber: 'عدد الحجوزات',
      action: 'إجراء',
      // Action buttons
      manage: 'إدارة',
      copy: 'نسخ',
      postpone: 'تأجيل',
      cancel: 'إلغاء',
      delete: 'حذف',
      // Dialog titles and content
      cancelClass: 'إلغاء الفصل',
      confirmCancelClass: 'هل تؤكد إلغاء الفصل',
      postponeClass: 'تأجيل الفصل',
      confirmPostponeClass: 'تأكيد تأجيل الفصل',
      confirmPostponeAndMerge: 'هل تؤكد تأجيل الفصل ودمجه مع فصل {name}',
      mergeSuccessful: 'نجح الدمج',
      offlineClass: 'فصل حضوري',
      virtualClass: 'فصل افتراضي',
      confirmPublishSchedule: 'هل تؤكد نشر الجدول الشهري',
      publishedSuccessfully: 'تم النشر بنجاح',
      classPublishedCannotSelect: 'تم نشر الفصل، لا يمكن اختياره',
      withdraw: 'سحب',
      withdrawClass: 'سحب الفصل',
      confirmWithdrawClass: 'هل تؤكد سحب الفصل',
      confirmPublishClass: 'هل تؤكد نشر الفصل',
      withdrawSuccessfully: 'تم السحب بنجاح',
      publishSuccessfully: 'تم النشر بنجاح',
      owner: 'المالك',
      startEndTime: 'وقت البداية والنهاية',
      basicInformation: 'المعلومات الأساسية',
      bookingManagement: 'إدارة الحجوزات',
      checkInOut: 'تسجيل الدخول/الخروج',
      studentsManagement: 'إدارة الطلاب',
      feedbackManagement: 'إدارة التعليقات',
      materialsManagement: 'إدارة المواد',
      classRoster: 'قائمة الفصل',
      classCode: 'رمز الفصل',
      trainingDays: 'أيام التدريب',
      translator: 'مترجم',
      maximumAttendanceNum: 'العدد الأقصى للحضور',
      minimumAttendanceNum: 'العدد الأدنى للحضور',
      trainingDescription: 'وصف التدريب',
      bookingInformation: 'معلومات الحجز',
      allowedBookingTime: 'وقت الحجز المسموح',
      bookingRate: 'معدل الحجز',
      bookingTime: 'وقت الحجز',
      prerequisite: 'المتطلبات المسبقة',
      reviewProcess: 'عملية المراجعة',
      studentInformation: 'معلومات الطالب',
      badgeNo: 'رقم الشارة',
      position: 'المنصب',
      fullRefresher: 'كامل/تجديد',
      full: 'كامل',
      refresher: 'تجديد',
      approve: 'موافقة',
      reject: 'رفض',
      approvalPassedSuccessfully: 'تمت الموافقة بنجاح',
      approvalFailed: 'فشلت الموافقة',
      attachment: 'المرفق',
      bookingOverallProgress: 'التقدم الإجمالي للحجز',
      bookingStatus: 'حالة الحجز',
      review: 'مراجعة',
      checkInStudentNumber: 'عدد الطلاب المسجلين',
      checkOutStudentNumber: 'عدد الطلاب المغادرين',
      attendanceStatus: 'حالة الحضور',
      checkIn: 'تسجيل الدخول',
      checkOut: 'تسجيل الخروج',
      checkInTime: 'وقت تسجيل الدخول',
      checkOutTime: 'وقت تسجيل الخروج',
      scanQRCode: 'مسح رمز الاستجابة السريعة',
      studentNumber: 'عدد الطلاب',
      completionRate: 'معدل الإنجاز',
      notPassed: 'لم ينجح',
      attendance: 'الحضور',
      companyName: 'اسم الشركة',
      classStatus: 'حالة الفصل',
      planned: 'مخطط',
      actualAttendance: 'الحضور الفعلي',
      noShow: 'عدم الحضور',
      attendanceRate: 'معدل الحضور',
      comments: 'التعليقات',
      evaluationTime: 'وقت التقييم',
      courseEvaluation: 'تقييم الدورة',
      trainerEvaluation: 'تقييم المدرب',
      facilityEvaluation: 'تقييم المرافق',
      messageNotification: 'إشعار الرسالة',
      notificationDescription: 'إذا قمت بتشغيل المفتاح، سيتلقى الطلاب الإشعارات ذات الصلة.',
      newClassTitle: 'عنوان الفصل الجديد',
      className: 'اسم الفصل:',
      publishClass: 'نشر الفصل',
      confirm: 'تأكيد',
      // Form validation
      courseNameRequired: 'اسم الدورة لا يمكن أن يكون فارغاً',
      // Success messages
      cancelSuccessfully: 'تم الإلغاء بنجاح',
      // ClassForm specific labels
      startDate: 'تاريخ البداية',
      pickADate: 'اختر تاريخاً',
      to: 'إلى',
      startTime: 'وقت البداية',
      endTime: 'وقت النهاية',
      days: 'يوم (أيام)',
      pleaseSelectClassroom: 'يرجى اختيار الفصل الدراسي',
      maximum: 'الحد الأقصى',
      minimum: 'الحد الأدنى',
      save: 'حفظ',
      // Form validation messages
      courseRequired: 'الدورة لا يمكن أن تكون فارغة',
      courseTypeRequired: 'نوع الدورة لا يمكن أن يكون فارغاً',
      trainerRequired: 'المدرب لا يمكن أن يكون فارغاً',
      durationRequired: 'المدة لا يمكن أن تكون فارغة',
      startDateRequired: 'تاريخ البداية لا يمكن أن يكون فارغاً',
      student: 'طالب'
    },
    internal: {
      // Search form labels
      type: 'النوع',
      place: 'المكان',
      company: 'الشركة',
      time: 'الوقت',
      courseTitleEnglish: 'عنوان الدورة (الإنجليزية)',
      courseTitleArabic: 'عنوان الدورة (العربية)',
      // Table column labels
      trainingCode: 'رمز التدريب',
      courseTitleEN: 'عنوان الدورة (إنجليزي)',
      courseTitleAR: 'عنوان الدورة (عربي)',
      startEndDate: 'تاريخ البداية والنهاية',
      courseDuration: 'مدة الدورة',
      studentNumber: 'عدد الطلاب',
      studentList: 'قائمة الطلاب',
      notes: 'الملاحظات',
      days: 'أيام',
      // Form labels
      form: {
        trainingCode: 'رمز التدريب',
        courseTitleEN: 'عنوان الدورة (إنجليزي)',
        courseTitleAR: 'عنوان الدورة (عربي)',
        trainingType: 'نوع التدريب',
        place: 'المكان',
        startEndTime: 'وقت البداية والنهاية',
        duration: 'المدة',
        implementingCompany: 'الشركة المنفذة',
        attachment: 'المرفق',
        comments: 'التعليقات',
        studentInformation: 'معلومات الطالب',
        multipleGroups: 'مجموعات متعددة',
        enableStudentsByMultipleGroups: 'تمكين الطلاب بمجموعات متعددة',
        addStudent: 'إضافة طالب',
        groupNo: 'رقم المجموعة',
        name: 'الاسم',
        arabicName: 'الاسم العربي',
        position: 'المنصب',
        notes: 'الملاحظات',
        action: 'إجراء',
        remove: 'إزالة',
        start: 'البداية',
        end: 'النهاية',
        // Validation messages
        courseTitleENRequired: 'عنوان الدورة (إنجليزي) لا يمكن أن يكون فارغاً',
        courseTitleARRequired: 'عنوان الدورة (عربي) لا يمكن أن يكون فارغاً',
        trainingCodeRequired: 'رمز التدريب لا يمكن أن يكون فارغاً',
        trainingTypeRequired: 'نوع التدريب لا يمكن أن يكون فارغاً',
        placeRequired: 'المكان لا يمكن أن يكون فارغاً',
        startEndTimeRequired: 'أوقات البداية والنهاية لا يمكن أن تكون فارغة',
        durationRequired: 'المدة لا يمكن أن تكون فارغة',
        implementingCompanyRequired: 'الشركة المنفذة لا يمكن أن تكون فارغة'
      }
    },
    external: {
      // Search form labels
      country: 'البلد',
      costBearer: 'متحمل التكلفة',
      travelDate: 'تاريخ السفر',
      returnDate: 'تاريخ العودة',
      courseTitleEnglish: 'عنوان الدورة (الإنجليزية)',
      courseTitleArabic: 'عنوان الدورة (العربية)',
      startDate: 'تاريخ البداية',
      endDate: 'تاريخ النهاية',
      // Table column labels
      trainingCode: 'رمز التدريب',
      courseTitleEN: 'عنوان الدورة (إنجليزي)',
      courseTitleAR: 'عنوان الدورة (عربي)',
      receivingCountry: 'البلد المستقبل',
      adminNo: 'رقم المدير',
      studentNumber: 'عدد الطلاب',
      studentList: 'قائمة الطلاب',
      // Form labels
      travelReturn: 'السفر والعودة',
      travel: 'السفر',
      return: 'العودة',
      receivingCountryLabel: 'البلد المستقبل',
      constBearer: 'متحمل التكلفة',
      attachment: 'المرفق',
      comments: 'التعليقات',
      studentInformation: 'معلومات الطلاب',
      multipleGroups: 'مجموعات متعددة',
      enableStudentsByMultipleGroups: 'تمكين الطلاب بمجموعات متعددة',
      addStudent: 'إضافة طالب',
      groupNo: 'رقم المجموعة',
      name: 'الاسم',
      arabicName: 'الاسم العربي',
      bocBadgeNo: 'رقم شارة BOC',
      position: 'المنصب',
      department: 'القسم',
      // Validation messages
      courseTitleENRequired: 'عنوان الدورة (إنجليزي) لا يمكن أن يكون فارغًا',
      courseTitleARRequired: 'عنوان الدورة (عربي) لا يمكن أن يكون فارغًا',
      trainingCodeRequired: 'رمز التدريب لا يمكن أن يكون فارغًا',
      receivingCountryRequired: 'البلد المستقبل لا يمكن أن يكون فارغًا',
      travelReturnRequired: 'تاريخ السفر والعودة لا يمكن أن يكون فارغًا',
      adminNoRequired: 'رقم المدير لا يمكن أن يكون فارغًا',
      costBearerRequired: 'متحمل التكلفة لا يمكن أن يكون فارغًا'
    }
  },
  bpm: {
    toDo: {
      taskTitlePlaceholder: 'الرجاء إدخال اسم المهمة',
      categoryPlaceholder: 'الرجاء اختيار تصنيف العملية',
      process: 'العملية التابعة لها',
      processPlaceholder: 'الرجاء اختيار تعريف العملية',
      startTime: 'وقت البدء',
      processName: 'العملية',
      summary: 'ملخص',
      initiator: 'المُبْدِئ',
      currentTask: 'المهمة الحالية',
      taskTime: 'وقت المهمة',
      processInstanceId: 'رقم مثيل العملية',
      taskId: 'رقم المهمة',
      handle: 'معالجة',
      processStatus: 'حالة العملية',
      processStatusApproving: 'قيد الموافقة',
      processStatusPerson: 'شخص',
      processStatusWait: 'انتظار',
      processStatusPlaceholder: 'الرجاء اختيار حالة العملية',
      processCategory: 'فئة العملية',
      processStartFailed: 'فشل إعادة بدء العملية. السبب: تستخدم هذه العملية نموذج أعمال ولا تدعم إعادة البدء.',
      processCancelReasonPlaceholder: 'الرجاء إدخال سبب الإلغاء',
      processCancel: 'إلغاء العملية',
      processCancelReasonRequired: 'سبب الإلغاء لا يمكن أن يكون فارغًا',
      processCancelSuccess: 'تم الإلغاء بنجاح'
    },
    processInstance: {
      nButtonApprove: 'الموافقة',
      operationButtonReject: 'رفض',
      operationButtonTransfer: 'نقل',
      operationButtonDelegate: 'تفويض',
      operationButtonAddSign: 'إضافة توقيع',
      operationButtonReturn: 'إرجاع',
      operationButtonCopy: 'نسخ',
      processInstanceDetailTitle: 'تفاصيل الموافقة',
      flowChartTitle: 'مخطط التدفق',
      circulationRecordTitle: 'سجل الدوران',
      number: 'رقم',
      submit: 'قدم',
      approve: 'ملاحظات الموافقة',
      approvePlaceholder: 'الرجاء إدخال ملاحظات الموافقة',
      processNode: 'عقدة الموافقة',
      approver: 'الموافق',
      status: 'حالة الموافقة',
      approveSuggestion: 'اقتراح الموافقة',
      viewForm: 'عرض النموذج',
      costTime: 'الوقت المستغرق',
      formDetail: 'تفاصيل النموذج',
      approveTitle: 'الموافقة',
      approveOpinionTitle: 'ملاحظات',
      pleaseInput: 'الرجاء إدخال',
      approveOpinionRequired: 'الموافقة على الرأي لا يمكن فارغة',
    }
  }
}
