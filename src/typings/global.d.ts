import type auth from '@/plugins/auth'
import type cache from '@/plugins/cache'
import type modal from '@/plugins/modal'
import type tab from '@/plugins/tab'
import type {
  addDateRange,
  handleTree,
  parseTime,
  resetForm,
  selectDictLabel,
  selectDictLabels,
} from '@/utils/ruoyi'

export interface VisibleDomRect {
  bottom: number
  height: number
  left: number
  right: number
  top: number
  width: number
}
export interface Dict {
  elTagClass?: string
  elTagType: string
  label: string
  value: string
}
export {}
declare module 'vue' {
  interface ComponentCustomProperties {
    useDict: string | number
    download: typeof utilsDownload
    parseTime: typeof parseTime
    resetForm: typeof resetForm
    handleTree: typeof handleTree
    addDateRange: typeof addDateRange
    selectDictLabel: typeof selectDictLabel
    selectDictLabels: typeof selectDictLabels

    $tab: typeof tab
    // 认证对象
    $auth: typeof auth
    // 缓存对象
    $cache: typeof cache
    // 模态框对象
    $modal: typeof modal
    // 下载文件
    $download: string | undefined
    //
    $refs: any
  }
}
declare module 'vue-router' {
  interface RouteMeta {
    hidden?: boolean
    title?: string
    icon?: string
    elSvgIcon?: string
    permissions?: string[]
  }
  interface _RouteRecordBase {
    hidden?: boolean
    parentPath?: string
    permissions?: string[]

  }
  interface _RouteLocationBase {
    title?: string
  }
}
declare global {
  interface Window {
    VConsole: any
    API: any
    API_1484_11: any
  }
}
