export interface QueryData {
  queryParams: QueryParams
  total: number
  queryRef: FormInstance | undefined
  loading: boolean
}
export interface Resource {
  pageNum: number // 页码
  pageSize: number // 每页大小
  createId: number // 创建人 ID
  createBy: string // 创建人
  createTime: string // 创建时间，格式为字符串
  updateId: number | null // 更新人 ID，可以为 null
  updateBy: string | null // 更新人，可以为 null
  updateTime: string // 更新时间，格式为字符串
  remark: string | null // 备注，可以为 null
  id: number // 记录 ID
  identity: string // 身份标识
  subjectId: number // 主题 ID
  deptId: number // 部门 ID
  type: string // 类型
  mediaType: number // 媒体类型
  format: string // 格式
  title: string // 标题
  lang: string // 语言
  duration: number // 时长
  size: number // 大小
  referenceCount: number // 参考计数
  enable: string // 是否启用
  scope: number // 范围
  delete: boolean // 是否删除
  deptIds: number[] | null // 部门 ID 数组，可以为 null
  address: string
  fileId: number
  scormVersion: string | undefined
  scormRunPath: string | undefined
  scormParseStatus: string | undefined
  aiccParseStatus: string | undefined
}
export interface Scorm {
  id: string
  title: string
  lang: string
  duration: number
  size: number
  scormParseStatus: number
  createBy: string
  createTime: string
}
