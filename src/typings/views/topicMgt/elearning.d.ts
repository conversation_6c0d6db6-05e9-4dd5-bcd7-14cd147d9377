export interface Statistics {
  userId: number
  assignmentDetailId: number
  studentName: string
  badgeNo: string | null
  email: string
  department: string | null
  section: string | null
  position: string | null
  type: string
  onSchedule: boolean
  status: number
  star: number
  operator: string
  company: string | null
  score: number | string| null
  examTimes: string | null
  examStatus: string | null
}
export interface ChapterData {
  pageNum: number
  pageSize: number
  createId: number | null
  createBy: string
  createTime: string
  updateId: number | null
  updateBy: string
  updateTime: string
  remark: string | null
  id: number
  courseId: number
  sort: number
  title: string
  type: number
  scormVersion: string
  fileId: number
  fileUrl: string
  contentExamId: number | null
  attachments: any | null
  status: number
  score: number | null
}
export interface CourseScope {
  pageNum: number
  pageSize: number
  createId: number
  createBy: string
  createTime: string // You may also want to use Date if you're planning to convert this to a Date object.
  updateId: number | null
  updateBy: string | null
  updateTime: string // Same here, consider using Date.
  remark: string | null
  id: number
  courseId: number
  relevanceId: number
  relevanceName: string
  scope: any
  type: number
  effectiveDay: number
  autoAssign: boolean | null // Assuming autoAssign can be a boolean or null based on your data.
}
