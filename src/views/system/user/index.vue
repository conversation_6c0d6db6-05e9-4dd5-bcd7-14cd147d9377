<script setup name="User" lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { ElMessageBox, ElTree } from 'element-plus'
import { Pane, Splitpanes } from 'splitpanes'
import { getAccessToken } from '@/utils/auth'
import download from '@/utils/download'
import {
  addUser,
  changeUserStatus,
  delUser,
  deptTreeSelect,
  exportUser,
  getUser,
  lineManagerSelect,
  listUser,
  resetUserPwd,
  updateUser,
  UserRespVO
} from '@/api/system/user'
import { listSection } from '@/api/system/section'
import 'splitpanes/dist/splitpanes.css'
import { listCompany } from '@/api/system/company'
import { DeptRespVO, listDept } from '@/api/system/dept'
import { OrgType } from '@/enums/OrgType'
import { listPosition } from '@/api/system/post'
import UserAssignRoleForm from './UserAssignRoleForm.vue'
import {defaultProps, handlePhaseTree, handleTree} from '@/utils/tree'
import * as RoleApi from "@/api/system/role"
import { timestampToDate } from "@/utils/formatDate"
const router = useRouter()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const userList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const dateRange = ref<any>([])
const deptName = ref('')
const deptOptions = ref<any[]>([])
const departmentOptions = ref<Tree[]>([])
const departList = ref()
const companyOptions = ref(undefined)
const positionOptions = ref<any[]>([])
const roleOptions = ref([] as RoleApi.RoleVO[]) // 角色的列表
const sectionOptions = ref<any[]>([])
const lineManagerOptions = ref<any[]>([])
const defaultExpand = ref()
const deptTreeRef = ref<InstanceType<typeof ElTree>>()
/** * 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: `Bearer ${getAccessToken()}` },
  // 上传的地址
  url: `${import.meta.env.VITE_API_URL}/system/user/import`,
})
// 列显隐信息
const columns = computed(() =>
  [
    { key: 0, label: t('sys.user.nickName'), visible: true },
    { key: 1, label: t('sys.user.badgeNo'), visible: true },
    { key: 2, label: t('sys.user.email'), visible: true },
    { key: 3, label: t('sys.user.department'), visible: true },
    { key: 4, label: t('sys.user.workType'), visible: true },
    { key: 5, label: t('sys.user.status'), visible: true },
    { key: 6, label: t('sys.user.nationality'), visible: true },
    { key: 7, label: t('sys.user.createTime'), visible: true },
  ],
)
const queryRef = ref()

const data = reactive<{
  form: any
  queryParams: any
}>({
  form: {
    nickName: undefined,
    password: undefined,
    userId: undefined,
    deptId: undefined,
    phone: undefined,
    email: undefined,
    sex: undefined,
    status: 0,
    remark: undefined,
    postIds: [],
    roleIds: [],
    sectIds: [],
    lineManagerIds: undefined,
    workType: undefined,
    companyName: undefined,
    badgeNumber: undefined,
    nationalityCode: undefined,
    userStatus: '1',
    workTerms: '99',
    // isSend: 0,
  },
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    nickname: undefined,
    mobile: undefined,
    status: undefined,
    deptId: undefined,
    nationalityCode: undefined,
    badgeNumber: undefined,
    workTerms: undefined,
    userStatus: undefined,
  },
})
const rules = computed<any>(() => (
  {
    nickName: [{ required: true, message: () => t('sys.user.userNicknameRule'), trigger: 'blur' }, { min: 1, max: 128, message: t('sys.user.nickNameLengthRule'), trigger: 'blur' }],
    email: [{ required: true, type: 'email', message: t('sys.user.emailRule'), trigger: ['blur', 'change'] }],
    phone: [{ required: false, pattern: /^[^a-z]{0,14}$/i, message: t('sys.user.phoneNumberRule'), trigger: 'blur' }],
    companyId: [{ required: true, message: t('sys.user.companyRule'), trigger: ['blur', 'change'] }],
    deptId: [{ required: true, message: t('sys.user.departmentRule'), trigger: ['blur', 'change'] }],
    postIds: [{ required: false, message: t('sys.user.positionRule'), trigger: ['blur', 'change'] }],
    sectIds: [{ required: false, message: t('sys.user.sectionRule'), trigger: ['blur', 'change'] }],
    badgeNumber: [{ required: true, message: t('sys.user.badgeNoRule'), trigger: 'blur' }],
    workType: [{ required: true, message: t('sys.user.workTypeRule'), trigger: ['blur', 'change'] }],
    roleIds: [{ required: true, message: t('sys.user.roleRule'), trigger: ['blur', 'change'] }],
    status: [{ required: true, message: t('sys.user.statusRule'), trigger: ['blur', 'change'] }],
    userStatus: [{ required: true, message: t('sys.user.userStatusRule'), trigger: ['blur', 'change'] }],
  }
))
const { queryParams, form } = toRefs(data)
const highCurrentStatus = ref(false)
const clickedNodeId = ref<number | null>(null) // 存储当前点击的节点ID
const assignRoleFormRef = ref()
/** 通过条件过滤节点  */
const filterNode = (value: any, data: any) => {
  if (!value)
    return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  deptTreeRef.value!.filter(val)
})
/** 查询左侧树结构 */
const getDeptTree = async () => {
  try {
    const res = await deptTreeSelect({})
    deptOptions.value = res
    getList()
  } catch (e) {}
};

/** 左侧树节点单击事件 */
const handleNodeClick = (data: DeptRespVO) => {
  highCurrentStatus.value = true
  clickedNodeId.value = data.virtualId // 点击节点时，更新当前点击的节点ID
  if (data.level === OrgType.Company) {
    queryParams.value.companyId = data.id
    queryParams.value.deptId = null
  }
  if (data.level === OrgType.Department) {
    queryParams.value.deptId = data.id
    queryParams.value.companyId = null
  }
  handleQuery()
};
/** 左侧树点击取消高亮 */
const handleCancelHighlight = () => {
  clickedNodeId.value = null
  highCurrentStatus.value = false
  queryParams.value.deptId = null
  queryParams.value.companyId = null
  handleQuery()
}
/** 右侧-查询用户列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await listUser(queryParams.value)
    userList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
};
/** 添加编辑-查询公司下拉树结构 */
const getCompanyTree = async () => {
  try {
    companyOptions.value = await listCompany()
  } catch (e) {}
}
/** 添加编辑-查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  try {
    const data = await listDept({ companyId })
    if (data) {
      departList.value = data
      departmentOptions.value = handleTree(data, 'id')
      form.value.deptId = form.value.deptId ?? departmentOptions.value[0].id
      // 部门返回的值没有回显label时，清空
      const test = departList.value.filter(dept => dept.id === form.value.deptId)
      if (test.length === 0) {
        form.value.deptId = null
      }
    }
    await getSectionData(form.value.deptId)
    await getPositionData(form.value.deptId)
  } catch (e) {}
}

/** 添加编辑-获取section下拉数据 */
const handleDeptNodeClick = (data: UserRespVO) => {
  form.value.postIds = null
  form.value.sectIds = null
  getPositionData(data.id)
  getSectionData(data.id)
};
/** 添加编辑-获取section下拉数据 */
const getSectionData = async (deptId: number) => {
  try {
    sectionOptions.value = await listSection({ deptId })
  } catch (e) {}
}
/** 添加编辑-获取position下拉数据 */
const getPositionData = async (deptId: number) => {
  try {
    positionOptions.value = await listPosition({ deptId })
  } catch (e) {}
}

/** 添加编辑-点击公司树查询部门数据 */
const handleCompanyNodeClick = (node: DeptRespVO) => {
  form.value.companyId = node.id
  form.value.deptId = null
  form.value.postIds = null
  form.value.sectIds = null
  /** 查询Department下拉数据 */
  getDepartmentTree(node.id)
}

/** 添加编辑-查询lineManager下拉数据 */
const getLineManagerData = async () => {
  try {
    lineManagerOptions.value = await lineManagerSelect()
  } catch (e) {}
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
};
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  queryRef.value?.resetFields()
  queryParams.value.deptId = undefined
  handleQuery()
}

/** 删除按钮操作 */
const handleDelete = async (row: UserRespVO) => {
  const userIds = row.id || ids.value
  if (!row.nickname) {
    row.nickname = 'them'
  }
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await delUser(userIds)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    const data = await exportUser(queryParams.value)
    download.excel(data, `user_${new Date().getTime()}.xlsx`)
  } catch {
  } finally {
  }
}
/** 用户状态修改  */
const handleStatusChange = async (user: UserRespVO) => {
  const text = user.status === 0 ? t('global.enable') : t('global.disable')
  try {
    await message.confirm()
    await changeUserStatus(user.id, user.status)
    message.success(`${text} ${t('common.delSuccess')}`)
  } catch {
    user.status = user.status === 0 ? 1 : 0
  }
}

/** 跳转角色分配 */
const handleRole = (row: UserRespVO) => {
  assignRoleFormRef.value.open(row)
}
/** 重置密码按钮操作 */
const handleResetPwd = (row: UserRespVO) => {
  ElMessageBox.confirm(`${t('sys.user.resetPasswordConfirm')}`, t('global.tip'), {
    confirmButtonText: t('global.ok'),
    cancelButtonText: t('global.cancel'),
    closeOnClickModal: false,
    inputErrorMessage: t('sys.user.passwordLengthRule'),
  }).then(({ value }) => {
    resetUserPwd(row.id, value).then(() => {
      message.success(t('sys.user.resetPwdSuccess'))
    })
  }).catch(() => {})
}
/** 选择条数  */
const handleSelectionChange = (selectionUsers: UserRespVO[]) => {
  ids.value = selectionUsers.map((user: UserRespVO) => user.id)
  single.value = selectionUsers.length !== 1
  multiple.value = !selectionUsers.length
}
/** 导入按钮操作 */
const handleImport = () => {
  upload.title = t('sys.user.import')
  upload.open = true
}
/** 下载模板操作 */
const importTemplate = () => {
}
/** 文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true
}
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: any) => {
  upload.open = false
  upload.isUploading = false
  getList()
}
/** 提交上传文件 */
const submitFileForm = () => {
}
const userRef = ref()
/** 重置操作表单 */
const reset = () => {
  form.value = {
    nickName: undefined,
    password: undefined,
    userId: undefined,
    deptId: undefined,
    phone: undefined,
    email: undefined,
    sex: undefined,
    status: 0,
    remark: undefined,
    postIds: [],
    roleIds: [],
    sectIds: [],
    lineManagerIds: undefined,
    workType: undefined,
    companyName: undefined,
    badgeNumber: undefined,
    nationalityCode: undefined,
    userStatus: '1',
    workTerms: '99',
    // isSend: '0',
  }
  userRef.value?.resetFields()
}
/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}

// 通过角色标识获取学生角色id
const getStudentRoleId = async () => {
  const data = await RoleApi.getRolePage({code: 'student' } )
  form.value.roleIds = [data?.list[0].id]

}
/** 新增按钮操作 */
const handleAdd = async () => {
  reset()
  // 获取角色信息id
  await getStudentRoleId()
  // const data = await getUser()
  // roleOptions.value = data?.roles
  open.value = true
  title.value = t('sys.user.addUser')
}

/** 修改按钮操作 */
const handleUpdate = async (row: UserRespVO) => {
  reset()
  const userId = row.id || ids.value
  try {
    const data = await getUser(userId)
    form.value = data.user
    form.value.userId = data.user.id
    form.value.phone = data.user.mobile
    form.value.nickName = data.user.nickname
    form.value.roleIds = data.roleIds
    if (data.user.onboardingDate) {
      form.value.onboardingDate = timestampToDate(data.user.onboardingDate)
    }
    form.value.lineManagerIds = data.lineManagerIds
    if (!form.value.companyId) {
      form.value.deptId = null
      form.value.postIds = null
      form.value.sectIds = null
    }
    else {
      form.value.deptId = data.user.deptId
      form.value.postIds = data.postIds
      form.value.sectIds = data.sectIds
      getDepartmentTree(form.value.companyId)
    }

    open.value = true
    title.value = t('sys.user.editUser')
  } catch (e) {}
}
/** 提交按钮 */
const submitForm = async () => {
  // 校验表单
  if (!userRef.value) return
  const valid = await userRef.value.validate()
  if (!valid) return
  try {
    if (form.value.id !== undefined) {
      await updateUser(form.value)
      message.success(t('common.updateSuccess'))
    } else {
      // for (const keys in form.value) {
      //   if (!form.value[keys]) {
      //     delete form.value[keys]
      //   }
      // }
      await addUser(form.value)
      message.success(t('common.createSuccess'))
    }
    open.value = false
    await getList()
  } finally {}
}

const getRole = async () => {
  // 获得角色列表
  try {
    roleOptions.value = await RoleApi.getSimpleRoleList()
  } catch (e) {}
}

/** 初始化 */
onMounted(() => {
  getRole()
  getCompanyTree()
  getDeptTree()
  getList()
  getLineManagerData()
})
</script>

<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <!-- 部门数据 -->
      <Pane :size="18" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <div class="head-container">
            <el-input
              v-model="deptName"
              :placeholder="t('sys.user.deptNamePH')"
              clearable
              class="mb-[20px]"
            >
              <template #prefix>
                <Icon class="mr-5px" icon="ep:search" />
              </template>
            </el-input>
          </div>
          <div class="head-container">
            <ElTree
              ref="deptTreeRef"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              :highlight-current="highCurrentStatus"
              :default-expanded-keys="defaultExpand"
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <div class="flex justify-between">
                  <div class="me-2.5">
                    <el-tag
                      v-if="node.data.level === OrgType.Company " :title="node.data.shortName"
                      :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0',
                      }"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Company' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Department" :title="node.data.shortName">
                      {{ node.data.shortName ? node.data.shortName : 'Dept' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Section" type="info">
                      {{ 'Section' }}
                    </el-tag>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex gap-1.5 me-5">
                      <span
                        :title="node.label" class="whitespace-normal line-clamp-1 break-all"
                      > {{ node.label }}</span>
                    </div>

                    <div class="flex justify-end ms-auto" @click.stop="handleCancelHighlight">
                      <el-icon v-if="clickedNodeId === node.data.virtualId">
                        <CircleClose />
                      </el-icon>
                    </div>
                  </div>
                </div>
              </template>
            </ElTree>
          </div>
        </el-scrollbar>

      </Pane>
      <!-- 用户数据 -->
      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <div class="p-2">
            <ContentWrap>
              <el-form v-show="showSearch" ref="queryRef" label-position="left" :model="queryParams" :inline="true" label-width="110px">
                <el-form-item :label="t('sys.user.nickName')" prop="nickname">
                  <el-input
                    v-model="queryParams.nickname"
                    :placeholder="t('sys.user.nickNamePH')"
                    clearable
                    class="!w-240px"
                    @keydown.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item :label="t('sys.user.badgeNo')" prop="badgeNumber">
                  <el-input
                    v-model="queryParams.badgeNumber"
                    :placeholder="t('sys.user.badgeNoPH')"
                    clearable
                    class="!w-240px"
                    @keydown.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item :label="t('sys.user.email')" prop="email">
                  <el-input
                    v-model="queryParams.email"
                    :placeholder="t('sys.user.emailPH')"
                    clearable
                    class="!w-240px"
                    @keydown.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item :label="t('sys.user.workType')" prop="workType">
                  <el-select
                    v-model="queryParams.workType"
                    :placeholder="t('sys.user.workTypePH')"
                    clearable
                    filterable
                    class="!w-240px"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_WORK_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="t('sys.user.status')" prop="status">
                  <el-select
                    v-model="queryParams.status"
                    :placeholder="t('sys.user.statusPH')"
                    clearable
                    filterable
                    class="!w-240px"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_STATUS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="t('sys.user.userStatus')" prop="userStatus">
                  <el-select
                    v-model="queryParams.userStatus"
                    :placeholder="t('sys.user.userStatusPH')"
                    clearable
                    filterable
                    class="!w-240px"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_USER_STATUS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
<!--                <el-form-item :label="t('sys.user.nationality')" prop="nationalityCode">-->
<!--                  <el-select-->
<!--                    v-model="queryParams.nationalityCode" :placeholder="t('sys.user.nationalityPH')"-->
<!--                    clearable filterable-->
<!--                    class="!w-240px"-->
<!--                  >-->
<!--                    <el-option-->
<!--                      v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_NATIONALITY)"-->
<!--                      :key="dict.value"-->
<!--                      :label="dict.label"-->
<!--                      :value="dict.value"-->
<!--                    />-->
<!--                  </el-select>-->
<!--                </el-form-item>-->
                <el-form-item :label="t('sys.user.workTerms')" prop="workTerms">
                  <el-select
                    v-model="queryParams.workTerms"
                    :placeholder="t('sys.user.workTermPH')"
                    clearable
                    filterable
                    class="!w-240px"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_WORK_TERMS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery">
                    <Icon class="mr-5px" icon="ep:search" />
                    {{ t('action.search') }}
                  </el-button>
                  <el-button @click="resetQuery">
                    <Icon class="mr-5px" icon="ep:refresh" />
                    {{ t('action.reset') }}
                  </el-button>
                  <el-button
                    v-hasPermi="['system:user:create']"
                    type="primary"
                    plain
                    @click="handleAdd"
                  >
                    <Icon class="mr-5px" icon="ep:plus" />
                    {{ t('action.add') }}
                  </el-button>
                  <el-button
                    v-hasPermi="['system:user:update']"
                    type="success"
                    plain
                    :disabled="single"
                    @click="handleUpdate"
                  >
                    <Icon class="mr-5px" icon="ep:edit" />
                    {{ t('action.update') }}
                  </el-button>
                  <el-button
                    v-hasPermi="['system:user:delete']"
                    type="danger"
                    plain
                    :disabled="multiple"
                    @click="handleDelete"
                  >
                    <Icon class="mr-5px" icon="ep:delete" />
                    {{ t('action.delete') }}
                  </el-button>
<!--                  <el-button-->
<!--                    v-hasPermi="['system:user:export']"-->
<!--                    type="warning"-->
<!--                    plain-->
<!--                    @click="handleExport"-->
<!--                  >-->
<!--                    <Icon class="mr-5px" icon="ep:download" />-->
<!--                    {{ t('action.export') }}-->
<!--                  </el-button>-->
                </el-form-item>
              </el-form>
            </ContentWrap>
            <ContentWrap>
              <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="50" align="center" fixed="left" />
                <el-table-column key="nickname" :label="t('sys.user.nickName')" align="center" prop="nickname" fixed="left" width="160" />
                <el-table-column key="badgeNumber" :label="t('sys.user.badgeNo')" align="center" prop="badgeNumber" :show-overflow-tooltip="true" width="160" />
                <el-table-column key="email" :label="t('sys.user.email')" align="center" prop="email" :show-overflow-tooltip="true" width="240" />
                <el-table-column key="deptName" :label="t('sys.user.department')" align="center" prop="deptName" :show-overflow-tooltip="true" width="160" />
                <el-table-column key="postNames" :label="t('sys.user.position')" align="center" prop="postNames" :show-overflow-tooltip="true" width="220" />
                <el-table-column key="sectNames" :label="t('sys.user.section')" align="center" prop="sectNames" :show-overflow-tooltip="true" width="220" />
                <el-table-column key="roleNames" :label="t('sys.user.role')" align="center" prop="roleNames" :show-overflow-tooltip="true" width="150" />
                <el-table-column key="worktype" :label="t('sys.user.workType')" align="center" prop="workType" width="120">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TYPE" :value="scope.row.workType" />
                  </template>
                </el-table-column>
                <el-table-column key="userStatus" :label="t('sys.user.userStatus')" align="center" prop="userStatus" width="120">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_USER_USER_STATUS" :value="scope.row.userStatus" />
                  </template>
                </el-table-column>
                <el-table-column key="status" :label="t('sys.user.status')" align="center" prop="status">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_USER_STATUS" :value="scope.row.status" />
                  </template>
                </el-table-column>
                <el-table-column key="workTerms" :label="t('sys.user.workTerms')" align="center" prop="workTerms" width="120">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TERMS" :value="scope.row.workTerms" />
                  </template>
                </el-table-column>
                <el-table-column prop="dataSource" align="center" label="Data Source" width="110" />
<!--                <el-table-column prop="createBy" align="center" label="Creator" width="110" />-->
                <el-table-column :label="t('global.action')" align="center" width="170" class-name="small-padding fixed-width" fixed="right">
                  <template #default="scope">
                    <el-tooltip v-if="scope.row.id !== 1" :content="t('global.edit')" placement="top">
                      <el-button v-hasPermi="['system:user:update']" link type="primary" @click="handleUpdate(scope.row)">
                        <Icon icon="ep:edit" />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip v-if="scope.row.id !== 1" :content="t('global.delete')" placement="top">
                      <el-button v-hasPermi="['system:user:delete']" link type="primary" @click="handleDelete(scope.row)">
                        <Icon icon="ep:delete" />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip v-if="scope.row.id !== 1" :content="t('sys.user.resetPassword')" placement="top">
                      <el-button v-hasPermi="['system:user:update-password']" link type="primary" @click="handleResetPwd(scope.row)">
                        <Icon icon="ep:key" />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip v-if="scope.row.id !== 1" :content="t('sys.user.assignRoles')" placement="top">
                      <el-button link type="primary" @click="handleRole(scope.row)">
                        <Icon icon="ep:circle-check" />
                      </el-button>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-show="total > 0"
                v-model:page="queryParams.pageNo"
                v-model:limit="queryParams.pageSize"
                :total="total"
                @pagination="getList"
              />
            </ContentWrap>
          </div>
        </el-scrollbar>
      </Pane>
    </Splitpanes>
    <!-- 添加或修改用户配置对话框 -->
    <Dialog v-model="open" :title="title">
      <el-form ref="userRef" :model="form" :rules="rules" label-width="110px" label-position="left">
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.nickName')" prop="nickName">
              <el-input v-model="form.nickName" :placeholder="t('sys.user.nickName')" maxlength="50" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('sys.user.company')" prop="companyId">
              <el-tree-select
                v-model="form.companyId"
                :data="companyOptions"
                :props="{ value: 'id', label: 'name', children: 'children' }"
                value-key="id"
                :placeholder="t('sys.user.companyPH')"
                check-strictly
                clearable
                filterable
                @node-click="handleCompanyNodeClick"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.department')" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="departmentOptions"
                :props="defaultProps"
                :placeholder="t('sys.user.departmentPH')"
                node-key="id"
                check-strictly
                clearable
                filterable
                :disabled="!form.companyId"
                @node-click="handleDeptNodeClick"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('sys.user.section')" prop="sectIds">
              <el-select
                v-model="form.sectIds"
                :placeholder="t('sys.user.sectionPH')"
                multiple
                clearable
                filterable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="1"
                :disabled="!form.companyId"
              >
                <el-option
                  v-for="section in sectionOptions"
                  :key="section.sectId"
                  :label="section.sectName"
                  :value="section.sectId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.position')" prop="postIds">
              <el-select
                v-model="form.postIds"
                :placeholder="t('sys.user.positionPH')"
                multiple
                clearable
                filterable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="1"
                :disabled="!form.companyId"
              >
                <el-option
                  v-for="position in positionOptions"
                  :key="position.id"
                  :label="position.name"
                  :value="position.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('sys.user.email')" prop="email">
              <el-input v-model="form.email" :placeholder="t('sys.user.emailPH')" maxlength="50" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.gender')">
              <el-select
                v-model="form.sex"
                :placeholder="t('sys.user.genderPH')"
                clearable
                filterable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_PhaseI_USER_SEX)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('sys.user.badgeNo')" prop="badgeNumber">
              <el-input v-model="form.badgeNumber" :placeholder="t('sys.user.badgeNoPH')" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.lineManager')" prop="lineManagerIds">
              <el-select
                v-model="form.lineManagerIds"
                :placeholder="t('sys.user.lineManagerPH')"
                multiple
                filterable
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="1"
              >
                <el-option
                  v-for="lineManager in lineManagerOptions"
                  :key="lineManager.id"
                  :label="lineManager.nickname"
                  :value="lineManager.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('sys.user.nationality')" prop="nationality">
              <el-select v-model="form.nationalityCode" :placeholder="t('sys.user.nationalityPH')">
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_NATIONALITY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.phoneNumber')" prop="phone">
              <el-input v-model="form.phone" :placeholder="t('sys.user.phoneNumberPH')" maxlength="14" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('sys.user.workType')" prop="workType">
              <el-select v-model="form.workType" placeholder="Please choose" clearable filterable>
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_USER_WORK_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.role')" prop="roleIds">
              <el-select v-model="form.roleIds" multiple :placeholder="t('sys.user.rolePH')" filterable clearable collapse-tags collapse-tags-tooltip :max-collapse-tags="1">
                <el-option
                  v-for="role in roleOptions"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.status')" prop="status">
              <el-select v-model="form.status" :placeholder="t('sys.user.statusPH')" clearable filterable>
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.userStatus')" prop="userStatus">
              <el-select v-model="form.userStatus" :placeholder="t('sys.user.userStatusPH')" clearable filterable>
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_USER_USER_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.onboardingDate')" prop="onboardingDate">
              <el-date-picker v-model="form.onboardingDate" type="date" :placeholder="t('sys.user.onboardingDatePH')" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12" :xs="12">
            <el-form-item :label="t('sys.user.workTerms')" prop="workTerms">
              <el-select v-model="form.workTerms" :placeholder="t('sys.user.workTermPH')" clearable filterable>
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_USER_WORK_TERMS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
<!--          <el-col :span="12" :xs="12" class="hidden">-->
<!--            <el-form-item label="isSend" prop="isSend">-->
<!--              <el-select v-model="form.isSend">-->
<!--                <el-option value="0">-->
<!--                  0-->
<!--                </el-option>-->
<!--                <el-option value="1">-->
<!--                  1-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button @click="cancel">
            {{ t('global.cancel') }}
          </el-button>
        </div>
      </template>
    </Dialog>

    <!-- 用户导入对话框 -->
    <Dialog v-model="upload.open" :title="upload.title">
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="`${upload.url}?updateSupport=${upload.updateSupport}`"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">
          {{ t('global.fileDrag') }},{{ t('global.or') }} <em>{{ t('global.clickToUpload') }}</em>
        </div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />{{ t('sys.user.updateData') }}
            </div>
            <span>{{ t('sys.user.importTip') }}.</span>
            <el-link type="primary" :underline="false" class="text-xs align-baseline" @click="importTemplate">
              {{ t("sys.user.downloadTemplate") }}
            </el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button @click="upload.open = false">
            {{ t('global.cancel') }}
          </el-button>
        </div>
      </template>
    </Dialog>

    <!-- 分配角色 -->
    <UserAssignRoleForm ref="assignRoleFormRef" @success="getList" />
  </div>
</template>

<style scoped lang="scss">
 /*
* select 多选时tag超长时去掉回车占位
*/
:deep .el-select__selection{
  flex-wrap: nowrap !important;
}
</style>
