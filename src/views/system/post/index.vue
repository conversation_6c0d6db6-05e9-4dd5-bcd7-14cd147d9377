<script setup name="Position" lang="ts">
import type { ComponentInternalInstance } from 'vue'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import type { ElTree } from 'element-plus'
import { OrgType } from '@/enums/OrgType'
import {getSection, listSectExcludeChild, listSection, SectionTreeRespVO, sectTreeSelect} from '@/api/system/section'
import {
  addPosition,
  delPosition,
  getPosition,
  listPosition,
  listPostExcludeChild, PostRespVO,
  PostSaveVO,
  updatePosition
} from '@/api/system/post'
import { checkPermi } from '@/utils/permission'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { handlePhaseTree } from '@/utils/tree'
import { useI18n } from "vue-i18n"
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const positionList = ref<any[]>([])
const sectionList = ref<any[]>([])
const positionOptions = ref<any[]>([])
const open = ref(false)
const loading = ref(true)
const ids = ref<number[]>([])
const title = ref('')
const postTotal = ref(0)
const sectionRef = ref()
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {},
  queryParams: {
    postCode: undefined,
    postName: undefined,
    status: undefined,
    parentId: undefined,
    deptId: undefined,
    deptName: undefined,
  },
  rules: {
    name: [{ required: true, message: t('sys.post.positionNameRule'), trigger: 'blur' }],
    code: [{ required: false, message: t('sys.post.positionCodeRule'), trigger: 'blur' }],
    sort: [{ required: true, message: t('sys.post.orderNumRule'), trigger: 'blur' }],
  },
})

const { queryParams, form, rules } = toRefs(data)
const deptName = ref('')
const deptOptions = ref<any[]>([])
const postTreeRef = ref<InstanceType<typeof ElTree>>()
/** 当前选中的部门信息 */
const selDept = ref()
const defaultExpand = ref()
const isExpandAll = ref(false)
const loadingForm = ref(false)
/** 左侧-查询公司部门下拉树结构 */
const getDeptTree = async () => {
  const data = await sectTreeSelect({ type: 0 })
  deptOptions.value = data
  selDept.value = deptOptions.value[0]
  queryParams.value.compId = deptOptions.value[0].id
  // 展开默认选中节点
  const firstOption = deptOptions.value[0]
  defaultExpand.value = [deptOptions.value[0].virtualId, firstOption.level === OrgType.Company && firstOption.children ? deptOptions.value[0].children[0].virtualId : undefined]
  nextTick(() => {
    postTreeRef.value?.setCurrentNode(deptOptions.value[0])
  })
  await getList()
};
/** 通过条件过滤节点  */
const filterNode = (value: string, data: SectionTreeRespVO) => {
  if (!value)
    return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  postTreeRef.value!.filter(val)
})
/** 查询Position列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await listPosition(queryParams.value)
    positionList.value = handlePhaseTree(data, 'id')
    postTotal.value = data.length
  } finally {
    loading.value = false
  }
}
/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}
/** 表单重置 */
const reset = () => {
  form.value = {
    sectId: undefined,
    code: undefined,
    name: undefined,
    sort: 0,
    status: 0,
    parentId: undefined,
  }
  sectionRef.value?.resetFields()
}
/** 新增按钮操作 */
const handleAdd = async () => {
  reset()
  open.value = true
  form.value.deptId = queryParams.value.deptId
  form.value.deptName = queryParams.value.deptName
  title.value = t('sys.post.addPost')
  const data = await listSection({ deptId: queryParams.value.deptId })
  sectionList.value = handlePhaseTree(data, 'sectId')
  const listPositionData = await listPosition({ deptId: queryParams.value.deptId })
  positionOptions.value = handlePhaseTree(listPositionData, 'id')
}
/** 修改按钮操作 */
const handleUpdate = async (position: PostRespVO) => {
  reset()
  const postId = position.id
  if (postId) {
    const data = await listSection({ deptId: queryParams.value.deptId })
    sectionList.value = handlePhaseTree(data, 'sectId')
    loadingForm.value = true
    // deptId: row.deptId 去掉部门限制，查询全部
    const listPositionData = await listPosition({ deptId: queryParams.value.deptId })
    positionOptions.value = handlePhaseTree(listPositionData, 'id')
    loadingForm.value = false
  }
  const positionInfo = await getPosition(postId as number)
  form.value = positionInfo
  form.value.deptName = queryParams.value.deptName
  form.value.sectId = positionInfo?.sectId
  form.value.parentId = positionInfo?.parent?.id
  // form.value
  if (form.value.parentId === 0)
    form.value.parentId = null
  open.value = true
  title.value = t('sys.post.editPost')
}
/** 提交按钮 */
const submitForm = async () => {
  const valid = await sectionRef.value.validate()
  if (!valid) return
  try {
    if (form.value.id !== undefined) {
      await updatePosition(form.value)
      message.success(t('common.updateSuccess'))
    } else {
      await addPosition(form.value)
      message.success(t('common.createSuccess'))
    }
    open.value = false
    await getList()
  } finally {}
}
/** 删除按钮操作 */
const handleDelete = async (position: PostRespVO) => {
  const postIds = position.id || ids.value
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await delPosition(postIds)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
const handleNodeClick = async (node: SectionTreeRespVO) => {
  selDept.value = node
  if (node.level === OrgType.Company) {
    queryParams.value.compId = node.id
    queryParams.value.deptId = null
    queryParams.value.sectId = null
    queryParams.value.sectName = null
  }
  if (node.level === OrgType.Department) {
    queryParams.value.deptName = node.label
    queryParams.value.deptId = node.id
    queryParams.value.compId = null
    queryParams.value.sectId = null
    queryParams.value.sectName = null
  }
  if (node.level === OrgType.Section) {
    queryParams.value.sectName = node.label
    queryParams.value.sectId = node.id
    queryParams.value.deptId = null
    queryParams.value.deptName = null
    queryParams.value.compId = null
    const data = await getSection(node.id as number)
    queryParams.value.deptId = data.department.id
    queryParams.value.deptName = data.department.name
  }
  await getList()
}
/** 初始化 */
onMounted(() => {
  getDeptTree()
})
</script>

<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <Pane :size="26" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <div class="head-container">
            <el-input
              v-model="deptName"
              :placeholder="t('sys.user.deptNamePH')"
              clearable
              class="mb-[20px]"
            >
              <template #prefix>
                <Icon class="mr-5px" icon="ep:search" />
              </template>
            </el-input>
          </div>
          <div class="head-container">
            <el-tree
              ref="postTreeRef"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :default-expanded-keys="defaultExpand"
              :filter-node-method="filterNode"
              node-key="virtualId"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <div class="flex justify-between">
                  <div class="me-2.5">
                    <el-tag
                      v-if="node.data.level === OrgType.Company " :title="node.data.shortName"
                      :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0',
                      }"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Company' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Department" :title="node.data.shortName">
                      {{ node.data.shortName ? node.data.shortName : 'Dept' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Section" type="info">
                      {{ 'Section' }}
                    </el-tag>
                  </div>
                  <span
                    :title="node.data.label" class="whitespace-normal line-clamp-1 break-all"
                  > {{ node.data.label }}</span>
                </div>
              </template>
            </el-tree>
          </div>
        </el-scrollbar>
      </Pane>
      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <div class="pt-3 flex items-center">
            <div class="p-2">
              <el-tag
                v-if="selDept?.level === OrgType.Company" :style="{
                  '--el-tag-text-color': '#630EB8',
                  '--el-tag-bg-color': '#F3F1FF',
                  '--el-tag-border-color': '#D3CEF0',
                }"
              >
                {{ t("global.company") }}
              </el-tag>
              <el-tag v-if="selDept?.level === OrgType.Department">
                {{ t('sys.user.department') }}
              </el-tag>
              <el-tag v-else-if="selDept?.level === OrgType.Section" type="info">
                {{ t('sys.user.section') }}
              </el-tag>
              <span class="ms-2.5">{{ selDept?.label }}</span>
            </div>
            <div class="ms-auto">
              <el-button
                v-show="selDept?.level !== OrgType.Company"
                v-hasPermi="['system:post:create']"
                type="primary"
                plain
                @click="handleAdd"
              >
                <Icon class="mr-5px" icon="ep:plus" />
                {{ t('action.add') }}
              </el-button>
            </div>
          </div>
          <OrgTotalBar :number="postTotal" :text="t('sys.post.totalTip')" />
          <ContentWrap>
            <el-table
              v-loading="loading" :data="positionList"
              row-key="id"
              :default-expand-all="isExpandAll"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column :label="t('sys.post.postName')" prop="name" min-width="360" />
              <el-table-column :label="t('sys.company.uniqueCode')" prop="code" min-width="180" />
              <el-table-column prop="status" :label="t('sys.company.status')" width="200">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.SYSTEM_NORMAL_DISABLE" :value="row.status" />
                </template>
              </el-table-column>
              <el-table-column prop="dataSource" align="center" :label="t('sys.company.dataSource')" width="110" />
<!--              <el-table-column prop="createBy" align="center" :label="t('sys.company.creator')" width="110" />-->
              <el-table-column
                v-if="(selDept?.level !== OrgType.Company) && (checkPermi(['system:post:update']) || checkPermi(['system:post:delete']))"
                :label="t('global.action')" min-width="200" align="center"
                class-name="small-padding fixed-width"
                fixed="right"
              >
                <template #default="scope">
                  <el-button v-hasPermi="['system:post:update']" link type="primary" @click="handleUpdate(scope.row)">
                    <Icon icon="ep:edit" />
                    {{ t('action.edit') }}
                  </el-button>
                  <el-button v-hasPermi="['system:post:delete']" link type="primary" @click="handleDelete(scope.row)">
                    <Icon icon="ep:delete" />
                    {{ t('action.delete') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </ContentWrap>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <!-- 添加或修改岗位对话框 -->
    <Dialog v-model="open" :title="title">
      <el-form ref="sectionRef" v-loading="loadingForm" :model="form" :rules="rules" label-width="150px" label-position="left">
        <el-form-item :label="t('profile.user.dept')">
          <el-input v-model="form.deptName" :placeholder="t('sys.post.deptName')" disabled />
        </el-form-item>
        <el-form-item :label="t('sys.post.section')" prop="section">
          <el-tree-select
            v-model="form.sectId"
            :data="sectionList"
            :props="{ value: 'sectId', label: 'sectName', children: 'children' }"
            value-key="sectId"
            :placeholder="t('sys.post.sectionPH')"
            check-strictly
          />
        </el-form-item>
        <el-form-item :label="t('sys.post.parentPosition')" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="positionOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            :placeholder="t('sys.post.parentPositionPH')"
            check-strictly
          />
        </el-form-item>
        <el-form-item :label="t('sys.post.positionName')" prop="name">
          <el-input v-model="form.name" :placeholder="t('sys.post.positionPH')" />
        </el-form-item>
        <el-form-item :label="t('sys.post.positionSort')" prop="sort">
          <el-input-number v-model="form.sort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item :label="t('sys.company.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_NORMAL_DISABLE)"
              :key="dict.value"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('sys.post.positionCode')" prop="code">
          <el-input v-model="form.code" :placeholder="t('sys.post.positionCodePH')" :disabled="form.dataSource === 'MDS' ? true : form.dataSource === 'IMPORT' ? true : false" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button @click="cancel">
            {{ t('global.cancel') }}
          </el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>
