<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="170px"
    >
      <el-form-item :label="t('form.previousMenu')">
        <el-tree-select
          v-model="formData.parentId"
          :data="menuTree"
          :props="defaultProps"
          check-strictly
          node-key="id"
          @node-click="handleParentIdChange"
        />
      </el-form-item>
      <el-form-item :label="t('form.menuCode')" prop="name">
<!--        把参数name作为唯一标识-->
        <el-input v-model="formData.name" clearable :placeholder="t('input.menuCodePlaceholder')" />
      </el-form-item>
<!--      中文-->
      <el-form-item :label="t('form.menuName')" prop="nameCn">
        <el-input v-model="formData.nameCn" clearable :placeholder="t('input.menuNamePlaceholder')" />
      </el-form-item>
<!--      英文-->
      <el-form-item :label="t('form.menuNameEn')" prop="nameEn">
        <el-input v-model="formData.nameEn" clearable :placeholder="t('input.menuNameEnPlaceholder')" />
      </el-form-item>
<!--      阿语-->
      <el-form-item :label="t('form.menuNameAr')" prop="nameAr">
        <el-input v-model="formData.nameAr" clearable :placeholder="t('input.menuNameArPlaceholder')" />
      </el-form-item>


      <el-form-item :label="t('form.menuType')" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio-button
            v-for="dict in menuTypeList"
            :key="dict.label"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.type !== 3" :label="t('form.menuIcon')">
          <IconSelect ref="iconSelectRef" clearable :is-show-catalog-menu="isShowCatalogMenu" :icon-infor="formData.icon" />
      </el-form-item>
      <el-form-item v-if="formData.type !== 3" :label="t('form.menuPath')" prop="path">
        <template #label>
          <Tooltip
            :message="t('common.message')"
            :title="t('common.menuPath')"
          />
        </template>
        <el-input v-model="formData.path" clearable :placeholder="t('input.routeAddressPlaceholder')" />
      </el-form-item>
      <el-form-item v-if="formData.type === 2" :label="t('form.menuComponent')" prop="component">
        <el-input v-model="formData.component" clearable :placeholder="t('input.menuComponentPlaceholder')" />
      </el-form-item>
      <el-form-item v-if="formData.type === 2" :label="t('form.menuComponentName')" prop="componentName">
        <el-input v-model="formData.componentName" clearable :placeholder="t('input.menuComponentNamePlaceholder')" />
      </el-form-item>
      <el-form-item v-if="formData.type !== 1" :label="t('form.menuPermissionKey')" prop="permission">
        <template #label>
          <Tooltip
            :message="t('common.menuMessage')"
            :title="t('form.menuPermissionKey')"
          />
        </template>
        <el-input v-model="formData.permission" :placeholder="t('input.menuPermissionPlaceholder')" clearable />
      </el-form-item>
      <el-form-item :label="t('form.menuSort')" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" clearable controls-position="right" />
      </el-form-item>
      <el-form-item :label="t('form.menuStatus')" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.label"
            :value="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.type !== 3" :label="t('form.menuShowStatus')" prop="visible">
        <template #label>
          <Tooltip :message="t('common.menuShowStatusMessage')" :title="t('form.menuShowStatus')" />
        </template>
        <el-radio-group v-model="formData.visible">
          <el-radio key="true" :value="true" border>{{ t('form.show') }}</el-radio>
          <el-radio key="false" :value="false" border>{{ t('form.hide') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.type !== 3" :label="t('form.alwaysShow')" prop="alwaysShow">
        <template #label>
          <Tooltip
            :message="t('common.menuAlwaysShowMessage')"
            :title="t('form.alwaysShow')"
          />
        </template>
        <el-radio-group v-model="formData.alwaysShow">
          <el-radio key="true" :value="true" border>{{ t('form.always') }}</el-radio>
          <el-radio key="false" :value="false" border>{{ t('form.not') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.type === 2" :label="t('form.cacheStatus')" prop="keepAlive">
        <template #label>
          <Tooltip
            :message="t('common.menuCacheStatusMessage')"
            :title="t('form.cacheStatus')"
          />
        </template>
        <el-radio-group v-model="formData.keepAlive">
          <el-radio key="true" :value="true" border>{{ t('form.cache') }}</el-radio>
          <el-radio key="false" :value="false" border>{{ t('form.noCache') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">{{ t('action.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('action.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as MenuApi from '@/api/system/menu'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { CommonStatusEnum, SystemMenuTypeEnum } from '@/utils/constants'
import { defaultProps, handleClientTree } from '@/utils/tree'

defineOptions({ name: 'SystemMenuForm' })

const { wsCache } = useCache()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const iconSelectRef = ref()
const formData = ref({
  id: undefined,
  // code: '',
  name: '',
  nameCn: '',
  nameEn: '',
  nameAr: '',
  permission: '',
  type: undefined,
  sort: Number(undefined),
  parentId: undefined,
  path: '',
  icon: '',
  component: '',
  componentName: '',
  status: CommonStatusEnum.ENABLE,
  visible: true,
  keepAlive: true,
  alwaysShow: true,
  client: undefined
})
const menuTypeList = ref([])
const formRules = reactive({
  name: [{ required: true, message: t('formValidate.menuCode'), trigger: 'blur' }],
  nameCn: [{ required: true, message: t('formValidate.menuName'), trigger: 'blur' }],
  nameEn: [{ required: true, message: t('formValidate.menuNameEn'), trigger: 'blur' }],
  nameAr: [{ required: true, message: t('formValidate.menuNameAr'), trigger: 'blur' }],
  sort: [{ required: true, message: t('formValidate.menuSort'), trigger: 'blur' }],
  path: [{ required: true, message: t('formValidate.menuPath'), trigger: 'blur' }],
  status: [{ required: true, message: t('formValidate.status'), trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const menuId = ref()
// const getLabel = (type: string) => {
//   const language = wsCache.get(CACHE_KEY.LANG)
//   const  ZH_CN = 'ZH_CN'
//   return language === 'zh-CN' ? MenuApi.labelConfig[ZH_CN]?.[type] : MenuApi.labelConfig[language]?.[type];
// }
/** 打开弹窗 */
const open = async (type: string, id?: number, parentId?: number) => {
  menuId.value = id
  resetForm()
  // 获得菜单列表
  await getTree()
  // 重置数据
  menuTypeList.value = [
    {value: 2, label: t('common.menu')},
    {value: 3, label: t('common.button')}
  ]
  if (id) {
    // 通过详情过来判断type按钮类型展示,如果返回是true证明当前修改的是管理端的信息展示目录按钮,根据菜单id相同并且client的值是1则认为是管理端的菜单,如果client不是1隐藏目录按钮(证明修改的是用户端的信息,用户端不展示目录按钮)
    isShowCatalogMenu.value = menuTreeOptions.value.some((item :MenuApi.MenuVO) => item.id === id && item.client === 1)
    if (isShowCatalogMenu.value) {
      formData.value.type = 1
      formData.value.client = 1
      menuTypeList.value.unshift({value: 1, label: t('common.catalog')})
    } else {
      formData.value.type = 2
      formData.value.client = 2
    }
  } else {
    // 当前不存在id,也就是当前是新增状态则默认往数组第一个添加数据
    formData.value.type = 1
    menuTypeList.value.unshift({value: 1, label: t('common.catalog')})
    // 默认添加为管理端
    formData.value.client = 1
  }
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  // resetForm()
  if (parentId) {
    formData.value.parentId = parentId
  }
  // 修改时，设置数据(再加入一种判断,如果有id并且parentId没有值的时候断定当前只是新增和修改操作,才调用接口;如果只判断id有值的时候点击一级后面的新增会触发调用接口,页面会展示当前的信息)
  // 新增的时候不考虑，修改的时候会传递一个id值，满足调用接口,点击一级后面的新增操作会传递一个id值和一个parentId值,不满足要求,不需要调用接口
  if (id && !parentId) {
    formLoading.value = true
    try {
      formData.value = await MenuApi.getMenu(id)
    } finally {
      formLoading.value = false
    }
  }

}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const isShowCatalogMenu = ref(true) // 是否展示菜单类型 (根据选择要添加的某个端是否展示目录)
const handleParentIdChange = (e: any) => {
  // 证明当前点击的是管理端的顶级
  if (e.id === 0 && e.client === 1) {
    if (menuTypeList.value.length !== 3) {
      menuTypeList.value.unshift({value: 1, label: t('common.catalog')})
     }
    formData.value.type = 1
    formData.value.client = 1
    // 设置输入框右侧图标是否展示,添加管理端需要展示,添加用户端则不需要展示(这种情况是通过点击顶级来判断)
    isShowCatalogMenu.value = true
  } else if(e.id ===0 && e.client ===2) {
    if (menuTypeList.value.length !== 2) {
      menuTypeList.value.shift()
     }
    formData.value.type = 2
    formData.value.client = 2
    isShowCatalogMenu.value = false
  } else {}

  if (e.id !== 0) {
    // 证明要添加的是管理端菜单 1.管理端 2.用户端
    isShowCatalogMenu.value = menuTreeOptions.value.some((item :MenuApi.MenuVO) => item.id === e.id && item.client === 1)
    // button按钮选中展示
    if (isShowCatalogMenu.value) {
      formData.value.type = 1
      if (menuTypeList.value.length !== 3) {
        menuTypeList.value.unshift({value: 1, label: t('common.catalog')})
      }
    } else {
      formData.value.type = 2
      if (menuTypeList.value.length !== 2) {
        menuTypeList.value.shift()
      }
    }
  }
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    if (
      formData.value.type === SystemMenuTypeEnum.DIR ||
      formData.value.type === SystemMenuTypeEnum.MENU
    ) {
      if (!isExternal(formData.value.path)) {
        if (formData.value.parentId === 0 && formData.value.path.charAt(0) !== '/') {
          message.error(t('error.pathError'))
          return
        } else if (formData.value.parentId !== 0 && formData.value.path.charAt(0) === '/') {
          message.error(t('error.pathErrorStart'))
          return
        }
      }
    }
    formData.value.icon = iconSelectRef.value?.inputValue
    const data = formData.value as unknown as MenuApi.MenuVO
    if (formType.value === 'create') {
      await MenuApi.createMenu(data)
      message.success(t('common.createSuccess'))
    } else {
      await MenuApi.updateMenu(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
    // 清空，从而触发刷新
    wsCache.delete(CACHE_KEY.ROLE_ROUTERS)
  }
}

/** 获取下拉框[上级菜单]的数据  */
const menuTree = ref<Tree[]>([]) // 树形结构
const menuTreeOptions = ref([]) // 下拉数据

const getTree = async () => {
  menuTree.value = []
  const res = await MenuApi.getSimpleMenusList()
  menuTreeOptions.value = res
  menuTree.value = handleClientTree(res,'id', 'parentId', 'children')
  // let menu: Tree = { id: 0, name: '主类目', children: [] }
  // menu.children = handleTree(res)
  // menuTree.value.push(menu)

}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    nameCn: '',
    nameEn: '',
    nameAr: '',
    // code: '',
    permission: '',
    type: 1,
    sort: Number(undefined),
    parentId: undefined,
    path: '',
    icon: '',
    component: '',
    componentName: '',
    status: CommonStatusEnum.ENABLE,
    visible: true,
    keepAlive: true,
    alwaysShow: true,
    client: undefined
  }
  formRef.value?.resetFields()
}

/** 判断 path 是不是外部的 HTTP 等链接 */
const isExternal = (path: string) => {
  return /^(https?:|mailto:|tel:)/.test(path)
}
</script>
