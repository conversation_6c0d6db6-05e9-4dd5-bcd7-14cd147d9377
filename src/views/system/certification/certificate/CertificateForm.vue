<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="Name" prop="name">
        <el-input v-model="formData.name" placeholder="Please input certificate name" />
      </el-form-item>
      <el-form-item label="Number Prefix" prop="numberPrefix">
        <el-input v-model="formData.numberPrefix" placeholder="Please input certificate number prefix" />
      </el-form-item>
      <el-form-item label="Template" prop="tempId">
        <el-select v-model="formData.tempId" placeholder="Please select">
          <el-option
            v-for="item in certificateTemplatList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Logo" prop="logo">
        <ImageUpload
          v-model="formData.logo"
          class="mt-2.5"
          :limit="1"
          :is-show-tip="true"
          :file-size="500"
          :tip-text="t('category.topic.coverPH')"
          :upload-module="1"
        />
      </el-form-item>
      <el-form-item label="Official Seal" prop="officialSeal">
        <ImageUpload
          v-model="formData.officialSeal"
          class="mt-2.5"
          :limit="1"
          :is-show-tip="true"
          :file-size="500"
          :tip-text="t('category.topic.coverPH')"
          :upload-module="1"
        />
      </el-form-item>
      <el-form-item label="Status" prop="status">
        <el-select v-model="formData.status" placeholder="Please select">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CERTIFICATE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">Cancel</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">Save</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import ImageUpload from '@/components/ImageUpload/index.vue'
import { CertificateApi, CertificateSaveVO } from '@/api/system/certification/certificate'
import { CertificateTemplateApi } from '@/api/system/certification/certificatetemplate'
/** 证书 表单 */
defineOptions({ name: 'CertificateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  tempId: undefined,
  name: undefined,
  numberPrefix: undefined,
  logo: undefined,
  officialSeal: undefined,
  status: undefined
})
const formRules = reactive({
  tempId: [{ required: true, message: 'Certificate template cannot be empty', trigger: 'change' }],
  name: [{ required: true, message: 'Certificate name cannot be empty', trigger: 'blur' }],
  numberPrefix: [{ required: true, message: 'Certificate number prefix cannot be empty', trigger: 'blur' }],
  logo: [{ required: true, message: t('common.uploadText'), trigger: 'change'}],
  officialSeal: [{ required: true, message: 'Official seal cannot be empty', trigger: 'blur' }],
  status: [{ required: true, message: 'Status cannot be empty', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

// 证书模板信息
const certificateTemplatList = ref([])
const getCertificateTemplate = async () => {
  const data = await CertificateTemplateApi.getCertificateTemplatePage({pageNo: 1, pageSize: 10})
  certificateTemplatList.value = data.list
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  await getCertificateTemplate()
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CertificateApi.getCertificate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CertificateSaveVO
    if (formType.value === 'create') {
      await CertificateApi.createCertificate(data)
      message.success(t('common.createSuccess'))
    } else {
      await CertificateApi.updateCertificate(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    tempId: undefined,
    name: undefined,
    numberPrefix: undefined,
    logo: undefined,
    officialSeal: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>
