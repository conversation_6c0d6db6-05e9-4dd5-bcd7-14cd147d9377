<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="Name" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="Please input"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Status" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CERTIFICATE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Time" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="start"
          end-placeholder="end"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset </el-button>
        <el-button
          type="primary"
          plain
          v-hasPermi="['system:certificate:create']"
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> Add
        </el-button>
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          @click="handleExport"-->
<!--          :loading="exportLoading"-->
<!--          v-hasPermi="['system:certificate:export']"-->
<!--        >-->
<!--          <Icon icon="ep:download" class="mr-5px" /> 导出-->
<!--        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="Name" align="center" prop="name" />
      <el-table-column label="Status" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CERTIFICATE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="Create Time"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="Action" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            v-hasPermi="['system:certificate:preview']"
            @click="handleView(scope.row.id,scope.row.tempId)"
          >
            Preview
          </el-button>
          <el-button
            link
            type="primary"
            v-hasPermi="['system:certificate:update']"
            @click="openForm('update', scope.row.id)"
          >
            Edit
          </el-button>
          <el-button
            link
            type="danger"
            v-hasPermi="['system:certificate:delete']"
            @click="handleDelete(scope.row.id)"
          >
            Delete
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CertificateForm ref="formRef" @success="getList" />

<!--  预览模板-->
  <TemplatePreview
    ref="templateRef"
    :certificate-details="certificateDetails"
    :coordinate-details="coordinateDetails"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CertificateApi, CertificateRespVO } from '@/api/system/certification/certificate'
import CertificateForm from './CertificateForm.vue'
import TemplatePreview from '@/components/TemplatePreview/index.vue'
import { CertificateTemplateApi } from "@/api/system/certification/certificatetemplate"
/** 证书 列表 */
defineOptions({ name: 'Certificate' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CertificateRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const templateRef = ref()
const exportLoading = ref(false) // 导出的加载中
const coordinateDetails = ref() // 模板详情
const certificateDetails = ref() // 证书详情
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CertificateApi.getCertificatePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CertificateApi.deleteCertificate(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

// 预览证书模板
const handleView = async (id: number,tempId: number) => {
  // 调用证书模板获取到x和y
  const data = await CertificateTemplateApi.getCertificateTemplate(tempId)
  coordinateDetails.value = data.coordinateDetails
  // 获取证书详情信息 最终将数据重组
  certificateDetails.value = await CertificateApi.getCertificate(id)
  templateRef.value.dialogVisible = true
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CertificateApi.exportCertificate(queryParams)
    download.excel(data, '证书.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
