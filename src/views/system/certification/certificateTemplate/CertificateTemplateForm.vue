<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="Name" prop="name">
        <el-input v-model="formData.name" placeholder="Please input template name" />
      </el-form-item>
      <el-form-item label="Cover" prop="image">
        <TemplateUpload
          v-model="formData.image"
          class="mt-2.5"
          :limit="1"
          :is-show-tip="true"
          :file-size="500"
          :tip-text="t('category.topic.coverPH')"
          :upload-module="1"
          @confirmName="handleTitleCoordinate"
          @confirmNumber="handleNumberCoordinate"
          @confirmLogo="handleLogoCoordinate"
          @confirmSeal="handleSealCoordinate"
          @confirmUser="handleUserCoordinate"
          @confirmTime="handleTimeCoordinate"
          :coordinate-details="formData.coordinateDetails"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">Save</el-button>
      <el-button @click="dialogVisible = false">Cancel</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CertificateTemplateApi, CertificateTemplateSaveVO } from '@/api/system/certification/certificatetemplate'
import TemplateUpload from '@/components/TemplateUpload/index.vue'

/** 证书模板 表单 */
defineOptions({ name: 'CertificateTemplateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  image: undefined,
  coordinateDetails: {
    nameDetails: {
      x: 20,
      y: 20,
    },
    logoDetails: {
      x: 20,
      y: 180,
    },
    numberDetails: {
      x: 20,
      y: 100,
    },
    officialSealDetails: {
      x: 20,
      y: 260,
    },
    userDetails: {
      x: 20,
      y: 340,
    },
    timeDetails: {
      x: 20,
      y: 400,
    }
  }
})

const handleTitleCoordinate = (x: number,y: number) => {
  formData.value.coordinateDetails.nameDetails.x = x
  formData.value.coordinateDetails.nameDetails.y = y
}
const handleNumberCoordinate = (x: number,y: number) => {
  formData.value.coordinateDetails.numberDetails.x = x
  formData.value.coordinateDetails.numberDetails.y = y
}
const handleLogoCoordinate = (x: number,y: number) => {
  formData.value.coordinateDetails.logoDetails.x = x
  formData.value.coordinateDetails.logoDetails.y = y
}
const handleSealCoordinate = (x: number,y: number) => {
  formData.value.coordinateDetails.officialSealDetails.x = x
  formData.value.coordinateDetails.officialSealDetails.y = y
}
const handleUserCoordinate = (x: number,y: number) => {
  formData.value.coordinateDetails.userDetails.x = x
  formData.value.coordinateDetails.userDetails.y = y
}
const handleTimeCoordinate = (x: number,y: number) => {
  formData.value.coordinateDetails.timeDetails.x = x
  formData.value.coordinateDetails.timeDetails.y = y
}
const formRules = reactive({
  name: [{ required: true, message: 'Certificate template name cannot be empty', trigger: 'blur' }],
  image: [{ required: true, message: 'Certificate template image cannot be empty', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CertificateTemplateApi.getCertificateTemplate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CertificateTemplateSaveVO
    if (formType.value === 'create') {
      await CertificateTemplateApi.createCertificateTemplate(data)
      message.success(t('common.createSuccess'))
    } else {
      await CertificateTemplateApi.updateCertificateTemplate(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    image: undefined,
    coordinateDetails: {
      nameDetails: {
        x: 0,
        y: 60,
      },
      logoDetails: {
        x: 0,
        y: 100,
      },
      numberDetails: {
        x: 0,
        y: 160,
      },
      officialSealDetails: {
        x: 0,
        y: 240,
      },
      userDetails: {
        x: 10,
        y: 150,
      },
      timeDetails: {
        x: 10,
        y: 30,
      }
    }
  }
  formRef.value?.resetFields()
}
</script>
