import type { CrudSchema } from '@/hooks/web/useCrudSchemas'
import { dateFormatter } from '@/utils/formatTime'
import * as MailAccountApi from '@/api/system/mail/account'

// 邮箱账号的列表
const accountList = await MailAccountApi.getSimpleMailAccountList()
const { t } = useI18n() // 国际化

// CrudSchema：https://doc.iocoder.cn/vue3/crud-schema/
const crudSchemas = reactive<CrudSchema[]>([
  {
    label: t('table.channelId'),
    field: 'id'
  },
  {
    label: t('table.createTime'),
    field: 'sendTime',
    formatter: dateFormatter,
    search: {
      show: true,
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        type: 'daterange',
        defaultTime: [new Date('1 00:00:00'), new Date('1 23:59:59')],
        style: {
          width: '240px'
        }
      }
    },
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    label: t('table.receiveEmail'),
    field: 'toMail'
  },
  {
    label: t('table.userNumber'),
    field: 'userId',
    isSearch: true,
    isTable: true,
    search: {
      componentProps: {
        style: {
          width: '240px'
        }
      }
    }
  },
  {
    label: t('table.emailUserType'),
    field: 'userType',
    dictType: DICT_TYPE.USER_TYPE,
    dictClass: 'number',
    isSearch: true,
    isTable: true,
    search: {
      componentProps: {
        style: {
          width: '240px'
        }
      }
    }
  },
  {
    label: t('table.emailTitle'),
    field: 'templateTitle'
  },
  {
    label: t('table.emailContent'),
    field: 'templateContent',
    isTable: false
  },
  {
    label: t('table.emailParams'),
    field: 'templateParams',
    isTable: false
  },
  {
    label: t('table.sendStatus'),
    field: 'sendStatus',
    dictType: DICT_TYPE.SYSTEM_MAIL_SEND_STATUS,
    dictClass: 'string',
    isSearch: true,
    search: {
      componentProps: {
        style: {
          width: '240px'
        }
      }
    }
  },
  {
    label: t('table.emailAccount'),
    field: 'accountId',
    isTable: true,
    search: {
      show: true,
      component: 'Select',
      api: () => accountList,
      componentProps: {
        optionsAlias: {
          labelField: 'mail',
          valueField: 'id'
        },
        style: {
          width: '240px'
        }
      }
    }
  },
  {
    label: t('table.emailAddress'),
    field: 'fromMail',
    table: {
      label: t('table.emailAccount'),
    }
  },
  {
    label: t('table.logTemplateCode'),
    field: 'templateId',
    isSearch: true,
    search: {
      componentProps: {
        style: {
          width: '240px'
        }
      }
    }
  },
  {
    label: t('table.templateCode'),
    field: 'templateCode',
    isTable: false
  },
  {
    label: t('table.emailTemplateFromName'),
    field: 'templateNickname',
    isTable: false
  },
  {
    label: t('table.emailSendNumber'),
    field: 'sendMessageId',
    isTable: false
  },
  {
    label: t('table.emailSendException'),
    field: 'sendException',
    isTable: false
  },
  {
    label: t('table.createTime'),
    field: 'createTime',
    isTable: false,
    formatter: dateFormatter,
    detail: {
      dateFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    label: t('table.action'),
    field: 'action',
    isDetail: false
  }
])
export const { allSchemas } = useCrudSchemas(crudSchemas)
