<script setup name="Company" lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { Pane, Splitpanes } from 'splitpanes'
import {
  addDept,
  delDept,
  DepartmentStatusEnum, DeptRespVO,
  getDept,
  listDept,
  listDeptExcludeChild,
  updateDept
} from '@/api/system/dept'
import 'splitpanes/dist/splitpanes.css'
import {CompanyRespVO, listCompany} from '@/api/system/company'
import type { ElTree } from 'element-plus'
import OrgTotalBar from '@/components/OrgTotalBar/index.vue'
import { useI18n } from 'vue-i18n'
import { handlePhaseTree } from '@/utils/tree'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { parseTime } from '@/utils/ruoyi'

const deptList = ref<any[]>([])
const open = ref(false)
const loading = ref(true)
const formLoading = ref(false)
const showSearch = ref(true)
const title = ref('')
const companyOptions = ref<any[]>([])
const departmentOptions = ref<any[]>([])
const isExpandAll = ref(true)
const refreshTable = ref(true)
const companyTreeRef = ref<InstanceType<typeof ElTree>>()
const queryRef = ref()
const deptRef = ref()
const deptTotal = ref(0)
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {
    deptId: undefined,
    companyId: undefined,
    parentId: undefined,
    deptName: undefined,
    sort: 0,
    status: DepartmentStatusEnum.ENABLE,
    deptCode: undefined,
    type: undefined,
    shortName: undefined,
  },
  queryParams: {
    name: undefined,
    status: undefined,
    type: undefined,
    companyId: undefined
  },
  rules: {
    parentId: [{ required: false, message: t('sys.dept.parentIdRule'), trigger: 'blur' }],
    deptName: [{ required: true, message: t('sys.dept.deptNameRule'), trigger: 'blur' }],
    shortName: [{ required: true, message: t('sys.dept.shortNameRule'), trigger: 'blur' }],
    sort: [{ required: true, message: t('sys.dept.orderNumRule'), trigger: 'blur' }],
    deptCode: [{ required: false, message: t('sys.dept.deptCodeRule'), trigger: 'blur' }],
    type: [{ required: true, message: t('sys.dept.typeRule'), trigger: 'blur' }],
  },
})

const { queryParams, form, rules } = toRefs(data)
const deptName = ref('')
/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await listDept(queryParams.value)
    deptList.value = handlePhaseTree(data, 'id')
    deptTotal.value = data.length
  } finally {
    loading.value = false
  }
}
/** 左侧查询公司树结构 */
const getCompanyList = async () => {
  try {
    const data = await listCompany({ type: 0 })
    companyOptions.value = handlePhaseTree(data, 'id')
    queryParams.value.companyId = data[0].id
    nextTick(() => {
      companyTreeRef.value?.setCurrentNode(companyOptions.value[0])
    })
    await getList()
  } finally {}
};
/** 通过条件过滤节点  */
const filterNode = (value: any, data: CompanyRespVO) => {
  if (!value)
    return true
  return data.name.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  companyTreeRef.value!.filter(val)
})
const handleNodeClick = (node: CompanyRespVO) => {
  queryParams.value.companyId = node.id
  getList()
}
/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}
/** 表单重置 */
const reset = () => {
  form.value = {
    deptId: undefined,
    companyId: undefined,
    parentId: undefined,
    deptName: undefined,
    sort: 0,
    status: DepartmentStatusEnum.ENABLE,
    deptCode: undefined,
    type: undefined,
    shortName: undefined,
  }
  deptRef.value?.resetFields()
}
/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
/** 展开/折叠操作 */
const toggleExpandAll = () => {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}
/** 新增按钮操作 */
const handleAdd = async (row: DeptRespVO) => {
  reset()
  try {
    const data =await listDept(queryParams.value)
    departmentOptions.value = handlePhaseTree(data, 'id')
  } catch (e) {}
  if (row !== undefined) {
    form.value.parentId = row.id
  }
  open.value = true
  title.value = t('sys.dept.addDept')
}
/** 修改按钮操作 */
const handleUpdate = async (row: DeptRespVO) => {
  reset()
  try {
    const data = await listDeptExcludeChild(row.companyId, row.id)
    departmentOptions.value = handlePhaseTree(data, 'id')
    const deptData = await getDept(row.id)
    form.value = deptData
    form.value.sort = deptData.sort
    form.value.deptId = deptData.id
    form.value.deptName = deptData.name
    if (form.value.parentId === form.value.companyId) {
      form.value.parentId = null
    }
    open.value = true
    title.value = t('sys.dept.editDept')
  } catch (e) {}
}
/** 提交按钮 */
const submitForm = async () => {
  const valid = await deptRef.value.validate()
  if (!valid) return
    try {
      formLoading.value = true
      if (form.value.deptId !== undefined) {
        await updateDept(form.value)
        message.success(t('common.updateSuccess'))
      } else {
        form.value.companyId = queryParams.value.companyId
        await addDept(form.value)
        message.success(t('common.createSuccess'))
      }
      open.value = false
      await getList()
    } finally {
      formLoading.value = false
    }
}
/** 删除按钮操作 */
const handleDelete = async (row: DeptRespVO) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await delDept(row.id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 */
onMounted(() => {
  getCompanyList()
})

</script>

<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <Pane :size="18" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <div class="head-container">
            <el-input
              v-model="deptName"
              :placeholder="t('sys.user.deptNamePH')"
              clearable
              class="mb-[20px]"
            >
              <template #prefix>
                <Icon class="mr-5px" icon="ep:search" />
              </template>
            </el-input>
          </div>
          <div class="">
            <el-tree
              ref="companyTreeRef"
              :data="companyOptions"
              :props="{ label: 'name', children: 'children' }"
              :expand-on-click-node="false"
              :default-expanded-keys="[]"
              :filter-node-method="filterNode"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <div class="flex justify-between">
                  <div class="me-2.5">
                    <el-tag
                      type="info" :title="node.data.name" :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0',
                      }"
                    >
                      {{ node.data.shortName || node.data.name }}
                    </el-tag>
                  </div>
                  <span :title="node.label" class="whitespace-normal line-clamp-1 break-all"> {{ node.label }}</span>
                </div>
              </template>
            </el-tree>
          </div>
        </el-scrollbar>
      </Pane>
      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <div class="p-2">
            <ContentWrap>
              <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" @submit.prevent class="-mb-15px">
                <el-form-item :label="t('sys.dept.deptName')" prop="name">
                  <el-input
                    v-model="queryParams.name"
                    :placeholder="t('sys.user.deptNamePH')"
                    clearable
                    class="!w-240px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery">
                    <Icon class="mr-5px" icon="ep:search" />
                    {{ t('action.search') }}
                  </el-button>
                  <el-button @click="resetQuery">
                    <Icon class="mr-5px" icon="ep:refresh" />
                    {{ t('action.reset') }}
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-button
                    v-hasPermi="['system:dept:create']"
                    type="primary"
                    plain
                    @click="handleAdd"
                  >
                    <Icon class="mr-5px" icon="ep:plus" />
                    {{ t('action.add') }}
                  </el-button>
                  <el-button
                    type="info"
                    plain
                    @click="toggleExpandAll"
                  >
                    <Icon class="mr-5px" icon="ep:sort" />
                    {{ t('sys.dept.expand') }}
                  </el-button>
                </el-form-item>
              </el-form>
            </ContentWrap>
            <OrgTotalBar :number="deptTotal" :text="t('sys.dept.totalTip')" />
            <ContentWrap>
              <el-table
                v-if="refreshTable"
                v-loading="loading"
                :data="deptList"
                row-key="id"
                :default-expand-all="isExpandAll"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              >
                <el-table-column prop="name" :label="t('sys.dept.deptName')" :width="400" fixed="left" />
                <el-table-column prop="shortName" :label="t('sys.company.abbreviation')" width="260" />
                <el-table-column prop="deptCode" :label="t('sys.company.uniqueCode')" width="260" />
                <el-table-column prop="status" :label="t('sys.company.status')" width="200">
                  <template #default="{ row }">
                    <dict-tag :type="DICT_TYPE.SYSTEM_NORMAL_DISABLE" :value="row.status" />
                  </template>
                </el-table-column>
                <el-table-column :label="t('sys.company.createTime')" align="center" prop="createTime" min-width="200">
                  <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="dataSource" align="center" :label="t('sys.company.dataSource')" width="110" />
<!--                <el-table-column prop="createBy" align="center" :label="t('sys.company.creator')" width="110" />-->
                <el-table-column
                  v-hasPermi="['system:dept:update', 'system:dept:delete']" :label="t('global.action')" align="center" class-name="small-padding fixed-width" width="250"
                  fixed="right"
                >
                  <template #default="scope">
                    <el-button v-hasPermi="['system:dept:update']" link type="primary" @click="handleUpdate(scope.row)">
                      <Icon icon="ep:edit" />
                      {{ t('action.edit') }}
                    </el-button>
                    <el-button v-hasPermi="['system:dept:add']" link type="primary" @click="handleAdd(scope.row)">
                      <Icon icon="ep:plus" />
                      {{ t('action.add') }}
                    </el-button>
                    <el-button v-if="scope.row.parentId !== 0" v-hasPermi="['system:dept:delete']" link type="primary" @click="handleDelete(scope.row)">
                      <Icon icon="ep:delete" />
                      {{ t('action.delete') }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </ContentWrap>
          </div>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <!-- 添加或修改部门对话框 -->
    <Dialog v-model="open" :title="title">
      <el-form ref="deptRef" :model="form" :rules="rules" label-width="170px" label-position="left">
        <el-form-item :label="t('sys.dept.superiorDept')" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="departmentOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            :placeholder="t('sys.dept.superiorDeptPH')"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item :label="t('sys.dept.deptName')" prop="deptName">
          <el-input v-model="form.deptName" :placeholder="t('sys.user.deptNamePH')" />
        </el-form-item>
        <el-form-item :label="t('sys.company.abbreviation')" prop="shortName">
          <el-input v-model="form.shortName" :placeholder="t('sys.company.abbreviationPH')" />
        </el-form-item>
        <el-form-item :label="t('sys.dept.deptSort')" prop="sort">
          <el-input-number v-model="form.sort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item :label="t('sys.company.status')">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_NORMAL_DISABLE)"
              :key="dict.value"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('sys.dept.deptCode')" prop="deptCode">
          <el-input v-model="form.deptCode" :placeholder="t('sys.dept.deptCodePH')" :disabled="form.dataSource === 'MDS' ? true : form.dataSource === 'IMPORT' ? true : false" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="formLoading" @click="submitForm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button @click="cancel">
            {{ t('global.cancel') }}
          </el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>
