<script setup name="Company" lang="ts">
import type { ComponentInternalInstance } from 'vue'
import EmployeeSelect from '@/components/EmployeeMultipleSelect/index.vue'
import { parseTime } from '@/utils/ruoyi'
import {
  addCompany,
  CompanyRespVO, CompanyStatusEnum, CompanyTypeEnum, createUserContractHolder,
  delCompany,
  getCompany, getUserContractHolderPage,
  listCompany,
  listCompanyExcludeChild,
  updateCompany, UserContractHolderRespVO
} from '@/api/system/company'
import { useI18n } from "vue-i18n"
import { handlePhaseTree } from '@/utils/tree'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { UserRespVO } from "@/api/system/user"

const { t } = useI18n()
const message = useMessage() // 消息弹窗

const deptList = ref<any[]>([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref('')
const deptOptions = ref<any[]>([])
const isExpandAll = ref(true)
const refreshTable = ref(true)
const queryRef = ref()
const deptRef = ref()
const formLoading = ref(false)
const selectEmployeeRef = ref()
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {},
  queryParams: {
    name: undefined,
    status: undefined,
    type: undefined,
    serviceCompanyId: undefined,
    deptCode: undefined,
    dataSource: undefined,
  },
  rules: {
    serviceCompanyId: [{ required: true, message: t('sys.company.serviceCompanyIdRule'), trigger: 'blur' }],
    deptName: [{ required: true, message: t('sys.company.deptNameRule'), trigger: 'blur' }],
    shortName: [{ required: true, message: t('sys.company.shortNameRule'), trigger: 'blur' }],
    sort: [{ required: true, message: t('sys.company.orderNumRule'), trigger: 'blur' }],
    deptCode: [{ required: false, message: t('sys.company.deptCodeRule'), trigger: 'blur' }],
    type: [{ required: true, message: t('sys.company.typeRule'), trigger: 'change' }],
  },
})

const { queryParams, form, rules } = toRefs(data)
const deptParamsId = ref()
const queryContractHolder = reactive({
  companyId: undefined,
  pageNo: 1,
  pageSize: -1,
})

const formData = ref({
  companyId: undefined,
  userIds: []
})
/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    deptList.value = await listCompany(queryParams.value)
  } finally {
    loading.value = false
  }
}
/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}
/** 表单重置 */
const reset = () => {
  form.value = {
    deptId: undefined,
    serviceCompanyId: undefined,
    deptName: undefined,
    sort: 0,
    status: CompanyStatusEnum.ENABLE,
    deptCode: undefined,
    type: CompanyTypeEnum.SERVICE,
    shortName: undefined,
  }
  deptRef.value?.resetFields()
}
/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
/** 新增按钮操作 */
const handleAdd = async (row: CompanyRespVO) => {
  reset()
  try {
    const response = await listCompany()
    deptOptions.value = handlePhaseTree(response, 'id')
  } finally {}
  if (row !== undefined)
    form.value.serviceCompanyId = row.id
  deptParamsId.value = row.id
  open.value = true
  title.value = t('sys.company.addCompany')
}
/** 是否是contractor */
const handleRadioChange = async (contractorValue: number) => {
  if (contractorValue === 1 && deptParamsId.value) {
    try {
      const data = await listCompanyExcludeChild(deptParamsId.value as number)
      deptOptions.value = handlePhaseTree(data, 'id')
    } catch (e) {}
  }
}

// 设置公司管理员
const handleAssign = async (id: number) => {
  formData.value.companyId = id
  formData.value.userIds = []
  queryContractHolder.companyId = id
  try {
    const res = await getUserContractHolderPage(queryContractHolder)
    const ids = res.list?.map((userContractHolder: UserContractHolderRespVO) => userContractHolder.userId)
    selectEmployeeRef.value.open(ids)
  } catch (e) {}
}
const employeeConfirm = async (userData: UserRespVO) => {
  formData.value.userIds = userData?.map((user: UserRespVO) => user.id)
  try {
    await createUserContractHolder(formData.value)
    message.success('Created successfully')
    getList()
  } catch (e) {}
}
/** 修改按钮操作 */
const handleUpdate = async (item: CompanyRespVO) => {
  deptParamsId.value = item.id
  reset()
  open.value = true
  try {
    if (item.type === CompanyTypeEnum.SUPPLIER) {
      const data = await listCompanyExcludeChild(item.id as number)
      deptOptions.value = handlePhaseTree(data, 'id')
    }
    const data = await getCompany(item.id as number)
    form.value = data
    form.value.deptId = data.id
    form.value.deptName = data.name
    // deptOptions.value = handlePhaseTree(data, 'id')
    delete form.value.parentId
    title.value = t('sys.company.editCompany')
  } catch (e) {}
}
/** 提交按钮 */
const submitForm = async () => {
  const valid = await deptRef.value.validate()
  if (!valid) return
  try {
    formLoading.value = true
    if (form.value.deptId !== undefined) {
      if (form.value.type === CompanyTypeEnum.SERVICE) {
        delete form.value.serviceCompanyId
        delete form.value.parentId
      }
      await updateCompany(form.value)
      message.success(t('common.updateSuccess'))
    } else {
      if (form.value.type === CompanyTypeEnum.SERVICE) {
        delete form.value.serviceCompanyId
        delete form.value.parentId
      }
      await addCompany(form.value)
      message.success(t('common.createSuccess'))
    }
    open.value = false
    await getList()
  } finally {
    formLoading.value = false
  }
}
/** 删除按钮操作 */
const handleDelete = async (item: CompanyRespVO) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await delCompany(item.id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
/** 初始化 */
onMounted(() => {
  getList()
})

</script>

<template>
  <div class="app-container">
    <ContentWrap>
      <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" label-width="140" label-position="left" @submit.prevent>
        <el-form-item :label="t('sys.company.companyName')" prop="name">
          <el-input
            v-model="queryParams.name"
            :placeholder="t('sys.company.companyNamePH')"
            clearable
            class="!w-240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="t('sys.company.type')" prop="type">
          <el-select v-model="queryParams.type" :placeholder="t('sys.company.typePH')" clearable class="!w-240px">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_COMPANY_CONTRACTOR)"
              :key="dict.value"
              :value="dict.value" :label="dict.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('sys.company.uniqueCode')" prop="deptCode">
          <el-input
            v-model="queryParams.deptCode"
            :placeholder="t('sys.company.uniqueCodePH')"
            clearable
            class="!w-240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="t('sys.company.dataSource')" prop="dataSource">
          <el-input
            v-model="queryParams.dataSource"
            :placeholder="t('sys.company.dataSourcePH')"
            clearable
            class="!w-240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="t('sys.company.superiorCompany')" prop="serviceCompanyId">
          <el-tree-select
            v-model="queryParams.serviceCompanyId"
            class="!w-240px"
            :data="deptList"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            :placeholder="t('sys.company.superiorCompanyPH')"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button
            v-hasPermi="['system:company:create']"
            type="primary"
            plain
            @click="handleAdd"
          >
            <Icon class="mr-5px" icon="ep:plus" />
            {{ t('action.add') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="deptList"
        row-key="id"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" :label="t('sys.company.companyName')" width="260" fixed="left" />
        <el-table-column prop="shortName" :label="t('sys.company.abbreviation')" width="260" />
        <el-table-column prop="type" :label="t('sys.company.type')" width="260">
          <template #default="scope">
            <span>{{ scope.row.type === CompanyTypeEnum.SERVICE ? t('sys.company.no') : t('sys.company.yes') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="deptCode" :label="t('sys.company.uniqueCode')" width="200" />
        <el-table-column prop="serviceCompanyId" :label="t('sys.company.superiorCompany')" width="240">
          <template #default="scope ">
            <div class="custom-wrapper-disabled">
              <el-tree-select
                v-if="scope.row.serviceCompanyId"
                v-model="scope.row.serviceCompanyId"
                :data="deptList"
                :props="{ value: 'id', label: 'name', children: 'children' }"
                value-key="id"
                check-strictly
                clearable
                :style="{ '--el-border-color-light': '#ffffff' }"
                disabled
                suffix-icon="hidden-icon"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sort" :label="t('sys.company.sort')" width="200" />
        <el-table-column prop="status" :label="t('sys.company.status')" width="200">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.SYSTEM_NORMAL_DISABLE" :value="row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="t('sys.company.createTime')" align="center" prop="createTime" width="200">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="dataSource" align="center" :label="t('sys.company.dataSource')" width="110" />
        <el-table-column prop="createBy" align="center" :label="t('sys.company.creator')" width="110" />
        <el-table-column
          v-hasPermi="['system:company:update', 'system:company:delete','system:company:assign']" :label="t('global.action')"
          align="center" class-name="small-padding fixed-width" width="250"
          fixed="right"
        >
          <template #default="scope">
            <el-button v-hasPermi="['system:company:assign']" link type="primary" @click="handleAssign(scope.row.id)">
              <Icon icon="ep:user" />
              {{ t('action.assignCourse') }}
            </el-button>
            <el-button v-hasPermi="['system:company:update']" link type="primary" @click="handleUpdate(scope.row)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <!-- <el-button v-hasPermi="['system:company:add']" link type="primary" icon="Plus" @click="handleAdd(scope.row)">
              Add
            </el-button> -->
            <el-button v-hasPermi="['system:company:delete']" link type="primary" @click="handleDelete(scope.row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </ContentWrap>

    <!-- 添加或修改部门对话框 -->
    <Dialog v-model="open" :title="title">
      <el-form ref="deptRef" :model="form" :rules="rules" label-width="170px" label-position="left">
        <el-form-item :label="t('sys.company.type')" prop="type">
          <el-radio-group v-model="form.type" @change="handleRadioChange">
            <el-radio :value="1" size="large">
              {{ t('sys.company.yes') }}
            </el-radio>
            <el-radio :value="0" size="large">
              {{ t('sys.company.no') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 是contractor时，显示superior company且必填 -->
        <el-form-item v-if="form.type === CompanyTypeEnum.SUPPLIER" :label="t('sys.company.superiorCompany')" :prop="form.type ? 'serviceCompanyId' : ''">
          <el-tree-select
            v-model="form.serviceCompanyId"
            :data="deptOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="deptId"
            :placeholder="t('sys.company.superiorCompanyPH')"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item :label="t('sys.company.companyName')" prop="deptName">
          <el-input v-model="form.deptName" :placeholder="t('sys.company.companyNamePH')" />
        </el-form-item>
        <el-form-item :label="t('sys.company.abbreviation')" prop="shortName">
          <el-input v-model="form.shortName" :placeholder="t('sys.company.abbreviationPH')" />
        </el-form-item>
        <el-form-item :label="t('sys.company.orderNum')" prop="sort">
          <el-input-number v-model="form.sort" controls-position="right" :min="0" />
        </el-form-item>

        <el-form-item :label="t('sys.company.status')">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_NORMAL_DISABLE)"
              :key="dict.value"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('sys.company.companyCode')" prop="deptCode">
          <el-input v-model="form.deptCode" :placeholder="t('sys.company.companyCodePH')" :disabled="form.dataSource === 'MDS' ? true : form.dataSource === 'IMPORT' ? true : false" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="formLoading" @click="submitForm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button @click="cancel">
            {{ t('global.cancel') }}
          </el-button>
        </div>
      </template>
    </Dialog>

    <EmployeeSelect
      ref="selectEmployeeRef"
      @confirm="employeeConfirm"
    />
  </div>
</template>

<style scoped lang="scss">
/** table中superior company的样式 */
:deep .custom-wrapper-disabled .el-select__wrapper.is-disabled{
  border: none;
  box-shadow: none;
  background-color: #e5f1ec;
  color: #007943;
  @apply cursor-text
}
:deep .custom-wrapper-disabled .el-select__wrapper.is-disabled .el-select__selected-item {
    color: #007943;
}
:deep .custom-wrapper-disabled .el-select__wrapper.is-disabled:hover{
  box-shadow: #cccccc;
  background-color: #e5f1ec;// #f5f7fa;
}
</style>
