<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="600px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="formLoading"
      class="category-form"
    >
      <el-form-item :label="t('survey.parentCategory')" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="categoryTree"
          :render-after-expand="false"
          :placeholder="t('survey.pleaseSelect') + t('survey.parentCategory')"
          check-strictly
          :default-expand-all="true"
          node-key="id"
          :props="{ label: 'name', children: 'children' }"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item :label="t('survey.categoryName')" prop="name">
        <el-input
          v-model="formData.name"
          :placeholder="t('survey.pleaseEnter') + t('survey.categoryName')"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      <el-form-item :label="t('survey.sortOrder')" prop="sort">
        <el-input-number
          v-model="formData.sort"
          :min="0"
          :max="999"
          controls-position="right"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item :label="t('survey.status')" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :value="0">{{ t('survey.enabled') }}</el-radio>
          <el-radio :value="1">{{ t('survey.disabled') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="t('survey.description')" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :placeholder="t('survey.pleaseEnter') + t('survey.description')"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('survey.confirm') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('survey.cancel') }}</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { CategoryApi } from '@/api/system/survey'
import type { SurveyCategory } from '@/api/system/survey/types'

defineOptions({ name: 'CategoryForm' })

const { t } = useI18n()
const message = useMessage()

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')
const formRef = ref()

// Category tree data
const categoryTree = ref<SurveyCategory[]>([])

// Form data
const formData = reactive({
  id: undefined as number | undefined,
  parentId: 0,
  name: '',
  sort: 0,
  status: 0,
  description: ''
})

// Form validation rules
const formRules = reactive({
  name: [
    { required: true, message: t('survey.categoryNameRequired'), trigger: 'blur' },
    { min: 1, max: 50, message: t('survey.categoryName') + ' length should be 1-50 characters', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: t('survey.pleaseEnter') + t('survey.sortOrder'), trigger: 'blur' }
  ],
  status: [
    { required: true, message: t('survey.pleaseSelect') + t('survey.status'), trigger: 'change' }
  ]
})

// Emits
const emit = defineEmits(['success'])

/** Open dialog */
const open = async (type: string, id?: number, parentId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? t('survey.addCategory') : t('survey.editCategory')
  formType.value = type
  resetForm()

  // Load category tree for parent selection
  await loadCategoryTree()

  // Set parent ID if provided
  if (parentId) {
    formData.parentId = parentId
  }

  // Load data for edit
  if (id) {
    formLoading.value = true
    try {
      const data = await CategoryApi.get(id)
      Object.assign(formData, data)
    } finally {
      formLoading.value = false
    }
  }
}

/** Load category tree */
const loadCategoryTree = async () => {
  try {
    const data = await CategoryApi.getTree()
    categoryTree.value = [
      { id: 0, name: t('survey.rootCategory'), children: data }
    ]
  } catch (error) {
    console.error('Failed to load category tree:', error)
  }
}

/** Submit form */
const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    formLoading.value = true
    try {
      const data = { ...formData }
      if (formType.value === 'create') {
        await CategoryApi.create(data)
        message.success(t('survey.createSuccess'))
      } else {
        await CategoryApi.update(data)
        message.success(t('survey.updateSuccess'))
      }
      dialogVisible.value = false
      emit('success')
    } finally {
      formLoading.value = false
    }
  })
}

/** Reset form */
const resetForm = () => {
  formData.id = undefined
  formData.parentId = 0
  formData.name = ''
  formData.sort = 0
  formData.status = 0
  formData.description = ''
  formRef.value?.resetFields()
}

defineExpose({ open })
</script>

<style scoped>
/* 防止表单标签换行 */
.category-form :deep(.el-form-item__label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
