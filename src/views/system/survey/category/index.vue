<template>
  <ContentWrap>
    <!-- Search Form -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item :label="t('survey.categoryName')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="t('survey.pleaseEnter') + t('survey.categoryName')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('survey.status')" prop="status">
        <el-select v-model="queryParams.status" class="!w-240px" clearable :placeholder="t('survey.pleaseSelect') + t('survey.status')">
          <el-option :label="t('survey.enabled')" :value="0" />
          <el-option :label="t('survey.disabled')" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> {{ t('survey.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> {{ t('survey.reset') }}
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:survey:category:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> {{ t('survey.addCategory') }}
        </el-button>
        <el-button
          type="info"
          plain
          @click="toggleExpandAll"
        >
          <Icon icon="ep:sort" class="mr-5px" /> {{ isExpandAll ? t('survey.collapseAll') : t('survey.expandAll') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- Category Tree Table -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="categoryList"
      row-key="id"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :stripe="true"
      :show-overflow-tooltip="true"
    >
      <el-table-column :label="t('survey.categoryName')" prop="name" min-width="200" />
      <el-table-column :label="t('survey.sortOrder')" align="center" prop="sort" width="120" />
      <el-table-column :label="t('survey.status')" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
            {{ scope.row.status === 0 ? t('survey.enabled') : t('survey.disabled') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('survey.description')" prop="description" min-width="200" show-overflow-tooltip />
      <el-table-column
        :label="t('survey.createTime')"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column :label="t('survey.actions')" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:survey:category:update']"
          >
            {{ t('survey.edit') }}
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('create', undefined, scope.row.id)"
            v-hasPermi="['system:survey:category:create']"
          >
            {{ t('survey.add') }} {{ t('survey.category') }}
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['system:survey:category:delete']"
          >
            {{ t('survey.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- Form Dialog -->
  <CategoryForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { CategoryApi } from '@/api/system/survey'
import CategoryForm from './CategoryForm.vue'
import type { SurveyCategory } from '@/api/system/survey/types'

defineOptions({ name: 'SurveyCategoryManagement' })

const message = useMessage()
const { t } = useI18n()

// Reactive data
const loading = ref(true)
const categoryList = ref<SurveyCategory[]>([])
const isExpandAll = ref(false)

// Query parameters
const queryParams = reactive({
  name: undefined as string | undefined,
  status: undefined as number | undefined
})

// Form reference
const queryFormRef = ref()
const formRef = ref()

/** Get category list */
const getList = async () => {
  loading.value = true
  try {
    const data = await CategoryApi.getTree(queryParams)
    categoryList.value = data
  } finally {
    loading.value = false
  }
}

/** Search button operation */
const handleQuery = () => {
  getList()
}

/** Reset button operation */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** Toggle expand all */
const toggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value
}

/** Add/Edit operation */
const openForm = (type: string, id?: number, parentId?: number) => {
  formRef.value.open(type, id, parentId)
}

/** Delete operation */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await CategoryApi.delete(id)
    message.success(t('survey.deleteSuccess'))
    await getList()
  } catch {}
}

/** Initialize */
onMounted(() => {
  getList()
})
</script>

<style scoped>
:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>
