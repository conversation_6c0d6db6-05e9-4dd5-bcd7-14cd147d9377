<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('survey.templateName')" prop="name">
            <el-input
              v-model="formData.name"
              :placeholder="t('survey.pleaseEnter') + t('survey.templateName')"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('survey.templateCode')" prop="templateCode">
            <el-input
              v-model="formData.templateCode"
              :placeholder="t('survey.autoGenerated')"
              maxlength="50"
              :disabled="formType === 'update'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('survey.category')" prop="categoryId">
            <el-tree-select
              v-model="formData.categoryId"
              :data="categoryTree"
              :render-after-expand="false"
              :placeholder="t('survey.pleaseSelect') + t('survey.category')"
              check-strictly
              node-key="id"
              :props="{ label: 'name', children: 'children' }"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('survey.status')" prop="status">
            <el-select v-model="formData.status" :placeholder="t('survey.pleaseSelect') + t('survey.status')" style="width: 100%">
              <el-option :label="t('survey.draft')" :value="0" />
              <el-option :label="t('survey.published')" :value="1" />
              <el-option :label="t('survey.archived')" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item :label="t('survey.description')" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :placeholder="t('survey.pleaseEnter') + t('survey.description')"
          :rows="4"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">
        {{ formType === 'create' ? t('common.create') : t('common.update') }}
      </el-button>
      <el-button @click="dialogVisible = false">{{ t('survey.cancel') }}</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { CategoryApi, TemplateApi } from '@/api/system/survey'
import type { SurveyCategory, SurveyTemplate } from '@/api/system/survey/types'

defineOptions({ name: 'TemplateForm' })

const { t } = useI18n()
const message = useMessage()

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')
const formRef = ref()

// Category tree data
const categoryTree = ref<SurveyCategory[]>([])

// Form data
const formData = reactive({
  id: undefined as number | undefined,
  name: '',
  templateCode: '',
  categoryId: undefined as number | undefined,
  status: 0,
  description: ''
})

// Form validation rules
const formRules = reactive({
  name: [
    { required: true, message: t('survey.templateNameRequired'), trigger: 'blur' },
    { min: 1, max: 100, message: t('survey.templateName') + ' length should be 1-100 characters', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: t('survey.pleaseSelect') + t('survey.category'), trigger: 'change' }
  ],
  status: [
    { required: true, message: t('survey.pleaseSelect') + t('survey.status'), trigger: 'change' }
  ]
})

// Emits
const emit = defineEmits(['success'])

/** Open dialog */
const open = async (type: string, id?: number, categoryId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? t('survey.addTemplate') : t('survey.editTemplate')
  formType.value = type
  resetForm()
  
  // Load category tree
  await loadCategoryTree()
  
  // Set category ID if provided
  if (categoryId) {
    formData.categoryId = categoryId
  }
  
  // Load data for edit
  if (id) {
    formLoading.value = true
    try {
      const data = await TemplateApi.get(id)
      Object.assign(formData, data)
    } finally {
      formLoading.value = false
    }
  }
}

/** Load category tree */
const loadCategoryTree = async () => {
  try {
    const data = await CategoryApi.getTree()
    categoryTree.value = data
  } catch (error) {
    console.error('Failed to load category tree:', error)
  }
}

/** Submit form */
const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return
    
    formLoading.value = true
    try {
      const data = { ...formData }
      if (formType.value === 'create') {
        await TemplateApi.create(data)
        message.success(t('survey.createSuccess'))
      } else {
        await TemplateApi.update(data)
        message.success(t('survey.updateSuccess'))
      }
      dialogVisible.value = false
      emit('success')
    } finally {
      formLoading.value = false
    }
  })
}

/** Reset form */
const resetForm = () => {
  formData.id = undefined
  formData.name = ''
  formData.templateCode = ''
  formData.categoryId = undefined
  formData.status = 0
  formData.description = ''
  formRef.value?.resetFields()
}

defineExpose({ open })
</script>
