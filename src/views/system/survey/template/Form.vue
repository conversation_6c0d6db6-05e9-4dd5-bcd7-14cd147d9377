<template>
  <div class="template-editor" v-loading="loading" :element-loading-text="t('survey.loadingTemplateData')">
    <!-- Header -->
    <div class="editor-header">
      <div class="flex items-center">
        <el-button @click="handleBack" circle class="mr-4">
          <Icon icon="ep:arrow-left" />
        </el-button>
        <div>
          <h1 class="text-xl font-bold">
            {{ isEdit ? t('survey.editTemplate') : t('survey.addTemplate') }}
          </h1>
<!--          <p class="text-sm text-gray-600 mt-1">-->
<!--            {{ isEdit ? 'Edit survey template' : 'Create a new survey template' }}-->
<!--          </p>-->
        </div>
      </div>
      <div class="flex gap-3">
        <el-button @click="handleSave" type="primary" :loading="saving">
          <Icon icon="ep:check" class="mr-2" />
          {{ t('survey.save') }}
        </el-button>
        <el-button @click="handlePreview">
          <Icon icon="ep:view" class="mr-2" />
          {{ t('survey.preview') }}
        </el-button>
        <el-button @click="handleBack">
          {{ t('survey.cancel') }}
        </el-button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="editor-content">
      <!-- Left Panel: Question Types -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>{{ t('survey.questionType') }}</h3>
          <p class="text-sm text-gray-600">{{ t('survey.dragOrClickToAdd') }}</p>
        </div>

        <div class="question-types">
          <div
            v-for="questionType in questionTypes"
            :key="questionType.type"
            class="question-type-item"
            :draggable="true"
            @dragstart="handleDragStart($event, questionType)"
            @dragend="handleDragEnd"
            @click="handleAddQuestionType(questionType)"
          >
            <div class="type-icon">
              <Icon :icon="questionType.icon" />
            </div>
            <div class="type-info">
              <div class="type-name">{{ questionType.name }}</div>
              <div class="type-desc">{{ questionType.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel: Template Editor -->
      <div class="right-panel" :class="{ 'expanded-panel': isExpanded }">
        <!-- Basic Information -->
        <div class="basic-info-section" :class="{ 'collapsed': isExpanded }">
          <h3 class="section-title">{{ t('survey.basicInfo') }}</h3>
          <el-form
            ref="formRef"
            :model="templateData"
            :rules="formRules"
            label-width="140px"
            class="basic-form"
          >
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="t('survey.templateName')" prop="name">
                  <el-input
                    v-model="templateData.name"
                    :placeholder="t('survey.pleaseEnter') + t('survey.templateName')"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('survey.templateCode')" prop="templateCode">
                  <el-input
                    v-model="templateData.templateCode"
                    :placeholder="t('survey.autoGenerated')"
                    maxlength="50"
                    :disabled="isEdit as boolean"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="t('survey.category')" prop="categoryId">
                  <el-tree-select
                    v-model="templateData.categoryId"
                    :data="categoryTree"
                    :render-after-expand="false"
                    :placeholder="t('survey.pleaseSelect') + t('survey.category')"
                    check-strictly
                    node-key="id"
                    :props="{ label: 'name', children: 'children' }"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('survey.status')" prop="status">
                  <el-select v-model="templateData.status" :placeholder="t('survey.pleaseSelect') + t('survey.status')" style="width: 100%">
                    <el-option :label="t('survey.enabled')" :value="0" />
                    <el-option :label="t('survey.disabled')" :value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item :label="t('survey.description')" prop="description">
              <el-input
                v-model="templateData.description"
                type="textarea"
                :placeholder="t('survey.pleaseEnter') + t('survey.description')"
                :rows="3"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- Questions Editor -->
        <div class="questions-section">
          <div class="section-header">
            <h3 class="section-title">
              {{ t('survey.questionEditor') }} ({{ templateData.questions?.length || 0 }})
            </h3>
            <el-button @click="toggleExpanded" link  size="small">
              <Icon :icon="isExpanded ? 'radix-icons:exit-full-screen' : 'ep:full-screen'" />
            </el-button>

          </div>

          <!-- Drop Zone -->
          <div class="drop-zone">
            <!-- Questions List -->
            <div v-if="templateData.questions && templateData.questions.length > 0" class="questions-list">
              <!-- Insert Zone at Top -->
              <div
                class="insert-zone"
                :class="{ 'drag-over': dragOverIndex === -1 }"
                @drop="handleDropAtPosition($event, 0)"
                @dragover.prevent="handleDragOverPosition(-1)"
                @dragleave="handleDragLeave"
              >
                <div class="insert-indicator">
                  <Icon icon="ep:plus" />
                  <span>{{ t('survey.insertHere') }}</span>
                </div>
              </div>

              <draggable
                v-model="templateData.questions"
                group="questions"
                handle=".drag-handle"
                @start="drag = true"
                @end="drag = false"
                item-key="id"
              >
                <template #item="{ element: question, index }">
                  <div class="question-wrapper">
                    <div class="question-item">
                      <QuestionEditor
                        :question="question"
                        :index="index"
                        @update="handleUpdateQuestion"
                        @delete="handleDeleteQuestion"
                        @duplicate="handleDuplicateQuestion"
                      />
                    </div>

                    <!-- Insert Zone Between Questions -->
                    <div
                      class="insert-zone"
                      :class="{ 'drag-over': dragOverIndex === index }"
                      @drop="handleDropAtPosition($event, index + 1)"
                      @dragover.prevent="handleDragOverPosition(index)"
                      @dragleave="handleDragLeave"
                    >
                      <div class="insert-indicator">
                        <Icon icon="ep:plus" />
                        <span>{{ t('survey.insertHere') }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>

            <!-- Empty State -->
            <div v-else class="empty-state"
                 @drop="handleDropAtPosition($event, 0)"
                 @dragover.prevent="isDragOver = true"
                 @dragleave="isDragOver = false"
                 :class="{ 'drag-over': isDragOver }">
              <Icon icon="ep:plus" class="empty-icon" />
              <p class="empty-text">{{ t('survey.dragQuestionHere') }}</p>
              <p class="empty-desc">{{ t('survey.dragQuestionDesc') }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview Dialog -->
    <TemplatePreviewDialog ref="previewRef" />
  </div>
</template>

<script setup lang="ts">
import { CategoryApi, TemplateApi, SurveyUtils } from '@/api/system/survey'
import type { SurveyCategory, SurveyTemplate, SurveyQuestion } from '@/api/system/survey/types'
import draggable from 'vuedraggable'
import QuestionEditor from './components/QuestionEditor.vue'
import TemplatePreviewDialog from './TemplatePreviewDialog.vue'
import { useTagsViewStore } from '@/store/modules/tagsView'


defineOptions({ name: 'SurveyTemplateForm' })

const { t } = useI18n()
const message = useMessage()
const {back, push, currentRoute} = useRouter()
const route = useRoute()

const { delVisitedView } = useTagsViewStore()
// Reactive data
const loading = ref(false)
const saving = ref(false)
const formRef = ref()
const previewRef = ref()
const categoryTree = ref<SurveyCategory[]>([])
const isDragOver = ref(false)
const drag = ref(false)
const dragOverIndex = ref(-2) // -2: 无拖拽, -1: 顶部, 0+: 对应题目后面
const isExpanded = ref(false) // 展开状态

// Template data
const templateData = reactive<Partial<SurveyTemplate>>({
  name: '',
  templateCode: '',
  categoryId: undefined,
  status: 0,
  description: '',
  questions: []
})
const id = ref<number | undefined>(route.query.id ? Number(route.query.id) : undefined)
const type = ref<string>(String(route.query.type))
// Question types configuration
const questionTypes = ref([
  {
    type: 1,
    name: t('survey.singleChoice'),
    description: t('survey.singleChoiceDesc'),
    icon: 'ep:circle-check',
    defaultConfig: {
      options: [
        { text: '选项1', value: 'option1', score: 1 },
        { text: '选项2', value: 'option2', score: 1 }
      ]
    }
  },
  {
    type: 2,
    name: t('survey.multipleChoice'),
    description: t('survey.multipleChoiceDesc'),
    icon: 'ep:select',
    defaultConfig: {
      options: [
        { text: '选项1', value: 'option1', score: 1 },
        { text: '选项2', value: 'option2', score: 1 }
      ],
      minSelections: 1,
      maxSelections: 3
    }
  },
  {
    type: 3,
    name: t('survey.trueFalse'),
    description: t('survey.trueFalseDesc'),
    icon: 'ep:check',
    defaultConfig: {
      trueText: '正确',
      falseText: '错误',
      trueScore: 1,
      falseScore: 0
    }
  },
  {
    type: 4,
    name: t('survey.rating'),
    description: t('survey.ratingDesc'),
    icon: 'ep:star',
    defaultConfig: {
      minScore: 1,
      maxScore: 5,
      step: 1,
      showLabels: true,
      minLabel: '很差',
      maxLabel: '很好',
      scorePerPoint: 1
    }
  },
  {
    type: 5,
    name: t('survey.fileUpload'),
    description: t('survey.fileUploadDesc'),
    icon: 'ep:upload',
    defaultConfig: {
      allowedTypes: ['pdf', 'doc', 'docx', 'jpg', 'png'],
      maxFileSize: 10485760,
      maxFiles: 3,
      score: 1
    }
  },
  {
    type: 6,
    name: t('survey.text'),
    description: t('survey.textDesc'),
    icon: 'ep:edit',
    defaultConfig: {
      inputType: 'textarea',
      minLength: 0,
      maxLength: 500,
      placeholder: '请输入您的答案',
      rows: 4,
      score: 1
    }
  }
])

// Form validation rules
const formRules = reactive({
  name: [
    { required: true, message: t('survey.templateNameRequired'), trigger: 'blur' },
    { min: 1, max: 100, message: t('survey.templateName') + ' length should be 1-100 characters', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: t('survey.pleaseSelect') + t('survey.category'), trigger: 'change' }
  ],
  status: [
    { required: true, message: t('survey.pleaseSelect') + t('survey.status'), trigger: 'change' }
  ]
})

// Computed
const isEdit = computed(() => type.value === 'update')

/** Load category tree */
const loadCategoryTree = async () => {
  try {
    const data = await CategoryApi.getTree()
    categoryTree.value = data
  } catch (error) {
    console.error('Failed to load category tree:', error)
  }
}

/** Load template data */
const loadTemplateData = async () => {
  if (!isEdit.value) {
    // 新增模板时，从URL参数获取分类ID
    const categoryId = route.query.categoryId
    if (categoryId) {
      templateData.categoryId = Number(categoryId)
    }
    return
  }

  loading.value = true
  try {
    const data = await TemplateApi.get(Number(id.value))
    Object.assign(templateData, data)
  } catch (error) {
    console.error('Failed to load template:', error)
    message.error('Failed to load template data')
  } finally {
    loading.value = false
  }
}

/** Handle back */
const handleBack = () => {
  delVisitedView(unref(currentRoute))
  push({
    path: `/survey/template-list`
  })
}

/** Handle save */
const handleSave = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    saving.value = true
    try {
      if (isEdit.value) {
        await TemplateApi.update(templateData as SurveyTemplate)
        message.success(t('survey.updateSuccess'))
      } else {
        id.value = await TemplateApi.create(templateData as SurveyTemplate)
        // type.value = 'edit'
        templateData.id = id.value
        message.success(t('survey.createSuccess'))
        // 重新刷新页面

        delVisitedView(unref(currentRoute))
        await push({
          path: `/survey/template`,
          query: {
            id: id.value,
            type: 'update'
          }
        })
      }
    } finally {
      saving.value = false
    }
  })
}

/** Handle preview */
const handlePreview = () => {
  // 打开预览弹窗，传入当前模板数据
  const previewData = {
    ...templateData,
    questions: templateData.questions || []
  }

  previewRef.value.openWithData(previewData)
}

/** Handle drag start */
const handleDragStart = (event: DragEvent, questionType: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(questionType))
  }
  // 显示所有插入区域
  document.body.classList.add('dragging-question-type')
}

/** Handle drag over position */
const handleDragOverPosition = (index: number) => {
  dragOverIndex.value = index
}

/** Handle drag leave */
const handleDragLeave = () => {
  dragOverIndex.value = -2
}

/** Handle drag end (cleanup) */
const handleDragEnd = () => {
  document.body.classList.remove('dragging-question-type')
  dragOverIndex.value = -2
  isDragOver.value = false
}

/** Handle drop at specific position */
const handleDropAtPosition = (event: DragEvent, position: number) => {
  event.preventDefault()
  isDragOver.value = false
  dragOverIndex.value = -2

  // 移除拖拽样式
  document.body.classList.remove('dragging-question-type')

  if (event.dataTransfer) {
    try {
      const questionType = JSON.parse(event.dataTransfer.getData('application/json'))
      handleAddQuestionTypeAtPosition(questionType, position)
    } catch (error) {
      console.error('Failed to parse dropped data:', error)
    }
  }
}

/** Handle add question type */
const handleAddQuestionType = (questionType: any) => {
  handleAddQuestionTypeAtPosition(questionType, templateData.questions?.length || 0)
}

/** Handle add question type at specific position */
const handleAddQuestionTypeAtPosition = (questionType: any, position: number) => {
  const newQuestion: Partial<SurveyQuestion> = {
    id: Date.now(), // 临时ID
    templateId: templateData.id,
    questionType: questionType.type,
    title: `${t('survey.new')}${questionType.name}`,
    description: '',
    required: false,
    sort: position + 1,
    config: questionType.defaultConfig
  }

  if (!templateData.questions) {
    templateData.questions = []
  }

  // 在指定位置插入题目
  templateData.questions.splice(position, 0, newQuestion as SurveyQuestion)

  // 重新排序所有题目
  templateData.questions.forEach((question, index) => {
    question.sort = index + 1
  })
}

/** Handle update question */
const handleUpdateQuestion = (index: number, updatedQuestion: SurveyQuestion) => {
  if (templateData.questions && templateData.questions[index]) {
    templateData.questions[index] = updatedQuestion
  }
}

/** Handle delete question */
const handleDeleteQuestion = (index: number) => {
  if (templateData.questions) {
    templateData.questions.splice(index, 1)
    // 重新排序
    templateData.questions.forEach((question, idx) => {
      question.sort = idx + 1
    })
  }
}

/** Handle duplicate question */
const handleDuplicateQuestion = (index: number) => {
  if (templateData.questions && templateData.questions[index]) {
    const originalQuestion = templateData.questions[index]
    const duplicatedQuestion: SurveyQuestion = {
      ...originalQuestion,
      id: Date.now(), // 新的临时ID
      title: originalQuestion.title + ' (副本)',
      sort: (templateData.questions?.length || 0) + 1
    }
    templateData.questions.push(duplicatedQuestion)
  }
}

/** Toggle expanded mode */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** Handle ESC key to exit expanded mode */
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isExpanded.value) {
    isExpanded.value = false
  }
}

/** Initialize */
onMounted(async () => {
  await loadCategoryTree()
  await loadTemplateData()

  // Add ESC key listener
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // Remove ESC key listener
  document.removeEventListener('keydown', handleKeydown)
})


</script>

<style scoped>
.template-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Left Panel */
.left-panel {
  width: 260px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid #f0f2f5;
}

.panel-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.question-types {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.question-type-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.question-type-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.type-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #409eff;
  color: white;
  border-radius: 8px;
  margin-right: 12px;
  font-size: 18px;
}

.type-info {
  flex: 1;
}

.type-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.type-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

/* Right Panel */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.basic-info-section {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
  transition: all 0.3s ease;
  overflow: hidden;
}

/* 展开模式样式 */
.basic-info-section.collapsed {
  max-height: 0;
  padding: 0 24px;
  border-bottom: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.right-panel.expanded-panel .questions-section {
  flex: 1;
  height: calc(100vh - 140px);
}

.right-panel.expanded-panel .drop-zone {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.basic-form {
  max-width: none;
}

.questions-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 0;
  background: white;
}

/* 展开按钮样式 */
.section-header .el-button {
  color: #909399;
  font-size: 18px;
  padding: 6px;
  border: none;
  background: none;
  transition: all 0.3s ease;
  border-radius: 6px;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-header .el-button:hover {
  color: #409eff;
  background: #ecf5ff;
  transform: scale(1.1);
}

.section-header .el-button.expanded-btn {
  color: #e6a23c;
  background: #fdf6ec;
}

.section-header .el-button.expanded-btn:hover {
  color: #cf9236;
  background: #f5dab1;
  transform: scale(1.1);
}

.drop-zone {
  flex: 1;
  margin: 16px 24px 24px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
  overflow-y: auto;
}

.drop-zone.drag-over {
  border-color: #409eff;
  background: #ecf5ff;
}

.questions-list {
  padding: 16px;
}

.question-wrapper {
  position: relative;
}

.question-item {
  margin-bottom: 8px;
}

.insert-zone {
  height: 40px;
  margin: 8px 0;
  border: 2px dashed transparent;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  position: relative;
}

.insert-zone.drag-over {
  border-color: #409eff;
  background: linear-gradient(135deg, #ecf5ff 0%, #e1f3ff 100%);
  opacity: 1;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.insert-zone:hover {
  opacity: 0.7;
  border-color: #c0c4cc;
  background: #f5f7fa;
}

/* 拖拽时显示所有插入区域 */
:global(.dragging-question-type) .insert-zone {
  opacity: 0.6 !important;
  border-color: #e4e7ed !important;
  background: #fafbfc !important;
}

.insert-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.insert-zone.drag-over .insert-indicator {
  color: #409eff;
  transform: scale(1.1);
}

.insert-indicator .icon {
  font-size: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-text {
  font-size: 16px;
  margin: 0 0 8px 0;
  color: #606266;
}

.empty-desc {
  font-size: 14px;
  margin: 0;
  color: #909399;
}

/* Form styles */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* Responsive */
@media (max-width: 1200px) {
  .left-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    height: 200px;
  }

  .question-types {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 16px;
  }

  .question-type-item {
    min-width: 200px;
    margin-bottom: 0;
  }
}

/* Prevent form label text wrapping */
.basic-form :deep(.el-form-item__label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
