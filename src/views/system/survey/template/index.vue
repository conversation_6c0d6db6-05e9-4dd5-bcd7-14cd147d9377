<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <!-- Left Panel: Category Tree -->
      <Pane :size="26" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <!-- Category Search and Actions -->
          <div class="head-container">
            <div class="category-toolbar">
              <!-- Search Input -->
              <el-input
                v-model="categorySearchName"
                :placeholder="t('survey.searchCategoryName')"
                clearable
                size="small"
                class="search-input"
                @input="handleCategorySearch"
              >
                <template #prefix>
                  <Icon icon="ep:search" />
                </template>
              </el-input>

              <!-- Action Buttons -->
              <div class="action-buttons">
                <el-tooltip :content="t('survey.refreshCategories')" placement="top">
                  <el-button
                    type="primary"
                    plain
                    size="small"
                    @click="handleRefreshCategory"
                    class="action-btn"
                  >
                    <Icon icon="ep:refresh" />
                  </el-button>
                </el-tooltip>

                <el-tooltip :content="t('survey.addCategory')" placement="top">
                  <el-button
                    type="success"
                    plain
                    size="small"
                    @click="handleAddRootCategory"
                    class="action-btn"
                  >
                    <Icon icon="ep:plus" />
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </div>

          <!-- Category Tree -->
          <div class="head-container">
            <el-tree
              ref="categoryTreeRef"
              v-loading="categoryLoading"
              :data="categoryTreeWithRoot"
              :props="{ label: 'name', children: 'children' }"
              :expand-on-click-node="false"
              :filter-node-method="filterCategoryNode"
              node-key="id"
              highlight-current
              @node-click="handleCategoryClick"
              :default-expanded-keys="[0]"
            >
              <template #default="{ node, data }">
                <div class="flex justify-between items-center w-full">
                  <div class="flex items-center">
                    <Icon
                      :icon="node.expanded ? 'ep:folder-opened' : 'ep:folder'"
                      class="mr-5px text-[var(--el-color-primary)]"
                    />
                    <span class="text-sm">{{ node.label }}</span>
                  </div>
                  <div class="flex items-center" v-if="data.id !== 0">
                    <el-dropdown
                      trigger="click"
                      @command="(command) => handleCategoryCommand(command, data)"
                    >
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click.stop
                      >
                        <Icon icon="ep:more-filled" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="add">
                            <Icon icon="ep:plus" class="mr-5px" />
                            {{ t('survey.addSubcategory') }}
                          </el-dropdown-item>
                          <el-dropdown-item command="edit">
                            <Icon icon="ep:edit" class="mr-5px" />
                            {{ t('survey.editCategory') }}
                          </el-dropdown-item>
                          <el-dropdown-item command="delete" divided>
                            <Icon icon="ep:delete" class="mr-5px" />
                            {{ t('survey.deleteCategory') }}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>


        </el-scrollbar>
      </Pane>

      <!-- Right Panel: Template List -->
      <Pane class="!bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <!-- Selected Category Info -->
          <div v-if="selectedCategory" class="pt-3 flex items-center">
            <div class="p-2 flex items-center">
              <el-tag type="primary">
                <div class="flex items-center">
                  <Icon icon="ep:folder" class="mr-5px" />
                  <span>{{ selectedCategory.name }}</span>
                </div>
              </el-tag>
              <span class="ms-2.5">{{ templateTotal }} {{ t('survey.template') }}</span>
            </div>
          </div>

          <!-- Search Form -->
          <ContentWrap>
            <el-form
              class="-mb-15px"
              :model="queryParams"
              ref="queryFormRef"
              :inline="true"
              label-width="120px"
            >
              <el-form-item :label="t('survey.templateName')" prop="name">
                <el-input
                  v-model="queryParams.name"
                  :placeholder="t('survey.pleaseEnter') + t('survey.templateName')"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>
              <el-form-item :label="t('survey.templateCode')" prop="templateCode">
                <el-input
                  v-model="queryParams.templateCode"
                  :placeholder="t('survey.pleaseEnter') + t('survey.templateCode')"
                  clearable
                  @keyup.enter="handleQuery"
                  class="!w-240px"
                />
              </el-form-item>
              <el-form-item :label="t('survey.status')" prop="status">
                <el-select v-model="queryParams.status" class="!w-240px" clearable :placeholder="t('survey.pleaseSelect') + t('survey.status')">
                  <el-option :label="t('survey.enabled')" :value="0" />
                  <el-option :label="t('survey.disabled')" :value="1" />
                </el-select>
              </el-form-item>
              <el-form-item :label="t('survey.createTime')" prop="createTime">
                <el-date-picker
                  v-model="queryParams.createTime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  type="daterange"
                  :start-placeholder="t('global.startDate')"
                  :end-placeholder="t('global.endDate')"
                  :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                  class="!w-220px"
                />
              </el-form-item>
              <el-form-item>
                <el-button @click="handleQuery">
                  <Icon icon="ep:search" class="mr-5px" /> {{ t('survey.search') }}
                </el-button>
                <el-button @click="resetQuery">
                  <Icon icon="ep:refresh" class="mr-5px" /> {{ t('survey.reset') }}
                </el-button>
                <el-button
                  type="primary"
                  plain
                  @click="openForm('create')"
                >
                  <Icon icon="ep:plus" class="mr-5px" /> {{ t('survey.addTemplate') }}
                </el-button>
<!--                <el-button-->
<!--                  type="success"-->
<!--                  plain-->
<!--                  @click="handleExport"-->
<!--                  :loading="exportLoading"-->
<!--                >-->
<!--                  <Icon icon="ep:download" class="mr-5px" /> {{ t('survey.export') }}-->
<!--                </el-button>-->
              </el-form-item>
            </el-form>
          </ContentWrap>

          <!-- Template List -->
          <ContentWrap>
            <el-table
              v-loading="loading"
              :data="templateList"
              :stripe="true"
              :show-overflow-tooltip="true"
            >
              <el-table-column type="index" :label="t('common.index')" min-width="80" />
              <el-table-column :label="t('survey.templateName')" prop="name" min-width="200" />
              <el-table-column :label="t('survey.templateCode')" prop="templateCode" min-width="150">
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    @click="handleCopyCode(scope.row.templateCode)"
                    class="p-0"
                  >
                    {{ scope.row.templateCode }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column :label="t('survey.category')" prop="categoryName" min-width="120" />
              <el-table-column :label="t('survey.questionCount')" align="center" prop="questionCount" min-width="140">
                <template #default="scope">
                  <el-tag type="info">{{ scope.row.questionCount || 0 }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column :label="t('survey.status')" align="center" prop="status" min-width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column :label="t('survey.description')" prop="description" min-width="200" show-overflow-tooltip />
              <el-table-column
                :label="t('survey.createTime')"
                align="center"
                prop="createTime"
                :formatter="dateFormatter"
                width="180px"
              />
              <el-table-column :label="t('survey.actions')" align="center" min-width="240" fixed="right">
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    @click="handlePreview(scope.row.id)"
                  >
                    {{ t('survey.preview') }}
                  </el-button>
                  <el-button
                    link
                    type="primary"
                    @click="openForm('update', scope.row.id)"
                  >
                    {{ t('survey.edit') }}
                  </el-button>
<!--                  <el-button-->
<!--                    link-->
<!--                    type="success"-->
<!--                    @click="handleCopy(scope.row.id)"-->
<!--                  >-->
<!--                    {{ t('survey.copy') }}-->
<!--                  </el-button>-->
                  <el-button
                    link
                    type="danger"
                    @click="handleDelete(scope.row.id)"
                  >
                    {{ t('survey.delete') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- Pagination -->
            <Pagination
              :total="templateTotal"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getTemplateList"
            />
          </ContentWrap>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <!-- Category Form Dialog -->
    <CategoryForm ref="categoryFormRef" @success="loadCategoryTree" />

    <!-- Template Form Dialog -->
    <TemplateForm ref="templateFormRef" @success="getTemplateList" />

    <!-- Template Preview Dialog -->
    <TemplatePreviewDialog ref="previewRef" />
  </div>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { Pane, Splitpanes } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import type { ElTree } from 'element-plus'
import { CategoryApi, TemplateApi, SurveyUtils } from '@/api/system/survey'
import type { SurveyCategory, SurveyTemplate, TemplateQuery } from '@/api/system/survey/types'
import CategoryForm from '../category/CategoryForm.vue'
import TemplateForm from './TemplateForm.vue'
import TemplatePreviewDialog from './TemplatePreviewDialog.vue'

defineOptions({ name: 'SurveyTemplateManagement' })

const message = useMessage()
const { t } = useI18n()
const router = useRouter()

// Reactive data
const loading = ref(true)
const categoryLoading = ref(false)
const exportLoading = ref(false)
const templateList = ref<SurveyTemplate[]>([])
const categoryTree = ref<SurveyCategory[]>([])
const templateTotal = ref(0)
const selectedCategory = ref<SurveyCategory | null>(null)
const categorySearchName = ref('')

// Query parameters
const queryParams = reactive<TemplateQuery>({
  pageNo: 1,
  pageSize: 20,
  name: undefined,
  templateCode: undefined,
  categoryId: undefined,
  status: undefined,
  createTime: undefined
})

// Form references
const queryFormRef = ref()
const categoryTreeRef = ref<InstanceType<typeof ElTree>>()
const categoryFormRef = ref()
const templateFormRef = ref()
const previewRef = ref()

// Computed
const categoryTreeWithRoot = computed(() => {
  return [
    {
      id: 0,
      name: t('survey.allTemplates'),
      parentId: -1,
      sort: 0,
      status: 0,
      children: categoryTree.value
    }
  ]
})

/** Load category tree */
const loadCategoryTree = async () => {
  categoryLoading.value = true
  try {
    const data = await CategoryApi.getTree()
    categoryTree.value = data
  } finally {
    categoryLoading.value = false
  }
}

/** Get template list */
const getTemplateList = async () => {
  loading.value = true
  try {
    const data = await TemplateApi.getPage(queryParams)
    templateList.value = data.list
    templateTotal.value = data.total
  } finally {
    loading.value = false
  }
}

/** Handle category search */
const handleCategorySearch = () => {
  categoryTreeRef.value?.filter(categorySearchName.value)
}

/** Filter category node */
const filterCategoryNode = (value: string, data: any) => {
  if (!value) return true
  return data.name.includes(value)
}

/** Handle category click */
const handleCategoryClick = (data: any) => {
  selectedCategory.value = data
  // 如果点击根节点（所有模板），则不设置分类ID过滤
  queryParams.categoryId = data.id === 0 ? undefined : data.id
  queryParams.pageNo = 1
  getTemplateList()
}

/** Handle category command */
const handleCategoryCommand = (command: string, data: any) => {
  switch (command) {
    case 'add':
      categoryFormRef.value.open('create', undefined, data.id)
      break
    case 'edit':
      categoryFormRef.value.open('update', data.id)
      break
    case 'delete':
      handleDeleteCategory(data.id)
      break
  }
}

/** Handle refresh category */
const handleRefreshCategory = () => {
  loadCategoryTree()
}

/** Handle add root category */
const handleAddRootCategory = () => {
  categoryFormRef.value.open('create')
}

/** Handle delete category */
const handleDeleteCategory = async (id: number) => {
  try {
    await message.delConfirm()
    await CategoryApi.delete(id)
    message.success(t('survey.deleteSuccess'))
    await loadCategoryTree()
  } catch {}
}

/** Search button operation */
const handleQuery = () => {
  queryParams.pageNo = 1
  getTemplateList()
}

/** Reset button operation */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** Add/Edit operation */
const openForm = (type: string, id?: number) => {

  // 新增和编辑都跳转到编辑页面
  router.push({
    path: `/survey/template`,
    // name: 'SurveyTemplateForm',
    // params: { id: id || 'new' },
    query: {
      type: type,
      categoryId: type === 'create' && selectedCategory.value?.id ? selectedCategory.value.id : undefined,
      id: id
    }
  })
}

/** Preview operation */
const handlePreview = (id: number) => {
  previewRef.value.open(id)
}

/** Copy template code to clipboard */
const handleCopyCode = async (templateCode: string) => {
  try {
    await navigator.clipboard.writeText(templateCode)
    message.success(t('survey.copySuccess'))
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = templateCode
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    const success = document.execCommand('copy')
    document.body.removeChild(textArea)
    if (success) {
      message.success(t('survey.copySuccess'))
    } else {
      message.error('Copy failed')
    }
  }
}

/** Copy operation */
const handleCopy = async (id: number) => {
  try {
    // 复制功能暂未实现
    message.warning(t('survey.featureNotImplemented'))
  } catch {}
}

/** Delete operation */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await TemplateApi.delete(id)
    message.success(t('survey.deleteSuccess'))
    await getTemplateList()
  } catch {}
}

/** Export operation */
const handleExport = async () => {
  try {
    exportLoading.value = true
    await TemplateApi.exportExcel(queryParams)
    message.success(t('survey.exportSuccess'))
  } finally {
    exportLoading.value = false
  }
}

/** Get status type for tag */
const getStatusType = (status: number) => {
  return SurveyUtils.getCommonStatusColor(status)
}

/** Get status text */
const getStatusText = (status: number) => {
  const statusMap = {
    0: t('survey.enabled'),
    1: t('survey.disabled')
  }
  return statusMap[status] || t('common.unknown')
}

/** Initialize */
onMounted(() => {
  loadCategoryTree()
  getTemplateList()
})
</script>

<style scoped>
:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

.head-container {
  margin-bottom: 16px;
}

/* 分类树工具栏样式 */
.category-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding: 10px 12px;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-page) 100%);
  border-radius: 10px;
  border: 1px solid var(--el-border-color-lighter);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
}

.category-toolbar:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: var(--el-border-color);
}

.search-input {
  flex: 1;
  min-width: 0;
}

.action-buttons {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.action-btn {
  width: 36px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.action-btn:hover::before {
  transform: translateX(100%);
}

.action-btn:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* 搜索框样式优化 */
:deep(.search-input .el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
}

:deep(.search-input .el-input__wrapper:hover) {
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--el-border-color);
  transform: translateY(-1px);
}

:deep(.search-input .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 3px rgba(var(--el-color-primary-rgb), 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary);
  transform: translateY(-1px);
}

:deep(.search-input .el-input__prefix) {
  color: var(--el-text-color-placeholder);
  transition: color 0.3s ease;
}

:deep(.search-input .el-input__wrapper.is-focus .el-input__prefix) {
  color: var(--el-color-primary);
}

:deep(.search-input .el-input__inner) {
  font-size: 13px;
  color: var(--el-text-color-regular);
}

:deep(.search-input .el-input__inner::placeholder) {
  color: var(--el-text-color-placeholder);
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-toolbar {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    width: 100%;
    justify-content: center;
  }

  .action-btn {
    flex: 1;
    max-width: 80px;
  }
}

:deep(.splitpanes__pane) {
  transition: none;
}

:deep(.splitpanes__splitter) {
  background-color: var(--el-border-color);
  position: relative;
}

:deep(.splitpanes__splitter:before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  transition: opacity 0.4s;
  background-color: var(--el-color-primary);
  opacity: 0;
  z-index: 1;
}

:deep(.splitpanes__splitter:hover:before) {
  opacity: 1;
}

:deep(.splitpanes--vertical > .splitpanes__splitter:before) {
  left: -1px;
  right: -1px;
  height: 100%;
}

/* Prevent table header text wrapping */
:deep(.el-table .el-table__header-wrapper .el-table__header th) {
  white-space: nowrap;
}

:deep(.el-table .el-table__header-wrapper .el-table__header th .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
