<template>
  <Dialog :title="t('survey.previewTemplate')" v-model="dialogVisible" width="900px">
    <TemplatePreview
      ref="templatePreviewRef"
      :template-id="currentTemplateId"
      :template="currentTemplate"
    />

    <template #footer>
      <el-button @click="close">{{ t('survey.close') }}</el-button>
      <el-button
        type="primary"
        @click="handleEdit"
        v-hasPermi="['system:survey:template:update']"
        v-if="currentTemplate || currentTemplateId"
      >
        {{ t('survey.editTemplate') }}
      </el-button>
      <el-button
        type="success"
        @click="handleCreateInstance"
        v-hasPermi="['system:survey:instance:create']"
        v-if="currentTemplate || currentTemplateId"
      >
        {{ t('survey.addInstance') }}
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useRouter } from 'vue-router'
import { Dialog } from '@/components/Dialog'
import TemplatePreview from './TemplatePreview.vue'
import type { SurveyTemplate } from '@/api/system/survey/types'

defineOptions({ name: 'TemplatePreviewDialog' })

const { t } = useI18n()
const router = useRouter()

// 弹窗状态
const dialogVisible = ref(false)
const currentTemplateId = ref<number | undefined>(undefined)
const currentTemplate = ref<SurveyTemplate | undefined>(undefined)

// 模板预览组件引用
const templatePreviewRef = ref()

/** 通过ID打开预览弹窗 */
const open = (templateId: number) => {
  currentTemplateId.value = templateId
  currentTemplate.value = undefined
  dialogVisible.value = true
}

/** 通过数据打开预览弹窗 */
const openWithData = (template: SurveyTemplate) => {
  currentTemplate.value = template
  currentTemplateId.value = undefined
  dialogVisible.value = true
}

/** 关闭弹窗 */
const close = () => {
  dialogVisible.value = false
  currentTemplateId.value = undefined
  currentTemplate.value = undefined
}

/** 编辑模板 */
const handleEdit = () => {
  const template = currentTemplate.value
  const templateId = currentTemplateId.value

  if (template) {
    router.push({
      name: 'SurveyTemplateForm',
      params: { id: template.id }
    })
  } else if (templateId) {
    router.push({
      name: 'SurveyTemplateForm',
      params: { id: templateId }
    })
  }
  close()
}

/** 创建问卷实例 */
const handleCreateInstance = () => {
  const template = currentTemplate.value
  const templateId = currentTemplateId.value

  if (template) {
    router.push({
      name: 'SurveyInstanceForm',
      query: { templateId: template.id }
    })
  } else if (templateId) {
    router.push({
      name: 'SurveyInstanceForm',
      query: { templateId: templateId }
    })
  }
  close()
}

// 暴露方法
defineExpose({
  open,
  openWithData,
  close
})
</script>

<style scoped>
/* 弹窗特定样式 */
:deep(.el-dialog__body) {
  padding: 0;
}
</style>
