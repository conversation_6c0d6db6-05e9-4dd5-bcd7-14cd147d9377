<template>
  <div class="text-preview">
    <el-input
      v-model="textValue"
      type="textarea"
      :rows="config.rows || 4"
      :placeholder="config.placeholder || '请输入您的答案'"
      :maxlength="config.maxLength"
      :minlength="config.minLength"
      :disabled="disabled"
      show-word-limit
    />
    <div v-if="config.minLength || config.maxLength" class="length-hint">
      <small class="text-gray-500">
        字数要求: {{ config.minLength || 0 }} - {{ config.maxLength || '无限制' }}
      </small>
    </div>
  </div>
</template>

<script setup lang="ts">
interface TextConfig {
  inputType: string
  minLength: number
  maxLength: number
  placeholder: string
  rows: number
  score: number
}

interface Props {
  config: TextConfig
  questionType: number
  disabled?: boolean
  showScore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showScore: false
})

const textValue = ref('')
</script>

<style scoped>
.text-preview {
  padding: 16px 0;
}

.length-hint {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f2f5;
}
</style>
