<template>
  <div class="file-upload-preview">
    <el-upload
      :disabled="disabled"
      :limit="config.maxFiles"
      :accept="acceptTypes"
      action="#"
      :auto-upload="false"
      list-type="text"
    >
      <el-button type="primary" :disabled="disabled">
        <Icon icon="ep:upload" class="mr-2" />
        选择文件
      </el-button>
      <template #tip>
        <div class="el-upload__tip">
          <div>允许上传: {{ config.allowedTypes.join(', ') }}</div>
          <div>最多 {{ config.maxFiles }} 个文件，单个文件不超过 {{ formatFileSize(config.maxFileSize) }}</div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
interface FileUploadConfig {
  allowedTypes: string[]
  maxFileSize: number
  maxFiles: number
  score: number
}

interface Props {
  config: FileUploadConfig
  questionType: number
  disabled?: boolean
  showScore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showScore: false
})

const acceptTypes = computed(() => {
  return props.config.allowedTypes.map(type => `.${type}`).join(',')
})

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-upload-preview {
  padding: 16px 0;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}
</style>
