<template>
  <div class="rating-preview">
    <el-rate
      v-model="ratingValue"
      :max="config.maxScore"
      :disabled="disabled"
      show-score
      :score-template="`{value} / ${config.maxScore}`"
    />
<!--    <div v-if="config.showLabels" class="rating-labels">-->
<!--      <span class="min-label">{{ config.minLabel || '很差' }}</span>-->
<!--      <span class="max-label">{{ config.maxLabel || '很好' }}</span>-->
<!--    </div>-->
<!--    <div v-if="showScore" class="score-info">-->
<!--      <small class="text-gray-500">t('survey.scorePerPoint'): {{ config.scorePerPoint }}</small>-->
<!--    </div>-->
  </div>
</template>

<script setup lang="ts">
interface RatingConfig {
  minScore: number
  maxScore: number
  step: number
  showLabels: boolean
  minLabel: string
  maxLabel: string
  scorePerPoint: number
}

interface Props {
  config: RatingConfig
  questionType: number
  disabled?: boolean
  showScore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showScore: false
})

const ratingValue = ref(0)
</script>

<style scoped>
.rating-preview {
  padding: 16px 0;
}

.rating-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.score-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f2f5;
}
</style>
