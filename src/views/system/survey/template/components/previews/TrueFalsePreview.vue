<template>
  <div class="true-false-preview">
    <el-radio-group v-model="selectedValue" :disabled="disabled">
      <el-radio :value="true" class="option-radio">
        {{ config.trueText || '正确' }}
        <span v-if="showScore" class="option-score">({{ config.trueScore }}分)</span>
      </el-radio>
      <el-radio :value="false" class="option-radio">
        {{ config.falseText || '错误' }}
        <span v-if="showScore" class="option-score">({{ config.falseScore }}分)</span>
      </el-radio>
    </el-radio-group>
  </div>
</template>

<script setup lang="ts">
interface TrueFalseConfig {
  trueText: string
  falseText: string
  trueScore: number
  falseScore: number
}

interface Props {
  config: TrueFalseConfig
  questionType: number
  disabled?: boolean
  showScore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showScore: false
})

const selectedValue = ref(null)
</script>

<style scoped>
.true-false-preview {
  padding: 16px 0;
}

.option-radio {
  display: block;
  margin-bottom: 12px;
  line-height: 1.5;
}

.option-score {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}
</style>
