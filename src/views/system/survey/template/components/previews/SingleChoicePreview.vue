<template>
  <div class="single-choice-preview">
    <el-radio-group v-model="selectedValue" :disabled="disabled">
      <el-radio
        v-for="(option, index) in config.options"
        :key="index"
        :value="option.value"
        class="option-radio"
      >
        {{ option.text }}
        <span v-if="showScore" class="option-score">({{ option.score }}分)</span>
      </el-radio>
    </el-radio-group>
  </div>
</template>

<script setup lang="ts">
interface Option {
  text: string
  value: string
  score: number
}

interface SingleChoiceConfig {
  options: Option[]
}

interface Props {
  config: SingleChoiceConfig
  questionType: number
  disabled?: boolean
  showScore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showScore: false
})

const selectedValue = ref('')
</script>

<style scoped>
.single-choice-preview {
  padding: 16px 0;
}

.option-radio {
  display: block;
  margin-bottom: 12px;
  line-height: 1.5;
}

.option-score {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}
</style>
