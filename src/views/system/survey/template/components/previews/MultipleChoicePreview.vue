<template>
  <div class="multiple-choice-preview">
    <el-checkbox-group v-model="selectedValues" :disabled="disabled">
      <el-checkbox
        v-for="(option, index) in config.options"
        :key="index"
        :value="option.value"
        class="option-checkbox"
      >
        {{ option.text }}
        <span v-if="showScore" class="option-score">({{ option.score }}分)</span>
      </el-checkbox>
    </el-checkbox-group>
    <div v-if="config.minSelections || config.maxSelections" class="selection-hint">
      <small class="text-gray-500">
        选择范围: {{ config.minSelections || 0 }} - {{ config.maxSelections || config.options.length }}
      </small>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Option {
  text: string
  value: string
  score: number
}

interface MultipleChoiceConfig {
  options: Option[]
  minSelections: number
  maxSelections: number
}

interface Props {
  config: MultipleChoiceConfig
  questionType: number
  disabled?: boolean
  showScore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showScore: false
})

const selectedValues = ref([])
</script>

<style scoped>
.multiple-choice-preview {
  padding: 16px 0;
}

.option-checkbox {
  display: block;
  margin-bottom: 12px;
  line-height: 1.5;
}

.option-score {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.selection-hint {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f2f5;
}
</style>
