<template>
  <div class="true-false-config">
    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item label="正确答案分数">
          <el-input-number
            v-model="localConfig.trueScore"
            :min="0"
            :max="100"
            size="small"
            style="width: 120px"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="错误答案分数">
          <el-input-number
            v-model="localConfig.falseScore"
            :min="0"
            :max="100"
            size="small"
            style="width: 120px"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
interface TrueFalseConfig {
  trueText: string
  falseText: string
  trueScore: number
  falseScore: number
}

interface Props {
  modelValue: TrueFalseConfig
  questionType: number
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const localConfig = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style scoped>
.true-false-config {
  margin-top: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input-number .el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
