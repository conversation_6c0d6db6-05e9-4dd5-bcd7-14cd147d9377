<template>
  <div class="text-config">
    <el-row :gutter="16">
      <el-col :span="6">
        <el-form-item label="最小长度">
          <el-input-number
            v-model="localConfig.minLength"
            :min="0"
            :max="localConfig.maxLength - 1"
            size="small"
            style="width: 100px"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="最大长度">
          <el-input-number
            v-model="localConfig.maxLength"
            :min="localConfig.minLength + 1"
            :max="2000"
            size="small"
            style="width: 100px"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="文本行数">
          <el-input-number
            v-model="localConfig.rows"
            :min="1"
            :max="10"
            size="small"
            style="width: 100px"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="占位符文本">
          <el-input
            v-model="localConfig.placeholder"
            placeholder="请输入占位符文本"
            size="small"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
interface TextConfig {
  inputType: string
  minLength: number
  maxLength: number
  placeholder: string
  rows: number
  score: number
}

interface Props {
  modelValue: TextConfig
  questionType: number
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const localConfig = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style scoped>
.text-config {
  margin-top: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
