<template>
  <div class="rating-config">
    <el-row :gutter="16">
      <el-col :span="6">
        <el-form-item label="最小分数">
          <el-input-number
            v-model="localConfig.minScore"
            :min="1"
            :max="localConfig.maxScore - 1"
            size="small"
            style="width: 100px"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="最大分数">
          <el-input-number
            v-model="localConfig.maxScore"
            :min="localConfig.minScore + 1"
            :max="10"
            size="small"
            style="width: 100px"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="每分分值">
          <el-input-number
            v-model="localConfig.scorePerPoint"
            :min="0.1"
            :max="10"
            :step="0.1"
            size="small"
            style="width: 100px"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
interface RatingConfig {
  minScore: number
  maxScore: number
  step: number
  showLabels: boolean
  minLabel: string
  maxLabel: string
  scorePerPoint: number
}

interface Props {
  modelValue: RatingConfig
  questionType: number
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const localConfig = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style scoped>
.rating-config {
  margin-top: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input-number .el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
