<template>
  <div class="single-choice-config">
    <el-form-item :label="t('survey.options')">
      <div class="options-container">
        <div class="options-list">
          <div
            v-for="(option, index) in modelValue.options"
            :key="`option-${index}-${option.value || index}`"
            class="option-item"
          >
            <el-input
              v-model="option.text"
              :placeholder="`${t('survey.option')} ${index + 1}`"
              size="default"
              style="width: 300px;"
            />
            <el-input-number
              v-model="option.score"
              :min="0"
              :max="100"
              size="small"
              placeholder="分数"
              style="width: 80px;"
            />
            <el-button
              link
              type="danger"
              size="small"
              @click="removeOption(index)"
              :disabled="modelValue.options.length <= 2"
              class="delete-btn"
            >
              <Icon icon="ep:delete" />
            </el-button>
          </div>
        </div>

        <div class="add-option-row">
          <el-button
            type="primary"
            plain
            size="small"
            @click="addOption"
            class="add-option-btn"
          >
            <Icon icon="ep:plus" class="mr-1" />
            {{ t('survey.addOption') }}
          </el-button>
        </div>
      </div>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
interface Option {
  text: string
  value: string
  score: number
}

interface SingleChoiceConfig {
  options: Option[]
}

interface Props {
  modelValue: SingleChoiceConfig
  questionType: number
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const { t } = useI18n()

const addOption = () => {
  const timestamp = Date.now()
  const newOption: Option = {
    text: `${t('survey.option')}${props.modelValue.options.length + 1}`,
    value: `option${timestamp}`,
    score: 1
  }

  const updatedConfig = {
    ...props.modelValue,
    options: [...props.modelValue.options, newOption]
  }

  emit('update:modelValue', updatedConfig)
}

const removeOption = (index: number) => {
  if (props.modelValue.options.length > 2) {
    const updatedConfig = {
      ...props.modelValue,
      options: props.modelValue.options.filter((_, i) => i !== index)
    }
    emit('update:modelValue', updatedConfig)
  }
}

// 处理选项文本变化
const updateOptionText = (index: number, text: string) => {
  const updatedOptions = [...props.modelValue.options]
  updatedOptions[index] = { ...updatedOptions[index], text }

  const updatedConfig = {
    ...props.modelValue,
    options: updatedOptions
  }

  emit('update:modelValue', updatedConfig)
}

// 处理选项分数变化
const updateOptionScore = (index: number, score: number) => {
  const updatedOptions = [...props.modelValue.options]
  updatedOptions[index] = { ...updatedOptions[index], score }

  const updatedConfig = {
    ...props.modelValue,
    options: updatedOptions
  }

  emit('update:modelValue', updatedConfig)
}
</script>

<style scoped>
.single-choice-config {
  margin-top: 0;
}

.options-container {
  width: 100%;
}

.options-list {
  margin-bottom: 0;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  min-height: 56px;
}

.option-item:hover {
  background: #e3f2fd;
  border-color: #90caf9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.option-item .el-input {
  flex: 0 0 auto;
}

.option-item .el-input-number {
  width: 80px;
  flex: 0 0 auto;
}

.add-option-row {
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding-left: 0;
}

.add-option-btn {
  height: 32px;
  border-radius: 6px;
  font-size: 13px;
  transition: all 0.3s ease;
}

.add-option-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.delete-btn {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex: 0 0 auto;
}

.delete-btn:hover {
  background: #fef0f0;
  transform: scale(1.1);
}

.delete-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.delete-btn:disabled:hover {
  background: none;
  transform: none;
}

/* 选项输入框样式优化 */
.option-item :deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.option-item :deep(.el-input__wrapper:focus-within) {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.option-item :deep(.el-input-number .el-input__wrapper) {
  border-radius: 6px;
}
</style>
