<template>
  <div class="file-upload-config">
    <el-form-item label="允许的文件类型">
      <el-select
        v-model="localConfig.allowedTypes"
        multiple
        placeholder="选择允许的文件类型"
        style="width: 100%"
        size="small"
      >
        <el-option label="PDF" value="pdf" />
        <el-option label="Word文档" value="doc" />
        <el-option label="Word文档(新)" value="docx" />
        <el-option label="图片(JPG)" value="jpg" />
        <el-option label="图片(PNG)" value="png" />
        <el-option label="Excel" value="xlsx" />
        <el-option label="PowerPoint" value="pptx" />
      </el-select>
    </el-form-item>

    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item label="最大文件数量">
          <el-input-number
            v-model="localConfig.maxFiles"
            :min="1"
            :max="10"
            size="small"
            style="width: 120px"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="单文件大小(MB)">
          <el-input-number
            v-model="fileSizeMB"
            :min="1"
            :max="100"
            size="small"
            style="width: 120px"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
interface FileUploadConfig {
  allowedTypes: string[]
  maxFileSize: number
  maxFiles: number
  score: number
}

interface Props {
  modelValue: FileUploadConfig
  questionType: number
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const localConfig = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const fileSizeMB = computed({
  get: () => Math.round(props.modelValue.maxFileSize / 1024 / 1024),
  set: (value) => {
    const updatedConfig = {
      ...props.modelValue,
      maxFileSize: value * 1024 * 1024
    }
    emit('update:modelValue', updatedConfig)
  }
})
</script>

<style scoped>
.file-upload-config {
  margin-top: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-select),
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper),
:deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-select .el-input__wrapper:hover),
:deep(.el-input-number .el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
