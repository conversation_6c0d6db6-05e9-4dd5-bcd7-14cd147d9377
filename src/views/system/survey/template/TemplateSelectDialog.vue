<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('survey.selectTemplate')"
    width="900px"
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <div class="template-dialog-content">
      <!-- 搜索区域 -->
      <div class="search-section">
        <el-row :gutter="16" class="mb-3">
          <el-col :span="8">
            <el-form-item :label="t('survey.templateName')" class="search-form-item">
              <el-input
                v-model="searchForm.name"
                :placeholder="t('survey.pleaseEnter') + t('survey.templateName')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('survey.templateCode')" class="search-form-item">
              <el-input
                v-model="searchForm.code"
                :placeholder="t('survey.pleaseEnter') + t('survey.templateCode')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('survey.category')" class="search-form-item">
              <el-tree-select
                v-model="searchForm.categoryId"
                :data="categoryOptions"
                :props="{ label: 'name', value: 'id', children: 'children' }"
                :placeholder="t('survey.pleaseSelect') + t('survey.category')"
                clearable
                :render-after-expand="false"
                show-checkbox
                :check-strictly="false"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 搜索按钮 -->
        <el-row>
          <el-col :span="24" class="text-right">
            <el-button @click="handleSearchReset">
              <Icon icon="ep:refresh" class="mr-1" />
              {{ t('common.reset') }}
            </el-button>
            <el-button type="primary" @click="handleSearch">
              <Icon icon="ep:search" class="mr-1" />
              {{ t('action.search') }}
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 模板列表 -->
      <div class="template-list-section">
        <el-table
          :data="templateList"
          v-loading="loading"
          style="width: 100%"
          max-height="400px"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <el-table-column width="55" align="center">
            <template #default="scope">
              <el-radio
                v-model="selectedTemplateId"
                :label="scope.row.id"
                @change="handleRadioChange(scope.row)"
              >
                <span></span>
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column :label="t('survey.templateName')" prop="name" min-width="200" show-overflow-tooltip />
          <el-table-column :label="t('survey.templateCode')" prop="templateCode" width="180">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.templateCode }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('survey.category')" prop="categoryName" width="120" />
          <el-table-column :label="t('survey.questionCount')" width="100" align="center">
            <template #default="scope">
              <el-tag type="primary" size="small">{{ scope.row.questionCount || 0 }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('survey.status')" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === CommonStatusEnum.ENABLED ? 'success' : 'info'" size="small">
                {{ scope.row.status === CommonStatusEnum.ENABLED ? t('survey.enabled') : t('survey.disabled') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="t('survey.actions')" width="80" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                link
                size="small"
                @click="handlePreview(scope.row)"
              >
                {{ t('survey.preview') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="searchForm.pageNo"
            v-model:page-size="searchForm.pageSize"
            :total="total"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadTemplateList"
            @current-change="loadTemplateList"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :disabled="!selectedRow"
      >
        {{ t('action.confirm') }}
      </el-button>
    </template>
  </el-dialog>

  <!-- 模板预览组件 -->
  <TemplatePreviewDialog ref="templatePreviewRef" />
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { Icon } from '@/components/Icon'
import { TemplateApi } from '@/api/system/survey/template'
import { CategoryApi } from '@/api/system/survey/category'
import { SurveyTemplate, CommonStatusEnum } from '@/api/system/survey/types'
import TemplatePreviewDialog from './TemplatePreviewDialog.vue'

interface Props {
  visible: boolean
  selectedTemplate?: SurveyTemplate | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', template: SurveyTemplate): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  categoryId: undefined,
  pageNo: 1,
  pageSize: 10
})

// 数据状态
const loading = ref(false)
const templateList = ref<SurveyTemplate[]>([])
const total = ref(0)
const selectedRow = ref<SurveyTemplate | null>(null)
const categoryOptions = ref<any[]>([])
const selectedTemplateId = ref<number | null>(null)

// 模板预览组件引用
const templatePreviewRef = ref()

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    // 设置初始选中状态
    if (props.selectedTemplate) {
      selectedRow.value = props.selectedTemplate
      selectedTemplateId.value = props.selectedTemplate.id
    } else {
      selectedRow.value = null
      selectedTemplateId.value = null
    }
    loadCategoryOptions()
    loadTemplateList()
  }
})

/** 加载分类选项 */
const loadCategoryOptions = async () => {
  try {
    // 调用分类API获取树形结构数据
    const data = await CategoryApi.getTree({ status: CommonStatusEnum.ENABLED })
    categoryOptions.value = data || []
  } catch (error) {
    console.error('Failed to load category options:', error)
    // 如果API调用失败，使用空数组
    categoryOptions.value = []
  }
}

/** 加载模板列表 */
const loadTemplateList = async () => {
  try {
    loading.value = true
    const params = {
      pageNo: searchForm.pageNo,
      pageSize: searchForm.pageSize,
      name: searchForm.name || undefined,
      templateCode: searchForm.code || undefined,
      categoryId: searchForm.categoryId || undefined,
      status: CommonStatusEnum.ENABLED // 只显示启用的模板
    }

    const data = await TemplateApi.getPage(params)
    templateList.value = data.list || []
    total.value = data.total || 0
  } catch (error) {
    console.error('Failed to load template list:', error)
  } finally {
    loading.value = false
  }
}

/** 搜索 */
const handleSearch = () => {
  searchForm.pageNo = 1
  loadTemplateList()
}

/** 重置搜索 */
const handleSearchReset = () => {
  searchForm.name = ''
  searchForm.code = ''
  searchForm.categoryId = undefined
  searchForm.pageNo = 1
  handleSearch()
}

/** 单选按钮变化 */
const handleRadioChange = (template: SurveyTemplate) => {
  selectedRow.value = template
  selectedTemplateId.value = template.id
}

/** 行点击 */
const handleRowClick = (row: SurveyTemplate) => {
  selectedRow.value = row
  selectedTemplateId.value = row.id
}

/** 确认选择 */
const handleConfirm = () => {
  if (selectedRow.value) {
    emit('confirm', selectedRow.value)
    dialogVisible.value = false
  }
}

/** 取消 */
const handleCancel = () => {
  dialogVisible.value = false
}

/** 预览模板 */
const handlePreview = async (template: SurveyTemplate) => {
  if (templatePreviewRef.value) {
    // 使用open方法通过ID加载完整的模板数据（包括问题）
    templatePreviewRef.value.open(template.id)
  }
}

/** 弹窗关闭 */
const handleDialogClosed = () => {
  // 重置状态
  selectedRow.value = null
  selectedTemplateId.value = null
  searchForm.name = ''
  searchForm.code = ''
  searchForm.categoryId = undefined
  searchForm.pageNo = 1
  templateList.value = []
  total.value = 0
}
</script>

<style scoped>
.template-dialog-content {
  padding: 0;
}

.search-section {
  margin-bottom: 16px;
}

.template-list-section {
  margin-top: 16px;
}

.pagination-section {
  margin-top: 16px;
  text-align: right;
}

/* 搜索表单项样式 */
.search-form-item {
  margin-bottom: 0;
}

.search-form-item :deep(.el-form-item__label) {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.search-form-item :deep(.el-form-item__content) {
  margin-left: 0 !important;
}
</style>
