<template>
  <div class="survey-scope-select">
    <!-- 作用域类型选择器 -->
    <div class="scope-type-selector">
      <div class="segmented-control">
        <div
          v-for="type in scopeTypes"
          :key="type.value"
          :class="['segmented-item', { 'segmented-item--active': selectedType === type.value }]"
          @click="handleTypeChange(type.value)"
        >
          {{ type.label }}
        </div>
      </div>
      <!--      <el-button-->
      <!--        type="primary"-->
      <!--        size="small"-->
      <!--        @click="handleAddScope"-->
      <!--        :disabled="!selectedType"-->
      <!--      >-->
      <!--        <Icon icon="ep:plus" class="mr-1" />-->
      <!--        {{ t('action.add') }}-->
      <!--      </el-button>-->
    </div>

    <!-- 左右布局容器 -->
    <div class="scope-content-container">
      <!-- 左侧：可选数据区域 -->
      <div class="left-panel">
        <!-- 搜索区域 -->
        <div class="search-section">
          <ContentWrap>
            <el-form :inline="true" class="search-form" ref="queryRef" :model="searchParams">
              <el-form-item
                prop="nickname"
                :label="t('sys.user.userName')"
                v-if="selectedType === ScopeTargetTypeEnum.USER"
              >
                <el-input
                  v-model="searchParams.nickname"
                  :placeholder="t('survey.searchUserName')"
                  clearable
                />
              </el-form-item>
              <el-form-item
                prop="mobile"
                :label="t('sys.login.mobile')"
                v-if="selectedType === ScopeTargetTypeEnum.USER"
              >
                <el-input
                  v-model="searchParams.mobile"
                  :placeholder="t('survey.searchMobile')"
                  clearable
                />
              </el-form-item>
              <el-form-item
                prop="email"
                :label="t('sys.login.email')"
                v-if="selectedType === ScopeTargetTypeEnum.USER"
              >
                <el-input
                  v-model="searchParams.email"
                  :placeholder="t('profile.rules.mail')"
                  clearable
                />
              </el-form-item>
              <el-form-item
                prop="name"
                :label="t('sys.dept.deptName')"
                v-if="selectedType === ScopeTargetTypeEnum.DEPT"
              >
                <el-input
                  v-model="searchParams.name"
                  :placeholder="t('survey.searchDeptName')"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">
                  <Icon icon="ep:search" class="mr-1" />
                  {{ t('action.search') }}
                </el-button>
                <el-button @click="handleSearchReset">
                  <Icon icon="ep:refresh" class="mr-1" />
                  {{ t('action.reset') }}
                </el-button>
              </el-form-item>
            </el-form>
          </ContentWrap>
        </div>

        <!-- 可选数据列表 -->
        <div class="available-data-section">
          <ContentWrap>
            <div class="section-header">
              <h4>{{
                selectedType === ScopeTargetTypeEnum.USER
                  ? t('survey.availableUsers')
                  : t('survey.availableDepts')
              }}</h4>
            </div>

            <!-- 用户列表 -->
            <div v-if="selectedType === ScopeTargetTypeEnum.USER" class="user-table-container">
              <el-table
                ref="availableTableRef"
                :data="availableUserList"
                v-loading="availableLoading"
                @selection-change="handleAvailableSelectionChange"
                style="width: 100%"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column
                  :label="t('sys.user.userName')"
                  prop="nickname"
                  min-width="120"
                  show-overflow-tooltip
                />
                <el-table-column
                  :label="t('sys.login.mobile')"
                  prop="mobile"
                  width="160"
                  show-overflow-tooltip
                />
                <el-table-column
                  :label="t('sys.login.email')"
                  prop="email"
                  width="160"
                  show-overflow-tooltip
                />
                <el-table-column
                  :label="t('sys.dept.deptName')"
                  prop="deptName"
                  min-width="180"
                  show-overflow-tooltip
                />
                <el-table-column :label="t('global.status')" width="80" align="center">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === 0 ? 'success' : 'info'" size="small">
                      {{ scope.row.status === 0 ? t('global.enable') : t('global.disable') }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 部门树形列表 -->
            <el-table
              v-if="selectedType === ScopeTargetTypeEnum.DEPT"
              ref="availableTableRef"
              :data="availableDeptList"
              v-loading="availableLoading"
              @selection-change="handleAvailableSelectionChange"
              style="width: 100%"
              max-height="400px"
              row-key="id"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column :label="t('sys.dept.deptName')" prop="name" min-width="200" />
              <el-table-column :label="t('global.status')" width="80" align="center">
                <template #default>
                  <el-tag type="success" size="small">
                    {{ t('global.enable') }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            <!-- 部门总数显示 -->
            <div
              class="dept-total"
              v-if="selectedType === ScopeTargetTypeEnum.DEPT && availableTotal > 0"
            >
              <span>{{ t('survey.totalDepts', { count: availableTotal }) }}</span>
            </div>
            <!-- 用户分页 -->
            <div
              class="pagination-section"
              v-if="selectedType === ScopeTargetTypeEnum.USER && availableTotal > 0"
            >
              <el-pagination
                v-model:current-page="searchParams.pageNo"
                v-model:page-size="searchParams.pageSize"
                :total="availableTotal"
                :page-sizes="[15, 30, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="loadAvailableData"
                @current-change="loadAvailableData"
                small
              />
            </div>

          </ContentWrap>

        </div>

      </div>

      <!-- 右侧：已选作用域区域 -->
      <div class="right-panel">
        <div class="selected-scope-section">
          <ContentWrap>
            <div class="section-header">
              <h4>{{ t('survey.selectedScopes') }}</h4>
              <el-button
                type="danger"
                size="small"
                :disabled="selectedScopeRows.length === 0"
                @click="handleBatchDelete"
              >
                <Icon icon="ep:delete" class="mr-1" />
                {{ t('action.batchDelete') }}
              </el-button>
            </div>

            <el-table
              ref="selectedTableRef"
              :data="scopeList"
              v-loading="loading"
              @selection-change="handleScopeSelectionChange"
              style="width: 100%"
              max-height="500px"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column
                prop="type"
                :label="t('learningCenter.course.type')"
                width="100"
                align="center"
              >
                <template #default="{ row }">
                  <el-tag :type="getTypeTagType(row.type)" size="small">
                    {{ getTypeLabel(row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="name" :label="t('sys.user.userName')" min-width="200" />
              <el-table-column :label="t('global.action')" width="100" align="center">
                <template #default="{ row, $index }">
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click.stop="handleDeleteScope(row, $index)"
                  >
                    <Icon icon="ep:delete" />
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 空状态 -->
            <div v-if="scopeList.length === 0" class="empty-state">
              <el-empty :description="t('survey.noScopeData')" />
            </div>
          </ContentWrap>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { Icon } from '@/components/Icon'
import { ContentWrap } from '@/components/ContentWrap'
import { deptTreeSelect, employeeData } from '@/api/system/user'
import { ScopeTargetTypeEnum } from '@/api/system/survey/types'
import { ElMessage, ElTable, FormInstance } from 'element-plus'
import { deepClone } from '@/utils'

interface ScopeItem {
  id: number
  name: string
  type: number
  deptName?: string
  parentName?: string
}

interface Props {
  modelValue: ScopeItem[]
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: ScopeItem[]): void

  (e: 'update:loading', value: boolean): void

  (e: 'confirm', scopes: ScopeItem[]): void

  (e: 'delete', deletedScopes: ScopeItem[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  loading: false
})

const emit = defineEmits<Emits>()

const { t } = useI18n()

// 作用域类型定义
const scopeTypes = [
  { value: ScopeTargetTypeEnum.DEPT, label: t('survey.department') },
  { value: ScopeTargetTypeEnum.USER, label: t('survey.user') }
]

// 响应式数据
const selectedType = ref<number>(ScopeTargetTypeEnum.DEPT)
const scopeList = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表格引用
const availableTableRef = ref<InstanceType<typeof ElTable>>()
const selectedTableRef = ref<InstanceType<typeof ElTable>>()
const queryRef = ref<FormInstance>()

// 搜索表单
const searchParams = reactive({
  nickname: '',
  mobile: '',
  email: '',
  name: '',
  pageNo: 1,
  pageSize: 15
})

// 可选数据相关
const availableLoading = ref(false)
const availableUserList = ref<any[]>([])
const availableDeptList = ref<any[]>([])
const availableTotal = ref(0)
const selectedAvailableRows = ref<any[]>([])

// 已选作用域相关
const selectedScopeRows = ref<ScopeItem[]>([])

// 选中状态管理
const selectedDeptIds = ref<Set<number>>(new Set())
const selectedUserIds = ref<Set<number>>(new Set())
const isRestoringSelection = ref(false)
const isLoadingData = ref(false)

// 监听loading状态
const loading = computed({
  get: () => props.loading,
  set: (value) => emit('update:loading', value)
})

/** 类型切换 */
const handleTypeChange = (type: number) => {
  // 设置加载标志，防止选择变化处理
  isLoadingData.value = true

  selectedType.value = type
  // 重置搜索参数
  searchParams.nickname = ''
  searchParams.mobile = ''
  searchParams.name = ''
  searchParams.pageNo = 1
  // 重新初始化选中状态（保持已选择的数据）
  initializeSelectionState()
  // 加载对应类型的数据
  loadAvailableData()
}

/** 获取类型标签 */
const getTypeLabel = (type: number) => {
  const typeItem = scopeTypes.find((item) => item.value === type)
  return typeItem ? typeItem.label : ''
}

/** 获取类型标签样式 */
const getTypeTagType = (type: number) => {
  return type === ScopeTargetTypeEnum.USER ? 'primary' : 'success'
}

/** 搜索 */
const handleSearch = () => {
  searchParams.pageNo = 1
  // 重新调用API进行搜索
  loadAvailableData()
}

/** 重置搜索 */
const handleSearchReset = () => {
  // 重置搜索参数
  searchParams.nickname = ''
  searchParams.mobile = ''
  searchParams.email = ''
  searchParams.name = ''
  searchParams.pageNo = 1
  // 重新调用API
  loadAvailableData()
}

/** 可选数据选择变化 */
const handleAvailableSelectionChange = (selection: any[]) => {
  selectedAvailableRows.value = selection

  // 防止在恢复选中状态或加载数据时重复触发
  if (isRestoringSelection.value || isLoadingData.value) {
    return
  }

  // 直接处理选择变化
  if (selectedType.value === ScopeTargetTypeEnum.USER) {
    handleUserSelectionChange(selection)
  } else if (selectedType.value === ScopeTargetTypeEnum.DEPT) {
    handleDeptSelectionChange(selection)
  }
}

/** 处理用户选择变化 */
const handleUserSelectionChange = (selection: any[]) => {
  try {
    const currentSelectedIds = new Set(selection.map((item) => item.id))
    const previousSelectedIds = selectedUserIds.value

    // 找出新增的选择
    const newSelections = selection.filter((item) => !previousSelectedIds.has(item.id))
    // 找出取消的选择 - 只有在当前选择的数据中才算取消
    const removedIds = Array.from(previousSelectedIds).filter((id) => {
      // 只有当前可见的数据中的取消选择才算真正的取消
      const isInCurrentData = availableUserList.value.some((user) => user.id === id)
      return isInCurrentData && !currentSelectedIds.has(id)
    })

    // 更新选中状态
    selectedUserIds.value = currentSelectedIds

    // 添加新选择的用户
    if (newSelections.length > 0) {
      const newScopes = newSelections.map((item) => ({
        id: item.id,
        name: item.nickname || item.username || `用户${item.id}`,
        type: selectedType.value,
        deptName: item.deptName || ''
      }))

      const updatedScopeList = [...scopeList.value, ...newScopes]
      emit('update:modelValue', updatedScopeList)
      emit('confirm', newScopes)

      // 显示成功提示
      if (newSelections.length > 0) {
        ElMessage.success(t('survey.scopeAddedSuccess', { count: newSelections.length }))
      }
    }

    // 移除取消选择的用户
    if (removedIds.length > 0) {
      const newScopeList = scopeList.value.filter(
        (scope) => !(scope.type === ScopeTargetTypeEnum.USER && removedIds.includes(scope.id))
      )

      const removedScopes = scopeList.value.filter(
        (scope) => scope.type === ScopeTargetTypeEnum.USER && removedIds.includes(scope.id)
      )

      emit('update:modelValue', newScopeList)
      emit('delete', removedScopes)

      // 显示成功提示
      if (removedIds.length > 0) {
        ElMessage.success(t('survey.scopeRemovedSuccess', { count: removedIds.length }))
      }
    }
  } catch (error) {
    console.error('Error handling user selection change:', error)
    ElMessage.error(t('survey.selectionError'))
  }
}

/** 检查ID是否在树形数据中 */
const checkIdInTreeData = (treeData: any[], targetId: number): boolean => {
  for (const item of treeData) {
    if (item.id === targetId) {
      return true
    }
    if (item.children && item.children.length > 0) {
      if (checkIdInTreeData(item.children, targetId)) {
        return true
      }
    }
  }
  return false
}

/** 处理部门选择变化 */
const handleDeptSelectionChange = (selection: any[]) => {
  try {
    const currentSelectedIds = new Set(selection.map((item) => item.id))
    const previousSelectedIds = selectedDeptIds.value

    // 找出新增的选择
    const newSelections = selection.filter((item) => !previousSelectedIds.has(item.id))
    // 找出取消的选择 - 只有在当前可见的数据中才算取消
    const removedIds = Array.from(previousSelectedIds).filter((id) => {
      // 递归检查树形数据中是否包含该ID
      const isInCurrentData = checkIdInTreeData(availableDeptList.value, id)
      return isInCurrentData && !currentSelectedIds.has(id)
    })

    // 更新选中状态
    selectedDeptIds.value = currentSelectedIds

    // 添加新选择的部门
    if (newSelections.length > 0) {
      const newScopes = newSelections.map((item) => ({
        id: item.id,
        name: item.name || `部门${item.id}`,
        type: selectedType.value
      }))

      const updatedScopeList = [...scopeList.value, ...newScopes]
      emit('update:modelValue', updatedScopeList)
      emit('confirm', newScopes)

      // 显示成功提示
      if (newSelections.length > 0) {
        ElMessage.success(t('survey.scopeAddedSuccess', { count: newSelections.length }))
      }
    }

    // 移除取消选择的部门
    if (removedIds.length > 0) {
      const newScopeList = scopeList.value.filter(
        (scope) => !(scope.type === ScopeTargetTypeEnum.DEPT && removedIds.includes(scope.id))
      )

      const removedScopes = scopeList.value.filter(
        (scope) => scope.type === ScopeTargetTypeEnum.DEPT && removedIds.includes(scope.id)
      )

      emit('update:modelValue', newScopeList)
      emit('delete', removedScopes)

      // 显示成功提示
      if (removedIds.length > 0) {
        ElMessage.success(t('survey.scopeRemovedSuccess', { count: removedIds.length }))
      }
    }
  } catch (error) {
    console.error('Error handling dept selection change:', error)
    ElMessage.error(t('survey.selectionError'))
  }
}

/** 已选作用域选择变化 */
const handleScopeSelectionChange = (selection: ScopeItem[]) => {
  selectedScopeRows.value = selection
}

/** 初始化选中状态 */
const initializeSelectionState = () => {
  // 从已选作用域中提取选中的ID
  selectedUserIds.value.clear()
  selectedDeptIds.value.clear()

  scopeList.value.forEach((scope) => {
    if (scope.type === ScopeTargetTypeEnum.USER) {
      selectedUserIds.value.add(scope.id)
    } else if (scope.type === ScopeTargetTypeEnum.DEPT) {
      selectedDeptIds.value.add(scope.id)
    }
  })
}

/** 批量删除 */
const handleBatchDelete = () => {
  if (selectedScopeRows.value.length === 0) return

  // 从scopeList中移除选中的项目
  const newScopeList = scopeList.value.filter(
    (scope) =>
      !selectedScopeRows.value.some(
        (selected) => selected.id === scope.id && selected.type === scope.type
      )
  )

  // 更新选中状态
  selectedScopeRows.value.forEach((scope) => {
    if (scope.type === ScopeTargetTypeEnum.USER) {
      selectedUserIds.value.delete(scope.id)
    } else if (scope.type === ScopeTargetTypeEnum.DEPT) {
      selectedDeptIds.value.delete(scope.id)
    }
  })

  emit('update:modelValue', newScopeList)
  emit('delete', deepClone(selectedScopeRows.value))
  selectedScopeRows.value = []
  selectedTableRef.value?.clearSelection()

  // 更新表格选中状态
  nextTick(() => {
    restoreSelectionState()
  })

  ElMessage.success(t('common.deleteSuccess'))
}

/** 删除单个作用域 */
const handleDeleteScope = (scope: ScopeItem, index: number) => {
  // 从scopeList中移除指定项目
  const newScopeList = scopeList.value.filter(
    (item) => !(item.id === scope.id && item.type === scope.type)
  )

  // 更新选中状态
  if (scope.type === ScopeTargetTypeEnum.USER) {
    selectedUserIds.value.delete(scope.id)
  } else if (scope.type === ScopeTargetTypeEnum.DEPT) {
    selectedDeptIds.value.delete(scope.id)
  }

  emit('update:modelValue', newScopeList)
  emit('delete', deepClone([scope]))

  // 更新表格选中状态
  nextTick(() => {
    restoreSelectionState()
  })

  ElMessage.success(t('common.deleteSuccess'))
}

/** 恢复选中状态 */
const restoreSelectionState = () => {
  if (!availableTableRef.value) return

  // 设置恢复状态标志，防止触发选择变化事件
  isRestoringSelection.value = true

  try {
    // 清除当前选择
    availableTableRef.value.clearSelection()

    if (selectedType.value === ScopeTargetTypeEnum.USER) {
      // 恢复用户选中状态
      availableUserList.value.forEach((user) => {
        if (selectedUserIds.value.has(user.id)) {
          availableTableRef.value?.toggleRowSelection(user, true)
        }
      })
    } else if (selectedType.value === ScopeTargetTypeEnum.DEPT) {
      // 恢复部门选中状态（递归处理树形数据）
      const selectTreeNodes = (depts: any[]) => {
        depts.forEach((dept) => {
          if (selectedDeptIds.value.has(dept.id)) {
            availableTableRef.value?.toggleRowSelection(dept, true)
          }
          if (dept.children && dept.children.length > 0) {
            selectTreeNodes(dept.children)
          }
        })
      }
      selectTreeNodes(availableDeptList.value)
    }
  } finally {
    // 恢复完成后重置标志
    nextTick(() => {
      isRestoringSelection.value = false
    })
  }
}

/** 加载可选数据 */
const loadAvailableData = async () => {
  if (selectedType.value === ScopeTargetTypeEnum.USER) {
    await loadAvailableUsers()
  } else if (selectedType.value === ScopeTargetTypeEnum.DEPT) {
    await loadAvailableDepts()
  }
}

/** 加载可选用户数据 */
const loadAvailableUsers = async () => {
  try {
    availableLoading.value = true
    isLoadingData.value = true

    const params = {
      pageNo: searchParams.pageNo,
      pageSize: searchParams.pageSize,
      nickname: searchParams.nickname || undefined,
      mobile: searchParams.mobile || undefined,
      email: searchParams.email || undefined,
      status: 0 // 只查询启用的用户
    }

    const data = await employeeData(params)

    // 不过滤已选择的用户，保留所有用户数据
    availableUserList.value = (data.list || []).map((user) => ({
      id: user.id,
      nickname: user.nickname || user.username || `用户${user.id}`,
      mobile: user.mobile || '',
      email: user.email || '',
      deptName: user.deptName || '',
      status: user.status || 0
    }))

    availableTotal.value = data.total || 0

    // 恢复选中状态
    await nextTick()
    restoreSelectionState()
  } catch (error) {
    console.error('Failed to load user data:', error)
    ElMessage.error(t('survey.loadUserDataError'))
  } finally {
    availableLoading.value = false
    isLoadingData.value = false
  }
}

/** 加载可选部门数据 */
const loadAvailableDepts = async () => {
  try {
    availableLoading.value = true
    isLoadingData.value = true

    // 构建搜索参数
    const params: any = { status: '0' }
    if (searchParams.name) {
      params.name = searchParams.name
    }

    const data = await deptTreeSelect(params)

    // 转换树形数据格式，保持完整的树形结构
    const convertTreeData = (depts: any[]): any[] => {
      return depts.map((dept) => ({
        id: dept.id,
        name: dept.label || `部门${dept.id}`,
        children:
          dept.children && dept.children.length > 0 ? convertTreeData(dept.children) : undefined
      }))
    }

    availableDeptList.value = convertTreeData(data || [])

    // 计算总数（递归计算树形数据的节点数）
    const countTreeNodes = (depts: any[]): number => {
      return depts.reduce((count, dept) => {
        return count + 1 + (dept.children ? countTreeNodes(dept.children) : 0)
      }, 0)
    }

    availableTotal.value = countTreeNodes(availableDeptList.value)

    // 恢复选中状态
    await nextTick()
    restoreSelectionState()
  } catch (error) {
    console.error('Failed to load dept data:', error)
    ElMessage.error(t('survey.loadDeptDataError'))
  } finally {
    availableLoading.value = false
    isLoadingData.value = false
  }
}

// 监听类型变化，重新加载数据
watch(selectedType, async (newType, oldType) => {
  if (newType !== oldType && !isLoadingData.value) {
    // 设置加载标志
    isLoadingData.value = true
    initializeSelectionState()
    await loadAvailableData()
  }
})

// 监听modelValue变化，更新选中状态
watch(
  () => props.modelValue,
  async (newValue, oldValue) => {
    // 避免在初始化时重复处理，并且避免在正常操作时重置状态
    if (newValue !== oldValue && !isLoadingData.value && !isRestoringSelection.value) {
      // 重置选择状态
      selectedScopeRows.value = []
      selectedAvailableRows.value = []
      // 初始化选中状态
      initializeSelectionState()
      // 恢复表格选中状态
      await nextTick()
      restoreSelectionState()
    }
  },
  { deep: true }
)

// 初始化加载数据
onMounted(async () => {
  initializeSelectionState()
  await loadAvailableData()
})
</script>

<style scoped>
.survey-scope-select {
  padding: 0;
}

.scope-type-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.segmented-control {
  display: flex;
  background: #f5f7fa;
  border-radius: 6px;
  padding: 2px;
}

.segmented-item {
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 14px;
  color: #606266;
}

.segmented-item:hover {
  color: #409eff;
}

.segmented-item--active {
  background: #409eff;
  color: white;
}

.search-section {
  margin-bottom: 16px;
}

.search-form {
  margin: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

.scope-table-section {
  min-height: 200px;
}

.empty-state {
  padding: 40px 0;
}

.add-scope-content {
  padding: 0;
}

.available-data-section {
  margin-bottom: 16px;
}

.selected-scope-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

/* 左右布局容器 */
.scope-content-container {
  display: flex;
  gap: 16px;
}

.left-panel {
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 416px); /* 为右侧面板和间距留出空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}

.right-panel {
  width: 400px;
  flex-shrink: 0;
}

.available-data-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.available-data-section :deep(.content-wrap) {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.selected-scope-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.selected-scope-section :deep(.content-wrap) {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 用户表格容器 - 支持横向滚动 */
.user-table-container {
  overflow-x: auto;
  overflow-y: hidden;
  min-width: 0;
  width: 100%;
  max-height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.user-table-container :deep(.el-table) {
  min-width: 575px; /* 确保表格有最小宽度 */
  width: max-content; /* 让表格根据内容确定宽度 */
}

.user-table-container :deep(.el-table__body-wrapper) {
  max-height: 350px; /* 为表头留出空间 */
  overflow-y: auto;
}

.user-table-container :deep(.el-table__header-wrapper) {
  overflow-x: hidden; /* 表头不需要横向滚动 */
}

.pagination-section {
  margin-top: 16px;
  text-align: right;
  flex-shrink: 0;
  padding: 0 4px; /* 避免分页被滚动条遮挡 */
}

.dept-total {
  margin-top: 12px;
  text-align: right;
  color: #909399;
  font-size: 12px;
  flex-shrink: 0;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .scope-content-container {
    flex-direction: column;
    height: auto;
  }

  .right-panel {
    width: 100%;
    margin-top: 16px;
  }

  .left-panel {
    margin-bottom: 16px;
  }
}

/* 参考ScopeSelect的样式 */
.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
}
</style>
