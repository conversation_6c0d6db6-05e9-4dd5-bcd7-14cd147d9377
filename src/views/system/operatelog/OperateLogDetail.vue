<template>
  <Dialog v-model="dialogVisible" :max-height="500" :scroll="true" :title="t('dialog.detail')" width="800">
    <el-descriptions :column="1" border>
      <el-descriptions-item :label="t('log.operaLog.logId')" min-width="120">
        {{ detailData.id }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.traceId')" v-if="detailData.traceId">
        {{ detailData.traceId }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.operatorId')">
        {{ detailData.userId }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.operatorName')">
        {{ detailData.userName }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.actionIP')">
        {{ detailData.userIp }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.operatorUA')">
        {{ detailData.userAgent }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.operationModule')">
        {{ detailData.type }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.operationName')">
        {{ detailData.subType }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.operationContent')">
        {{ detailData.action }}
      </el-descriptions-item>
      <el-descriptions-item v-if="detailData.extra" :label="t('log.operaLog.operatorExt')">
        {{ detailData.extra }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.requestURL')">
        {{ detailData.requestMethod }} {{ detailData.requestUrl }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.operationTime')">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('log.operaLog.businessNo')">
        {{ detailData.bizId }}
      </el-descriptions-item>
    </el-descriptions>
  </Dialog>
</template>
<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'
import * as OperateLogApi from '@/api/system/operatelog'
import { useI18n } from "vue-i18n"

defineOptions({ name: 'SystemOperateLogDetail' })
const { t } = useI18n()

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中
const detailData = ref({} as OperateLogApi.OperateLogVO) // 详情数据

/** 打开弹窗 */
const open = async (data: OperateLogApi.OperateLogVO) => {
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    detailData.value = data
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
