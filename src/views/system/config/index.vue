<script setup name="Config" lang="ts">
import type { ComponentInternalInstance } from 'vue'
import {
  addConfig, ConfigRespVO,
  delConfig,
  exportConfig,
  getConfig,
  listConfig,
  refreshCache,
  updateConfig
} from '@/api/system/config'
import { parseTime } from '@/utils/ruoyi'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import {useI18n} from "vue-i18n";
import download from "@/utils/download";
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const configList = ref<ConfigRespVO[]>([])
const open = ref(false)
const loading = ref(true)
const formLoading = ref(false)
const showSearch = ref(true)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
// const dateRange = ref<any>([])

const queryRef = ref()
const configRef = ref()
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    configName: '',
    configKey: '',
    configType: '',
    dateRange: []
  },
  rules: {
    configName: [{ required: true, message: t('sys.config.configNameRule'), trigger: 'blur' }],
    configKey: [{ required: true, message: t('sys.config.configKeyRule'), trigger: 'blur' }],
    configValue: [{ required: true, message: t('sys.config.configValueRule'), trigger: 'blur' }],
  },
})

const { queryParams, form, rules } = toRefs(data)

/** 查询参数列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await listConfig(queryParams.value)
    configList.value = data.list
    total.value = data.total
  } finally {
  loading.value = false
  }
}
/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}
/** 表单重置 */
const reset = () => {
  form.value = {
    configId: undefined,
    configName: undefined,
    configKey: undefined,
    configValue: undefined,
    configType: 'Y',
    remark: undefined,
  }
  configRef.value?.resetFields()
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
/** 多选框选中数据 */
const handleSelectionChange = (selection: ConfigRespVO[]) => {
  ids.value = selection.map(item => item.configId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}
/** 新增按钮操作 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = t('sys.config.addConfig')
}
/** 修改按钮操作 */
const handleUpdate = async (row: ConfigRespVO) => {
  reset()
  const configId = row.configId || ids.value
  form.value = await getConfig(configId as number)
  open.value = true
  title.value = t('sys.config.editConfig')
}
/** 提交按钮 */
const submitForm = async () => {
  const valid = await configRef.value.validate()
  if (!valid) return
  try {
    formLoading.value = true
    if (form.value.configId !== undefined) {
      await updateConfig(form.value)
      message.success(t('common.updateSuccess'))
    } else {
      await addConfig(form.value)
      message.success(t('common.createSuccess'))
    }
    open.value = false
    await getList()
  } finally {
    formLoading.value = false
  }
}
/** 删除按钮操作 */
const handleDelete = async (row: ConfigRespVO) => {
  const configIds = row.configId || ids.value
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await delConfig(configIds)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    const data = await exportConfig(queryParams.value)
    download.excel(data, `config_${new Date().getTime()}.xlsx`)
  } finally {}
}
/** 刷新缓存按钮操作 */
const handleRefreshCache = async () => {
  await refreshCache()
  message.success(t('common.delSuccess'))
}
/** 初始化 */
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="app-container">
    <ContentWrap>
      <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" label-width="120px">
        <el-form-item :label="t('sys.config.configName')" prop="configName">
          <el-input
            v-model="queryParams.configName"
            :placeholder="t('sys.config.configNamePH')"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="t('sys.config.configKey')" prop="configKey">
          <el-input
            v-model="queryParams.configKey"
            :placeholder="t('sys.config.configKeyPH')"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item :label="t('sys.config.builtInSystem')" prop="configType">
          <el-select v-model="queryParams.configType" :placeholder="t('sys.config.builtInSystem')" style="width: 240px" clearable>
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_YES_NO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('sys.company.createTime')" prop="dateRange" style="width: 308px;">
          <el-date-picker
            v-model="queryParams.dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            :start-placeholder="t('global.startDate')"
            :end-placeholder="t('global.endDate')"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            v-hasPermi="['system:config:add']"
            type="primary"
            plain
            @click="handleAdd"
          >
            <Icon class="mr-5px" icon="ep:plus" />
            {{ t('action.add') }}
          </el-button>
          <el-button
            v-hasPermi="['system:config:edit']"
            type="success"
            plain
            :disabled="single"
            @click="handleUpdate"
          >
            <Icon class="mr-5px" icon="ep:edit" />
            {{ t('action.edit') }}
          </el-button>
          <el-button
            v-hasPermi="['system:config:remove']"
            type="danger"
            plain
            :disabled="multiple"
            @click="handleDelete"
          >
            <Icon class="mr-5px" icon="ep:delete" />
            {{ t('action.delete') }}
          </el-button>
          <el-button
            v-hasPermi="['system:config:export']"
            type="warning"
            plain
            @click="handleExport"
          >
            <Icon class="mr-5px" icon="ep:download" />
            {{ t('action.export') }}
          </el-button>
          <el-button
            v-hasPermi="['system:config:remove']"
            type="danger"
            plain
            @click="handleRefreshCache"
          >
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.refreshConfigCache') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :label="t('sys.config.configId')" align="center" prop="configId" />
        <el-table-column :label="t('sys.config.configName')" align="center" prop="configName" :show-overflow-tooltip="true" />
        <el-table-column :label="t('sys.config.configKey')" align="center" prop="configKey" :show-overflow-tooltip="true" />
        <el-table-column :label="t('sys.config.configValue')" align="center" prop="configValue" :show-overflow-tooltip="true" />
        <el-table-column :label="t('sys.config.builtInSystem')" align="center" prop="configType">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_YES_NO" :value="scope.row.configType" />
          </template>
        </el-table-column>
        <el-table-column :label="t('sys.config.remark')" align="center" prop="remark" :show-overflow-tooltip="true" />
        <el-table-column :label="t('sys.company.createTime')" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('global.action')" align="center" width="150" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button v-hasPermi="['system:config:edit']" link type="primary" @click="handleUpdate(scope.row)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <el-button v-hasPermi="['system:config:remove']" link type="primary" @click="handleDelete(scope.row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </ContentWrap>

    <!-- 添加或修改参数配置对话框 -->
    <Dialog v-model="open" :title="title">
      <el-form ref="configRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="t('sys.config.configName')" prop="configName">
          <el-input v-model="form.configName" :placeholder="t('sys.config.configNamePH')" />
        </el-form-item>
        <el-form-item :label="t('sys.config.configKey')" prop="configKey">
          <el-input v-model="form.configKey" :placeholder="t('sys.config.configKeyPH')" />
        </el-form-item>
        <el-form-item :label="t('sys.config.configValue')" prop="configValue">
          <el-input v-model="form.configValue" :placeholder="t('sys.config.configValuePH')" />
        </el-form-item>
        <el-form-item :label="t('sys.config.builtInSystem')" prop="configType">
          <el-radio-group v-model="form.configType">
            <el-radio
              v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_YES_NO)"
              :key="dict.value"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('sys.config.remark')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="t('sys.config.remarkPH')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="formLoading" @click="submitForm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button @click="cancel">
            {{ t('global.cancel') }}
          </el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>
