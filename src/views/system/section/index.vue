<script setup name="Section" lang="ts">
import type { ComponentInternalInstance } from 'vue'
import {
  addSection,
  delSection,
  getSection,
  getSectionOption,
  listSectExcludeChild,
  listSection,
  SectionResqVO, SectionStatusEnum,
  updateSection
} from '@/api/system/section'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import { parseTime } from '@/utils/ruoyi'
import {DepartmentTreeRespVO, deptTreeSelect} from '@/api/system/user'
import type { ElTree } from 'element-plus'
import { OrgType } from '@/enums/OrgType'
import { checkPermi } from '@/utils/permission'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { handlePhaseTree } from '@/utils/tree'
import {DeptRespVO} from "@/api/system/dept";
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const sectionList = ref<any[]>([])
const sectionOptions = ref<any[]>([])
const open = ref(false)
const loading = ref(true)
const formLoading = ref(false)
const showSearch = ref(true)
const ids = ref<number[]>([])
const sectionTotal = ref(0)
const title = ref('')
const queryRef = ref()
const sectionRef = ref()
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {
    sectId: undefined,
    sectCode: undefined,
    sectName: undefined,
    orderNum: 0,
    status: SectionStatusEnum.ENABLE,
  },
  queryParams: {
    sectCode: undefined,
    sectName: undefined,
    status: undefined,
    parentId: undefined,
    deptId: undefined,
    deptName: undefined,
    serviceCompanyId: undefined,
  },
  rules: {
    sectName: [{ required: true, message: t('sys.section.sectionNameRule'), trigger: 'blur' }],
    sectCode: [{ required: false, message: t('sys.section.sectionCodeRule'), trigger: 'blur' }],
    orderNum: [{ required: true, message: t('sys.section.orderNumRule'), trigger: 'blur' }],
  },
})

const { queryParams, form, rules } = toRefs(data)
const deptName = ref('')
const deptOptions = ref<any[]>([])
const deptTreeRef = ref<InstanceType<typeof ElTree>>()
/** 当前选中的部门信息 */
const selDept = ref()
const defaultExpand = ref()
/** 左侧-公司部门下拉树结构 */
const getDeptTree = async () => {
  try {
    const data = await deptTreeSelect({ type: 0 })
    deptOptions.value = data
    selDept.value = deptOptions.value[0]
    queryParams.value.compId = deptOptions.value[0].id
    const firstOption = deptOptions.value[0]
    defaultExpand.value = [deptOptions.value[0].id, firstOption.level === OrgType.Company && firstOption.children ? deptOptions.value[0].children[0].id : undefined]
    nextTick(() => {
      deptTreeRef.value?.setCurrentNode(deptOptions.value[0])
    })
    getList()
  } catch (e) {}
};
/** 左侧-节点触发事件 */
const handleNodeClick = (node: any) => {
  selDept.value = node
  if (node.level === OrgType.Company) {
    queryParams.value.compId = node.id
    queryParams.value.deptId = null
    queryParams.value.deptName = null
  }
  if (node.level === OrgType.Department) {
    queryParams.value.deptId = node.id
    queryParams.value.compId = null
    queryParams.value.deptName = node.label
  }
  getList()
}
/** 左侧-通过条件过滤节点  */

const filterNode = (value: any, data: DepartmentTreeRespVO) => {
  if (!value)
    return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 左侧-根据名称筛选部门树 */
watch(deptName, (val) => {
  deptTreeRef.value!.filter(val)
})
/** 右侧-查询Section列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await listSection(queryParams.value)
    sectionList.value = handlePhaseTree(data, 'sectId')
    sectionTotal.value = data.length
  } finally {
    loading.value = false
  }
}
/** 右侧-重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
/** 右侧-搜索按钮操作 */
const handleQuery = () => {
  getList()
}
/** 添加编辑-取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}
/** 添加编辑-表单重置 */
const reset = () => {
  form.value = {
    sectId: undefined,
    sectCode: undefined,
    sectName: undefined,
    orderNum: 0,
    status: SectionStatusEnum.ENABLE,
  }
  sectionRef.value?.resetFields()
}

/** 添加按钮操作 */
const handleAdd = async () => {
  reset()
  open.value = true
  form.value.deptId = queryParams.value.deptId
  form.value.deptName = queryParams.value.deptName
  title.value = t('sys.section.addSection')
  const data = await listSection(queryParams.value)
  sectionOptions.value = handlePhaseTree(data, 'deptId')
}
/** 编辑按钮操作 */
const handleUpdate = async (row: SectionResqVO) => {
  reset()
  const data = await listSectExcludeChild(row.deptId, row.sectId)
  sectionOptions.value = handlePhaseTree(data, 'sectId')
  const sectionInfo = await getSection(row.sectId)
  form.value = sectionInfo
  form.value.deptName = queryParams.value.deptName
  open.value = true
  title.value = t('sys.section.editSection')
  if (form.value.parentId === 0) {
    form.value.parentId = null
  }
}
/** 添加编辑-提交按钮 */
const submitForm = async () => {
  const valid = await sectionRef.value.validate()
  if (!valid) return
  try {
    formLoading.value = true
    if (form.value.sectId !== undefined) {
      await updateSection(form.value)
      message.success(t('common.updateSuccess'))
    } else {
      await addSection(form.value)
      message.success(t('common.createSuccess'))
    }
    open.value = false
    await getList()
  } finally {
    formLoading.value = false
  }
}
/** 删除按钮操作 */
const handleDelete = async (row: SectionResqVO) => {
  const sectIds = row.sectId || ids.value
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await delSection(sectIds)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
/** 初始化 */
onMounted(() => {
  getDeptTree()
})
</script>

<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <Pane :size="18" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <div class="head-container">
            <el-input
              v-model="deptName"
              :placeholder="t('sys.user.deptNamePH')"
              clearable
              class="mb-[20px]"
              @keydown.enter="handleNodeClick"
            >
              <template #prefix>
                <Icon class="mr-5px" icon="ep:search" />
              </template>
            </el-input>
          </div>
          <div class="head-container">
            <el-tree
              ref="deptTreeRef"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :default-expanded-keys="defaultExpand"
              :filter-node-method="filterNode"
              node-key="id"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <div class="flex justify-between">
                  <div class="me-2.5">
                    <el-tag
                      v-if="node.data.level === OrgType.Company " :title="node.data.shortName"
                      :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0',
                      }"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Company' }}
                    </el-tag>
                    <el-tag
                      v-else
                      :title="node.data.shortName"
                    >
                      {{ node.data.shortName }}
                    </el-tag>
                  </div>
                  <span
                    :title="node.data.label" class="whitespace-normal line-clamp-1 break-all"
                  > {{ node.data.label }}</span>
                </div>
              </template>
            </el-tree>
          </div>
        </el-scrollbar>
      </Pane>
      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height" height="calc(100vh)">
          <div>
            <ContentWrap>
              <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" @submit.prevent class="-mb-15px">
                <el-form-item :label="t('sys.section.sectionName')" prop="sectName">
                  <el-input
                    v-model="queryParams.sectName"
                    :placeholder="t('sys.section.sectionNamePH')"
                    clearable
                    class="!w-200px"
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleQuery">
                    <Icon class="mr-5px" icon="ep:search" />
                    {{ t('action.search') }}
                  </el-button>
                  <el-button @click="resetQuery">
                    <Icon class="mr-5px" icon="ep:refresh" />
                    {{ t('action.reset') }}
                  </el-button>
                </el-form-item>
              </el-form>
            </ContentWrap>
            <div class="pt-3 flex items-center">
              <div>
                <el-tag
                  v-if="selDept?.level === OrgType.Company" :style="{
                    '--el-tag-text-color': '#630EB8',
                    '--el-tag-bg-color': '#F3F1FF',
                    '--el-tag-border-color': '#D3CEF0',
                  }"
                >
                  {{ t("global.company") }}
                </el-tag>
                <el-tag v-else>
                  {{ t('sys.user.department') }}
                </el-tag>
                <span class="ms-2.5">{{ selDept?.label }}</span>
              </div>
              <div class="ms-auto">
                <el-button
                  v-show="selDept?.level !== OrgType.Company"
                  type="primary"
                  plain
                  @click="handleAdd"
                >
                  <Icon class="mr-5px" icon="ep:plus" />
                  {{ t('action.add') }}
                </el-button>
              </div>
            </div>
            <OrgTotalBar :number="sectionTotal" :text="t('hr.section.totalTip')" class="mt-3" />
            <ContentWrap>
              <el-table
                v-loading="loading" :data="sectionList"
                row-key="sectId"
                default-expand-all
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              >
                <el-table-column :label="t('sys.section.sectionName')" prop="sectName" min-width="420" fixed="left" />
                <el-table-column :label="t('sys.company.abbreviation')" prop="shortName" width="140" />
                <el-table-column :label="t('sys.company.uniqueCode')" prop="sectCode" width="200" />
                <el-table-column prop="status" :label="t('sys.company.status')" width="200">
                  <template #default="{ row }">
                    <dict-tag :type="DICT_TYPE.SYSTEM_NORMAL_DISABLE" :value="row.status" />
                  </template>
                </el-table-column>
                <el-table-column :label="t('sys.company.createTime')" align="center" prop="createTime" width="180">
                  <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="dataSource" align="center" :label="t('sys.company.dataSource')" width="110" />
<!--                <el-table-column prop="createBy" align="center" :label="t('sys.company.creator')" width="110" />-->
<!--                暂时先注释,一期允许编辑和删除,二期需求是MDS数据不允许编辑和删除,默认新增的时候都是MDS数据!!!  -->
<!--                <el-table-column-->
<!--                  v-if="(selDept?.level !== OrgType.Company) && (checkPermi(['system:sect:update']) || checkPermi(['system:sect:delete']))"-->
<!--                  :label="t('global.action')" min-width="180" align="center" class-name="small-padding fixed-width" fixed="right"-->
<!--                >-->
<!--                  <template #default="scope">-->
<!--                    <el-button v-hasPermi="['system:sect:update']" link type="primary" @click="handleUpdate(scope.row)">-->
<!--                      <Icon icon="ep:edit" />-->
<!--                      {{ t('action.edit') }}-->
<!--                    </el-button>-->
<!--                    <el-button v-hasPermi="['system:sect:delete']" link type="primary" @click="handleDelete(scope.row)">-->
<!--                      <Icon icon="ep:delete" />-->
<!--                      {{ t('action.delete') }}-->
<!--                    </el-button>-->
<!--                  </template>-->
<!--                </el-table-column>-->
              </el-table>
            </ContentWrap>
          </div>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <!-- 添加或修改Section对话框 -->
    <Dialog v-model="open" :title="title">
      <el-form ref="sectionRef" :model="form" :rules="rules" label-width="150px" label-position="left">
        <el-form-item :label="t('sys.dept.deptName')">
          <el-input v-model="form.deptName" :placeholder="t('sys.dept.deptNamePH')" disabled />
        </el-form-item>
        <el-form-item :label="t('sys.section.superiorSection')" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="sectionOptions"
            :props="{ value: 'sectId', label: 'sectName', children: 'children' }"
            value-key="sectId"
            :placeholder="t('sys.section.superiorSectionRule')"
            check-strictly
          />
        </el-form-item>
        <el-form-item :label="t('sys.section.sectionName')" prop="sectName">
          <el-input v-model="form.sectName" :placeholder="t('sys.section.sectionNamePH')" />
        </el-form-item>
        <el-form-item :label="t('sys.section.sectionSort')" prop="orderNum">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item :label="t('sys.company.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_NORMAL_DISABLE)"
              :key="dict.value"
              :value="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('sys.section.sectionCode')" prop="sectCode">
          <el-input v-model="form.sectCode" :placeholder="t('sys.section.sectionCodePH')" :disabled="form.dataSource === 'MDS' ? true : form.dataSource === 'IMPORT' ? true : false" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="formLoading" @click="submitForm">
            {{ t('global.confirm') }}
          </el-button>
          <el-button @click="cancel">
            {{ t('global.cancel') }}
          </el-button>
        </div>
      </template>
    </Dialog>
  </div>
</template>
