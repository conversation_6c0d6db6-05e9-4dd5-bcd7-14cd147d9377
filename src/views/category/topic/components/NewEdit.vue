<script setup lang="ts" name="newEdit">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import ImageUpload from '@/components/ImageUpload/index.vue'
import { addTopic, listTopic, TopicRespVO, updateTopic } from '@/api/category/topic'
import { handlePhaseTree } from '@/utils/tree'
const props = defineProps<{ titleName: string, modelValue: boolean, formData: object }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'parentEmit'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const labelPosition = ref<FormProps['labelPosition']>('top')
const formRef = ref<FormInstance>()
const formLoading = ref(false)
const subjectList = ref()
const data = reactive<{
  rules: any
}>({
  rules: {
    name: [{ required: true, message: t('category.topic.subjectNameRule'), trigger: 'change' }],
    keywords: [
      {
        required: false,
        message: t('category.topic.keyWordsRule'),
        trigger: 'blur',
      },
    ],
    introduction: [
      {
        required: false,
        message: t('category.topic.introductionRule'),
        trigger: 'change',
      },
    ],
  },
})

const { rules } = toRefs(data)
const form = ref({
  name: '',
  keywords: [],
  sort: 1,
  parentId: '',
  cover: '',
  introduction: '',
})
const isVisible = ref(false)
const inputValue = ref('')
const inputVisible = ref()
const InputRef = ref()
/** 表单重置 */
const reset = () => {
  formRef.value?.resetFields()
}
const handleOpen = () => {
  getList()
  form.value = {
    sort: 1,
    keywords: [],
  }
  isVisible.value = true
}
const handleEdit = (row: TopicRespVO) => {
  getList()

  form.value = { ...row, parentId: +row.parentId === 0 ? '' : +row.parentId, sort: row.sort ? row.sort : 1, keywords: row.keywords ? row.keywords.split(',') : [] }
  isVisible.value = true
  // if (row.id) {
  //   form.value.parentId = row.id
  // }
}
const handleClose = () => {
  isVisible.value = false
}

const getList = async () => {
  const data = await listTopic({})
  data.forEach((element) => {
    element.value = element.id
    element.label = element.name
  })
  subjectList.value = handlePhaseTree(data, 'id')
}
/** 提交按钮 */
const handleConfirm = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  try {
    formLoading.value = true
    const keywords = form.value.keywords.join(',')
    form.value.parentId = form.value.parentId && form.value.parentId !== '' ? form.value.parentId : 0
    if (form.value.id !== undefined) {
      await updateTopic({ ...form.value, keywords })
      message.success(t('common.updateSuccess'))
      emit('parentEmit')
    } else {
      await addTopic({ ...form.value, keywords })
      message.success(t('common.createSuccess'))
      emit('parentEmit')
    }
    isVisible.value = false
  } finally {
    formLoading.value = false
  }
}
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value) {
    form.value.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCloseTag = (tag: string) => {
  form.value.keywords.splice(form.value.keywords.indexOf(tag), 1)
}

defineExpose({ handleOpen, handleEdit })
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" @close="handleClose">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-dynamic" :label-position="labelPosition" :rules="rules">
      <el-form-item v-if="form.id && !form.parentId && props.titleName === t('category.topic.addTopic')" prop="parentId" :label="t('category.topic.parentSubject0')">
        <el-tree-select
          v-model="form.parentId" :data="subjectList" check-strictly :render-after-expand="false" clearable
        />
      </el-form-item>
      <el-form-item v-else-if="+form.parentId && props.titleName === t('category.topic.editTopic')" prop="parentId" :label="t('category.topic.parentSubject2')">
        <el-tree-select
          v-model="form.parentId" :data="subjectList" check-strictly :render-after-expand="false" clearable
          :disabled="props.titleName === t('category.topic.editTopic')"
        />
      </el-form-item>
      <el-form-item prop="name" :label="t('category.topic.subjectName')">
        <el-input v-model="form.name" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item prop="keywords" :label="t('category.topic.keyWords')">
        <div>
          <div class="flex flex-wrap gap-2">
            <el-tag v-for="tag in form.keywords" :key="tag" closable :disable-transitions="false" @close="handleCloseTag(tag)">
              {{ tag }}
            </el-tag>
            <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" class="!w-60" size="small" maxlength="50" show-word-limit @keyup.enter="handleInputConfirm" @blur="handleInputConfirm" />
            <el-button v-else-if="form.keywords && form.keywords.length < 10" class="button-new-tag" size="small" @click="showInput">
              {{ t('action.addKeyWord') }}
            </el-button>
          </div>
          <!-- <p class="text-[#878787]">
            Enter save tag
          </p> -->
        </div>
      </el-form-item>
      <el-form-item prop="sort" :label="t('category.topic.sort')">
        <el-input-number v-model="form.sort" :min="1" controls-position="right" />
      </el-form-item>
      <el-form-item :label="t('category.topic.cover')">
        <ImageUpload v-model="form.cover" :limit="1" :is-show-tip="true" :file-size="500" :tip-text="t('category.topic.coverPH')" :upload-module="1" />
      </el-form-item>
      <el-form-item prop="introduction" :label="t('category.topic.introduction')">
        <el-input v-model="form.introduction" type="textarea" show-word-limit maxlength="5000" :rows="4" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="formLoading" @click="handleConfirm">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="handleClose">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>
