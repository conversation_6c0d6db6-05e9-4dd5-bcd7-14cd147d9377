<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { ElDialog, type FormInstance } from 'element-plus'
import {
  EditCategory,
  addCategory,
  deleteCategory,
  getCategory,
  OnboardingCategoryRespVO, OnboardingCategoryReqVO
} from '@/api/category/onboarding'
import { dateFormatter } from '@/utils/formatTime'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const tableList = ref<OnboardingCategoryRespVO[]>([])
const total = ref(0)
const title = ref()
const queryParams = ref<OnboardingCategoryReqVO>({
  pageNo: 1,
  pageSize: 10,
  title: undefined
})
const addDialog = ref(false)
const formLoading = ref(false)
const formRef = ref<FormInstance>()
const dialogRef = ref<FormInstance>()
const dialogForm = ref({
  id: undefined,
  title: '',
  sort: undefined
})
const loading = ref(false)
const rules = ref({
  title: [{ required: true, message: t('category.journey.titleRule'), trigger: 'blur' }],
  sort: [{ required: true, message: t('category.journey.sortRule'), trigger: 'blur' }]
})
/** 获取列表 */
const getCategoryList = async () => {
  try {
    loading.value = true
    const data = await getCategory(queryParams.value)
    tableList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 搜索 */
const handleSearch = () => {
  getCategoryList()
}
/** 重置搜索 */
const handleReset = () => {
  formRef.value?.resetFields()
  handleSearch()
}
/** 点击添加弹窗 */
const handleAdd = () => {
  addDialog.value = true
  title.value = t('category.journey.addCategory')
  dialogRef.value?.resetFields()
  dialogForm.value.id = undefined
}
/** 编辑弹窗 */
const handleEdit = async (row: OnboardingCategoryRespVO) => {
  addDialog.value = true
  title.value = t('category.journey.editCategory')
  dialogRef.value?.resetFields()
  dialogForm.value.id = undefined
  dialogForm.value = row
}
/** 添加/编辑提交 */
const handleSubmit = async () => {
  if (!dialogRef.value) return
  const valid = await dialogRef.value.validate()
  if (!valid) return
  try {
    formLoading.value = true
    if (dialogForm.value.id) {
      await EditCategory(dialogForm.value)
      message.success(t('common.updateSuccess'))
    } else {
      await addCategory(dialogForm.value)
      message.success(t('common.createSuccess'))
    }
    addDialog.value = false
    await getCategoryList()
  } finally {
    formLoading.value = false
  }
}
/** 删除 */
const handleDelete = async (event: OnboardingCategoryRespVO) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    await deleteCategory(event.id)
    message.success(t('common.delSuccess'))
    getCategoryList()
  } catch {}
}
/** 初始化 */
onMounted(() => {
  getCategoryList()
})
</script>

<template>
  <ContentWrap>
    <el-form ref="formRef" :model="queryParams" @submit.prevent class="-mb-15px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('category.journey.categoryTitle')" prop="title">
            <el-input v-model="queryParams.title" :placeholder="t('category.journey.categoryTitlePH')" clearable @keyup.enter="handleSearch" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon class="mr-5px" icon="ep:search" />
              {{ t('action.search') }}
            </el-button>
            <el-button @click="handleReset">
              <Icon class="mr-5px" icon="ep:refresh" />
              {{ t('action.reset') }}
            </el-button>
            <el-button plain type="primary" @click="handleAdd">
              <Icon class="mr-5px" icon="ep:plus" />
              {{ t('action.add') }}
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" :data="tableList">
      <el-table-column :label="t('category.journey.title')" prop="title" />
      <el-table-column :label="t('category.journey.sort')" prop="sort" />
      <el-table-column :label="t('category.journey.creator')" prop="createBy" />
      <el-table-column :label="t('category.journey.creationTime')" :formatter="dateFormatter" prop="createTime" />
      <el-table-column :label="t('global.action')" :width="400" align="center" fixed="right">
        <template #default="{ row }">
          <el-button text type="primary" @click="handleEdit(row)">
            <Icon icon="ep:edit" />
            {{ t('action.edit') }}
          </el-button>
          <el-button text type="primary" @click="handleDelete(row)">
            <Icon icon="ep:delete" />
            {{ t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getCategoryList" />
  </ContentWrap>
  <Dialog v-model="addDialog" :title="title">
    <el-form ref="dialogRef" :model="dialogForm" :rules="rules" label-position="top">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('category.journey.categoryTitle')" prop="title">
            <el-input v-model="dialogForm.title" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('category.journey.sort')" prop="sort">
            <el-input-number v-model="dialogForm.sort" type="number" :min="0" :max="9999" clearable controls-position="right" style="width: 240px" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="formLoading" @click="handleSubmit">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="addDialog = false">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>

<style scoped lang="scss"></style>
