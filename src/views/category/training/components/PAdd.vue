<script setup lang="ts" name="newEdit">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import { addTopic, listTopic, updateTopic, TopicRespVO } from '@/api/category/training'
import { defaultProps, handleTree } from '@/utils/tree'
const props = defineProps<{ titleName: string, modelValue: boolean, formData: object }>()
const emit = defineEmits(['update:isRowVisible', 'update:modelValue', 'parentEmit'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const labelPosition = ref<FormProps['labelPosition']>('top')
const formRef = ref<FormInstance>()
const subjectList = ref()
const data = reactive<{
  rules: any
}>({
  rules: {
    name: [{ required: true, message: t('category.topic.subjectNameRule'), trigger: 'change' }],
    ddt: [{ required: true, message: t('sys.user.statusRule'), trigger: 'change' }],
    sort: [{ required: false, message: t('category.topic.sortRule'), trigger: 'blur' }],
  },
})

const { rules } = toRefs(data)
const form = ref()
const formLoading = ref(false)
const isRowVisible = ref(false)
const inputValue = ref('')
const inputVisible = ref()
const InputRef = ref()
/** 表单重置 */
const reset = () => {
  formRef.value?.resetFields()
}
const handleOpen = () => {
  getList()
  form.value = {
    sort: 1,
  }
  isRowVisible.value = true
}
const handleEdit = (row: TopicRespVO) => {
  getList()
  form.value = { parentId: +row.parentId === 0 ? '' : +row.parentId, sort: row.sort ? row.sort : 1, level: row.level ? row.level + 1 : 1 }
  isRowVisible.value = true
  if (row.id) {
    form.value.parentId = row.id
  }
}
const handleClose = () => {
  isRowVisible.value = false
}

const getList = async () => {
  const data = await listTopic({})
  subjectList.value = handleTree(data)
}

/** 提交按钮 */
const handleConfirm = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  try {
    formLoading.value = true
    form.value.parentId = form.value.parentId && form.value.parentId !== '' ? form.value.parentId : 0
    await addTopic({ ...form.value })
    message.success(t('common.createSuccess'))
    emit('parentEmit')
    isRowVisible.value = false
  } finally {
    formLoading.value = false
  }
}
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value) {
    form.value.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCloseTag = (tag: string) => {
  form.value.keywords.splice(form.value.keywords.indexOf(tag), 1)
}

defineExpose({ handleOpen, handleEdit })
</script>

<template>
  <Dialog v-model="isRowVisible" :title="props.titleName" @close="handleClose">
    <el-form ref="formRef" :model="form" label-width="auto" class="demo-dynamic" :label-position="labelPosition" :rules="rules">
      <el-form-item prop="parentId" :label="t('category.topic.parentSubject')">
        <el-tree-select
          v-model="form.parentId"
          :data="subjectList"
          :props="defaultProps"
          filterable
          clearable
          check-strictly
          default-expand-all
          value-key="deptId"
          class="!w-240px"
          disabled
        />
      </el-form-item>
      <el-form-item prop="name" :label="t('category.topic.subjectName')">
        <el-input v-model="form.name" maxlength="50" show-word-limit />
      </el-form-item>
      <el-form-item prop="sort" label="Sort">
        <el-input-number v-model="form.sort" :min="1" :max="99" controls-position="right" />
      </el-form-item>
      <el-form-item label="Status" prop="ddt">
        <el-radio-group v-model="form.ddt">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_CATEGORY_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="description" :label="t('category.topic.introduction')">
        <el-input v-model="form.description" type="textarea" show-word-limit maxlength="5000" :rows="4" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="formLoading" @click="handleConfirm">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="handleClose">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>
