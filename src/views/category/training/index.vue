<script setup lang="ts" name="courseIndex">
import type { ComponentInternalInstance } from 'vue'
import {delTopic, listTopic, TopicRespVO} from '@/api/category/training'
import { getConfigKey } from '@/api/system/config'
import { handleTree } from '@/utils/tree'
import NewEdit from './components/NewEdit.vue'
import PAdd from './components/PAdd.vue'
import { dateFormatter } from '@/utils/formatTime'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const isVisible = ref<boolean>(false)
const isRowVisible = ref<boolean>(false)
const title = ref('')
const tableData = ref<Array<TopicRespVO>>([])
const total = ref(0)
const queryRef = ref()
const paramsObj = reactive<{
  name: string
  keywords: string
  introduction: string
}>({
  name: '',
  keywords: '',
  introduction: '',
})
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    name: undefined,
  },
})
const { queryParams } = toRefs(data)
const RefChild = ref()
const RefChildRowAdd = ref()
// const deptList = ref()
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    name: undefined,
  }
  queryRef.value?.resetFields()
}
const handleAdd = () => {
  title.value = t('category.topic.addTopic')
  RefChild.value.handleOpen()
}
const handleEdit = (row: any) => {
  title.value = t('category.topic.editTopic')
  RefChild.value.handleEdit(row)
}
const handleAddWithRow = (row: any) => {
  title.value = t('category.topic.addTopic')
  const newRow = { parentId: 0, id: null,level: 0 }
  newRow.id = row.id
  newRow.level = row.level
  if (row.id) {
    RefChildRowAdd.value.handleEdit(newRow)
  }
}

const handleDelete = async (row: any) => {
  const noticeIds = row.id
  try {
    // 删除的二次确认
    await message.delConfirm()
    loading.value = true
    // 发起删除
    await delTopic(noticeIds)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } finally {
    loading.value = false
  }
}
const getList = async () => {
  loading.value = true
  try {
    const data = await listTopic(queryParams.value)
    tableData.value = handleTree(data)
  } finally {
    loading.value = false
  }
}
const formatKeywords = (keywords: string) => {
  if (!keywords)
    return
  return keywords
    .split(',')
}
/** 初始化 */
onMounted(() => {
  getList()
})
</script>

<template>
  <ContentWrap>
    <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px" @submit.prevent>
      <el-form-item :label="t('category.topic.subjectName')">
        <el-input v-model="queryParams.name" :placeholder="t('category.topic.subjectNamePH')" clearable class="!w-240px" @keydown.enter="handleSearch" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('action.search') }}
        </el-button>
        <el-button type="default" @click="handleReset">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('action.reset') }}
        </el-button>
        <el-button plain type="primary" @click="handleAdd">
          <Icon class="mr-5px" icon="ep:plus" />
          {{ t('action.add') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" :data="tableData" row-key="id">
      <el-table-column prop="name" :label="t('category.topic.subjectName')" min-width="400" fixed="left" />
      <el-table-column prop="description" :label="t('category.topic.introduction')" min-width="180" />
      <el-table-column prop="sort" :label="t('category.topic.sort')" min-width="180" />
      <el-table-column prop="createTime" :label="t('category.topic.creationTime')" min-width="180" :formatter="dateFormatter"/>
      <el-table-column fixed="right" :label="t('global.action')" align="center" min-width="240">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">
            <Icon icon="ep:edit" />
            {{ t('action.edit') }}
          </el-button>
          <el-button link type="primary" @click="handleAddWithRow(row)">
            <Icon icon="ep:plus" />
            {{ t('action.add') }}
          </el-button>
          <el-button link type="primary" @click="handleDelete(row)">
            <Icon icon="ep:delete" />
            {{ t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="relative">
      <div class="absolute right-0 top-2">
        <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
      </div>
    </div>
  </ContentWrap>
  <NewEdit ref="RefChild" :title-name="title" :form-data="paramsObj" @parent-emit="getList" />
  <PAdd ref="RefChildRowAdd" :title-name="title" :form-data="paramsObj" @parent-emit="getList" />
</template>

<style scoped lang="scss"></style>
