<script setup lang='ts' name="Resource-list">
import type { ComponentInternalInstance } from 'vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { omit, toUpper } from 'lodash-es'
import type { TableInstance } from 'element-plus'
import BatchUpload from './components/BatchUpload/index.vue'
import ResourceManagement from './components/ResourceManagement/index.vue'
import ScormParsingList from './components/ScormParsingList/index.vue'
import Records from './components/Records/index.vue'
import { useHeight } from '@/hooks/useHeight/index'
import ResourcePreview from '@/components/ResourcePreview/index.vue'
import {
  changeResourceStatus,
  delResource,
  getResource,
  listResource,
  getAiccResource,
  ResourceReqVO,
  ResourceRespVO
} from '@/api/resource/list'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { formatBytes, formatSecond } from '@/utils/ruoyi'
import { setupManualClosePage, setupManualRefreshPage } from '@/utils/tag'
import { MediaType } from '@/enums/resource'
// import { getConfigKey } from "@/api/system/config"
import tab from '@/plugins/tab'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import * as ConfigApi from '@/api/infra/config'
import { dateFormatter } from '@/utils/formatTime'

const { t } = useI18n()
const resourceManagement = ref()
const records = ref()
const message = useMessage() // 消息弹窗
const activeName = ref(MediaType.Video)
const queryData = reactive<ResourceReqVO>({
  queryParams: {
    title: undefined,
    identity: undefined,
    duration: undefined,
    enable: undefined,
    reference: undefined,
    lang: undefined,
    createBy: undefined,
    pageNo: 1,
    pageSize: 20,
    mediaType: MediaType.Video,
  },
  total: 0,
  queryRef: undefined,
  loading: false,
})
const { queryParams, total, queryRef, loading } = toRefs(queryData)
const list = ref<ResourceRespVO[]>()
const isMounted = ref(false)
const show = ref(false)
const isMini = ref(false)
const managementShow = ref(false)
const filePreviewConfig = ref() // 配置项
const selectedRow = ref<ResourceRespVO>()
const { name: ViewName } = useRoute()
const route = useRoute()
const tagsStore = useTagsViewStore()
const PREFIX = 'resource-tab-'
const batchUploadRef = ref()
const showParsing = ref(false)
// 预览相关
const preview = reactive({
  previewShow: false,
  previewData: undefined,
})
const { previewShow, previewData } = toRefs(preview)
const { contentElement, setResizeObserver, visibleDomRect } = useHeight()
const tableRef = ref<TableInstance>()
const resourceLength = computed(() => {
  return batchUploadRef.value?.form.resource?.length
})
const isShowParsing = computed(() => {
// 非scorm和aicc正常显示
  if (!(activeName.value === MediaType.Scorm || activeName.value === MediaType.Aicc)) {
    return false
  }
  // 如果是Scorm或者AICC，则有两个状态
  return showParsing.value
})
const tableHeight = computed(() => {
  if (!visibleDomRect.value)
    return 'auto'
  const formHeight = document.querySelector('.custom-resource-search')?.clientHeight || 0
  const paginationHeight = document.querySelector('.custom-resource-pagination')?.clientHeight || 0
  // 这里判断如果是scorm需要再减掉一个按钮高度
  const scormBtnHeight = activeName.value === MediaType.Scorm ? 44 : 0
  return visibleDomRect.value.height - formHeight - paginationHeight - scormBtnHeight - 16
})
const DURATIONLIST = [
  { id: 1, label: '<15min', durationLower: 0, durationUpper: 900 },
  { id: 2, label: '15 - 30min', durationLower: 900, durationUpper: 1800 },
  { id: 3, label: '30 - 60min', durationLower: 1800, durationUpper: 3600 },
  { id: 4, label: '>60min', durationLower: 3600, durationUpper: 999999 },
]
const getViewInfo = (): RouteLocationNormalizedLoaded => {
  return tagsStore.visitedViews.find(view => view.name === ViewName)!
}
const handleClick = () => {
  if (show.value) {
    isMini.value = false
  }
  else {
    batchUploadRef.value.show = true
  }
}
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...omit(queryParams.value, ['duration']),
      durationLower: queryParams.value.duration?.durationLower,
      durationUpper: queryParams.value.duration?.durationUpper,
    }
    const res = await listResource(params)
    if (!res) {
      total.value = 0
      list.value = []
      return
    }
    list.value = res.list
    total.value = res.total
    nextTick(() => {
      tableRef.value?.setScrollTop(0)
    })
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
const handleTabChange = () => {
  queryRef.value?.resetFields()
  // 每次切换tab时,都需要隐藏parsing list页面
  showParsing.value = false
  queryParams.value.mediaType = activeName.value
  handleQuery()
}
// 预览课程信息
const handlePreview = async (row: ResourceRespVO) => {
  loading.value = true
  try {
    // 后端人员提供关于AICC新的详情接口，只获取课程url
    if (activeName.value === MediaType.Aicc) {
      const data = await getAiccResource(row.fileId)
      // 通过配置项来进行预览
      if (filePreviewConfig.value === 1) {
        preview.previewShow = true
        previewData.value = data
      } else {
        window.open(data,'_blank')
      }
    } else {
      const data = await getResource(row.id)
      preview.previewShow = true
      previewData.value = data
    }
  } finally {
    loading.value = false
  }
}
const handleEdit = (row: ResourceRespVO) => {
  selectedRow.value = row
  resourceManagement.value.show = true
  // managementShow.value = true
}
const handleParsingClick = () => {
  showParsing.value = true
}
const openPage = (view: any) => {
  tab.openPage(view.fullPath)
}
const onCloseTag = (view: any) => {
  function closeTag() {
    tab.closePage(view).then((res) => {
      const visitedViewsLength = res.visitedViews?.length
      if (visitedViewsLength === 1 || route.name === ViewName) {
        openPage(res.visitedViews[visitedViewsLength - 1])
      }
    })
  }
  if (resourceLength.value > 0) {
    message.confirm(t('confirm.uploading'))
    closeTag()
  }
  else {
    closeTag()
  }
}
const onRefreshTag = (view: any) => {
  if (resourceLength.value > 0) {
    message.confirm(t('confirm.refreshSuccess'))
    tab.refreshPage(view)
  }
  else {
    tab.refreshPage(view)
  }
}
const setManual = (view: RouteLocationNormalizedLoaded, flag: boolean) => {
  if (flag) {
    setupManualClosePage(view, onCloseTag)
    setupManualRefreshPage(view, onRefreshTag)
    tagsStore.addManualCloseCachedViews(view)
  }
  else {
    setupManualClosePage(view, undefined)
    setupManualRefreshPage(view, undefined)
    tagsStore.delManualCloseCachedView(view)
  }
}
/** 监控用户离开页面行为，避免文件未处理就退出系统 */
const handleBeforeUnload = (e: BeforeUnloadEvent) => {
  if (resourceLength.value > 0) {
    e.preventDefault()
    // 避免点击重新加载按钮会闪现，延迟一秒执行
    setTimeout(() => {
      batchUploadRef.value.createNotication(2)
    }, 1000)
  }
}
const handleStatusChange = async (row: ResourceRespVO) => {
  const text = +row.enable ? 'enable' : 'disable'
  try {
    await message.confirm(`${t('confirm.sureTo')} ${text} ${t('confirm.resourceOf')} "${row.title}"?`)
    await changeResourceStatus(row.id, row.fileId, row.enable)
    message.success(`${text} ${t('confirm.success')}`)
  } catch (e) {
    row.enable = `${+!+row.enable}`
  }
}
const handleDelete = async (row: ResourceRespVO) => {
  try {
    await message.confirm(`${t('confirm.deleteResource')} "${row.identity || '--'}"？`)
    loading.value = true
    await delResource(row.id)
    getList()
    message.success(t('global.deleteSuccess'))
  } finally {
    loading.value = false
  }
}
const handleViewRecords = (row: ResourceRespVO) => {
  selectedRow.value = row
  records.value.show = true
}
const calcCss = async () => {
  await nextTick()
  contentElement.value = document.querySelector('.el-tabs__content')
  setResizeObserver()
}
const formatBg = (type: MediaType) => {
  const color = {
    [MediaType.Video]: 'bg-[#36A5D8]',
    [MediaType.Audio]: 'bg-[#B858F0]',
    [MediaType.File]: 'bg-[#F2A353]',
    [MediaType.Scorm]: 'bg-[#21AC6E]',
    [MediaType.Aicc]: 'bg-[#21AC6E]',
  }
  return color[type]
}
onMounted(() => {
  isMounted.value = true
  calcCss()
  // 上线前要把这行注释取消
  window.addEventListener('beforeunload', handleBeforeUnload)
  getList()
  getPreviewConfig('file.preview.config')
})
onUnmounted(async () => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
watch(resourceLength, (newValue) => {
  if (!newValue || newValue.length === 0) {
    return setManual(getViewInfo(), false)
  }
  setManual(getViewInfo(), true)
})
const getPreviewConfig = async (configKey: string) => {
  const data = await ConfigApi.getConfigKey(configKey)
  filePreviewConfig.value = Number(data)
}
</script>

<template>
  <div class="app-main">
    <div class="absolute top-[20px] right-[20px] cursor-pointer z-[10]">
      <el-button type="primary" plain @click="handleClick">
        <Icon icon="ep:plus" class="mr-5px" />
        {{ t('action.uploadInBatch') }}
      </el-button>
    </div>
    <el-tabs
      v-model="activeName" class="custom-resource-tabs" @tab-change="handleTabChange"
    >
      <el-tab-pane :label="t('global.video')" :name="MediaType.Video">
        <template #label>
          <span>{{ t('global.video') }}</span>
        </template>
        <div :id="`${PREFIX}${MediaType.Video}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <el-tab-pane :label="t('global.audio')" :name="MediaType.Audio">
        <template #label>
          <span>{{ t('global.audio') }}</span>
        </template>
        <div :id="`${PREFIX}${MediaType.Audio}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <el-tab-pane :label="t('global.file')" :name="MediaType.File">
        <template #label>
          <span>{{ t('global.file') }}</span>
        </template>
        <div :id="`${PREFIX}${MediaType.File}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <el-tab-pane :label="t('global.scorm')" :name="MediaType.Scorm">
        <template #label>
          <span>
            {{ t('global.scorm') }}
          </span>
        </template>
        <div :id="`${PREFIX}${MediaType.Scorm}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
<!--      AICC课程-->
      <el-tab-pane :label="t('global.aicc')" :name="MediaType.Aicc">
        <template #label>
          <span>
            {{ t('global.aicc') }}
          </span>
        </template>
        <div :id="`${PREFIX}${MediaType.Aicc}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <!-- <el-tab-pane label="Upload Records" name="records">
        <template #label>
          <span>
            Upload Records
          </span>
        </template>
      </el-tab-pane> -->
    </el-tabs>
    <!-- 查询区域 -->
    <Teleport v-if="isMounted" :to="`#${PREFIX}${activeName}`">
      <el-scrollbar v-show="!isShowParsing" class="relative h-full custom-resource-scrollbar">
        <ContentWrap>
          <el-form ref="queryRef" :model="queryParams" inline label-width="90px" class="custom-resource-search">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item prop="title" :label="t('category.journey.title')">
                  <el-input v-model="queryParams.title" clearable class="!w-240px" @keydown.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="identity" :label="t('resource.id')">
                  <el-input v-model="queryParams.identity" clearable class="!w-240px" @keydown.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="createBy" :label="t('category.journey.creator')">
                  <el-input v-model="queryParams.createBy" clearable class="!w-240px" @keydown.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="enable" :label="t('global.status')">
                  <el-select v-model="queryParams.enable" clearable class="!w-240px">
                    <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="lang" :label="t('learningCenter.course.language')" >
                  <el-select v-model="queryParams.lang" clearable class="!w-240px">
                    <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item :label="t('learningCenter.course.duration')" prop="duration">
                  <el-select v-model="queryParams.duration" clearable value-key="id" class="!w-240px">
                    <el-option v-for="item in DURATIONLIST" :key="item.id" :label="item.label" :value="item" />
                  </el-select>
                  <!-- <el-row>
                    <el-col :span="11">
                      <el-form-item prop="durationLower">
                        <el-input-number v-model="queryParams.durationLower" :min="0" class="!w-full" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="2" class="text-center">
                      <span class="text-gray-500">-</span>
                    </el-col>
                    <el-col :span="11">
                      <el-form-item prop="durationUpper">
                        <el-input-number v-model="queryParams.durationUpper" :min="0" class="!w-full" />
                      </el-form-item>
                    </el-col>
                  </el-row> -->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="handleQuery">
                  <Icon class="mr-5px" icon="ep:search" />
                  {{ t('action.search') }}
                </el-button>
                <el-button @click="resetQuery">
                  <Icon class="mr-5px" icon="ep:refresh" />
                  {{ t('action.reset') }}
                </el-button>
                <el-button v-show="activeName === MediaType.Scorm || activeName === MediaType.Aicc" plain type="primary" @click="handleParsingClick">
                  {{ t('action.parsingList') }}
                </el-button>
              </el-col>
            </el-row>
          </el-form>
        </ContentWrap>
      </el-scrollbar>
    </Teleport>
    <!--        列表信息-->
    <ContentWrap v-show="!isShowParsing">
      <el-table ref="tableRef" v-loading="loading" :data="list" >
        <el-table-column prop="identity" :label="t('resource.id')" align="center" :width="150" />
        <el-table-column prop="format" :label="t('learningCenter.course.format')" align="center" :width="90">
          <template #default="{ row }">
            <div
              v-if="row?.format"
              class="size-11 text-sm text-white rounded-full flex items-center justify-center cursor-pointer flex-shrink-0 m-auto relative overflow-hidden group"
              :class="formatBg(row.mediaType)"
              @click="handlePreview(row)"
            >
              {{ toUpper(row.format) }}
              <div class="absolute inset-0 opacity-0 bg-[#131313]/[.5] flex items-center justify-center group-hover:opacity-100 transition-opacity duration-200">
                <el-icon>
                  <View />
                </el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="title" :label="t('category.journey.title')" align="center" :width="200" show-overflow-tooltip />
        <el-table-column :label="t('learningCenter.course.language')" align="center" :min-width="100">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="row.lang" />
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.duration')" align="center" :width="120">
          <template #default="{ row }">
            <span>{{ row.duration !== null && row.duration > 0 ? formatSecond(row.duration) : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="size" :label="t('resource.size')" align="center">
          <template #default="{ row }">
            <span>{{ formatBytes(row.size || 0) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('resource.referenced')" align="center" :width="110">
          <template #default="{ row }">
            <span>{{ row.referenceCount }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('common.status')" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.enable"
              active-value="1"
              inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column label="可用范围" align="center">
          <template #default="{ row }">
            <span>{{ row.scope }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="createBy" :label="t('category.journey.creator')" align="center" />
        <el-table-column prop="createTime" :formatter="dateFormatter" :label="t('category.journey.creationTime')" align="center" :width="180" show-overflow-tooltip />
        <el-table-column :label="t('global.action')" fixed="right" :width="320" align="center">
          <template #default="{ row }">
            <el-button link type="primary" @click="handlePreview(row)">
              <Icon icon="ep:view" />
              {{ t('action.view') }}
            </el-button>
            <el-button link type="primary" @click="handleEdit(row)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <el-button link type="primary" :disabled="!!row.referenceCount" @click="handleDelete(row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
            <el-button link type="primary" @click="handleViewRecords(row)">
              <Icon icon="ep:document" />
              {{ t('action.record') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        class="z-[999] custom-resource-pagination"
        :class="isMini ? 'justify-center' : 'justify-end'"
        :total="total"
        @pagination="getList"
      />
    </ContentWrap>

    <!-- Scorm待解析列表 -->
    <ScormParsingList v-if="isShowParsing" v-model:is-show-parsing="showParsing" :media-type="activeName" />

    <BatchUpload ref="batchUploadRef" v-model:is-mini="isMini" :media-type="activeName" @handleSearch="handleQuery" />
    <ResourceManagement ref="resourceManagement" :data="selectedRow" @refresh="getList" />
    <ResourcePreview v-model="previewShow" :data="previewData" :media-type="activeName" />
    <Records ref="records" :record-data="selectedRow" />
  </div>
</template>

