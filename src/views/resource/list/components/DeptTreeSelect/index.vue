<script setup lang='ts'>
import type { TreeData } from './types'
import { deptTreeSelect } from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'

const props = withDefaults(defineProps<{
  data?: TreeData[]
  /** 是否需要设置默认选中功能 */
  isSelectedDept?: boolean
  maxTag?: number
}>(), {
  maxTag: 1,
})
const { deptId } = useUserStore()

const options = ref<TreeData[]>()
const computedOptions = computed(() => {
  return props.data
})
const isSetChecked = computed(() => props.isSelectedDept && deptId)

const value = defineModel<any[]>({ default: [] })
const initOptions = async () => {
  if (!props.data) {
    const res = await deptTreeSelect({})
    options.value = res.data
  }

  // 设置默认选中项
  if (isSetChecked.value) {
    const setValue = new Set([deptId, ...value.value])

    value.value = [...setValue]
  }
}

const checkDisabled = (item: TreeData) => {
  return item.id === +deptId
}
initOptions()
</script>

<template>
  <el-tree-select
    v-model="value"
    class="custom-resource-tree"
    v-bind="$attrs"
    :data="props.data ? computedOptions : options"
    multiple
    node-key="id"
    :props="{
      value: 'id',
      label: 'label',
      disabled: isSetChecked ? checkDisabled : undefined,
    }"
    filterable
    clearable
    collapse-tags
    collapse-tags-tooltip
    show-checkbox
    popper-class="custom-resource-tree-popper"
    :max-collapse-tags="props.maxTag"
  />
</template>

<style lang='scss'>
.custom-resource-tree{
  .el-select__selected-item:not(:nth-child(n+3)){
    @apply max-w-28 ;
    .el-tag__content{
      .el-select__tags-text{
        @apply whitespace-normal line-clamp-1;
      }
    }
  }
}
.custom-resource-tree-popper + div{
  @apply max-w-96;
  .el-select__selection{
    @apply flex-wrap #{!important};
  }
}
</style>
