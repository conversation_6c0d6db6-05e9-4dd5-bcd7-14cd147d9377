<script setup lang='ts'>
import { formatSecond } from '@/utils/ruoyi'

const model = defineModel<number | undefined>({ default: 0 })
const inputValue = ref<Date>()
const formatDate = (date: Date) => {
  const dateIns = new Date(date)
  const hour = dateIns.getHours()
  const min = dateIns.getMinutes()
  const second = dateIns.getSeconds()

  return hour * 60 * 60 + min * 60 + second
}

const handleChange = (value: Date) => {
  model.value = value ? formatDate(value) : undefined
}
watch(model, (newValue) => {
  if (newValue !== undefined) {
    inputValue.value = new Date(`1970-01-01 ${formatSecond(newValue)}`)
  }
}, {
  immediate: true,
})
</script>

<template>
  <el-time-picker v-model="inputValue" :default-value="new Date('1970-01-01 00:00:00')" class="!w-full" @change="handleChange" />
</template>

<style scoped lang='scss'>
:deep(.el-input-group__append){
  @apply px-3;
}
</style>
