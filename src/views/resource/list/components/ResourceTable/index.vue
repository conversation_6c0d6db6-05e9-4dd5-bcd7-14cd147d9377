<script setup lang='ts'>
import type { ComponentInternalInstance } from 'vue'
import type { QueryData, Resource } from '@/typings/views/Resource/list'
import { omit, toUpper, union } from 'lodash-es'
import type { TableInstance } from 'element-plus'
import { MediaType } from '@/enums/resource'
import { getResource, listResource } from '@/api/resource/list'
import { formatBytes, formatSecond } from '@/utils/ruoyi'
import { useHeight } from '@/hooks/useHeight'
import ResourcePreview from '@/components/ResourcePreview/index.vue'
import Appendix from '@/views/learning-center/course/course-form/components/CourseCatalogue/CourseDialog/components/Appendix.vue'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

// const props = withDefaults(defineProps<{
//   type: 'none' | 'single' | 'multiple'
//   oocvalue: 'true' | 'false'
// }>(), {
//   type: 'multiple',
//   oocValue: null,
// })
const props = defineProps({
  type: {
    type: String,
    default: 'single',
  },
  oocValue: {
    type: Boolean,
    default: false,
  },
  courseValue: {
    type: Boolean,
    default: false,
  },
  appendixsValue: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits<{
  (event: 'selectionChange', args: Resource[]): void
}>()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { contentElement, setResizeObserver, visibleDomRect } = useHeight()
const activeName = ref(props.appendixsValue ? MediaType.File : MediaType.Video)
const PREFIX = 'resource-tab-'
const queryData = reactive<QueryData>({
  queryParams: {
    title: '',
    identity: '',
    duration: '',
    enable: '1', // 默认查询已启用的数据
    reference: undefined,
    lang: '',
    createBy: '',
    pageNo: 1,
    pageSize: 20,
    mediaType: props.appendixsValue ? MediaType.File : MediaType.Video,
  },
  total: 0,
  queryRef: undefined,
  loading: false,
})
const { queryParams, total, loading } = toRefs(queryData)
const queryRef = ref()
const list = ref<Resource[]>()
const tableRef = ref<TableInstance>()
const isMounted = ref(false)
// 预览相关
const preview = reactive({
  previewShow: false,
  previewData: undefined,
})
const { previewShow, previewData } = toRefs(preview)
const tableHeight = computed(() => {
  if (!visibleDomRect.value)
    return 'auto'
  const formHeight = document.querySelector('.custom-resource-search')?.clientHeight || 0
  const paginationHeight = document.querySelector('.custom-resource-pagination')?.clientHeight || 0
  // 这里判断如果是scorm需要再减掉一个按钮高度
  // const scormBtnHeight = activeName.value === MediaType.Scorm ? 44 : 0
  const scormBtnHeight = 0
  return visibleDomRect.value.height - formHeight - paginationHeight - scormBtnHeight - 16
})
const DURATIONLIST = [
  { id: 1, label: '<15min', durationLower: 0, durationUpper: 900 },
  { id: 2, label: '15 - 30min', durationLower: 900, durationUpper: 1800 },
  { id: 3, label: '30 - 60min', durationLower: 1800, durationUpper: 3600 },
  { id: 4, label: '>60min', durationLower: 3600, durationUpper: 999999 },
]
// const selections = ref<number | number[]>([])
const selections = ref<number | number[]>()
// const modelIds = defineModel<number | number[]>('ids', { default: [5235, 5234, 5233, 5232, 5231] })
const modelIds = defineModel<number[] | undefined>('ids')

/**
 * 设置默认选中
 */
const setChecked = () => {
  // console.log('ids', modelIds.value)

  if (!modelIds.value)
    return

  modelIds.value.forEach((item) => {
    // 找到对应的数据项
    const findItem = list.value?.find(r => r.id === item)
    if (!findItem)
      return
    if (props.type === 'single') {
      handleRowClick(findItem)
    }
    else {
      tableRef.value?.toggleRowSelection(findItem, true)
    }
  })
  selections.value = props.type === 'multiple' ? modelIds.value : modelIds.value[0]
  // if (tableRef.value?.getSelectionRows()) {
  //   const selectRowsArray = tableRef.value?.getSelectionRows()
  //   testsy.value = selectRowsArray.filter(itemA => modelIds.value?.some(itemB => itemB === itemA.id))
  //   console.log(testsy, 'testsy---------->>>')
  // }
}
const getList = async () => {
  loading.value = true
  if (props.oocValue && activeName.value === MediaType.File) {
    queryParams.value.formatList = 'pdf'
  }
  else if (props.oocValue && activeName.value === MediaType.Video) {
    queryParams.value.formatList = 'mp4'
  }
  else {
    delete queryParams.value.formatList
  }
  const params = {
    ...omit(queryParams.value, ['duration']),
    durationLower: queryParams.value.duration?.durationLower,
    durationUpper: queryParams.value.duration?.durationUpper,
  }
  try {
    const res = await listResource(params)
    if (!res) {
      total.value = 0
      list.value = []
      return
    }
    list.value = res.list
    total.value = res.total

    nextTick(() => {
      setChecked()
      tableRef.value?.setScrollTop(0)
    })
  } finally {
    loading.value = false
  }
}
const formatBg = (type: MediaType) => {
  const color = {
    [MediaType.Video]: 'bg-[#36A5D8]',
    [MediaType.Audio]: 'bg-[#B858F0]',
    [MediaType.File]: 'bg-[#F2A353]',
    [MediaType.Scorm]: 'bg-[#21AC6E]',
  }
  return color[type]
}
const handlePreview = async (row: Resource) => {
  loading.value = true
  try {
    const data = await getResource(row.id)
    preview.previewShow = true
    previewData.value = data
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
const handleTabChange = () => {
  queryRef.value?.resetFields()
  queryParams.value.mediaType = activeName.value
  handleQuery()
}
const calcCss = async () => {
  await nextTick()
  contentElement.value = document.querySelector('.el-tabs__content')
  setResizeObserver()
}
/**
 * 表格行的点击事件，用于单选或者多选选中
 * @param row 当前行数据
 */
const handleRowClick = (row: Resource) => {
  if (props.type === 'single') {
    selections.value = row.id
  }

  // @ts-expect-error ...
  tableRef.value!.toggleRowSelection(row)
  // 只有在单选模式下在这里传递数据，因为多选模式下会触发表格的selectionchange事件，可以在哪里触发，避免重复触发
  if (props.type === 'single') {
    emits('selectionChange', [row])
  }
}
/**
 * 表格切换选中的事件
 * @param rows 选中的资源
 */
const handleSelectionChange = (rows: Resource[]) => {
  selections.value = rows.map(item => item.id)

  emits('selectionChange', rows)
}
const clearSelection = () => {
  tableRef.value?.clearSelection()
  selections.value = undefined
}
onMounted(() => {
  isMounted.value = true
  calcCss()
})

watch(selections, () => {
  // 如果是数组，并且数组长度为0，直接undefined
  const isArray = Array.isArray(selections.value)
  if (!selections.value || (isArray && (selections.value as Array<number>).length === 0)) {
    modelIds.value = undefined
  }
  else {
    modelIds.value = isArray ? selections.value as Array<number> : [selections.value as number]
  }
})

getList()
defineExpose({
  clearSelection,
  setChecked,
})
</script>

<template>
  <div class="h-full w-full pb-5">
    <el-tabs
      v-model="activeName" class="custom-resource-tabs" @tab-change="handleTabChange"
    >
      <el-tab-pane v-if="!props.appendixsValue" :label="t('global.video')" :name="MediaType.Video">
        <template #label>
          <span>{{ t('global.video' )}}</span>
        </template>
        <div :id="`${PREFIX}${MediaType.Video}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <el-tab-pane v-if="!props.appendixsValue && props.courseValue" :label="t('global.audio')" :name="MediaType.Audio">
        <template #label>
          <span>{{ t('global.audio' )}}</span>
        </template>
        <div :id="`${PREFIX}${MediaType.Audio}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <el-tab-pane :label="t('global.file')" :name="MediaType.File">
        <template #label>
          <span>{{ t('global.file' )}}</span>
        </template>
        <div :id="`${PREFIX}${MediaType.File}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <el-tab-pane v-if="props.courseValue" :label="t('global.scorm')" :name="MediaType.Scorm">
        <template #label>
          <span>
            {{ t('global.scorm' )}}
          </span>
        </template>
        <div :id="`${PREFIX}${MediaType.Scorm}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <el-tab-pane v-if="props.courseValue" :label="t('global.aicc')" :name="MediaType.Aicc">
        <template #label>
          <span>
            {{ t('global.aicc' )}}
          </span>
        </template>
        <div :id="`${PREFIX}${MediaType.Aicc}`" class="flex-1 overflow-x-hidden"></div>
      </el-tab-pane>
      <!-- <el-tab-pane label="Upload Records" name="records">
        <template #label>
          <span>
            Upload Records
          </span>
        </template>
      </el-tab-pane> -->
    </el-tabs>

    <!-- 查询区域 -->
    <Teleport v-if="isMounted" :to="`#${PREFIX}${activeName}`">
      <el-scrollbar class="relative h-full custom-resource-scrollbar">
        <ContentWrap>
          <el-form ref="queryRef" :model="queryParams" inline class="custom-resource-search">
                <el-form-item prop="title" :label="t('learningCenter.course.title')">
                  <el-input v-model="queryParams.title" clearable @keydown.enter="handleQuery" style="width: 240px" />
                </el-form-item>
                <el-form-item prop="identity" :label="t('learningCenter.course.id')">
                  <el-input v-model="queryParams.identity" clearable @keydown.enter="handleQuery" style="width: 240px" />
                </el-form-item>
                <el-form-item prop="lang" :label="t('learningCenter.course.language')">
                  <el-select v-model="queryParams.lang" clearable style="width: 200px">
                    <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
                <el-form-item :label="t('learningCenter.course.duration')" prop="duration">
                  <el-select v-model="queryParams.duration" clearable value-key="id" style="width: 200px">
                    <el-option v-for="item in DURATIONLIST" :key="item.id" :label="item.label" :value="item" />
                  </el-select>
                </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery">
                <Icon class="mr-5px" icon="ep:search" />
                {{ t('action.search') }}
              </el-button>
              <el-button @click="resetQuery">
                <Icon class="mr-5px" icon="ep:refresh" />
                {{ t('action.reset') }}
              </el-button>
            </el-form-item>
          </el-form>
        </ContentWrap>
        <ContentWrap>
          <el-table
            ref="tableRef"
            v-loading="loading"
            :height="tableHeight"
            :data="list"
            row-key="id"
            v-on="{ rowClick: props.type !== 'none' ? handleRowClick : undefined,
                  selectionChange: props.type === 'multiple' ? handleSelectionChange : undefined,
          }"
          >
            <!-- 单选列 -->
            <el-table-column v-if="props.type === 'single'" label="" :width="50" align="center">
              <template #default="{ row }">
                <el-radio v-model="selections" class="custom-resource-radio" :value="row.id" />
              </template>
            </el-table-column>
            <!-- 多选列 -->
            <el-table-column v-else-if="props.type === 'multiple'" reserve-selection type="selection" :width="50" />
            <el-table-column prop="identity" :label="t('learningCenter.course.id')" align="center" :width="150" />
            <el-table-column prop="format" :label="t('learningCenter.course.format')" align="center" :width="90">
              <template #default="{ row }">
                <div
                  v-if="row?.format"
                  class="size-11 text-sm text-white rounded-full flex items-center justify-center cursor-pointer flex-shrink-0 m-auto relative overflow-hidden group"
                  :class="formatBg(row.mediaType)"
                  @click.stop="handlePreview(row)"
                >
                  {{ toUpper(row.format) }}
                  <div class="absolute inset-0 opacity-0 bg-[#131313]/[.5] flex items-center justify-center group-hover:opacity-100 transition-opacity duration-200">
                    <el-icon>
                      <View />
                    </el-icon>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="title" :label="t('learningCenter.course.title')" align="center" :min-width="200" show-overflow-tooltip />
            <el-table-column :label="t('learningCenter.course.language')" align="center" :width="200">
              <template #default="{ row }">
                <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="row.lang" />
              </template>
            </el-table-column>
            <el-table-column :label="t('learningCenter.course.duration')" align="center" :width="120">
              <template #default="{ row }">
                <span>{{ row.duration !== null && row.duration > 0 ? formatSecond(row.duration) : '' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            class="z-[999] custom-resource-pagination"
            :total="total"
            @pagination="getList"
          />
        </ContentWrap>
      </el-scrollbar>
    </Teleport>
    <ResourcePreview v-model="previewShow" :data="previewData" />
  </div>
</template>

<style scoped lang='scss'>
:deep(.custom-resource-tabs) {
  @apply h-full flex flex-col;
  .el-tab-pane{
    @apply flex flex-1 overflow-x-hidden;
  }
  .el-tabs__content{
    @apply flex flex-1;
  }
  .el-tabs__item {
    @apply p-0 #{!important};
    &>span{
      @apply mx-4;
    }
    &.is-active{
      @apply bg-[#E4F4EE];
    }
  }

}

:deep(.custom-resource-search){
  @apply overflow-x-hidden;
  .el-form-item{
    @apply w-full mr-0;
  }
}
:deep(.custom-resource-scrollbar){
  .el-scrollbar__bar{
    @apply z-[9999];
  }
}
:deep(.custom-resource-pagination){
  @apply m-[unset] pt-[unset] p-[unset] pb-[unset] h-auto flex justify-end mt-4 #{!important};
  .el-pagination{
    @apply relative;
  }
}
// 清除默认label边距，修复视觉上不居中的问题
:deep(.custom-resource-radio){
  .el-radio__label{
    @apply p-0;
  }
}
</style>
