<script setup lang='ts'>
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import DeptTreeSelect from '../DeptTreeSelect/index.vue'
import DurationInput from '../DurationInput/index.vue'
import { getResource, updateResource } from '@/api/resource/list'
import type { Resource } from '@/typings/views/Resource/list'
// import LargeFileUpload from '@/components/LargeFileUpload/index.vue'
import type { ModelValue as FileType } from '@/components/LargeFileUpload/index.vue'
import { MediaType } from '@/enums/resource'
import { formatSecond } from '@/utils/ruoyi'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
const props = defineProps<{
  data: Resource | undefined
}>()
const emits = defineEmits<{
  (event: 'refresh'): void
}>()


interface Form {
  address: string
  title: string
  identity: string
  lang: string[] | undefined
  deptIds: any[]
  duration: any
  id?: any
  fileId?: any
  mediaType?: number
}
const { t } = useI18n()
const files = ref<FileType[]>()
const show = ref(false)
const form = ref<Form>({
  lang: undefined,
  deptIds: [],
  duration: 1,
  address: '',
  title: '',
  identity: '',
})
const rules = ref({
  lang: [{ required: true, message: t('resource.languageRule') }],
  deptIds: [{ required: true, message: t('resource.dataPermissionsRule'), trigger: 'change' }],
  duration: [{ required: true, message: t('learningCenter.course.durationRule'), validator: (rule, value, callback) => {
    if (!value || value === 0) {
      callback(new Error(t('learningCenter.course.durationRule')))
      return
    }
    callback()
  } }],
  title: [{ required: true, message: t('sys.user.userNicknameRule'), trigger: 'blur' }],
})
const formRef = ref<FormInstance>()
const loading = ref(false)
const message = useMessage() // 消息弹窗
const getInfo = async () => {
  loading.value = true
  try {
    const resData = await getResource(props.data?.id)
    files.value = [{
      name: resData.title,
      type: `${resData.mediaType}`,
      url: resData.address,
      id: resData.id,
      origin: {
        name: resData.title,
        fullName: resData.title,
        type: `${resData.mediaType}`,
        size: resData.size,
      },
    }]
    form.value.address = resData.address
    form.value.deptIds = resData.deptIds || []
    form.value.duration = resData.duration
    form.value.lang = resData.lang ? resData.lang.split(',') : undefined
    form.value.id = resData.id
    form.value.fileId = resData.fileId
    form.value.title = resData.title
    form.value.identity = resData.identity
    form.value.mediaType = resData.mediaType
  } finally {
    loading.value = false
  }
}
const handleCancel = () => {
  show.value = false
}
const handleSubmit = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const params = {
          ...form.value,
          duration: form.value.duration,
          lang: form.value.lang ? form.value.lang.join(',') : undefined,
        }
        await updateResource(params)
        message.success(t('global.submitSuccess'))
        show.value = false
        emits('refresh')
      } finally {
        loading.value = false
      }
    }
  })
}
defineExpose({ show })
watch(show, (newValue) => {
  if (newValue) {
    nextTick(() => {
      formRef.value?.resetFields()

    })
    getInfo()
  }
})
</script>

<template>
  <Dialog v-model="show" :title="t('dialog.edit')" :width="600">
    <el-form ref="formRef" v-loading="loading" :model="form" label-width="auto" :rules="rules">
      <!-- <el-form-item prop="address">
        <LargeFileUpload v-model="files" class="w-full" :limit="1" :multiple="false" />
      </el-form-item> -->
      <el-form-item label="ID" prop="id">
        <span>
          {{ form.identity }}
        </span>
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.title')" prop="title">
        <el-input v-model="form.title" clearable type="textarea" />
        <!-- <span class="break-all">
          {{ form.title }}
        </span> -->
      </el-form-item>
      <!-- <el-form-item label="Format" prop="mediaType">
        <dict-tag :options="resource_list_type" :value="`${form.mediaType}`" />
      </el-form-item> -->
      <el-form-item :label="t('learningCenter.course.language')" prop="lang">
        <el-select v-model="form.lang" clearable class="!w-full" multiple>
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('action.dataPermission')"
        prop="deptIds"
      >
        <DeptTreeSelect v-model="form.deptIds" class="!w-full" :is-selected-dept="true" :max-tag="2" />
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.duration')" prop="duration">
        <!-- 除了视频与音频，其他都需要设置时间 -->
        <span v-if="[MediaType.Audio, MediaType.Video].includes(form.mediaType!)">
          {{ form.duration ? formatSecond(+(form.duration || 0)) : '' }}
        </span>
        <DurationInput v-else v-model="form.duration" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="handleSubmit">
        {{ t('action.submit') }}
      </el-button>
      <el-button @click="handleCancel">
        {{ t('action.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>

<style scoped lang='scss'>

</style>
