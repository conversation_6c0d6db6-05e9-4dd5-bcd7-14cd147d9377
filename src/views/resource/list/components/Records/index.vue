<script setup lang='ts'>
import type { ComponentInternalInstance } from 'vue'
import type { TableColumnCtx } from 'element-plus'
import { listRecords } from '@/api/resource/list'
import type { Resource } from '@/typings/views/Resource/list'
import type { Dict } from '@/typings/global'
import CustomDialog from '@/components/CustomDialog/index.vue'
import { formatSecond } from '@/utils/ruoyi'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

interface Record {
  id: string
  content: string
  item: string
  operateTime: string
  operatorId: number
  operatorName: string
  resourceId: number
  beforeData: string
  afterData: string
}
interface SpanMethodProps {
  row: Record
  column: TableColumnCtx<User>
  rowIndex: number
  columnIndex: number
}
const { t } = useI18n()
const props = defineProps<{
  recordData: Resource | undefined
}>()
const show = ref(false)
const list = ref<Record[]>()
// const types = {
//   Depts: 'Data Permission',
//   Duration: 'Duration',
//   Title: 'Title',
//   Lang: 'Language',
//   Enable: 'Status',
//   Create: 'Create',
// }
interface Types {
  [key: string]: {
    label: string
    dict?: Dict[]
  }
}

const types = computed<Types>(() => ({
  Depts: {
    label: t('action.dataPermission'),
  },
  Duration: {
    label: t('learningCenter.course.duration'),
  },
  Title: {
    label: t('learningCenter.course.title'),
  },
  Lang: {
    label: t('learningCenter.course.language'),
    dict: getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG),
  },
  Enable: {
    label: t('learningCenter.course.status'),
    dict: getIntDictOptions(DICT_TYPE.RESOURCE_LIST_STATUS),
  },
  Create: {
    label: t('learningCenter.create'),
  },
}))
const loading = ref(true)
const getList = async () => {
  loading.value = true
  try {
    const data = await listRecords(props.recordData?.id)
    const resData = data as Record[]
    list.value = resData.map((item) => {
      if (item.item === 'Duration') {
        item.beforeData = formatSecond(+item.beforeData)
        item.afterData = formatSecond(+item.afterData)
      }
      return item
    })
  } finally {
    loading.value = false
  }
}

const arraySpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) => {
  if (row.item === 'Create') {
    if (columnIndex === 1) {
      return [1, 2]
    }
    else if ([1, 2].includes(columnIndex)) {
      return [0, 0]
    }
  }
}
defineExpose({ show })
watch(show, (newValue) => {
  if (newValue)
    getList()
})
</script>

<template>
  <Dialog v-model="show" :title="t('dialog.resourceModificationLog')" auto-height close-on-click-modal :need-mini="false" width="60%" @close="show = false">
    <div v-loading="loading" class="h-full p-7 flex items-center justify-center">
      <el-table
        v-if="list && list.length > 0" :data="list" border class="!h-full" :tooltip-options="{
          popperClass: 'custom-record-table-tooltip',
        }"
        :span-method="arraySpanMethod"
      >
        <el-table-column prop="item" :label="t('resource.eventType')" width="120">
          <template #default="{ row }">
            {{ types[row.item].label }}
          </template>
        </el-table-column>
        <el-table-column :width="220" :label="t('resource.before')" prop="beforeData" show-overflow-tooltip>
          <template #default="{ row }">
            <template v-if="row.item === 'Create'">
              <span>{{ t('resource.resourceCreated') }}</span>
            </template>
            <template v-else>
              <dict-tag v-if="types[row.item].dict" :options="types[row.item].dict" :value="row.beforeData" />
              <span v-else>{{ row.beforeData }}</span>
            </template>
          </template>
        </el-table-column>
        <el-table-column :width="220" :label="t('resource.after')" prop="afterData" show-overflow-tooltip>
          <template #default="{ row }">
            <dict-tag v-if="types[row.item].dict" :options="types[row.item].dict" :value="row.afterData" />
            <span v-else>{{ row.afterData }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="operatorName" :label="t('log.operaLog.operator')" />
        <el-table-column prop="operateTime" :label="t('resource.operateTime')" width="180" />
      </el-table>
      <el-empty v-else :description="t('resource.noResourceModificationRecord') " />
    </div>
    <template #footer>
      <el-button @click="show = false">
        {{ t('action.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>

<style lang='scss'>
.custom-record-table-tooltip{
  @apply max-w-96 #{!important};
}
</style>
