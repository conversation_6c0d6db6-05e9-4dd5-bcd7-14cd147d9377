<script setup lang='ts'>
import type { ComponentInternalInstance } from 'vue'
import { delResource, listScorm, unzipScorm, listAicc } from '@/api/resource/list'
import type { Scorm } from '@/typings/views/Resource/list'
import { formatBytes, formatSecond } from '@/utils/ruoyi'
import { ScormFileStatus } from '@/enums/chapter'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'

const props = defineProps<{
  mediaType: number
}>()
const list = ref<Scorm[]>()
const isShowParsing = ref(false)
const loading = ref(false)
const { t } = useI18n()
const message = useMessage() // 消息弹窗
function handleBack() {
  isShowParsing.value = false
}
const getList = async () => {
  loading.value = true
  try {
    // 4.scorm 6.aicc
    if (props.mediaType === 4) {
      list.value = await listScorm()
    } else {
      list.value = await listAicc()
    }
  } finally {
    loading.value = false
  }
}
const handleUnzip = async (row: Scorm) => {
  loading.value = true
  try {
    await unzipScorm(row.id)
    getList()
  } finally {
    loading.value = false
  }
}
const handleDelete = async (row: Scorm) => {
  try {
    await message.confirm(`${t('confirm.deleteScorm') } ${row.title || '--'}"？`)
    loading.value = true
    await delResource(row.id)
    getList()
    message.success(t('global.deleteSuccess'))
  } finally {
    loading.value = false
  }
}
const handleRefresh = () => {
  getList()
}
let interval: any

onMounted(() => {
  interval = setInterval(() => {
    getList()
  }, 3000)
})
onUnmounted(() => {
  clearInterval(interval)
})
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="w-full">
    <el-page-header @back="handleBack">
      <template #content>
        <span class="text-large font-600 mr-3"> {{ t('action.parsingList') }} </span>
        <el-button link type="primary" @click="handleRefresh">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('action.refresh') }}
        </el-button>
      </template>
    </el-page-header>
    <!-- <el-button type="primary" plain class="mb-3" @click="handleTabClick">
      Scorm Resource
    </el-button> -->
    <ContentWrap class="mt-4">
      <el-table v-loading="loading" :data="list">
        <el-table-column :label="t('learningCenter.course.id')" align="center" :width="150" type="index" />
        <el-table-column prop="title" :label="t('learningCenter.course.title')" align="center" :width="200" />
        <el-table-column :label="t('learningCenter.course.language')" align="center">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="row.lang" />
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.duration')" align="center">
          <template #default="{ row }">
            <span>{{ row.duration !== null && row.duration > 0 ? formatSecond(row.duration) : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="size" :label="t('resource.size')" align="center">
          <template #default="{ row }">
            <span>{{ formatBytes(row.size) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="size" :label="t('learningCenter.task.progress')" align="center">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.RESOURCE_SCORM_PROGRESS" :value="props.mediaType === 4 ?row.scormParseStatus : row.aiccParseStatus" />
          </template>
        </el-table-column>
        <el-table-column prop="createBy" :label="t('sys.company.creator')" align="center" />
        <el-table-column prop="createTime" :formatter="dateFormatter" :label="t('sys.company.createTime')" align="center" />
        <el-table-column :label="t('global.action')" align="center">
          <template #default="{ row }">
            <el-button v-if="row.scormParseStatus === ScormFileStatus.Failure" link type="primary" @click="handleUnzip(row)">
              {{ t('action.unzip') }}
            </el-button>
            <el-button link type="primary" @click="handleDelete(row)">
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </ContentWrap>
  </div>
</template>

<style scoped lang='scss'>

</style>
