import type { UploadRawFile } from 'element-plus'
import type { CustomUploadUserFile } from '@/components/LargeFileUpload/index.vue'
import { Audio, Compressed, EXCEL, PDF, PPT, TXT, Video, WORD } from '@/components/LargeFileUpload/script/FileAllowTypes'
import { MediaType } from '@/enums/resource'

/**
 * 转换文件类型
 * @param file 文件
 */
export const formatFileType = (file: CustomUploadUserFile['raw'] | UploadRawFile) => {
  const originType = file!.type
  console.log('formatFileType - 文件类型:', originType, '文件名:', file!.name)

  if (Video.includes(originType)) {
    console.log('识别为视频类型:', MediaType.Video)
    return MediaType.Video
  }
  else if (Audio.includes(originType)) {
    console.log('识别为音频类型:', MediaType.Audio)
    return MediaType.Audio
  }
  else if (Compressed.includes(originType)) {
    console.log('识别为压缩文件类型:', MediaType.Scorm)
    return MediaType.Scorm
  }
  // else if (PDF.includes(originType) || PPT.includes(originType) || WORD.includes(originType) || EXCEL.includes(originType) || TXT.includes(originType)) {
  //   return MediaType.File
  // }
  else {
    console.log('识别为文件类型:', MediaType.File)
    return MediaType.File
  }
}
