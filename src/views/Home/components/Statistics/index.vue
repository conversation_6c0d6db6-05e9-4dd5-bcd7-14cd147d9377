<script setup lang='ts'>
import { getLoginRecord } from '@/api/dashboard/admin'
import { parseTime } from '@/utils/ruoyi'
const { t } = useI18n()
const detail = ref<{
  studentCount: number
  studentDistinctCount: number
  teacherCount: number
  teacherDistinctCount: number
}>({
  studentCount: 0,
  studentDistinctCount: 0,
  teacherCount: 0,
  teacherDistinctCount: 0,
})
const cards = computed(() => [
  {
    icon: 'TotalStudents',
    num: detail.value.studentCount,
    text: t('dashboard.statistics.totalStudents'),
  },
  {
    icon: 'DistinctStudents',
    num: detail.value.studentDistinctCount,
    text: t('dashboard.statistics.distinctStudents'),
  },
  {
    icon: 'TotalAdmins',
    num: detail.value.teacherCount,
    text: t('dashboard.statistics.totalAdmins'),
  },
  {
    icon: 'DistinctAdmins',
    num: detail.value.teacherDistinctCount,
    text: t('dashboard.statistics.distinctAdmins'),
  },
])
const start = new Date()
const end = new Date()
start.setDate(start.getDate() - 7)
const selectedDate = ref<[Date, Date]>([start, end])
const pattern = '{y}-{m}-{d}'
const loading = ref(true)
const getDetail = async () => {
  loading.value = true
  try {
    const data = await getLoginRecord(`${parseTime(selectedDate.value[0], pattern)} 00:00:00`, `${parseTime(selectedDate.value[1], pattern)} 23:59:59`)
    detail.value.studentCount = data?.studentCount ?? 0
    detail.value.studentDistinctCount = data?.studentDistinctCount ?? 0
    detail.value.teacherCount = data?.teacherCount ?? 0
    detail.value.teacherDistinctCount = data?.teacherDistinctCount ?? 0
  } finally {
    loading.value = false
  }
}

function handleDateChange() {
  getDetail()
}
onMounted(() => {
  getDetail()
})
</script>

<template>
  <div class="rounded-[10px] bg-white p-5">
    <div class="flex items-center gap-2.5 mb-5">
      <svg-icon icon-class="DashboardLogin" />
      <span class="text-lg text-[#233a35]">{{ t('dashboard.statistics.loginStatistics') }}</span>
    </div>

    <el-date-picker
      v-model="selectedDate"
      :clearable="false"
      type="daterange"
      start-placeholder="Start Date"
      end-placeholder="End Date"
      @change="handleDateChange"
    />

    <div v-loading="loading" class="grid grid-rows-2 grid-cols-2 gap-5 mt-5">
      <div v-for="(item, index) in cards" :key="index" class="flex flex-col bg-[#F3FBF7] rounded-[10px] px-5 py-3.5">
        <div class="w-6 h-6 rounded-[4px] bg-primary flex items-center justify-center">
          <svg-icon :icon-class="item.icon" />
        </div>
        <span class="text-[#233A35] text-xl mt-2.5 mb-1">{{ item.num }}</span>
        <span class="text-[#626967] text-base">{{ item.text }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang='scss'>

</style>
