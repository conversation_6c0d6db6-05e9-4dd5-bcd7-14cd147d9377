<script setup lang="ts">
const { t } = useI18n()
const router = useRouter()
const quicks = [
  {
    text: t('learningCenter.course.course'),
    icon: 'QuickCourse',
    name: 'Course'
  },
  {
    text: t('learningCenter.course.addCourse'),
    icon: 'QuickAddCourse',
    name: 'CourseForm'
  },
  {
    text: t('learningCenter.course.exam'),
    icon: 'QuickExam',
    name: 'Exam'
  },
  {
    text: t('learningCenter.exam.addExam'),
    icon: 'QuickAddExam',
    name: 'CreateExam'
  },
  {
    text: t('learningCenter.boarding.boarding'),
    icon: 'QuickOnboarding',
    name: 'Onboarding'
  },
  {
    text: t('learningCenter.orientation.orientation'),
    icon: 'QuickOrientation',
    name: 'Orientation'
  },
  {
    text: t('learningCenter.companyPolicy.companyPolicy'),
    icon: 'QuickPolicy',
    name: 'CompanyPolicy'
  }
]

function handleToPage(name: string) {
  router.push({
    name
  })
}
</script>

<template>
  <div class="rounded-[10px] bg-white p-5 pb-10">
    <div class="flex items-center gap-2.5 mb-10">
      <svg-icon icon-class="QuickHeader" />
      <span class="text-lg text-[#233a35]">{{ t('dashboard.quick.quickEntrance') }}</span>
    </div>
    <div class="grid grid-cols-2 gap-14">
      <div v-for="(item, index) in quicks" :key="index" class="flex flex-col items-center gap-2.5 cursor-pointer group" @click="handleToPage(item.name)">
        <svg-icon :icon-class="item.icon" class="text-[80px]" />
        <span class="text-[#222222] text-base group-hover:text-primary whitespace-nowrap">
          {{ item.text }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
