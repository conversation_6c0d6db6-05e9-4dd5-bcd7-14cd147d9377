<script setup lang='ts'>
import { ChartFactory } from '@/views/Home/scripts/chart'
import type { ECOption } from '@/plugins/echarts'
import type { AdminCourseBar } from '@/typings/dashboard/admin'
import { getCourseBar } from '@/api/dashboard/admin'

const { t } = useI18n()
const loading = ref(true)
const chartRef = ref()
const courseBarInfo = ref<AdminCourseBar[]>()
let chart: ChartFactory
// const ChartData = [
//   {
//     topicName: 'Ariba',
//     courseCount: 0,
//   },
//   {
//     topicName: 'Business Skills ',
//     courseCount: 0,
//   },
//   {
//     topicName: 'Business Skills',
//     courseCount: 12,
//   },
//   {
//     topicName: 'Contract',
//     courseCount: 0,
//   },
//   {
//     topicName: 'CPL',
//     courseCount: 0,
//   },
//   {
//     topicName: 'CPL ',
//     courseCount: 0,
//   },
//   {
//     topicName: 'Daily tasks',
//     courseCount: 12,
//   },
//   {
//     topicName: 'Datacenter',
//     courseCount: 0,
//   },
//   {
//     topicName: 'Drilling ',
//     courseCount: 0,
//   },
//   {
//     topicName: 'Drilling Fundamental',
//     courseCount: 2,
//   },
//   {
//     topicName: 'EPTW Training',
//     courseCount: 8,
//   },
//   {
//     topicName: 'EPTW Training ',
//     courseCount: 54,
//   },
//   {
//     topicName: 'Events',
//     courseCount: 0,
//   },
//   {
//     topicName: 'Experience',
//     courseCount: 2,
//   },
//   {
//     topicName: 'Experience',
//     courseCount: 6,
//   },
//   {
//     topicName: 'FICO',
//     courseCount: 6,
//   },
//   {
//     topicName: 'FM ',
//     courseCount: 5,
//   },
//   {
//     topicName: 'general HSE',
//     courseCount: 6,
//   },
//   {
//     topicName: 'HR',
//     courseCount: 1,
//   },
//   {
//     topicName: 'HSE',
//     courseCount: 24,
//   },
//   {
//     topicName: 'HSSE',
//     courseCount: 0,
//   },
//   {
//     topicName: 'IMIT ',
//     courseCount: 14,
//   },
//   {
//     topicName: 'IMIT ',
//     courseCount: 3,
//   },
//   {
//     topicName: 'IT',
//     courseCount: 6,
//   },
//   {
//     topicName: 'IT',
//     courseCount: 1,
//   },
//   {
//     topicName: 'Job Positions Desc',
//     courseCount: 0,
//   },
//   {
//     topicName: 'Knowledge',
//     courseCount: 0,
//   },
//   {
//     topicName: 'MAINT',
//     courseCount: 0,
//   },
//   {
//     topicName: 'Majnoon Events',
//     courseCount: 114,
//   },
//   {
//     topicName: 'MM',
//     courseCount: 3,
//   },
//   {
//     topicName: 'New subject',
//     courseCount: 0,
//   },
//   {
//     topicName: 'New Technology',
//     courseCount: 0,
//   },
//   {
//     topicName: 'PA Training',
//     courseCount: 1,
//   },
//   {
//     topicName: 'PE ',
//     courseCount: 0,
//   },
//   {
//     topicName: 'PM',
//     courseCount: '2',
//   },
//   {
//     topicName: 'Project Management',
//     courseCount: '0',
//   },
//   {
//     topicName: 'PS',
//     courseCount: '3',
//   },
//   {
//     topicName: 'Recommended Courses',
//     courseCount: '0',
//   },
//   {
//     topicName: 'Refresher Course ',
//     courseCount: '2',
//   },
//   {
//     topicName: 'Required Courses',
//     courseCount: '4',
//   },
//   {
//     topicName: 'SAP',
//     courseCount: '1',
//   },
//   {
//     topicName: 'Sourcing',
//     courseCount: '0',
//   },
//   {
//     topicName: 'Sourcing',
//     courseCount: '2',
//   },
//   {
//     topicName: 'SPS',
//     courseCount: '0',
//   },
//   {
//     topicName: 'Supply',
//     courseCount: '1',
//   },
//   {
//     topicName: 'Test-826',
//     courseCount: '1',
//   },
//   {
//     topicName: 'Test-zhug819',
//     courseCount: '2',
//   },
//   {
//     topicName: 'Wells',
//     courseCount: '2',
//   },
//   {
//     topicName: 'Wells ',
//     courseCount: '3',
//   },
//   {
//     topicName: 'Wells ',
//     courseCount: '1',
//   },
// ]

function initChart() {
  const xData = courseBarInfo.value?.map(item => item.topicName)
  const yData = courseBarInfo.value?.map(item => item.courseCount)

  const options: ECOption = {
    xAxis: {
      interval: 1,
      axisTick: {
        show: false,
      },
      // axisLabel: {
      //   rotate: 45,
      // },
      data: xData,
      // data: ChartData.map(item => item.topicName),
    },
    dataZoom: [
      {
        show: true,
        realtime: true,
        start: 0,
        end: 40,
        xAxisIndex: [0, 1],
      },
      {
        type: 'inside',
        realtime: true,
        start: 0,
        end: 40,
        xAxisIndex: [0, 1],
      },
    ],
    grid: {
      left: '30px',
      right: '30px',
      top: '20px',
    },
    tooltip: {},
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [17, 9],
        symbolOffset: [0, -6], // 上部椭圆
        symbolPosition: 'end',
        // @ts-expect-error 需要使用函数方式确定值
        symbol: (value) => {
          if (!value || +value === 0) {
            return 'none'
          }
          return 'circle'
        },
        z: 12,
        color: '#5CB58D',
        data: yData,
        animation: false,
      },
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [17, 9],
        symbolOffset: [0, 0], // 下部椭圆
        // "barWidth": "20",
        z: 11,
        color: '#007943',
        // @ts-expect-error 需要使用函数方式确定值
        symbol: (value) => {
          if (!value || +value === 0) {
            return 'none'
          }
          return 'circle'
        },
        data: yData,
        animation: false,
      },
      {
        name: 'Course Total',
        type: 'bar',
        barWidth: '17',
        data: yData,
        itemStyle: {
          color: '#007943',
        },
        animation: false,
      },
    ],
  }
  chart = new ChartFactory(chartRef.value, options)
}

const getCourseBarInfo = async () => {
  loading.value = true
  try {
    courseBarInfo.value = await getCourseBar()
    initChart()
  } finally {
    loading.value = false
  }
}

function resize() {
  chart.resize()
}

getCourseBarInfo()
window.addEventListener('resize', resize)
onDeactivated(() => {
  window.removeEventListener('resize', resize)
})
onActivated(() => {
  window.addEventListener('resize', resize)
})
</script>

<template>
  <div v-loading="loading" class="h-[400px] bg-white rounded-[10px] flex flex-col p-5">
    <div class="flex items-center gap-2.5 mb-5">
      <Icon icon="ep:histogram" color="#007943" :size="20" />
      <span class="text-lg text-[#233a35]">{{ t('statistics.course.subjectCourseStatistics') }}</span>
    </div>
    <div ref="chartRef" class="flex-1 shrink-0"></div>
  </div>
</template>

<style scoped lang='scss'>

</style>
