<script setup lang='ts'>
import Total from './components/Total/index.vue'
import Quick from './components/Quick/index.vue'
import CourseBarChart from './components/CourseBarChart/index.vue'
import Statistics from './components/Statistics/index.vue'
import UserPieChart from './components/UserPieChart/index.vue'
import TaskTodo from '@/views/bpm/task/todo/index.vue'

const tabName = ref('1')
const taskTodoRef = ref()
// 每次切换tab页签进行代办数据刷新
const selectActive = (val: string) => {
  if (val === '2') {
    taskTodoRef.value.resetQuery()
  }
}
</script>

<template>
  <ContentWrap>
    <el-tabs v-model="tabName" @tab-change="selectActive">
      <el-tab-pane label="Dashboard" name="1">
        <div class="grid grid-rows-[auto_minmax(0,1fr)_minmax(0,1fr)] grid-cols-[313px_440px_auto] gap-x-5">
          <Total :data="totalInfo" class="row-span-1 col-span-3" />
          <Quick class="row-span-2 col-span-1 mt-5" />
          <!-- <div class="row-span-2 border border-red-600">
            1
          </div> -->
          <CourseBarChart :data="courseBarInfo" class="mt-5 col-span-2" />
          <Statistics class="row-span-1 col-span-1 mt-5" />
          <UserPieChart class="row-span-1 col-span-1 mt-5" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="My-To do" name="2">
        <TaskTodo ref="taskTodoRef" :is-showCategory="false" :is-showProcessDDefinition="false"  />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style scoped lang='scss'>

</style>
