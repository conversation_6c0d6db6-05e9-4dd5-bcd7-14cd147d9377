<script setup lang='ts'>
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { BannerStatus } from './enums'
import {addBanner, BannerSaveVO, getBanner, updateBanner} from '@/api/setting/banner'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
// 文件上传组件
import FileUpload from '@/components/ImageUpload/index.vue'
import { useI18n } from "vue-i18n"
import { useTagsViewStore } from "@/store/modules/tagsView"
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { push, currentRoute } = useRouter() // 路由
const { delView } = useTagsViewStore() // 视图操作
const route = useRoute()
const id = route.query.id
const formRef = ref<FormInstance>()
const formLoading = ref(false)
const form = ref<BannerSaveVO>({
  title: '',
  summary: '',
  image: '',
  details: '',
  sort: 0,
  status: BannerStatus.On,
})
const formRules = reactive({
  title: [{ required: true, message: t('setting.banner.titleRule')}],
  image: [{ required: true, message: t('setting.banner.coverRule')}],
})
const getDetail = async () => {
  form.value = await getBanner(id as unknown as number)
}
const closeToBannerlist = () => {
  delView(unref(currentRoute))
  push('/setting/banner')

}
const handleSubmit = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  try {
    formLoading.value = true
    if (id !== undefined) {
      await updateBanner(form.value)
      message.success(t('common.updateSuccess'))
    } else {
      await addBanner(form.value)
      message.success(t('common.createSuccess'))
    }
    closeToBannerlist()
  } finally {
    formLoading.value = false
  }
}
/** 初始化 */
onMounted(() => {
  if (route.query.id) {
    getDetail()
  }
})
</script>

<template>
  <div class="app-container">
    <ContentWrap>
      <p class="text-[#233A35] text-xl">
        {{ t('setting.banner.basicInfo')}} <span class="font-bold">{{ t('setting.banner.maxSize')}}</span>
      </p>
    </ContentWrap>
    <ContentWrap>
      <el-form ref="formRef" :model="form" label-width="120" label-position="left" :rules="formRules" class="mt-5">
        <el-form-item prop="title" :label="t('setting.banner.title')">
          <el-input v-model="form.title" class="!w-[500px]" :placeholder="t('setting.banner.titlePH')" />
        </el-form-item>
        <el-form-item prop="summary" :label="t('setting.banner.summary')">
          <el-input
            v-model:model-value="form.summary" type="textarea" :rows="5" :placeholder="t('setting.banner.summaryPH')" maxlength="5000" show-word-limit
          />
        </el-form-item>
        <el-form-item prop="image" :label="t('setting.banner.cover')">
          <FileUpload v-model="form.image" :tip-text="t('setting.banner.coverPH')" :file-ratio="[990, 310]" :limit="1" />
        </el-form-item>
        <el-form-item prop="details" :label="t('setting.banner.detail')">
          <editor v-model="form.details" :min-height="192" />
        </el-form-item>
        <el-form-item :label="t('setting.banner.sort')" prop="sort">
          <el-input-number v-model="form.sort" :min="0" />
        </el-form-item>
        <el-form-item prop="status" :label="t('setting.banner.onShelfTime')">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.SETTING_BANNER_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="+dict.value"
            />
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="formLoading" @click="handleSubmit">
            {{ t('global.confirm') }}
          </el-button>
          <el-button @click="closeToBannerlist">
            {{ t('global.cancel') }}
          </el-button>
        </el-form-item>
      </el-form>

    </ContentWrap>
  </div>
</template>

<style scoped lang='scss'>

</style>
