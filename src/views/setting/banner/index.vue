<script setup lang='ts'>
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance } from 'element-plus'
import { BannerStatus } from './enums/index'
import {delBanner, listBanner, updateBanner, BannerRespVO, BannerReqVO, BannerSaveVO} from '@/api/setting/banner'
import { formatImgUrl } from '@/utils'
import { dateFormatter } from '@/utils/formatTime'

import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
const queryRef = ref<FormInstance>()
const queryData = reactive<{ queryParams: BannerReqVO, total: number }>({
  queryParams: {
    title: '',
    summary: '',
    image: '',
    details: '',
    status: undefined,
    pageNo: 1,
    pageSize: 10,
  },
  total: 0,
})
const { queryParams, total } = toRefs(queryData)
const list = ref<BannerRespVO[]>()
const loading = ref(false)
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const router = useRouter()
// const ids = ref<number[]>()
// const single = ref(false)
// const multiple = ref(true)
const getList = async () => {
  loading.value = true
  try {
    const data = await listBanner(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
// const handleSelectionChange(selection: Banner[]) {
//   ids.value = selection.map(item => item.id)
//   single.value = selection.length !== 1
//   multiple.value = !selection.length
// }
const handleUpdateStatus = async (banner: BannerSaveVO, status: number) => {
  loading.value = true
  try {
    await updateBanner({ ...banner, status })
    await getList()
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row: BannerSaveVO) => {
  // const noticeIds = row.id || ids.value
  const ids = row.id
  try {
    // 删除的二次确认
    await message.delConfirm()
    loading.value = true
    // 发起删除
    await delBanner(ids)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } finally {
    loading.value = false
  }
}

const handleToAction = (row?: BannerRespVO) => {
  router.push({ name: row.id ? 'BannerEdit' : 'BannerAdd', query: {
    id: row.id,
  } })
}
/** 初始化 */
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="app-container">
    <ContentWrap>
      <el-form ref="queryRef" :model="queryParams" inline>
        <el-form-item :label="t('setting.banner.status')" prop="status">
          <el-select v-model="queryParams.status" class="!w-36">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.SETTING_BANNER_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('setting.banner.title')" prop="title">
          <el-input v-model="queryParams.title" class="!w-50" clearable @keydown.enter="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button type="primary" plain @click="handleToAction">
            <Icon class="mr-5px" icon="ep:plus" />
            {{ t('action.add') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <!-- @selection-change="handleSelectionChange" -->
      <el-table v-loading="loading" :data="list">
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column prop="title" :label="t('setting.banner.title')">
          <template #default="{ row }">
            <div class="flex items-center gap-2.5">
              <el-image :src="formatImgUrl(row.image)" class="w-[86px] h-[50px] rounded-[2px] shrink-0" />
              <p class="text-sm text-[#23293A] line-clamp-2" :title="row.title">
                {{ row.title }}
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sort" :label="t('setting.banner.no')" width="145" />
        <el-table-column prop="status" :label="t('setting.banner.status')" width="175">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.SETTING_BANNER_STATUS" :value="row.status" />
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" :label="t('setting.banner.updateTime')" width="230" :formatter="dateFormatter" />
        <el-table-column :label="t('global.action')" align="center" fixed="right">
          <template #default="{ row }">
            <div class="w-[130px] inline-block me-3">
              <el-button
                v-if="row.status === BannerStatus.Off"
                v-hasPermi="['setting:banner:shelf']" link type="primary" @click="handleUpdateStatus(row, BannerStatus.On)"
              >
                <Icon icon="ep:upload" />
                {{ t('action.putOnShelf') }}
              </el-button>
              <el-button v-else v-hasPermi="['setting:banner:shelf']" link type="primary" @click="handleUpdateStatus(row, BannerStatus.Off)">
                <Icon icon="ep:download" />
                {{ t('action.removeOffShelf') }}
              </el-button>
            </div>

            <el-button v-hasPermi="['setting:banner:edit']" link type="primary" @click="handleToAction(row)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <el-button v-hasPermi="['setting:banner:remove']" link type="primary" @click="handleDelete(row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </ContentWrap>
  </div>
</template>

<style scoped lang='scss'>

</style>
