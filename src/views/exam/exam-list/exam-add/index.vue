<script setup lang="ts" name="createExam">
import type { ComponentInternalInstance } from 'vue'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import NewBasic from '../components/NewBasic.vue'
import NewAssignStaff from '../components/NewAssignStaff.vue'
import { addExam, updateExam } from '@/api/topicMgt/exam'
import { useTagsViewStore } from "@/store/modules/tagsView"
import moment from 'moment'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { push, currentRoute } = useRouter() // 路由
const { delView } = useTagsViewStore() // 视图操作
// const router = useRouter()
const activeIndex = ref('1')
const ifShowBasc = ref(true)
const ifShowAssign = ref(false)
const RefNewBasic = ref()
const RefNewAssignStaff = ref()
const examId = ref('')
const menuItems = computed(() => [
  {
    id: 1,
    text: t('learningCenter.course.basicInfo'),
  },
  {
    id: 2,
    text: t('learningCenter.course.assignScope'),
    disabled: !examId.value,
  },
])
const menuSelected = (key: string, keyPath: string[]) => {
  activeIndex.value = key
}

const createExam = async () => {
  RefNewBasic.value.ruleForm.usage = 0
  // 应二期接口传参格式，将开始时间和结束时间转换为时间戳格式
  RefNewBasic.value.ruleForm.beginTime = moment(RefNewBasic.value.ruleForm.beginTime).valueOf()
  RefNewBasic.value.ruleForm.endTime = moment(RefNewBasic.value.ruleForm.endTime).valueOf()
  if (RefNewBasic.value.ruleForm.id) {
    await updateExam(RefNewBasic.value.ruleForm)
    message.success(t('global.addSuccess'))
  }
  else {
    const data = await addExam(RefNewBasic.value.ruleForm)
    message.success(t('global.addSuccess'))
    examId.value = data
    RefNewBasic.value.ruleForm.id = data
    activeIndex.value = '2'
  }
}
/** 跳转添加页面 */
const handleCancel = () => {
  delView(unref(currentRoute))
  push('/exam-mgt/exam')
}
watch(
  () => activeIndex.value,
  (newValue: any) => {
    activeIndex.value = newValue
    if (newValue === '1') {
      ifShowBasc.value = true
      ifShowAssign.value = false
    }
    else if (newValue === '2') {
      ifShowBasc.value = false
      ifShowAssign.value = true
    }
  },
  { immediate: true },
)
</script>

<template>
  <div class="app-container">
    <div class="flex items-center mb-4 bg-[#E4F4EE] rounded-[10px] px-4 h-[140px]">
      <!-- <el-image class="my-[20px] ml-[30px] w-[60px] h-[75px]" fit="fill" /> -->
      <div class="ml-[20px]">
        <div class="text-2xl text-[#23293A] font-medium" style="line-height: 60px">
          <!-- {{ examInfo?.name }} -->
          {{ t('router.createExam') }}
        </div>
        <!-- <div class="w-[100px] h-[23px] leading-[23px] rounded-[4px] text-center text-sm text-white bg-[#007943]">
          {{ statusList[examInfo?.status] }}
        </div> -->
      </div>
      <el-button class="ms-auto" type="primary" @click="handleCancel">
        {{ t('action.back') }}
      </el-button>
    </div>
    <div class="flex mt-6 gap-5">
      <ContentWrap>
        <div class="w-[220px] shrink-0 rounded-x p-2.5 border border-[#CDDBF1]">
          <el-menu
            v-model="activeIndex" :default-active="activeIndex" :style="{
            '--el-menu-item-height': '34px',
          }"
            @select="menuSelected"
          >
            <el-menu-item v-for="menu in menuItems" :key="menu.id" :index="`${menu.id}`" :disabled="menu.disabled">
              {{ menu.text }}
            </el-menu-item>
          </el-menu>
        </div>
      </ContentWrap>

      <div class="flex-1 rounded-x border border-[#CDDBF1]">
        <ContentWrap v-show="ifShowBasc">
          <NewBasic ref="RefNewBasic" :title-name="t('learningCenter.course.basicInfo')" @save-exam="createExam" />
        </ContentWrap>
        <ContentWrap v-show="ifShowAssign">
          <NewAssignStaff ref="RefNewAssignStaff" :title-name="t('learningCenter.course.assignScope')" :exam-id="examId" type="add" />
        </ContentWrap>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.el-menu-item {
  height: 34px;
}
:deep(.el-menu) {
  border-right: 0 !important;
  .el-menu-item {
    &.is-active {
      background-color: #E4F4EE;
      border-radius: 4px;
    }
  }
}
</style>
