<script setup lang="ts" name="EditeExam">
import type { ComponentInternalInstance } from 'vue'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import NewBasic from '../components/NewBasic.vue'
import NewAssignStaff from '../components/NewAssignStaff.vue'
import { getExam, updateExam } from '@/api/topicMgt/exam'
import examTopIcon from '@/assets/images/exam/exam_top_icon.png'
import { useTagsViewStore } from "@/store/modules/tagsView"
import { formatDate } from "@/utils/formatTime"
import moment from 'moment'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { push, currentRoute } = useRouter() // 路由
const { delView } = useTagsViewStore() // 视图操作
const route = useRoute()
const activeIndex = ref('1')
const examInfo = ref()
const RefNewBasic = ref()
const RefNewAssignStaff = ref()
const statusList = ref([t('examMgt.exam.notStarted'), t('examMgt.exam.inProcess'), t('examMgt.exam.expired')])
const statusBtn = ref()
const menuItems = computed(() => [
  {
    id: 1,
    text: t('learningCenter.course.basicInfo'),
    disabled: statusBtn.value,
  },
  {
    id: 2,
    text: t('learningCenter.course.assignScope'),
  },
])

const menuSelected = (key: string, keyPath: string[]) => {
  activeIndex.value = key
}

const getExamInfo = async () => {
  const data = await getExam(route.query.id)
  examInfo.value = data
  statusBtn.value = data.status
  // 将返回的时间戳格式转换为YYYY-MM-DD HH:mm:ss格式
  data.beginTime = formatDate(data.beginTime)
  data.endTime = formatDate(data.endTime)
  RefNewBasic.value.handleEdit(data)
  if (route.query?.activeNum) {
    activeIndex.value = route.query?.activeNum
  }
}
// 编辑Exam
const editeExam = async () => {
  // 应二期接口传参格式，将开始时间和结束时间转换为时间戳格式
  RefNewBasic.value.ruleForm.beginTime = moment(RefNewBasic.value.ruleForm.beginTime).valueOf()
  RefNewBasic.value.ruleForm.endTime = moment(RefNewBasic.value.ruleForm.endTime).valueOf()
  await updateExam(RefNewBasic.value.ruleForm)
  message.success(t('global.editSuccess'))
}
/** 跳转添加页面 */
const handleCancel = () => {
  delView(unref(currentRoute))
  push('/exam-mgt/exam')
}
getExamInfo()
</script>

<template>
  <div class="app-container">
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4 mb-4">
      <el-image class="my-[20px] ml-[30px] w-[60px] h-[75px]" :src="examTopIcon" fit="fill" />
      <div class="ml-[20px]">
        <div class="text-xl text-[#23293A] font-medium" style="line-height: 60px">
          {{ examInfo?.name }}
        </div>
        <div class="w-[100px] h-[23px] leading-[23px] rounded-[4px] text-center text-sm text-white bg-[#007943]">
          {{ statusList[examInfo?.status] }}
        </div>
      </div>
      <el-button class="ms-auto flex-direction" type="primary" @click="handleCancel">
        {{ t('action.back') }}
      </el-button>
    </div>
    <div class="flex mt-6 gap-5">
      <ContentWrap>
        <div class="w-[220px] shrink-0 rounded-x p-2.5 border border-[#CDDBF1]">
          <el-menu
            v-model="activeIndex" :default-active="activeIndex" :style="{
            '--el-menu-item-height': '34px',
          }"
            @select="menuSelected"
          >
            <el-menu-item v-for="menu in menuItems" :key="menu.id" :index="`${menu.id}`" :disabled="menu.disabled">
              {{ menu.text }}
            </el-menu-item>
          </el-menu>
        </div>
      </ContentWrap>
      <div class=" flex-1 border border-[#CDDBF1] rounded-x">
        <ContentWrap v-show="activeIndex === '1'">
          <NewBasic ref="RefNewBasic" :title-name="t('learningCenter.course.basicInfo')" @save-exam="editeExam" />
        </ContentWrap>
        <ContentWrap v-show="activeIndex === '2'">
          <NewAssignStaff ref="RefNewAssignStaff" :title-name="t('learningCenter.course.assignScope')" :exam-id="route.query.id" type="edit" />
        </ContentWrap>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-menu) {
  border-right: 0 !important;
  .el-menu-item {
    &.is-active {
      background-color: #E4F4EE;
      border-radius: 4px;
    }
  }
}
</style>
