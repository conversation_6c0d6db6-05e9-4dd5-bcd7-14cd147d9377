<script setup lang="ts" name="ExamEditeNoCash">
import type { ComponentInternalInstance } from 'vue'
import AssignStaffDialog from './components/AssignStaffDialog.vue'
import SubjectSelect from '@/components/SubjectSelect/index.vue'
import { delExam, ExamRespVO, listExam,ExamStatusEnum } from '@/api/topicMgt/exam'
import { listTopicAll } from '@/api/category/topic'
import { listDept } from '@/api/system/dept'
import {dateFormatter, formatDate} from '@/utils/formatTime'
interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  name: string
  keywords?: string | null
  introduction: string
  deptCode: string
  answerTime: number
  beginTime: Date
  endTime: Date
  cutCount: number
  examMax: number
  optionRandom: boolean
  paperId: number
  params: object
  passScore: number
  type: number
  quesRandom: boolean
  status: number
  classifyName: string
}
const router = useRouter()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const RefAssignStaffDialog = ref()
const loading = ref(false)
const isVisible = ref<boolean>(false)
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryRef = ref()
const statusList = ref([t('examMgt.exam.notStarted'), t('examMgt.exam.inProcess'), t('examMgt.exam.expired')])
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject')
  },
])
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    deptCode: undefined,
  },
})
const { queryParams } = toRefs(data)
const deptList = ref()
// 查找
function handleSearch() {
  getList()
}
function handleReset() {
  reset()
  getList()
}
/** 表单重置 */
function reset() {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    deptCode: undefined,
    status: undefined,
  }
  queryRef.value?.resetFields()
}
function handleAdd() {
  router.push({ name: 'CreateExam' })
}
function handleEdit(row: any) {
  router.push({ name: 'EditeExam', query: { id: row.id } })
}
function handleAssign(row: any, activeNum: number) {
  router.push({ name: 'EditeExam', query: { id: row.id, activeNum } })
  // RefAssignStaffDialog.value.handleOpen(row.id)
}
const handleDelete = async (row: ExamRespVO) => {
  try {
    // 删除的二次确认
    await message.delConfirm(`${t('global.deleteTip') + row.name}?`)
    await delExam(row.id)
    getList()
    message.success(t('global.deleteSuccess'))
  } catch {}
}
function handleView(row: any, active: any) {
  if (active === '2-1') {
    router.push({ name: 'ExamView', query: { id: row.id, tabActive: active } })
  }
  else {
    router.push({ name: 'ExamView', query: { id: row.id } })
  }
}
const getList = async () => {
  loading.value = true
  try {
    const res = await listExam(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
/** 查询部门列表 */
// function getDeptList() {
//   listDept().then((response) => {
//     deptList.value = proxy!.handleTree(response.data, 'deptId')
//   })
// }
// 监听当前路由 刷新列表数据
watch(
  () => router.currentRoute.value,
  (newValue: any, oldValue: any) => {
    getList()
  },
  { immediate: true },
)
// getDeptList()
onMounted(() => {
  getList()
  getSubjectData()
})
</script>

<template>
  <ContentWrap>
    <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
      <el-form-item :label="t('examMgt.paper.name')">
        <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable style="width: 200px" @keydown.enter="handleSearch" />
      </el-form-item>
      <!-- <el-form-item :label="$t('sys.user.department')" prop="departmentId">
        <el-tree-select
          v-model="queryParams.departmentId" :data="deptList"
          :props="{ value: 'deptId', label: 'deptName', children: 'children' }" value-key="deptId" check-strictly
          clearable
          style="width: 240px;"
        />
      </el-form-item> -->
      <el-form-item :label="t('common.status')">
        <el-select v-model="queryParams.status" :placeholder="t('common.chooseText')" clearable style="width: 160px">
          <el-option :label="t('examMgt.exam.notStarted')" value="0" />
          <el-option :label="t('examMgt.exam.inProcess')" value="1" />
          <el-option :label="t('examMgt.exam.expired')" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('category.topic.subjectName')">
        <!-- <el-select v-model="queryParams.classifyId" placeholder="Please choose" clearable style="width: 200px">
          <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
        </el-select> -->
        <SubjectSelect v-model="queryParams.classifyId" :has-no-subject="true" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('action.search') }}
        </el-button>
        <el-button type="default" @click="handleReset">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('action.reset') }}
        </el-button>
        <el-button type="primary" plain @click="handleAdd">
          <Icon class="mr-5px" icon="ep:plus" />
          {{ t('action.add') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" :data="tableData">
      <el-table-column prop="name" :label="t('examMgt.exam.examName')" min-width="240" fixed="left" />
      <el-table-column prop="classifyName" :label="t('category.topic.subjectName')" min-width="170">
        <template #default="{ row }">
          {{ row.classifyId === 0 || !row.classifyName ? t('common.noSubject') : row.classifyName }}
        </template>
      </el-table-column>
      <el-table-column prop="type" :label="t('learningCenter.course.type')" min-width="160">
        <template #default="{ row }">
          {{ row.type === 1 ? t('examMgt.paper.autoPaper') : t('learningCenter.exam.customizedPaper') }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="deptCode" label="Department" width="150" align="center" /> -->
      <el-table-column prop="answerTime" :label="t('examMgt.exam.examTime')" min-width="180">
        <template #default="{ row }">
          <p>
            {{ formatDate(row.beginTime) }}
          </p>
          <p>
            {{ formatDate(row.endTime) }}
          </p>
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="t('common.status')" min-width="120">
        <template #default="{ row }">
          <div>
            {{ statusList[row.status] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalNum" :label="t('learningCenter.course.assignedNumber')" min-width="140">
        <template #default="{ row }">
          <div v-if="row.totalNum > 0">
            <el-link type="primary" :underline="false" @click="handleView(row, '2-1')">
              {{ row.totalNum }}
            </el-link>
          </div>
          <div v-else class="text-primary">
            {{ row.totalNum }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createBy" :label="t('category.journey.creator')" min-width="130" />
      <el-table-column prop="creationTime" :label="t('category.journey.creationTime')" :formatter="dateFormatter" min-width="180" />
      <el-table-column fixed="right" :label="t('global.action')" min-width="260">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleView(row)">
            <Icon icon="ep:view" />
            {{ t('action.view') }}
          </el-button>
          <el-button v-show="row.status === 0" link type="primary" @click="handleEdit(row)">
            <Icon icon="ep:edit" />
            {{ t('action.edit') }}
          </el-button>
          <el-button v-show="row.status === ExamStatusEnum.IN_PROGRESS || row.status === ExamStatusEnum.NOT_STARTED" link type="primary" @click="handleAssign(row, 2)">
            <Icon icon="ep:user" />
            {{ t('action.assignCourse') }}
          </el-button>
          <el-button link type="primary" @click="handleDelete(row)">
            <Icon icon="ep:delete" />
            {{ t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
  </ContentWrap>
  <AssignStaffDialog ref="RefAssignStaffDialog" v-model="isVisible" :title-name="t('learningCenter.course.assignScope')" />
</template>

<style scoped lang="scss"></style>
