<script setup lang="ts" name="ExamRecord">
import { getExamPaperResult } from '@/api/topicMgt/exam'
import examTopIcon from '@/assets/images/exam/exam_top_icon.png'
import { formatContent } from '@/utils'
import { useTagsViewStore } from "@/store/modules/tagsView"
const { t } = useI18n()
const { push, currentRoute } = useRouter() // 路由
const { delView } = useTagsViewStore() // 视图操作
const route = useRoute()
const examResult = ref()

// 获取考试答题结果
const getExamResult = async () => {
  examResult.value = await getExamPaperResult({ examRecordId: route.query.recordId })
}
const getOptionClass = (question, option) => {
  if (!question.answer) {
    if (option.answer) {
      return 'text-[#FF9900]'
    }
    else {
      return 'text-[#4A4A4A]'
    }
  }
  if (question.right) {
    if (option.answer) {
      return 'text-[#53BC00]'
    }
    return 'text-[#4A4A4A]'
  }
  else {
    if (option.answer) {
      return 'text-[#FF9900]'
    }
    else {
      if (question.answer.includes(option.tag)) {
        return 'text-[#F51515]'
      }
      return 'text-[#4A4A4A]'
    }
  }
}
/** Todo:跳转添加页面 */
const handleCancel = () => {
  delView(unref(currentRoute))
  push('/exam-mgt/exam-view')
}
onMounted(() => {
  getExamResult()
})
</script>

<template>
  <div>
    <!-- <h2 class="text-xl mb-10">Create exam</h2> -->
    <div class="flex mb-4 bg-[#E4F4EE] rounded-[10px]">
      <el-image class="my-[20px] ml-[30px] w-[60px] h-[75px]" :src="examTopIcon" fit="fill" />
      <div class="ml-[20px]">
        <div class="text-[20px] text-[#23293A] font-medium" style="line-height: 60px">
          {{ examResult?.examName }}
        </div>
        <div class="text-[#51586A] text-[14px]">
          {{ t('examMgt.exam.score')}}
          <span class="text-[#DB3D3D]">
            {{ examResult?.examRecord.totalScore }}
          </span>
        </div>
      </div>
      <!-- <el-button class="ms-auto" type="primary" @click="handleCancel">
        Back
      </el-button> -->
    </div>
    <div v-for="(question, index) in examResult?.questions" :key="index">
      <div class="my-[10px] flex">
        <span>{{ `${index + 1}.` }}</span>
        <span v-dompurify-html="formatContent(question.content)"></span>
        <span class="font-bold ml-1">{{ `[${question.answer ? question.answer : t('examMgt.exam.nonAnswered')}]` }}</span>
      </div>
      <!-- 1.回答错误：正确答案标橙色，错误答案标红
           2.回答正确：答案为绿 -->
      <div v-for="(option, optionsIndex) in question.options" :key="optionsIndex" class="ml-4 my-1 flex" :class="getOptionClass(question, option)">
        <span>{{ `${option.tag}. ` }}</span>
        <span v-dompurify-html="formatContent(option.content)"></span>
      </div>
    </div>
  </div>
</template>
