<script setup lang="ts" name="newBasic">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps, FormRules } from 'element-plus'
import moment from 'moment'
import ChoosePaper from './ChoosePaper.vue'
import {getPaper, PaperRespVO} from '@/api/topicMgt/paper'
import { deepClone } from '@/utils'
import tabPlugin from '@/plugins/tab'
const props = defineProps<{ titleName: string }>()
const emit = defineEmits(['saveExam'])

interface RuleForm {
  id: number | undefined
  name: string
  examTimeTemp: Array<Date>
  beginTime: string
  endTime: string
  type: string | undefined
  examMax: number
  passScore: number
  answerTime: number
  paperId: number | undefined
  paperName: string
  cutCount: number
  optionRandom: boolean
  quesRandom: boolean
  instruction: string
}

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const router = useRouter()
const labelPosition = ref<FormProps['labelPosition']>('left')
const isVisible = ref<boolean>(false)
const defaultDate = ref()
const RefChoosePaper = ref()
const formRef = ref<FormInstance>()
const ruleForm = ref<RuleForm>({
  id: undefined,
  name: '',
  examTimeTemp: [],
  beginTime: '',
  endTime: '',
  type: undefined,
  examMax: 0,
  passScore: 0,
  answerTime: 0,
  paperId: undefined,
  paperName: '',
  cutCount: 0,
  optionRandom: true,
  quesRandom: true,
  instruction: '',
})
const rules = reactive<FormRules<RuleForm>>({
  name: [{ required: true, message: t('examMgt.exam.examNameRule'), trigger: 'blur' }],
  examTimeTemp: [
    {
      required: true,
      message: t('examMgt.exam.examTimeRule'),
      trigger: 'change',
    },
  ],
  // type: [{ required: true, message: 'Please choose paper', trigger: 'blur' }],
  examMax: [{ required: true, message: t('examMgt.exam.examAttemptsRule'), trigger: 'blur' }],
  passScore: [{ required: true, message: t('examMgt.exam.passScoreRule'), trigger: 'blur' }],
  answerTime: [{ required: true, message: t('examMgt.exam.examDurationRule'), trigger: 'blur' }],
  paperId: [
    {
      required: true,
      message: t('examMgt.exam.examPaperRule'),
      trigger: 'change',
    },
  ],
})
const defaultTime2: [Date, Date] = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]
// const form = ref()

const asideHeight = ref('300px')
/** 表单重置 */
function reset() {
  formRef.value?.resetFields()
}

const handleSave = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  emit('saveExam')
}

const handleCancel = () => {
  tabPlugin.closeOpenPage()
  router.push({ name: 'Exam', query: { tabSelect: 'exam' } })
}
// 编辑试卷
const handleEdit = (val: PaperRespVO) => {
  ruleForm.value = { ...val }
  // ruleForm.value.examTimeTemp = [...ruleForm.value.beginTime, ruleForm.value.endTime]
  // defaultDate.value = [new Date(ruleForm.value.beginTime), new Date(ruleForm.value.endTime)]
  ruleForm.value.examTimeTemp = [new Date(ruleForm.value.beginTime), new Date(ruleForm.value.endTime)]
  getPaperInfo(ruleForm.value.paperId)
}
// 获取试卷信息
const getPaperInfo = async (paperId: number) => {
  const data = await getPaper(paperId)
  ruleForm.value.paperName = data.name
  ruleForm.value.type = data.isRandom ? '1' : '0'
}

// 考试时间段选择
function handelExamTimeChange(val) {
  ruleForm.value.beginTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
  ruleForm.value.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
}

// 选择试卷
function handleChoosePaper() {
  RefChoosePaper.value.handleOpen(ruleForm.value.paperId)
}
function handlePaperChooseConfirm(val) {
  ruleForm.value.paperId = val.id
  ruleForm.value.paperName = val.name
  ruleForm.value.type = val.isRandom ? '1' : '0'
  // RefChoosePaper.value.handleOpen()
}

// 动态计算高度
function mixAsideHeight() {
  const windowHeight = window.innerHeight // 获取当前窗口的高度
  const offset = 200 // 偏移量，即需要减去的像素值
  asideHeight.value = `${windowHeight - offset > 400 ? windowHeight - offset : 400}px`
}
onMounted(() => {
  mixAsideHeight() // 调用 mixAsideHeight 函数来计算并更新 aside 高
  window.addEventListener('resize', mixAsideHeight) // 使用 window.addEventListener 监听窗口大小变化事件，在窗口大小发生变化时自动重新计算和更新第一部分的高度。
})
onUnmounted(() => {
  window.removeEventListener('resize', mixAsideHeight) // 取消监听窗口大小变化事件，以避免出现内存泄漏。
})

defineExpose({
  ruleForm,
  handleEdit,
})
</script>

<template>
  <div class="rounded-x py-5 px-7" :style="{ height: asideHeight }">
    <h2 class="text-xl">
      {{ titleName }}
    </h2>
    <el-form ref="formRef" class="mt-5 w-3/4" :model="ruleForm" label-width="auto" :label-position="labelPosition" :rules="rules">
      <el-form-item prop="name" :label="t('examMgt.exam.examName')">
        <el-input v-model="ruleForm.name" style="width: 400px" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item prop="examTimeTemp" :label="t('examMgt.exam.examTime')">
        <div class="w-[600px]">
          <el-date-picker v-model="ruleForm.examTimeTemp" type="datetimerange" range-separator="to" :start-placeholder="t('global.startDate')" :end-placeholder="t('global.endDate')" format="YYYY-MM-DD HH:mm:ss" :default-time="defaultTime2" @change="handelExamTimeChange" />
        </div>
      </el-form-item>
      <el-form-item prop="paperId" :label="t('examMgt.exam.selectExamPaper')">
        <span class="mr-4">{{ ruleForm.paperName }}</span>
        <el-button type="primary" plain @click="handleChoosePaper">
          {{ t('action.chooseExamPaper') }}
        </el-button>
      </el-form-item>
      <el-form-item prop="type" :label="t('examMgt.exam.examType')">
        <span v-if="ruleForm.type === '0'"> {{ t('learningCenter.exam.customizedPaper') }} </span>
        <span v-if="ruleForm.type === '1'"> {{ t('learningCenter.exam.autoPaper') }} </span>
      </el-form-item>
      <el-form-item prop="examMax" :label="t('learningCenter.exam.examAttempts')">
        <el-input-number v-model="ruleForm.examMax" :min="1" :max="10" />
        <span class="ml-2">{{ t('examMgt.exam.times') }}</span>
      </el-form-item>
      <el-form-item prop="passScore" :label="t('learningCenter.exam.passScore')">
        <el-input v-model="ruleForm.passScore" type="number" style="width: 200px" :placeholder="t('learningCenter.exam.passScorePH')">
          <template #append>
            {{ t('learningCenter.exam.points') }}
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="answerTime" :label="t('examMgt.exam.examDescription')">
        <el-input v-model="ruleForm.answerTime" type="number" style="width: 200px" :placeholder="t('learningCenter.exam.examScorePH')">
          <template #append>
            {{ t('learningCenter.exam.minutes') }}
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="instruction" :label="t('examMgt.exam.examDescription')">
        <el-input v-model="ruleForm.instruction" :rows="4" type="textarea" :placeholder="t('examMgt.exam.examDescriptionPH')" style="width: 400px" show-word-limit maxlength="5000" />
      </el-form-item>
      <el-form-item label-width="140" class="mt-8">
        <el-button type="primary" @click="handleSave">
          {{ t('global.confirm') }}
        </el-button>
        <el-button @click="handleCancel">
          {{ t('global.cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
  <ChoosePaper ref="RefChoosePaper" v-model="isVisible" title-name="Choose exam paper" @paper-choose="handlePaperChooseConfirm" />
</template>
