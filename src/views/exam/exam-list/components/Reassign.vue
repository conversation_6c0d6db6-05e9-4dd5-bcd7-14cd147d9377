<script setup lang="ts" name="ReassignExam">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'

interface BatchReassign {
  assignmentId: number
  examMax: number
}

const props = defineProps<{ modelValue: boolean }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'reassignExam'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const formRef = ref()
const labelPosition = ref<FormProps['labelPosition']>('left')
const reassignForm = ref<BatchReassign>({
  assignmentId: 0,
  examMax: 0,
})
const recordData = ref()

const isVisible = computed({
  get() {
    reset()
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})
function reset() {
  formRef.value?.resetFields()
}

function handleOpen(itemData: any) {
  recordData.value = itemData
  reassignForm.value.examMax = recordData.value.examMax - recordData.value.examNum
  reassignForm.value.assignmentId = itemData.assignmentId
  isVisible.value = true
}
function handleClose() {
  isVisible.value = false
}
// 确认选择
function handleConfirm() {
  emit('reassignExam', { assignmentId: reassignForm.value.assignmentId, examMax: reassignForm.value.examMax * 1 + recordData.value.examNum * 1 })
  isVisible.value = false
}

defineExpose({ handleOpen })
</script>

<template>
  <Dialog v-model="isVisible" :title="t('action.reAssign')" width="500" class="notice-dialog" @close="handleClose">
    <el-form style="max-width: 80%" :model="reassignForm" label-width="auto" class="demo-dynamic m-auto" :label-position="labelPosition">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="t('examMgt.exam.examTaken')">
            <span class="text-black font-bold mr-2">
              {{ recordData.examNum }}
            </span>
            <span>{{ t('examMgt.exam.times') }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="examMax" :label="t('examMgt.exam.remain')">
            <el-input v-model="reassignForm.examMax" type="number">
              <template #append>
                {{ t('examMgt.exam.times') }}
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div>
        <el-button type="primary" @click="handleConfirm">
          {{ t('global.confirm') }}
        </el-button>
        <el-button @click="handleClose">
          {{ t('global.cancel') }}
        </el-button>
      </div>
    </template>
  </Dialog>
</template>
