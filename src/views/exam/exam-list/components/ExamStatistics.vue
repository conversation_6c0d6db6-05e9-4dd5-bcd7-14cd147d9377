<script setup lang="ts" name="ExamStatistics">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import Reassign from './Reassign.vue'
import {exportExam, infoExam, reassignExam, staticsExam} from '@/api/topicMgt/exam'
import examDiscountChart from '@/assets/images/exam/exam_discount_chart.png'
import download from "@/utils/download"

interface DataItem {
  userId: number
  assignmentId: number
  recordId: number
  studentName: string
  badgeNo: string
  email: string
  department: string
  section: string
  position: string
  onSchedule: boolean
  status: number // 0：未考试，1：未通过，2：已通过）
  submitTime: string
  quizScore: number
  operator: number
  examMax: number
  examNum: number
  company: string
}
interface StaticsItem {
  failedNum: number
  onScheduleNum: number
  overtimeNum: number
  passRate: number
  passedNum: number
  takenNum: number
  totalNum: number
}
const props = defineProps<{
  exameTitle: string
}>()
const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const RefReassign = ref()
const isVisible = ref<boolean>(false)
const queryRef = ref()
const loading = ref(false)
const activeName = ref('first')
const statusList = ref([t('examMgt.exam.notAttend'), t('examMgt.exam.failed'), t('examMgt.exam.pass')])
const total = ref(0)
const tableData = ref<Array<DataItem>>([])
const staticsData = ref<StaticsItem>()
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    id: undefined,
    studentName: undefined,
    badgeNo: undefined,
    email: undefined,
    onSchedule: undefined,
    status: undefined,
  },
})
const { queryParams } = toRefs(data)
// 查找
function handleSearch() {
  getList()
}
function handleReset() {
  reset()
  getList()
}
/** 表单重置 */
function reset() {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    id: undefined,
    studentName: undefined,
    badgeNo: undefined,
    email: undefined,
    onSchedule: undefined,
    status: undefined,
  }
  queryRef.value?.resetFields()
}
// 获取考试用户列表
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.id = route.query.id
    const res = await infoExam(queryParams.value)
    if (res) {
      tableData.value = res.list
      total.value = res.total
    }
    else {
      tableData.value = []
      total.value = 0
    }
  } finally {
    loading.value = false
  }
}
// 查询考试统计
const getStaticsExam = async () => {
  queryParams.value.id = route.query.id
  staticsData.value = await staticsExam(queryParams.value)
}

// 查看考试详情
function handleView(row: any) {
  // router.push(`/topicMgt/exam-view/record/${row.recordId}`)
  router.push({ name: `ExamRecord`, query: { recordId: row.recordId } })
}
// reassign
function handleReassign(row: any) {
  RefReassign.value.handleOpen(row)
}
// 设置考试次数
const handleReassignConfirm = async (val: any) => {
  await reassignExam(val)
  message.success(t('global.addSuccess'))
  getList()
}
function calculatePercentage(numerator: number | undefined, denominator: number | undefined) {
  if (!numerator)
    return 0
  if (!denominator)
    return 0
  if (denominator === 0)
    return 0 // 防止除以0错误
  const percentage = (numerator / denominator) * 100
  return Math.floor(percentage) // 不四舍五入
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    const data = await exportExam(queryParams.value)
    download.excel(data, `Exam-${props.exameTitle}-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}

onMounted(() => {
  getList()
  getStaticsExam()
})
getList()
getStaticsExam()
</script>

<template>
  <div :style="{ maxHeight: asideHeight }">
    <el-row :gutter="20" class="p-5">
      <el-col :span="6">
        <div class="top_item_bg">
          <div class="item_content">
            <div class="item_number">
              {{ `${staticsData?.takenNum}/${staticsData?.totalNum}` }}
            </div>
            <div class="item_desc">
              {{ t('examMgt.exam.numberExam') }}
            </div>
          </div>
          <el-progress :width="90" type="circle" :percentage="calculatePercentage(staticsData?.takenNum, staticsData?.totalNum)" :stroke-width="15" color="#007943" stroke-linecap="butt" />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="top_item_bg">
          <div class="item_content">
            <div class="item_number">
              {{ staticsData?.passedNum ? staticsData?.passedNum : 0 }}
            </div>
            <div class="item_desc">
              {{ t('examMgt.exam.numberPass') }}
            </div>
          </div>
          <el-image class="statistic_icon" :src="examDiscountChart" fit="contain" />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="top_item_bg">
          <div class="item_content">
            <div class="item_number">
              {{ staticsData?.failedNum ? staticsData?.failedNum : 0 }}
            </div>
            <div class="item_desc">
              {{ t('examMgt.exam.numberFailed') }}
            </div>
          </div>
          <el-image class="statistic_icon" :src="examDiscountChart" fit="contain" />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="top_item_bg">
          <div class="item_content">
            <div class="item_number">
              {{ calculatePercentage(staticsData?.passedNum, staticsData?.takenNum) }}%
            </div>
            <div class="item_desc">
              {{ t('examMgt.exam.examPassRate') }}
            </div>
          </div>
          <el-progress :width="90" type="circle" :percentage="calculatePercentage(staticsData?.passedNum, staticsData?.takenNum)" :stroke-width="15" stroke-linecap="butt" />
        </div>
      </el-col>
    </el-row>
    <el-tabs v-model="activeName" class="px-5">
      <el-tab-pane :label="t('examMgt.exam.userList')" name="first">
        <ContentWrap>
          <!-- 查询条件 -->
          <el-form ref="queryRef" :inline="true" :model="queryParams" label-position="left">
            <el-form-item :label="t('examMgt.paper.name')">
              <el-input v-model="queryParams.studentName" :placeholder="t('common.inputText')" clearable style="width: 180px" />
            </el-form-item>
            <el-form-item :label="t('learningCenter.boarding.badgeNumber')">
              <el-input v-model="queryParams.badgeNo" :placeholder="t('common.inputText')" clearable style="width: 180px" />
            </el-form-item>
            <el-form-item :label="t('sys.user.email')">
              <el-input v-model="queryParams.email" :placeholder="t('common.inputText')" clearable style="width: 180px" />
            </el-form-item>

            <el-form-item :label="t('sys.company.status')">
              <el-select v-model="queryParams.status" :placeholder="t('common.chooseText')" clearable style="width: 180px">
                <el-option :label="t('examMgt.exam.notAttend')" value="0" />
                <el-option :label="t('examMgt.exam.failed')" value="1" />
                <el-option :label="t('examMgt.exam.pass')" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item :label="t('examMgt.exam.onSchedule')">
              <el-select v-model="queryParams.onSchedule" :placeholder="t('common.chooseText')" clearable style="width: 180px">
                <el-option :label="t('common.yes')" :value="true" />
                <el-option :label="t('common.no')" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                <Icon icon="ep:search" class="mr-5px" />
                {{ t('action.search') }}
              </el-button>
              <el-button type="default" @click="handleReset">
                <Icon icon="ep:refresh" class="mr-5px" />
                {{ t('action.reset') }}
              </el-button>
              <el-button plain type="primary" @click="handleExport">
                <Icon class="mr-5px" icon="ep:download" />
                {{ t('action.export') }}
              </el-button>
            </el-form-item>
          </el-form>
        </ContentWrap>
        <ContentWrap>
          <el-table v-loading="loading" :data="tableData">
            <el-table-column fixed="left" prop="studentName" :label="t('examMgt.paper.name')" min-width="240" />
            <el-table-column prop="badgeNo" :label="t('learningCenter.boarding.badgeNumber')" min-width="240" />
            <el-table-column prop="email" :label="t('learningCenter.course.email')" min-width="240" />
            <el-table-column prop="company" :label="t('learningCenter.course.company')" min-width="240" />
            <el-table-column prop="department" :label="t('learningCenter.course.deptName')" min-width="240" />
            <el-table-column prop="section" :label="t('learningCenter.course.section')" min-width="240" />
            <el-table-column prop="position" :label="t('learningCenter.course.position')" min-width="240" />
            <el-table-column prop="status" :label="t('learningCenter.course.status')" width="150">
              <template #default="{ row }">
                <div>
                  {{ statusList[row.status] }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="onSchedule" :label="t('examMgt.exam.onSchedule')" min-width="240">
              <template #default="{ row }">
                <div>
                  {{ row.status === 2 ? (row.onSchedule ? t('common.yes') : t('common.no')) : '' }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="submitTime" :label="t('examMgt.exam.submitTime')" width="200" />
            <el-table-column prop="quizScore" :label="t('examMgt.exam.topScore')" width="180" align="center" />
            <el-table-column prop="operator" :label="t('examMgt.exam.topScore')" width="180" align="center" />
            <el-table-column fixed="right" :label="t('global.action')" width="180" align="center">
              <template #default="{ row }">
                <el-button :disabled="!row.quizScore" link type="primary" @click="handleView(row)">
                  <Icon icon="ep:view" />
                  {{ t('action.view') }}
                </el-button>
                <el-button link type="primary" @click="handleReassign(row)">
                  <Icon icon="ep:user" />
                  {{ t('action.reAssign') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
        </ContentWrap>

      </el-tab-pane>
    </el-tabs>
    <!-- 重新分配考试次数 -->
    <Reassign ref="RefReassign" v-model="isVisible" @reassign-exam="handleReassignConfirm" />
  </div>
</template>

<style scoped lang="scss">
.statistic_icon {
  width: 144px;
  height: 74px;
}
:deep(.el-progress-circle__track) {
  stroke: #32a773;
}
:deep(.el-progress-circle__path) {
  // color="#32A773"
  stroke: #007943;
}
.top_item_bg {
  display: flex;
  align-items: center;
  height: 120px;
  padding: 1.25rem; /* p-5 */
  border-radius: 10px;
  background-color: #E4F4EE;

  .item_content {
    width: 110px;

    .item_number {
      margin-bottom: 0.25rem; /* mb-1 */
      font-size: 20px;
      color: #222222;
      font-weight: bold;
    }
    .item_desc {
      height: 36px;
      font-size: 12px;
      color: #5B6068;
    }
  }
}
</style>
