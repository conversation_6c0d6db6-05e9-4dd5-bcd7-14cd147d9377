<script setup lang="ts" name="newExam">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps, FormRules } from 'element-plus'
import { assignExam, assignListExam, deleteAssignListExam } from '@/api/topicMgt/exam'
import ScopeSelect from '@/components/ScopeSelect/index.vue'
import type { ScopeData } from '@/components/ScopeSelect/typings/index'

const props = defineProps<{ titleName: string, modelValue: boolean }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'paperChoose'])

interface APIScopeData extends ScopeData {
  id: string
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const scopeSelectRef = ref()
const examId = ref()
const formRef = ref()
// const form = ref()
const scopeList = ref<APIScopeData[]>([])
const asideHeight = ref('300px')

const isVisible = computed({
  get() {
    reset()
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})
/** 表单重置 */
function reset() {
  formRef.value?.resetFields()
}
// 获取考试分配结果
const getAssignListExam = async (exam_id: number) => {
  try {
    scopeList.value = await assignListExam({ examId: exam_id })
    mixAsideHeight()
  } catch (e) {}
}

// 分配考试
const handleAssignExam = async (list: any) => {
  await assignExam({ examId: examId.value, scope: list.scope }, list.scopes)
  message.success(t('global.addSuccess'))
  getAssignListExam(examId.value)
}

function handleAssignConfirm(list: any) {
  handleAssignExam(list)
  scopeSelectRef.value.closeDialog()
}
const handleAssignDelete = async (list: APIScopeData[] | APIScopeData) => {
  const ids: any = (list as APIScopeData[])?.length ? (list as APIScopeData[]).map(item => item.id) : (list as APIScopeData).id
  try {
    await message.confirm(t('confirm.deleteAssignCourse'))
    loading.value = true
    await deleteAssignListExam(ids)
    message.success(t('global.deleteSuccess'))
    getAssignListExam(examId.value)
    scopeSelectRef.value.closeDialog()
  } finally {
    loading.value = false
  }
}

function handleLogScoprList() {
  console.log(scopeList.value)
}
const handleOpen = (exam_id: number) => {
  examId.value = exam_id
  getAssignListExam(exam_id)
  isVisible.value = true
}
function handleClose() {
  isVisible.value = false
}
// 动态计算高度
function mixAsideHeight() {
  const windowHeight = window.innerHeight // 获取当前窗口的高度
  const offset = 340 // 偏移量，即需要减去的像素值
  asideHeight.value = `${windowHeight + (scopeList.value.length - 3) * 40 - offset > 400 ? windowHeight + (scopeList.value.length - 3) * 40 - offset : 400}px`
}

onMounted(() => {
  mixAsideHeight() // 调用 mixAsideHeight 函数来计算并更新 aside 高
  window.addEventListener('resize', mixAsideHeight) // 使用 window.addEventListener 监听窗口大小变化事件，在窗口大小发生变化时自动重新计算和更新第一部分的高度。
})

onUnmounted(() => {
  window.removeEventListener('resize', mixAsideHeight) // 取消监听窗口大小变化事件，以避免出现内存泄漏。
})
defineExpose({ handleOpen })
</script>

<template>
  <div>
    <Dialog v-model="isVisible" :title="props.titleName" width="70%" class="notice-dialog" @close="handleClose">
      <ScopeSelect
        ref="scopeSelectRef"
        v-model="scopeList"
        v-loading="loading"
        class="p-7"
        :table-props="{
          height: 400,
        }"
        :employee-table-props="{
          height: 400,
        }"
        @confirm="handleAssignConfirm"
        @delete="handleAssignDelete"
      />
    </Dialog>
  </div>
</template>
