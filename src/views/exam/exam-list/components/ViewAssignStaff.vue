<script setup lang="ts" name="newExam">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps, FormRules } from 'element-plus'
import { assignExam, assignListExam, deleteAssignListExam } from '@/api/topicMgt/exam'
import ScopeSelect from '@/components/ScopeSelect/index'
import type { ScopeData } from '@/components/ScopeSelect/typings/index'
import { deepClone } from '@/utils'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

interface APIScopeData extends ScopeData {
  id: string
}
const props = defineProps<{ titleName: string }>()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const tableRef = ref()
const cacheData = ref<ScopeData[] | undefined>()
const queryData = reactive({
  queryParams: {
    type: undefined,
    name: '',
  },
})
const queryRef = ref<FormInstance>()
const { queryParams } = toRefs(queryData)

// const form = ref()
const scopeList = ref<APIScopeData[]>([])
const asideHeight = ref('300px')

// 获取考试分配结果
const getAssignListExam = async () => {
  try {
    scopeList.value = await assignListExam({ examId: route.query.id })
    cacheData.value = deepClone(scopeList.value)
    mixAsideHeight()
  } catch (e) {}
}

const handleSearch = () => {
  cacheData.value = scopeList.value?.filter(item => item.relevanceName.toLocaleLowerCase().includes(queryParams.value.name.toLocaleLowerCase()))
  if (queryParams.value.type !== undefined) {
    cacheData.value = cacheData.value?.filter(item => item.scope === queryParams.value.type)
  }
}
const handleReset = () => {
  queryRef.value?.resetFields()
  if (scopeList.value)
    cacheData.value = deepClone(scopeList.value)
}

getAssignListExam()
// 动态计算高度
const mixAsideHeight = () => {
  const windowHeight = window.innerHeight // 获取当前窗口的高度
  const offset = 450 // 偏移量，即需要减去的像素值
  const tableHeight = (scopeList.value.length + 1) * 40 > 400 ? 400 : (scopeList.value.length + 1) * 40
  asideHeight.value = `${windowHeight + tableHeight - offset > 400 ? windowHeight + tableHeight - offset : 400}px`
}

onMounted(() => {
  mixAsideHeight() // 调用 mixAsideHeight 函数来计算并更新 aside 高
  window.addEventListener('resize', mixAsideHeight) // 使用 window.addEventListener 监听窗口大小变化事件，在窗口大小发生变化时自动重新计算和更新第一部分的高度。
})

onUnmounted(() => {
  window.removeEventListener('resize', mixAsideHeight) // 取消监听窗口大小变化事件，以避免出现内存泄漏。
})
</script>

<template>
  <div class="mt-6 mb-6" :style="{ maxHeight: asideHeight }">
    <h2 class="text-xl ml-6">
      {{ titleName }}
    </h2>
    <div class="px-6 pt-6">
      <div class="flex justify-between">
        <el-form ref="queryRef" :model="queryParams" inline>
          <el-form-item prop="type" :label="t('learningCenter.paper.type')">
            <el-select v-model="queryParams.type" class="!w-44">
              <el-option v-for="(dict, index) in getIntDictOptions(DICT_TYPE.SCOPE_TYPE)" :key="index" :label="dict.label" :value="+dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="name" :label="t('learningCenter.boarding.name')">
            <el-input v-model="queryParams.name" :placeholder="t('examMgt.exam.namePH')" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon class="mr-5px" icon="ep:search" />
              {{ t('action.search') }}
            </el-button>
            <el-button @click="handleReset">
              <Icon class="mr-5px" icon="ep:refresh" />
              {{ t('action.reset') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-table ref="tableRef" :data="cacheData" max-height="440" border>
          <el-table-column type="index" :label="t('setting.banner.no')" min-width="20" />
          <el-table-column prop="scope" :label="t('learningCenter.paper.type')">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.SCOPE_TYPE" :value="row.scope" />
            </template>
          </el-table-column>
          <el-table-column prop="relevanceName" :label="t('examMgt.paper.name')" />
        </el-table>
      </div>
    </div>
  </div>
</template>
