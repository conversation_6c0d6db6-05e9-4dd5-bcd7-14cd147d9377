<script setup lang="ts" name="newBasic">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps, FormRules } from 'element-plus'
import moment from 'moment'
import { getPaper } from '@/api/topicMgt/paper'
import { formatContent } from '@/utils'

const props = defineProps<{ titleName: string }>()

interface RuleForm {
  name: string
  examTime: Array<Date>
  beginTime: string
  endTime: string
  type: string
  examMax: number
  passScore: number
  answerTime: number
  paperId: number
  paperName: string
  cutCount: number
  optionRandom: boolean
  quesRandom: boolean
  instruction: string
}

const labelPosition = ref<FormProps['labelPosition']>('left')
const defaultDate = ref()
const RefInstruction = ref()
const formRef = ref<FormInstance>()
const ruleForm = ref<RuleForm>({
  name: '',
  examTime: [],
  beginTime: '',
  endTime: '',
  type: '',
  examMax: 0,
  passScore: 0,
  answerTime: 0,
  paperId: 0,
  paperName: '',
  cutCount: 0,
  optionRandom: true,
  quesRandom: true,
  instruction: '',
})
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const rules = reactive<FormRules<RuleForm>>({
  name: [{ required: true, message: t('examMgt.exam.examNameRule'), trigger: 'blur' }],
  examTime: [
    {
      required: true,
      message: t('examMgt.exam.examTimeRule'),
      trigger: 'change',
    },
  ],
  type: [{ required: true, message: t('examMgt.exam.paperRule'), trigger: 'blur' }],
  examMax: [{ required: true, message: t('examMgt.exam.examAttemptsRule'), trigger: 'blur' }],
  passScore: [{ required: true, message: t('examMgt.exam.passScoreRule'), trigger: 'blur' }],
  answerTime: [{ required: true, message: t('examMgt.exam.examDurationRule'), trigger: 'blur' }],
  paperId: [
    {
      required: true,
      message: t('examMgt.exam.examPaperRule'),
      trigger: 'change',
    },
  ],
})

// const form = ref()

const asideHeight = ref('300px')
/** 表单重置 */
function reset() {
  formRef.value?.resetFields()
}

// 编辑试卷
function handleEdit(val: any) {
  ruleForm.value = { ...val }
  // ruleForm.value.examTime = [...ruleForm.value.beginTime, ruleForm.value.endTime]
  // defaultDate.value = [new Date(ruleForm.value.beginTime), new Date(ruleForm.value.endTime)]
  ruleForm.value.examTime = [ruleForm.value.beginTime, ruleForm.value.endTime]
  getPaperInfo(ruleForm.value.paperId)
}
// 获取试卷信息
const getPaperInfo = async (paperId: number) => {
  const data = await getPaper(paperId)
  ruleForm.value.paperName = data.name
  ruleForm.value.type = data.isRandom	 ? '1' : '0'
  mixAsideHeight()
}

// 动态计算高度
function mixAsideHeight() {
  console.log(RefInstruction.value.offsetHeight)

  const windowHeight = window.innerHeight // 获取当前窗口的高度
  const offset = 320 // 偏移量，即需要减去的像素值
  asideHeight.value = `${windowHeight + RefInstruction.value.offsetHeight - offset > 400 ? windowHeight + RefInstruction.value.offsetHeight - offset : 400}px`
}
onMounted(() => {
  mixAsideHeight() // 调用 mixAsideHeight 函数来计算并更新 aside 高
  window.addEventListener('resize', mixAsideHeight) // 使用 window.addEventListener 监听窗口大小变化事件，在窗口大小发生变化时自动重新计算和更新第一部分的高度。
})
onUnmounted(() => {
  window.removeEventListener('resize', mixAsideHeight) // 取消监听窗口大小变化事件，以避免出现内存泄漏。
})

defineExpose({
  ruleForm,
  handleEdit,
})
</script>

<template>
  <div class="rounded-x py-5 px-7" :style="{ height: asideHeight }">
    <span class="text-xl">
      {{ titleName }}
    </span>
    <el-form ref="formRef" class="mt-5 w-3/4" :model="ruleForm" label-width="200" :label-position="labelPosition" :rules="rules">
      <el-form-item prop="name" :label="t('examMgt.exam.examName')">
        <span class="base_content">
          {{ ruleForm.name }}
        </span>
      </el-form-item>
      <el-form-item prop="examTime" :label="t('examMgt.exam.examTime')">
        <span class="base_content mr-2">
          {{ ruleForm.examTime[0] }}
        </span>
        {{ t('examMgt.exam.to') }}
        <span class="base_content ml-2">
          {{ ruleForm.examTime[1] }}
        </span>
      </el-form-item>
      <el-form-item prop="paperId" :label="t('examMgt.exam.selectExamPaper')">
        <span class="mr-4">{{ ruleForm.paperName }}</span>
      </el-form-item>
      <el-form-item prop="type" :label="t('examMgt.exam.examType')">
        <span v-if="ruleForm.type === '0'"> {{ t('learningCenter.exam.customizedPaper') }} </span>
        <span v-if="ruleForm.type === '1'"> {{ t('examMgt.paper.autoPaper') }} </span>
      </el-form-item>
      <el-form-item prop="examMax" :label="t('learningCenter.exam.examAttempts')">
        <span>{{ ruleForm.examMax }}</span>
        <span class="ml-2">{{ t('examMgt.exam.times') }}</span>
      </el-form-item>
      <el-form-item prop="passScore" :label="t('learningCenter.exam.passScore')">
        <span>{{ ruleForm.passScore }}</span>
        <span class="ml-2">{{ t('learningCenter.exam.points') }}</span>
      </el-form-item>
      <el-form-item prop="answerTime" :label="t('learningCenter.exam.examDuration')">
        <span>{{ ruleForm.answerTime }}</span>
        <span class="ml-2">{{ t('learningCenter.exam.minutes') }}</span>
      </el-form-item>
      <el-form-item prop="instruction" :label="t('examMgt.exam.examDescription')">
        <span ref="RefInstruction" v-dompurify-html="formatContent(ruleForm.instruction)"></span>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.base_content {
  @apply text-black;
}
</style>
