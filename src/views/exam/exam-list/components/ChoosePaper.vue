<script setup lang="ts" name="ChoosePaper">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'

import { delPaper, listPaper, updatePaperStatus } from '@/api/topicMgt/paper'
import { listTopicAll } from '@/api/category/topic'
import { deepClone } from '@/utils'

interface PaperItem {
  id: number
  judgeScore: number
  multipleChoiceScore: number
  singleChoiceScore: number
  totalScore: number
  name: string
  classifyId: string
  status: number
  isRandom: boolean
  questionNum: number
}

const props = defineProps<{ titleName: string, modelValue: boolean, courseId: string }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'paperChoose'])
const { t } = useI18n()
const paperList = ref<Array<PaperItem>>([])
const RefSingleTable = ref()
const multipleSelection = ref<PaperItem>()
const templateSelection = ref<string[]>([])
const selectedId = ref()
const isVisible = ref<boolean>(false)
const queryRef = ref()
const total = ref(0)
const paperData = reactive<{
  paperParams: any
}>({
  paperParams: {
    pageNo: 1,
    pageSize: 10,
    name: '',
    status: 1,
    classifyId: '',
    isRandom: undefined,
    courseId: undefined,
  },
})
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject'),
  },
])
const { paperParams } = toRefs(paperData)

// const isVisible = computed({
//   get() {
//     reset()
//     return props.modelValue
//   },
//   set(value) {
//     emit('update:modelValue', value)
//   }
// })

const handleOpen = (paperId: number) => {
  selectedId.value = paperId
  templateSelection.value = []
  getPaperList()
  getSubjectData()

  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}
// 获取试题集列表
const getPaperList = async () => {
  // 取消table选中行
  if (templateSelection.value && templateSelection.value.length !== 0) {
    RefSingleTable.value.setCurrentRow()
  }
  paperParams.value.courseId = props.courseId
  const res = await listPaper(paperParams.value)
  paperList.value = res.list
  paperList.value.forEach((element) => {
    if (element.id === selectedId.value) {
      RefSingleTable.value?.setCurrentRow(element)
    }
  })
  total.value = res.list
}
// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
// paper搜索
const handleBankSearch = () => {
  getPaperList()
}
const handleReset = () => {
  reset()
  getPaperList()
}
/** 表单重置 */
const reset = () => {
  paperParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: '',
    status: 1,
    classifyId: '',
    isRandom: undefined,
  }
  queryRef.value?.resetFields()
}
// 确认选择
const handleConfirm = () => {
  emit('paperChoose', deepClone(multipleSelection.value))
  isVisible.value = false
}
// table 选中
const handleSingleElection = (currentRow, oldCurrentRow) => {
  if (currentRow) {
    multipleSelection.value = currentRow
    templateSelection.value = [currentRow.id]
  }
  else {
    multipleSelection.value = {
      judgeScore: 0,
      multipleChoiceScore: 0,
      singleChoiceScore: 0,
      totalScore: 0,
      name: '',
      classifyId: '',
      status: 0,
      isRandom: false,
      questionNum: 0,
    }
    templateSelection.value = []
  }
  RefSingleTable.value?.setCurrentRow(currentRow)
}
defineExpose({ handleOpen, handleClose })
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" width="1100" @close="handleClose">
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="paperParams">
        <el-form-item :label="t('learningCenter.paper.paperName')">
          <el-input v-model="paperParams.name" :placeholder="t('common.inputText')" clearable style="width: 160px" @keydown.enter="handleBankSearch" />
        </el-form-item>
        <el-form-item :label="t('category.topic.subjectName')">
          <el-select v-model="paperParams.classifyId" :placeholder="t('common.chooseText')" clearable style="width: 160px">
            <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('learningCenter.paper.type')">
          <el-select v-model="paperParams.isRandom" :placeholder="t('common.chooseText')" clearable style="width: 160px">
            <el-option :label="t('learningCenter.exam.customizedPaper')" value="false" />
            <el-option :label="t('learningCenter.exam.autoPaper')" value="true" />
          </el-select>
        </el-form-item>
        <el-form-item style="margin-right: 0">
          <el-button type="primary" @click="handleBankSearch">
            <Icon icon="ep:search" class="mr-5px" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon icon="ep:refresh" class="mr-5px" />
            {{ t('action.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table ref="RefSingleTable" :data="paperList" border style="width: 100%;" highlight-current-row max-height="500" :row-style="{ cursor: 'pointer' }" @current-change="handleSingleElection">
        <el-table-column label="" width="34">
          <template #default="{ row }">
            <el-radio-group v-model="templateSelection[0]">
              <el-radio :label="row.id" size="small">
                {{}}
              </el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="t('learningCenter.paper.paperName')" />
        <el-table-column prop="classifyName" :label="t('category.topic.subjectName')" width="180">
          <template #default="{ row }">
            {{ row.classifyId === 0 ? t('common.noSubject') : row.classifyName }}
          </template>
        </el-table-column>
        <el-table-column prop="isRandom" :label="t('learningCenter.paper.type')" width="150">
          <template #default="{ row }">
            {{ row.isRandom === true ? t('learningCenter.exam.autoPaper') : t('learningCenter.exam.customizedPaper') }}
          </template>
        </el-table-column>
        <el-table-column prop="questionNum" :label="t('learningCenter.paper.itemNumber')" width="120" />
        <el-table-column prop="totalScore" :label="t('learningCenter.paper.fullScore')" width="120" />
      </el-table>
      <div class="relative h-[95px]">
        <div class="absolute right-0 top-2">
          <pagination v-show="total > 0" v-model:page="paperParams.pageNo" v-model:limit="paperParams.pageSize" :total="total" @pagination="getPaperList" />
        </div>
      </div>
    </ContentWrap>
    <template #footer>
      <div>
        <el-button type="primary" @click="handleConfirm">
          {{ t('global.confirm') }}
        </el-button>
        <el-button @click="handleClose">
          {{ t('global.cancel') }}
        </el-button>
      </div>
    </template>
  </Dialog>
</template>
