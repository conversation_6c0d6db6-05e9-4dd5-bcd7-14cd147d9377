<script setup lang="ts" name="newExam">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps, FormRules } from 'element-plus'
import { assignExam, assignListExam, deleteAssignListExam } from '@/api/topicMgt/exam'
import ScopeSelect from '@/components/ScopeSelect/index.vue'
import type { ScopeData } from '@/components/ScopeSelect/typings/index'

interface APIScopeData extends ScopeData {
  id: string
}
const props = defineProps<{ titleName: string, type: string, examId: string }>()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const scopeSelectRef = ref()
const formRef = ref()
// const form = ref()
const scopeList = ref<APIScopeData[]>([])
const asideHeight = ref('300px')
/** 表单重置 */
function reset() {
  formRef.value?.resetFields()
}
// 获取考试分配结果
const getAssignListExam = async () => {
  if (props.examId || route.query.id) {
    scopeList.value = await assignListExam({ examId: props.type === 'add' ? props.examId : route.query.id })
    mixAsideHeight()
  }
}

// 分配考试
const handleAssignExam = async (list: any) => {
  await assignExam({ examId: props.type === 'add' ? props.examId : route.query.id, scope: list.scope }, list.scopes)
  message.success(t('global.addSuccess'))
  getAssignListExam()
}

function handleAssignConfirm(list: any) {
  handleAssignExam(list)
  scopeSelectRef.value.closeDialog()
}
const handleAssignDelete = async (list: APIScopeData[] | APIScopeData) => {
  const ids: any = (list as APIScopeData[])?.length ? (list as APIScopeData[]).map(item => item.id) : (list as APIScopeData).id
  await message.confirm(t('confirm.deleteAssignCourse'))
  await deleteAssignListExam(ids)
  getAssignListExam()
  message.success(t('global.deleteSuccess'))
  scopeSelectRef.value.closeDialog()
}


// 动态计算高度
function mixAsideHeight() {
  const windowHeight = window.innerHeight // 获取当前窗口的高度
  const offset = 340 // 偏移量，即需要减去的像素值
  const tableHeight = (scopeList.value.length + 1) * 40 > 400 ? 400 : (scopeList.value.length + 1) * 40
  asideHeight.value = `${windowHeight + tableHeight - offset > 400 ? windowHeight + tableHeight - offset : 400}px`
}

onMounted(() => {
  mixAsideHeight() // 调用 mixAsideHeight 函数来计算并更新 aside 高
  getAssignListExam()
  window.addEventListener('resize', mixAsideHeight) // 使用 window.addEventListener 监听窗口大小变化事件，在窗口大小发生变化时自动重新计算和更新第一部分的高度。
})

onUnmounted(() => {
  window.removeEventListener('resize', mixAsideHeight) // 取消监听窗口大小变化事件，以避免出现内存泄漏。
})
</script>

<template>
  <ContentWrap>
    <div class="mt-6 mb-6" :style="{ maxHeight: asideHeight }">
      <h2 class="text-xl ml-6">
        {{ titleName }}
      </h2>
      <ScopeSelect
        ref="scopeSelectRef"
        v-model="scopeList"
        class="p-7"
        :table-props="{
        height: 400,
      }"
        :employee-table-props="{
        height: 400,
      }"
        @confirm="handleAssignConfirm"
        @delete="handleAssignDelete"
      />
    </div>
  </ContentWrap>
</template>
