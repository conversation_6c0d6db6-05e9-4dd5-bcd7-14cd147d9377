<script setup lang="ts" name="Question-edite">
import SingleChoice from '../components/SingleChoice.vue'
import MultipleChoice from '../components/MultipleChoice.vue'
import TrueOrFalse from '../components/TrueOrFalse.vue'
import { InfoQustion, delQustion } from '@/api/topicMgt/question'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const route = useRoute()
const RefSingleChoice = ref()
const RefMultipleChoice = ref()
const RefTrueOrFalse = ref()
const activeName = ref('single')

const getList = async () => {
  try {
    const data = await InfoQustion(route.query.id)
    if (data.type === 0) {
      activeName.value = 'single'
      RefSingleChoice.value.handleEdit(data)
    }
    else if (data.type === 1) {
      activeName.value = 'multiple'
      RefMultipleChoice.value.handleEdit(data)
    }
    else {
      activeName.value = 'trueorfalse'
      RefTrueOrFalse.value.handleEdit(data)
    }
  } catch {}
}
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="app-container">
    <h2 class="text-xl mb-4 mt-2">
      {{ t('examMgt.question.editQuestion') }}
    </h2>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane :label="t('examMgt.question.singleChoice')" name="single">
        <SingleChoice ref="RefSingleChoice" :is-edite="true" />
      </el-tab-pane>
      <el-tab-pane :label="t('examMgt.question.multipleChoice')" name="multiple">
        <MultipleChoice ref="RefMultipleChoice" :is-edite="true" />
      </el-tab-pane>
      <el-tab-pane :label="t('examMgt.question.trueOrFalse')" name="trueorfalse">
        <TrueOrFalse ref="RefTrueOrFalse" :is-edite="true" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped lang="scss">
/* 去掉长分割线 */
:deep(.el-tabs__header) {
  border-bottom: 0 !important;
}
/* 去掉下划线 */
:deep(.el-tabs__nav) {
  border: 1px solid darkgray !important;
  border-radius: 4px !important;
  overflow: hidden;
}
/* 未选中状态的样式 */
:deep(.el-tabs__item) {
  color: darkgray;
  background-color: white;
  border-bottom: 0;
}

/* 选中状态的样式 */
:deep(.el-tabs__item.is-active) {
  color: white;
  background-color: #007943;
  border-bottom: 0;
}
</style>
