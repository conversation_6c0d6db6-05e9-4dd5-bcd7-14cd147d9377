<script setup lang="ts" name="Management">
import type { StringDecoder } from 'node:string_decoder'
import type { ComponentInternalInstance } from 'vue'

import QuestionView from '../components/QuestionView.vue'
import { delQustion, getQustionBank, listQustion } from '@/api/topicMgt/question'
import { formatContent } from '@/utils'
import { dateFormatter } from '@/utils/formatTime'

interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  bankId: number
  content: string
  image: string
  rightAnswer: string
  score: number
  type: number
  classifyId: number
  options: Array<Option>
}
interface Option {
  content: string
  answer: boolean
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const route = useRoute()
const router = useRouter()
const isVisible = ref<boolean>(false)
const title = ref('')
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryRef = ref()
const bankInfo = ref()
const typeTitle = ref([t('examMgt.question.singleChoice'), t('examMgt.question.multipleChoice'), t('examMgt.question.trueOrFalse')])
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    type: undefined,
    content: undefined,
    bankId: undefined,
  },
})
const { queryParams } = toRefs(data)
const RefChild = ref()
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    type: undefined,
    content: undefined,
    bankId: undefined,
  }
  queryRef.value?.resetFields()
}
const handleAdd = () => {
  router.push({ name: 'QuestionAdd', query: { bankId: route.query.id } })
}
const handleView = (row: any) => {
  title.value = t('examMgt.question.questionPreview')
  RefChild.value.handleView(row)
}
const handleEdit = (row: any) => {
  // router.push(`/topicMgt/question-add/edite/${row.id}`)
  router.push({ name: 'QuestionEdite', query: { id: row.id, bankId: route.query.id } })
}
const handleDelete = async (row: any) => {
  const noticeIds = row.id
  await message.confirm(`${t('global.deleteTip')} ${t('examMgt.question.thisQuestion')}`)
  await delQustion(noticeIds)
  getList()
  message.success(t('global.deleteSuccess'))
}
// 获取试题列表
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.bankId = route.query.id
    const res = await listQustion(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// 获取试题集详情
const getQustionBankInfo = async () => {
  bankInfo.value = await getQustionBank(route.query.id)
}

onMounted(() => {
  getList()
  getQustionBankInfo()
})
watch(
  () => isVisible,
  (newProps: any) => {
    isVisible.value = newProps
  },
)
</script>

<template>
  <div class="app-container">
    <div class="mb-4 h-[60px] bg-[#E3F1EB] rounded-[10px]">
      <div class="flex items-center ml-5 text-[#23293A] text-[20px]">
        <div class="leading-[60px] font-medium">
          {{ bankInfo ? bankInfo.name : '' }}
        </div>
        <div class="ml-[16px] px-[10px] h-[25px] leading-[25px] rounded-[2px] font-medium text-[14px] text-[#BF9121] bg-[#EFE0B5]">
          {{ bankInfo ? (bankInfo.classifyId === 0 ? t('common.noSubject') : bankInfo.classifyName) : t('common.noSubject') }}
        </div>
        <!-- <div>No category</div> -->
      </div>
      <!-- <div class="ml-5 text-sm text-gray-600">
        {{ bankInfo ? bankInfo.deptCode : '' }}
      </div> -->
    </div>
    <ContentWrap>
      <el-Form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
        <el-form-item :label="t('examMgt.question.questionContent')">
          <el-input v-model="queryParams.content" :placeholder="t('common.inputText')" clearable style="width: 180px" />
        </el-form-item>
        <el-form-item :label="t('examMgt.question.questionType')">
          <el-select v-model="queryParams.type" :placeholder="t('common.chooseText')" clearable style="width: 180px">
            <el-option :label="t('examMgt.question.singleChoice')" value="0" />
            <el-option :label="t('examMgt.question.multipleChoice')" value="1" />
            <el-option :label="t('examMgt.question.trueOrFalse')" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button type="primary" plain @click="handleAdd">
            <Icon class="mr-5px" icon="ep:plus" />
            {{ t('action.add') }}
          </el-button>
        </el-form-item>
      </el-Form>
    </ContentWrap>
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData">
        <el-table-column prop="content" :label="t('examMgt.question.questionContent')" min-width="240">
          <template #default="{ row }">
            <div v-dompurify-html="formatContent(row.content)"></div>
          </template>
        </el-table-column>
        <el-table-column prop="type" :label="t('examMgt.question.questionType')" min-width="180">
          <template #default="{ row }">
            {{ typeTitle[row.type] }}
          </template>
        </el-table-column>
        <el-table-column prop="createBy" :label="t('category.journey.creator')" min-width="160" />
        <el-table-column prop="creationTime" :label="t('category.journey.creationTime')" :formatter="dateFormatter" min-width="160" />
        <el-table-column fixed="right" :label="t('global.action')" min-width="240">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">
              <Icon icon="ep:view" />
              {{ t('action.view') }}
            </el-button>
            <el-button link type="primary" @click="handleEdit(row)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <el-button link type="primary" @click="handleDelete(row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>
    <QuestionView ref="RefChild" v-model="isVisible" :title-name="title" />
  </div>
</template>

<style scoped>
.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}
</style>
