<script setup lang="ts" name="Question-add">
import SingleChoice from '../components/SingleChoice.vue'
import MultipleChoice from '../components/MultipleChoice.vue'
import TrueOrFalse from '../components/TrueOrFalse.vue'
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const activeName = ref('single')

const handleTabChange = (targetName: string) => {
  console.log(targetName)
}
</script>

<template>
  <ContentWrap>
    <div class="app-container">
      <h2 class="text-xl mb-4 mt-2">
        {{ t('examMgt.question.addNewQuestion') }}
      </h2>
      <el-tabs v-model="activeName" type="card" @tab-change="handleTabChange">
        <el-tab-pane :label="t('examMgt.question.singleChoice')" name="single">
          <SingleChoice :is-edite="false" />
        </el-tab-pane>
        <el-tab-pane :label="t('examMgt.question.multipleChoice')" name="multiple">
          <MultipleChoice :is-edite="false" />
        </el-tab-pane>
        <el-tab-pane :label="t('examMgt.question.trueOrFalse')" name="trueorfalse">
          <TrueOrFalse :is-edite="false" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </ContentWrap>
</template>

<style scoped lang="scss">
/* 去掉长分割线 */
:deep(.el-tabs__header) {
  border-bottom: 0 !important;
}
/* 去掉下划线 */
:deep(.el-tabs__nav) {
  border: 1px solid darkgray !important;
  border-radius: 4px !important;
  overflow: hidden;
}
/* 未选中状态的样式 */
:deep(.el-tabs__item) {
  color: darkgray;
  background-color: white;
  border-bottom: 0;
}

/* 选中状态的样式 */
:deep(.el-tabs__item.is-active) {
  color: white;
  background-color: #007943;
  border-bottom: 0;
}
</style>
