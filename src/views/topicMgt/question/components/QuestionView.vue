<script setup lang="ts" name="QuestionView">
import type { ComponentInternalInstance } from 'vue'
import { InfoQustion, delQustion } from '@/api/topicMgt/question'
import { formatContent } from '@/utils'

interface Question {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNum: number
  pageSize: number
  id: number
  bankId: number
  content: string
  image: string
  rightAnswer: string
  score: number
  type: number
  options: Array<Option>
}
interface Option {
  content: string
  answer: boolean
}
const { t } = useI18n()
const props = defineProps<{ titleName: string; modelValue: boolean }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'parentEmit'])
const question = ref()
const corrent = ref([])
const typeTitle = ref([t('examMgt.question.singleChoice'), t('examMgt.question.multipleChoice'), t('examMgt.question.trueOrFalse')])

const isVisible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const handleOpen = () => {
  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}
const handleView = async (row: any) => {
  // question.value = { ...row }
  const data = await InfoQustion(row.id)
  question.value = { ...data }
  isVisible.value = true
  corrent.value = []
  question.value.options.forEach(option => {
    if (option.isAnswer === true) {
      corrent.value.push(option.tag)
    }
  })
}
defineExpose({ handleOpen, handleView })
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" class="notice-dialog font-medium w-2/4" @close="handleClose">
    <div v-dompurify-html="formatContent(question.content)" class="text-black"></div>
    <div class="mt-[15px]">
      <p class="border-[1px] rounded-[3px] border-[#00B1AF] text-[#00B1AF] bg-[#DDF6F6] w-[120px] h-[30px] leading-[30px] text-center">
        {{ typeTitle[question.type] }}
      </p>
    </div>
    <div v-for="(choice, index) in question.options" :key="index" class="flex mt-[15px] text-black">
      <div>
        {{ choice.tag }}
      </div>
      .
      <div v-dompurify-html="formatContent(choice.content)" class="w-full"></div>
    </div>
    <el-divider />
    <div class="text-black">
      {{t('examMgt.question.correctAnswer') }} <span class="text-[#53BC00]"> {{ corrent.join(',') }} </span>
    </div>
    <template #footer>
      <div>
        <el-button @click="handleClose"> {{ t('action.close') }} </el-button>
      </div>
    </template>
  </Dialog>
</template>
