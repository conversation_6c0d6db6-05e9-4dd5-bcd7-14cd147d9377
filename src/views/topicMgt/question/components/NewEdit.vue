<script setup lang="ts" name="newEdit">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import SubjectSelect from '@/components/SubjectSelect/index.vue'
import { addQustionBank, updateQustionBank } from '@/api/topicMgt/question'

interface rowObject {
  name: string
  classifyId: number
}
const props = defineProps<{ titleName: string; modelValue: boolean; subjectList: any }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'parentEmit'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const labelPosition = ref<FormProps['labelPosition']>('top')
const formRef = ref<FormInstance>()
const data = reactive<{
  rules: any
}>({
  rules: {
    name: [{ required: true, message: t('examMgt.question.itemTitleRule'), trigger: 'change' }],
    classifyId: [{ required: true, message: t('examMgt.question.choiceSubjectRule'), trigger: 'change' }]
  }
})

const { rules } = toRefs(data)
const form = ref({
  name: undefined,
  classifyId: undefined
})
const isVisible = ref(false)
/** 表单重置 */
function reset() {
  formRef.value?.resetFields()
}
const handleOpen = () => {
  form.value = {
    name: undefined,
    classifyId: undefined
  }
  isVisible.value = true
}
const handleEdit = (row: rowObject) => {
  form.value = row
  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}

defineExpose({ handleOpen, handleEdit })
/** 提交按钮 */
const handleConfirm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  try {
    if (form.value.id !== undefined) {
      await updateQustionBank(form.value)
      message.success(t('global.editSuccess'))
    } else {
      await addQustionBank(form.value)
      message.success(t('global.addSuccess'))
    }
    isVisible.value = false
    emit('parentEmit')
  } catch {}
}
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" width="500" @close="handleClose">
    <el-form ref="formRef" style="max-width: 600px" :model="form" label-width="auto" :label-position="labelPosition" :rules="rules">
      <el-form-item prop="name" :label="t('examMgt.question.poolTitle')">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item prop="classifyId" :label="t('category.topic.subjectName')">
        <!-- <el-select v-model="form.classifyId" placeholder="Please choose" clearable style="width: 100%">
          <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select> -->
        <SubjectSelect v-model="form.classifyId" :has-no-subject="true" selecte-tree-style="width: 100%" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button type="primary" @click="handleConfirm">
          {{ t('global.confirm') }}
        </el-button>
        <el-button @click="handleClose">
          {{ t('global.cancel') }}
        </el-button>
      </div>
    </template>
  </Dialog>
</template>
