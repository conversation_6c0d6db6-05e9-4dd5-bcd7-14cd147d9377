<script setup lang="ts" name="SingleChoice">
import { ref } from 'vue'
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps, FormRules } from 'element-plus'

import { addQustion, updateQustion } from '@/api/topicMgt/question'
import Editor from '@/components/Editor/index.vue'
import { deepClone } from '@/utils'
import tabPlugin from '@/plugins/tab'
const props = defineProps<{ isEdite: boolean }>()

interface RuleForm {
  content: string
  bankId: any
  id: any
  type: number
  remark: string
  options: Array<Option>
}
interface Option {
  content: string
  isAnswer: boolean
  sort: number
  tag: string
}

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const router = useRouter()
const route = useRoute()
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<RuleForm>({
  content: '',
  bankId: '',
  type: 0,
  id: '',
  remark: '',
  options: [
    {
      content: '',
      isAnswer: false,
      sort: 0,
      tag: ''
    },
    {
      content: '',
      isAnswer: false,
      sort: 0,
      tag: ''
    },
    {
      content: '',
      isAnswer: false,
      sort: 0,
      tag: ''
    },
    {
      content: '',
      isAnswer: false,
      sort: 0,
      tag: ''
    }
  ]
})
const rules = reactive<FormRules<RuleForm>>({
  content: [
    {
      required: true,
      message: t('examMgt.question.contentRule'),
      trigger: 'change'
    }
  ]
})
const addStatus = ref(false)
const optionsTopic = ref([
  {
    id: '',
    name: '',
    keywords: ''
  }
])

const optionsTitle = ref(['ChoiceA', 'ChoiceB', 'ChoiceC', 'ChoiceD', 'ChoiceE', 'ChoiceF', 'ChoiceG', 'ChoiceH', 'ChoiceI', 'ChoiceJ'])
const tagsArray = ref<string[]>(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'])
const correct = ref('')

// 删除选项
const handleDeleteContent = async (index: number) => {
  if (ruleForm.value.options.length === 1) {
    message.warning(t('warning.canNotDelete'))
  } else {
    await message.confirm(t('confirm.deleteChoice'))
    ruleForm.value.options.splice(index, 1)
    updateOptionSelect()
  }
}

// 添加选项
const handleAddOption = () => {
  if (ruleForm.value.options.length < 10) {
    ruleForm.value.options.push({
      content: '',
      isAnswer: false,
      sort: 0,
      tag: ''
    })
    if (ruleForm.value.options.length === 10) {
      // 选项最多10个
      addStatus.value = true
    }
  }
}
// 更新选项选中状态
const updateOptionSelect = () => {
  correct.value = ''
  ruleForm.value.options.forEach((option, index) => {
    if (option.isAnswer === true) {
      correct.value = optionsTitle.value[index]
    }
  })
}

const handleSave = async () => {
  const valid = await ruleFormRef.value.validate()
  if (!valid) return
  try {
    if (ruleForm.value.content === '<p><br></p>') {
      message.warning(t('warning.delContent'))
      return
    }
    const allContentHaveValue = ruleForm.value.options.every(option => option.content !== '')
    if (!allContentHaveValue) {
      message.warning(t('warning.delOption'))
      return
    }
    updateChoiceSort()
    if (ruleForm.value.options.every(option => option.isAnswer === false)) {
      message.warning(t('warning.answerOption'))
      return
    }
    const params = deepClone(ruleForm.value)
    encodeContent(params)
    params.bankId = route.query.bankId
    if (props.isEdite) {
      params.id = route.query.id
      await updateQustion(params)
      message.success(t('common.updateSuccess'))
      await tabPlugin.closeOpenPage()
      backToManagement(params.bankId)
    } else {
      await addQustion(params)
      message.success(t('global.addSuccess'))
      await tabPlugin.closeOpenPage()
      backToManagement(params.bankId)
    }
  } catch {}

}
const handleCancel = () => {
  tabPlugin.closeOpenPage()
  backToManagement(route.query.bankId)
}
const backToManagement = (bankId: number) => {
  router.push({ name: 'QuestionManagement', query: { id: bankId } })
}

const handleEdit = (row: any) => {
  ruleForm.value = { ...row }
  updateOptionSelect()
}
const updateChoiceSort = () => {
  ruleForm.value.options.forEach((choice, index) => {
    choice.sort = index
    choice.tag = tagsArray.value[index]
    const correctIndex = optionsTitle.value.indexOf(correct.value)
    if (index === correctIndex) {
      choice.isAnswer = true
    } else {
      choice.isAnswer = false
    }
  })
}
const encodeContent = (params: RuleForm) => {
  params.content = encodeURIComponent(params.content)
  params.remark = encodeURIComponent(params.remark)

  params.options.forEach(item => {
    item.content = encodeURIComponent(item.content)
  })
}

onBeforeUpdate(() => {
  ruleFormRef.value?.resetFields()
})

onMounted(() => {
  if (!props.isEdite) {
    ruleForm.value.bankId = route.params.id
  }
})

defineExpose({ handleEdit })
</script>

<template>
  <div class="p-5">
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="auto" label-position="right">
      <el-form-item :label="t('examMgt.question.questionContent')" prop="content">
        <div>
          <Editor v-model="ruleForm.content" :height="300" />
        </div>
      </el-form-item>
      <el-form-item :label="t('examMgt.question.choice')">
        <el-radio-group v-model="correct">
          <div v-for="(item, index) in ruleForm.options" :key="index" class="w-full">
            <div class="mt-4 w-40 text-sm" styles="color:black">
              {{ optionsTitle[index] }}
            </div>
            <Editor v-model="item.content" :height="300" />
            <div class="flex">
              <el-radio class="me-2.5" :label="optionsTitle[index]" size="large">
                {{ t('examMgt.question.correct') }}
              </el-radio>
              <el-button class="delete_button" type="primary" link icon="Delete" @click="handleDeleteContent(index)">
                <Icon icon="ep:delete" />
              </el-button>
            </div>
          </div>
          <el-button class="m-auto" type="primary" :disabled="addStatus" @click="handleAddOption"> {{ t('action.addChoice') }} ({{ ruleForm.options?.length }}/10) </el-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="t('examMgt.question.questionAnalysis')" prop="remark">
        <Editor v-model="ruleForm.remark" :height="300" />
      </el-form-item>
      <el-form-item class="pt-4" label=" ">
        <el-button type="primary" @click="handleSave"> {{ t('action.save') }} </el-button>
        <el-button :style="{ '--el-button-text-color': '#007943', border: '1px solid #007943' }" @click="handleCancel"> {{ t('action.cancel') }} </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.delete_button {
  width: 40px;
  height: 40px;
  color: black;
  font-size: 20px;
}
</style>
