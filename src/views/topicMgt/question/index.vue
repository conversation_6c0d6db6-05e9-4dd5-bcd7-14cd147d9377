<script setup lang="ts" name="QuestionPool">
import type { ComponentInternalInstance } from 'vue'

import { delQustionBank, listQustionBank } from '@/api/topicMgt/question'
import NewEdit from './components/NewEdit.vue'
import SubjectSelect from '@/components/SubjectSelect/index.vue'
import { listTopicAll } from '@/api/category/topic'
import { dateFormatter } from '@/utils/formatTime'
interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  name: string
  classifyId: string
  questionNum: string
  deptCode: string
}
// const open = ref(false)
const router = useRouter()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const isVisible = ref<boolean>(false)
const title = ref('')
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryRef = ref()
const paramsObj = reactive<{
  title: string
  topic: string
}>({
  name: '',
  keywords: '',
})
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    deptCode: undefined,
  },
})
const { queryParams } = toRefs(data)
const RefChild = ref()
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject'),
  },
])

// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    classifyId: undefined,
  }
  queryRef.value?.resetFields()
}
const handleAdd = () => {
  title.value = t('action.add')
  RefChild.value.handleOpen()
}
const handleManagement = (row: DataItem) => {
  router.push({ name: 'QuestionManagement', query: { id: row.id } })
}
const handleEdit = (row: DataItem) => {
  title.value = t('action.edit')
  RefChild.value.handleEdit(row)
}
const handleDelete = async (row: DataItem) => {
  const noticeIds = row.id || ids.value
  try {
    await message.confirm(`${t('global.deleteTip') + row.name}?`)
    await delQustionBank(noticeIds)
    getList()
    message.success(t('global.deleteSuccess'))
  } catch {}
}
const getList = async () => {
  loading.value = true
  try {
    const res = await listQustionBank(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

watch(
  () => isVisible,
  (newProps: any) => {
    isVisible.value = newProps
  },
)
onMounted(() => {
  getSubjectData()
  getList()
})
</script>

<template>
  <div class="app-container">
    <ContentWrap>
      <el-Form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
        <el-form-item :label="t('examMgt.question.poolTitle')">
          <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable class="!w-180px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('category.topic.subjectName')">
          <!-- <el-select v-model="queryParams.classifyId" placeholder="Please choose" clearable style="width: 180px">
            <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
          <SubjectSelect v-model="queryParams.classifyId" :has-no-subject="true" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button type="primary" class="mr-2" @click="handleAdd">
            <Icon class="mr-5px" icon="ep:plus" />
            {{ t('action.add') }}
          </el-button>
        </el-form-item>
      </el-Form>
    </ContentWrap>
    <ContentWrap>
      <el-table v-loading="loading" :show-overflow-tooltip="true" :data="tableData">
        <el-table-column prop="name" :label="t('examMgt.question.poolTitle')" min-width="240" />
        <el-table-column prop="classifyName" :label="t('category.topic.subjectName')" min-width="180">
          <template #default="{ row }">
            {{ row.classifyId === 0 ? t('common.noSubject') : row.classifyName }}
          </template>
        </el-table-column>
        <el-table-column prop="questionNum" :label="t('learningCenter.paper.itemNumber')" min-width="120" />
        <el-table-column prop="createBy" :label="t('category.journey.creator')" min-width="160" />
        <el-table-column prop="createTime" :label="t('category.journey.creationTime')" :formatter="dateFormatter" min-width="160" />
        <el-table-column fixed="right" :label="t('global.action')" min-width="360">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleManagement(row)">
              <Icon icon="ep:user" />
              {{ t('action.questionManagement') }}
            </el-button>
            <el-button link type="primary" @click="handleEdit(row)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <el-button link type="primary" @click="handleDelete(row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="relative">
        <div class="absolute right-0 top-2">
          <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
        </div>
      </div>
    </ContentWrap>

    <NewEdit ref="RefChild" v-model="isVisible" :title-name="title" :subject-list="optionsSubject" @parent-emit="getList" />
  </div>
</template>

<style scoped>
.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}
</style>
