<script setup lang="ts" name="CustomPaper">
import { ref } from 'vue'
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps, FormRules } from 'element-plus'
import { SortDown, SortUp } from '@element-plus/icons-vue'
import ChooseQuestion from '../components/ChooseQuestion.vue'
import SetScore from '../components/SetScore.vue'
import SubjectSelect from '@/components/SubjectSelect/index.vue'
import { listTopicAll } from '@/api/category/topic'
import { addPaper, updatePaper } from '@/api/topicMgt/paper'
import { deepClone, formatContent } from '@/utils'
import { useTagsViewStore } from "@/store/modules/tagsView"
import {QuestionTypeEnum} from "@/api/topicMgt/question";
const props = defineProps<{ titleName: string }>()

interface BankItem {
  bankId: number
  id: number
  singleChoiceNum: number
  multipleChoiceScore: number
  judgeNum: number
  sort: number
}

interface PaperItem {
  banks: Array<BankItem>
  questions: Array<QuestionItem>
  judgeScore: number
  multipleChoiceScore: number
  singleChoiceScore: number
  totalScore: number
  name: string
  classifyId: string
  status: number
  isRandom: boolean
}

interface QuestionItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  questionId: number
  bankId: number
  content: string
  score: number
  type: number
  sort: number
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const isVisible = ref<boolean>(false)
const isChooseQuestion = ref<boolean>(false)
const hasSetScore = ref<boolean>(true)
const RefChild = ref()
const RefSetScore = ref()
const typeTitle = ref([t('examMgt.question.singleChoice'), t('examMgt.question.multipleChoice'), t('examMgt.question.trueOrFalse')])

const ruleFormRef = ref<FormInstance>()
const questionList = ref<Array<QuestionItem>>([])
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const paperItem = ref<PaperItem>({
  banks: [],
  questions: [],
  judgeScore: 0,
  multipleChoiceScore: 0,
  singleChoiceScore: 0,
  totalScore: 0,
  name: '',
  classifyId: '',
  status: 1,
  isRandom: false,
})

const statistics = ref({
  single: 0,
  multiple: 0,
  judge: 0,
})
const rules = reactive<FormRules<PaperItem>>({
  name: [
    {
      required: true,
      message: t('examMgt.paper.paperNameRule'),
      trigger: 'change',
    },
  ],
})
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject')
  },
])
const getTopicData = async () => {
  const data = await listTopicAll(null)
  optionsSubject.value.push(...data)
}

const handleSave = async () => {
  const valid = await ruleFormRef.value.validate()
  if (!valid) return
  try {
    paperItem.value.questions = []
    questionList.value.forEach((question) => {
      const tempQues = deepClone(question)
      tempQues.content = ''
      tempQues.remark = ''
      paperItem.value.questions.push(tempQues)
    })
    // 判断是否设置分数
    hasSetScore.value = true
    paperItem.value.questions.forEach((ques) => {
      if ((ques.type === 0 && paperItem.value.singleChoiceScore <= 0) || (ques.type === 1 && paperItem.value.multipleChoiceScore <= 0) || (ques.type === 2 && paperItem.value.judgeScore <= 0)) {
        hasSetScore.value = false
      }
    })
    if (!hasSetScore.value) {
      handleSetScore()
      return
    }
    if (paperItem.value.id !== undefined) {
      await updatePaper(paperItem.value)
      message.success(t('global.editSuccess'))
      delView(unref(currentRoute))
      push('/exam-mgt/exampaper')
    }
    else {
      await addPaper(paperItem.value)
      message.success(t('global.addSuccess'))
      delView(unref(currentRoute))
      push('/exam-mgt/exampaper')
    }
  } catch {}
}

const handleCancel = () => {
  delView(unref(currentRoute))
  push('/exam-mgt/exampaper')
}
// 编辑
const handleEdit = (row: PaperItem) => {
  paperItem.value = { ...row }
  questionList.value = paperItem.value.questions
  updateQuestionSort()
  updateScore()
}
// 选择试题
const handleChooseQuestion = () => {
  RefChild.value.handleOpen(questionList.value)
}
// 选择试题确认
const handleChooseConfirm = (val: any) => {
  val.forEach((element) => {
    element.questionId = element.id
    element.id = ''
  })
  questionList.value = val
  updateQuestionSort()
  updateScore()
}
// 更新试题排序
const updateQuestionSort = () => {
  statistics.value.single = 0
  statistics.value.multiple = 0
  statistics.value.judge = 0
  questionList.value.forEach((question, index) => {
    question.sort = index + 1

    switch (question.type) {
      case 0:
        statistics.value.single++
        break
      case 1:
        statistics.value.multiple++
        break
      case 2:
        statistics.value.judge++
        break
      default:
        break
    }
  })
}

// 设置分数
const handleSetScore = () => {
  RefSetScore.value.handleOpen({ single: paperItem.value.singleChoiceScore, multiple: paperItem.value.multipleChoiceScore, judge: paperItem.value.judgeScore })
}
// 设置分数确认
const handleSetScoreConfirm = (val: { single: number, multiple: number, judge: number }) => {
  paperItem.value.singleChoiceScore = val.single
  paperItem.value.multipleChoiceScore = val.multiple
  paperItem.value.judgeScore = val.judge
  updateScore()
}
// 更新试题分数 在设置分数，添加试题之后调用
const updateScore = () => {
  questionList.value.forEach((question) => {
    switch (question.type) {
      case QuestionTypeEnum.SINGLE_CHOICE:
        question.score = paperItem.value.singleChoiceScore
        break
      case QuestionTypeEnum.MULTIPLE_CHOICE:
        question.score = paperItem.value.multipleChoiceScore
        break
      case QuestionTypeEnum.JUDGE:
        question.score = paperItem.value.judgeScore
        break
      default:
        break
    }
  })
  paperItem.value.totalScore = paperItem.value.singleChoiceScore * statistics.value.single + paperItem.value.multipleChoiceScore * statistics.value.multiple + paperItem.value.judgeScore * statistics.value.judge
}
// 删除选中的试题
const handleDelete = (row: PaperItem, index: number) => {
  questionList.value.splice(index, 1)
  updateQuestionSort()
}
// 试题上移
const handleMoveUp = (row: PaperItem, index: number) => {
  const questions = questionList.value
  questions[index] = questions.splice(index - 1, 1, questions[index])[0]
  questionList.value = [...questions]
  updateQuestionSort()
}
// 试题下移
const handleMoveDown = (row: PaperItem, index: number) => {
  const questions = questionList.value
  questions[index] = questions.splice(index + 1, 1, questions[index])[0]
  questionList.value = [...questions]
  updateQuestionSort()
}

onMounted(() => {
  getTopicData()
})
watch(
  () => isChooseQuestion,
  (newProps: any) => {
    isChooseQuestion.value = newProps
  },
)
defineExpose({ handleEdit })
</script>

<template>
  <ContentWrap>
    <div class="p-5">
      <h2 class="text-xl mb-10">
        {{ props.titleName && props.titleName.length > 0 ? props.titleName : t('examMgt.paper.createCustomizedPaper') }}
      </h2>
      <el-form ref="ruleFormRef" class="w-[600px]" :model="paperItem" :rules="rules" label-width="auto" label-position="left">
        <el-form-item :label="t('learningCenter.paper.paperName')" prop="name">
          <el-input v-model="paperItem.name" :placeholder="t('examMgt.paper.namePH')" maxlength="50" show-word-limit />
        </el-form-item>

        <el-form-item :label="t('category.topic.subjectName')">
          <!-- <el-select v-model="paperItem.classifyId" placeholder="Please choose" clearable>
            <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
          <SubjectSelect v-model="paperItem.classifyId" :has-no-subject="true" selecte-tree-style="width: 100%" />
        </el-form-item>
        <el-form-item label="Type">
          <div>{{ t('learningCenter.exam.customizedPaper') }}</div>
        </el-form-item>
        <el-form-item label="Question Pool">
          <el-button type="primary" @click="handleChooseQuestion">
            {{ t('examMgt.paper.chooseQuestion') }}
          </el-button>
          <el-button type="primary" @click="handleSetScore">
            {{ t('examMgt.paper.setScoreInBatch') }}
          </el-button>
        </el-form-item>
      </el-form>
      <el-table class="mb-4" :data="questionList" border>
        <el-table-column prop="sort" :label="t('setting.banner.sort')" width="60" align="center" />

        <el-table-column prop="content" :label="t('setting.banner.title')" min-width="180" header-align="center">
          <template #default="{ row }">
            <div v-dompurify-html="formatContent(row.content)"></div>
          </template>
        </el-table-column>
        <el-table-column prop="type" :label="t('examMgt.question.questionType')" width="140" align="center">
          <template #default="{ row }">
            {{ typeTitle[row.type] }}
          </template>
        </el-table-column>
        <el-table-column prop="score" :label="t('learningCenter.course.score')" width="80" align="center" />
        <el-table-column fixed="right" :label="t('global.action')" width="300" align="center">
          <template #default="{ row, $index }">
            <el-button link type="primary" @click="handleDelete(row, $index)">
              <Icon class="me-1" icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
            <el-button link type="primary" :disabled="$index === 0" @click="handleMoveUp(row, $index)">
              <Icon icon="ep:upload" /> {{ t('action.moveUp') }}
            </el-button>
            <el-button link type="primary" :disabled="$index === questionList.length - 1" @click="handleMoveDown(row, $index)">
              <Icon icon="ep:download" /> {{ t('action.moveDown') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="question-select mb-1 text-sm">
        {{ t('examMgt.paper.selectedQuestion') }} <span>{{ statistics.single }}</span> single choice <span>{{ statistics.judge }}</span> true or false <span>{{ statistics.multiple }}</span> multiple choice
      </div>
      <div class="mb-4 text-sm">
        {{ t('examMgt.paper.fullScore') }} {{ paperItem.singleChoiceScore * statistics.single + paperItem.multipleChoiceScore * statistics.multiple + paperItem.judgeScore * statistics.judge }}
      </div>
      <el-button type="primary" @click="handleSave">
        {{ t('action.save') }}
      </el-button>
      <el-button :style="{ '--el-button-text-color': '#007943', 'border': '1px solid #007943' }" @click="handleCancel">
        {{ t('action.cancel') }}
      </el-button>
    </div>
  </ContentWrap>
  <SetScore ref="RefSetScore" v-model="isVisible" @set-score="handleSetScoreConfirm" />
  <ChooseQuestion ref="RefChild" v-model="isChooseQuestion" :title-name="t('examMgt.paper.chooseQuestion')" @question-choose="handleChooseConfirm" />
</template>

<style scoped>
.question-select span {
  color: #007943;
  font-weight: bold;
}
</style>
