<script setup lang="ts" name="AutoPaper">
import { ref } from 'vue'
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps, FormRules } from 'element-plus'
import SetScore from '../components/SetScore.vue'
import ChoosePackage from '../components/ChoosePackage.vue'
import SubjectSelect from '@/components/SubjectSelect/index.vue'
import { listTopicAll } from '@/api/category/topic'
import { addPaper, updatePaper } from '@/api/topicMgt/paper'
import {useTagsViewStore} from "@/store/modules/tagsView"
const props = defineProps<{ titleName: string }>()

interface BankItem {
  bankId: number
  id: number
  bankName: string
  singleChoiceNum: number
  multipleChoiceNum: number
  judgeNum: number
  totalJudgeNum: number
  totalMultipleChoiceNum: number
  totalSingleChoiceNum: number
  sort: number
}

interface PaperItem {
  banks: Array<BankItem>
  questions: Array<QuestionItem>
  judgeScore: number
  multipleChoiceScore: number
  singleChoiceScore: number
  totalScore: number
  singleChoiceNum: number
  multipleChoiceNum: number
  judgeNum: number
  name: string
  classifyId: string
  status: number
  isRandom: boolean
}

interface QuestionItem {
  id: number
  name: string
  title: string
  questionType: number
  score: number
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const RefChild = ref()
const RefSetScore = ref()
const isVisible = ref<boolean>(false)
const isChooseQuestion = ref<boolean>(false)
const labelPosition = ref<FormProps['labelPosition']>('left')
const ruleFormRef = ref<FormInstance>()
const paperItem = ref<PaperItem>({
  banks: [],
  questions: [],
  judgeScore: 0,
  multipleChoiceScore: 0,
  singleChoiceScore: 0,
  totalScore: 0,
  singleChoiceNum: 0,
  multipleChoiceNum: 0,
  judgeNum: 0,
  name: '',
  classifyId: '',
  status: 1,
  isRandom: true,
})
const rules = reactive<FormRules<PaperItem>>({
  name: [
    {
      required: true,
      message: t('examMgt.paper.paperNameRule'),
      trigger: 'change',
    },
  ],
})
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject')
  },
])
const getTopicData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
const handleSave = async () => {
  const valid = await ruleFormRef.value.validate()
  if (!valid) return
  try {
    // 判断是否设置分数
    if ((paperItem.value.singleChoiceNum > 0 && paperItem.value.singleChoiceScore <= 0) || (paperItem.value.multipleChoiceNum > 0 && paperItem.value.multipleChoiceScore <= 0) || (paperItem.value.judgeNum > 0 && paperItem.value.judgeScore <= 0)) {
      handleSetScore()
      return
    }
    paperItem.value.totalScore = paperItem.value.singleChoiceNum * paperItem.value.singleChoiceScore + paperItem.value.multipleChoiceNum * paperItem.value.multipleChoiceScore + paperItem.value.judgeNum * paperItem.value.judgeScore
    if (paperItem.value.id !== undefined) {
      await updatePaper(paperItem.value)
      message.success(t('global.editSuccess'))
      delView(unref(currentRoute))
      push('/exam-mgt/exampaper')
    }
    else {
      await addPaper(paperItem.value)
      message.success(t('global.addSuccess'))
      delView(unref(currentRoute))
      push('/exam-mgt/exampaper')
    }
  } catch {}
}
const handleCancel = () => {
  delView(unref(currentRoute))
  push('/exam-mgt/exampaper')
}
// 编辑
const handleEdit = (row: PaperItem) => {
  paperItem.value = { ...row }
  handleQuestionNumberChange()
}
// 选择试题库
const handleChooseQuestion = () => {
  RefChild.value.handleOpen(paperItem.value.banks)
}
// 选择试题库确认
const handleChooseConfirm = (val: any) => {
  val.forEach((bank, index) => {
    bank.totalSingleChoiceNum = bank.singleChoiceNum * 1
    bank.totalMultipleChoiceNum = bank.multipleChoiceNum * 1
    bank.totalJudgeNum = bank.judgeNum * 1
    bank.singleChoiceNum = 0
    bank.multipleChoiceNum = 0
    bank.judgeNum = 0
    bank.bankId = bank.id
    bank.id = null
    bank.sort = index + paperItem.value.banks.length
  })
  paperItem.value.banks = val
}
// 设置分数
const handleSetScore = () => {
  RefSetScore.value.handleOpen({ single: paperItem.value.singleChoiceScore, multiple: paperItem.value.multipleChoiceScore, judge: paperItem.value.judgeScore })
}
// 设置分数确认
const handleSetScoreConfirm = (val: { single: number, multiple: number, judge: number }) => {
  paperItem.value.singleChoiceScore = val.single * 1
  paperItem.value.multipleChoiceScore = val.multiple * 1
  paperItem.value.judgeScore = val.judge * 1
}
// 试题数量更新
const handleQuestionNumberChange = () => {
  paperItem.value.singleChoiceNum = 0
  paperItem.value.multipleChoiceNum = 0
  paperItem.value.judgeNum = 0
  paperItem.value.banks.forEach((bank) => {
    bank.singleChoiceNum = bank.singleChoiceNum ? bank.singleChoiceNum * 1 : 0
    bank.multipleChoiceNum = bank.multipleChoiceNum ? bank.multipleChoiceNum * 1 : 0
    bank.judgeNum = bank.judgeNum ? bank.judgeNum * 1 : 0
    // 对比试题输入数量是否大于试题总数量
    bank.singleChoiceNum = bank.singleChoiceNum < bank.totalSingleChoiceNum ? bank.singleChoiceNum : bank.totalSingleChoiceNum
    bank.multipleChoiceNum = bank.multipleChoiceNum < bank.totalMultipleChoiceNum ? bank.multipleChoiceNum : bank.totalMultipleChoiceNum
    bank.judgeNum = bank.judgeNum < bank.totalJudgeNum ? bank.judgeNum : bank.totalJudgeNum

    paperItem.value.singleChoiceNum += bank.singleChoiceNum
    paperItem.value.multipleChoiceNum += bank.multipleChoiceNum
    paperItem.value.judgeNum += bank.judgeNum
  })
}
// 选择试题包所有试题
const handleSelectAll = (row) => {
  row.singleChoiceNum = row.totalSingleChoiceNum
  row.multipleChoiceNum = row.totalMultipleChoiceNum
  row.judgeNum = row.totalJudgeNum
  handleQuestionNumberChange()
}
// 删除试题包
const handleDelete = (row, index) => {
  paperItem.value.banks.splice(index, 1)
  handleQuestionNumberChange()
}

onMounted(() => {
  getTopicData()
})
watch(
  () => isChooseQuestion,
  (newProps: any) => {
    isChooseQuestion.value = newProps
  },
)
defineExpose({ handleEdit })
</script>

<template>
  <ContentWrap>
    <div class="p-5">
      <h2 class="text-xl mb-10">
        {{ props.titleName && props.titleName.length > 0 ? props.titleName : t('examMgt.paper.createAutoPaper') }}
      </h2>
      <el-form ref="ruleFormRef" class="w-[600px]" :model="paperItem" :rules="rules" label-width="auto" :label-position="labelPosition">
        <el-form-item :label="t('learningCenter.paper.paperName')" prop="name">
          <el-input v-model="paperItem.name" :placeholder="t('examMgt.paper.namePH')" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item :label="t('category.topic.subjectName')">
          <!-- <el-select v-model="paperItem.classifyId" placeholder="Please choose" clearable>
            <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
          <SubjectSelect v-model="paperItem.classifyId" :has-no-subject="true" selecte-tree-style="width: 100%" />
        </el-form-item>
        <el-form-item label="Type">
          <div>{{ t('examMgt.paper.autoPaper') }}</div>
        </el-form-item>
        <el-form-item label="Question Pool">
          <el-button type="primary" @click="handleChooseQuestion">
            {{ t('examMgt.paper.chooseQuestion') }}
          </el-button>
          <el-button type="primary" @click="handleSetScore">
            {{ t('examMgt.paper.setScoreInBatch') }}
          </el-button>
        </el-form-item>
      </el-form>
      <el-table class="mb-4" :data="paperItem.banks" border>
        <el-table-column prop="name" :label="t('examMgt.paper.questionPackage')" align="center" />
        <el-table-column prop="singleChoiceNum" :label="t('examMgt.question.singleChoice')" width="180" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <el-input v-model="row.singleChoiceNum" class="mr-[8px]" style="width: 50%" oninput="value=value.replace(/^(0+)|[^\d]+/g,''); " @change="handleQuestionNumberChange" />
              <span> {{ `/ ${row.totalSingleChoiceNum}` }} </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="multipleChoiceNum" :label="t('examMgt.paper.multipleChoiceType')" width="180" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <el-input v-model="row.multipleChoiceNum" class="mr-[8px] w-6/12" oninput="value=value.replace(/^(0+)|[^\d]+/g,''); " @change="handleQuestionNumberChange" />
              <span> {{ `/ ${row.totalMultipleChoiceNum}` }} </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="judgeNum" :label="t('examMgt.question.trueOrFalse')" width="180" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <el-input v-model="row.judgeNum" class="mr-[8px] w-6/12" oninput="value=value.replace(/^(0+)|[^\d]+/g,''); " @change="handleQuestionNumberChange" />
              <span> {{ `/ ${row.totalJudgeNum}` }} </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="t('global.action')" width="240" align="center">
          <template #default="{ row, index }">
            <el-button link type="primary" @click="handleSelectAll(row)">
              <el-icon class="me-1">
                <IconNavigation />
              </el-icon>
              {{ t('action.chooseAll') }}
            </el-button>
            <el-button link type="primary" @click="handleDelete(row, index)">
              <el-icon class="me-1">
                <IconLess />
              </el-icon>
              {{ t('action.remove') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="question-select mb-1 text-sm">
        {{ t('examMgt.paper.totalQuestion')}}
        <span>
        {{ paperItem.selectedQuestion }}
      </span>
        {{ t('examMgt.question.singleChoice')}}
        <span>
        {{ paperItem.multipleChoiceNum }}
      </span>
        {{ t('examMgt.question.multipleChoice')}}
        <span>
        {{ paperItem.judgeNum }}
      </span>
        {{ t('examMgt.question.trueOrFalse')}}
      </div>
      <div class="mb-4 text-sm">
        {{ t('examMgt.paper.fullScore')}} {{ paperItem.singleChoiceNum * paperItem.singleChoiceScore + paperItem.multipleChoiceNum * paperItem.multipleChoiceScore + paperItem.judgeNum * paperItem.judgeScore }}
      </div>
      <el-button type="primary" @click="handleSave">
        {{ t('action.save')}}
      </el-button>
      <el-button :style="{ '--el-button-text-color': '#007943', 'border': '1px solid #007943' }" @click="handleCancel">
        {{ t('action.cancel')}}
      </el-button>
    </div>
  </ContentWrap>

  <SetScore ref="RefSetScore" v-model="isVisible" @set-score="handleSetScoreConfirm" />
  <ChoosePackage ref="RefChild" v-model="isChooseQuestion" :title-name="t('examMgt.paper.chooseQuestionPackage')" @question-bank-choose="handleChooseConfirm" />
</template>

<style scoped>
.question-select span {
  color: #007943;
  font-weight: bold;
}
</style>
