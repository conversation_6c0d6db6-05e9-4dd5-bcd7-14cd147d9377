<script setup lang="ts" name="PaperEdite">
import type { ComponentInternalInstance } from 'vue'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import AutoPaper from '../paper-add/autoPaper.vue'
import CustomPaper from '../paper-add/customPaper.vue'
import { getPaper } from '@/api/topicMgt/paper'

interface BankItem {
  bankId: number
  id: number
  bankName: string
  name: string
  singleChoiceNum: number
  multipleChoiceScore: number
  judgeNum: number
  sort: number
}

interface PaperItem {
  banks: Array<BankItem>
  questions: Array<QuestionItem>
  judgeScore: number
  multipleChoiceScore: number
  singleChoiceScore: number
  totalScore: number
  name: string
  classifyId: string
  status: number
  isRandom: boolean
}

interface QuestionItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNum: number
  pageSize: number
  id: number
  questionId: number
  bankId: number
  content: string
  score: number
  type: number
  sort: number
}

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const router = useRouter()
const route = useRoute()
const paperItem = ref<PaperItem>()
const RefCustomPaper = ref()
const RefAutoPaper = ref()

// 获取试卷详情
const getPaperInfo = async () => {
  paperItem.value = await getPaper(route.query.id)
  if (paperItem.value?.isRandom === true) {
    paperItem.value.banks.forEach(bank => {
      bank.name = bank.bankName
    })
    RefAutoPaper.value.handleEdit(paperItem.value)
  } else if (paperItem.value?.isRandom === false) {
    RefCustomPaper.value.handleEdit(paperItem.value)
  }
}

onMounted(() => {
  getPaperInfo()
})
</script>

<template>
  <div v-show="paperItem && paperItem.isRandom === true">
    <AutoPaper ref="RefAutoPaper" :title-name="t('examMgt.paper.editAutoPaper')" />
  </div>
  <div v-show="paperItem && paperItem.isRandom === false">
    <CustomPaper ref="RefCustomPaper" :title-name="t('examMgt.paper.editCustomizedPaper')" />
  </div>
</template>
