<script setup lang="ts" name="newEdit">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import { listTopicAll } from '@/api/category/topic'

import {listQustion, listQustionBank, QuestionBankRespVO} from '@/api/topicMgt/question'

interface BankItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  name: string
  classifyId: string
  questionNum: string
  deptCode: string
}

const props = defineProps<{ titleName: string; modelValue: boolean }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'questionBankChoose'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const bankList = ref<Array<BankItem>>([])
const typeTitle = ref([t('examMgt.question.singleChoice'), t('examMgt.question.multipleChoice'), t('examMgt.question.trueOrFalse')])
const multipleSelection = ref<Array<BankItem>>([])
const selectFlag = ref(true)
const RefTable = ref()
const formRef = ref()
const total = ref(0)
const bankData = reactive<{
  bankParams: any
}>({
  bankParams: {
    pageNo: 1,
    pageSize: 10,
    name: '',
    type: ''
  }
})
const { bankParams } = toRefs(bankData)
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject')
  }
])
const isVisible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
/** 表单重置 */
const reset = () => {
  formRef.value?.resetFields()
}
const handleOpen = (banks: Array<BankItem>) => {
  reset()
  multipleSelection.value = []
  banks.forEach(element => {
    element.id = element.bankId
    multipleSelection.value.push(element)
  })
  getBankList()
  getTopicData()
  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}
const getTopicData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
// 获取试题集列表
const getBankList = async () => {
  const res = await listQustionBank(bankParams.value)
  bankList.value = res.list
  total.value = res.total
  selectFlag.value = true
  setTableSelected()
}
// 试题集搜索
const handleBankSearch = () => {
  getBankList()
}
// 确认选择
const handleConfirm = () => {
  emit('questionBankChoose', multipleSelection.value)
  isVisible.value = false
}
// table 选中
const handleSelectionChange = (val: BankItem[]) => {
  if (!selectFlag.value) {
    // 1、循环选中试题
    // 2、循环当前试题数据如果选中列表中有数据，则判断当前还是否选中,如果未选中则删除
    // 3、循环当前选中试题，如果已选中试题中没有，则添加
    const deleteIds = []
    multipleSelection.value?.forEach((selectedQues, index) => {
      let flag = true
      bankList.value.forEach(listQues => {
        // 只有在当前列表展示的才能操作删除
        if (selectedQues.id === listQues.id) {
          flag = false
          val.forEach(currentQues => {
            if (selectedQues.id === currentQues.id) {
              flag = true
            }
          })
          if (!flag) {
            deleteIds.push(selectedQues.id)
            // multipleSelection.value?.splice(index, 1)
          }
        }
      })
    })
    deleteIds.forEach(delId => {
      multipleSelection.value?.forEach((selectedItem, index) => {
        if (selectedItem.id === delId) {
          multipleSelection.value?.splice(index, 1)
        }
      })
    })
    val.forEach(currentQues => {
      let contentFlag = false
      multipleSelection.value?.forEach((selectedQues, index) => {
        if (selectedQues.id === currentQues.id) {
          contentFlag = true
        }
      })
      if (!contentFlag) {
        multipleSelection.value?.push(currentQues)
      }
    })
  }

  // multipleSelection.value = val
}
// 设置表格数据回显
const setTableSelected = () => {
  setTimeout(() => {
    bankList.value.forEach((item, index) => {
      multipleSelection.value.forEach(sel => {
        if (sel.id === item.id) {
          sel = item
          RefTable.value.toggleRowSelection(item, true)
        }
      })

      if (index === bankList.value.length - 1) {
        selectFlag.value = false
      }
    })
  }, 0)
}
const handleRowClick = (row: QuestionBankRespVO) => {
  let flag = true
  if (multipleSelection.value && multipleSelection.value.find(item => item === row.id)) {
    flag = false
  }
  RefTable.value?.toggleRowSelection(row, flag)
}
const getRowKeys = (row) => {
  return row.id
}
defineExpose({ handleOpen })
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" width="70%" class="notice-dialog" @close="handleClose">
    <el-form ref="formRef" :inline="true" :model="bankParams">
      <el-form-item :label="t('examMgt.paper.name')" prop="name">
        <el-input v-model="bankParams.name" :placeholder="t('common.inputText')" clearable class="!w-160px" />
      </el-form-item>
      <el-form-item :label="t('category.topic.subjectName')" prop="classifyId">
        <el-select v-model="bankParams.classifyId" :placeholder="t('common.chooseText')" clearable class="!w-160px" >
          <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item class="me-0">
        <el-button type="default" @click="handleBankSearch">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('action.search') }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-table ref="RefTable" :data="bankList" border :row-key="getRowKeys" @selection-change="handleSelectionChange" @row-click="handleRowClick">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="name" :label="t('examMgt.paper.itemName')" header-align="center" />
      <el-table-column prop="classifyName" :label="t('category.topic.subjectName')" width="180" header-align="center">
        <template #default="{ row }">
          {{ row.classifyId === 0 ? t('common.noSubject') : row.classifyName }}
        </template>
      </el-table-column>
      <el-table-column prop="questionNum" :label="t('examMgt.paper.questionNumber')" width="180" align="center" />
    </el-table>
    <div class="relative h-[95px]">
      <div class="absolute right-0 top-2">
        <pagination v-show="total > 0" v-model:page="bankParams.pageNo" v-model:limit="bankParams.pageSize" :total="total" @pagination="getBankList" />
      </div>
    </div>
    <template #footer>
      <div>
        <el-button type="primary" @click="handleConfirm"> {{ t('global.confirm') }} </el-button>
        <el-button @click="handleClose"> {{ t('global.cancel') }} </el-button>
      </div>
    </template>
  </Dialog>
</template>
