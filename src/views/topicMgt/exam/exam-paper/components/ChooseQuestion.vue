<script setup lang="ts" name="newEdit">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'

import { listQustion, listQustionBank } from '@/api/topicMgt/question'
import { formatContent } from '@/utils'

interface BankItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  name: string
  classifyId: string
  questionNum: string
  deptCode: string
}
interface QuestionItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  bankId: number
  questionId: number
  content: string
  image: string
  rightAnswer: string
  score: number
  type: number
  options: Array<Option>
}
interface Option {
  content: string
  answer: boolean
}

const props = defineProps<{ titleName: string; modelValue: boolean }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'questionChoose'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const RefAside = ref()
const RefTable = ref()
const MenuRef = ref()
const formRef = ref()
const visible = ref(false)
const tipContent = ref('')
const asideHeight = ref(400)
const bankList = ref<Array<BankItem>>([])
const questionList = ref<Array<QuestionItem>>([])
const typeTitle = ref([t('examMgt.question.singleChoice'), t('examMgt.question.multipleChoice'), t('examMgt.question.trueOrFalse')])
const multipleSelection = ref<Array<QuestionItem>>([])
const selections = ref<Array<QuestionItem>>([])
const selectedIds = ref<number[]>([])
const selectFlag = ref(true)
const statistics = ref({
  single: 0,
  multiple: 0,
  trueorfalse: 0
})
const bankData = reactive<{
  bankParams: any
}>({
  bankParams: {
    pageNo: 1,
    pageSize: 999,
    name: ''
  }
})
const { bankParams } = toRefs(bankData)
const questionTotal = ref(0)
const questionData = reactive<{
  questionParams: any
}>({
  questionParams: {
    pageNo: 1,
    pageSize: 10,
    type: undefined,
    content: undefined,
    bankId: undefined
  }
})
const { questionParams } = toRefs(questionData)

const isVisible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
/** 表单重置 */
const reset = () => {
  formRef.value?.resetFields()
}
const handleOpen = (selectedList: Array<QuestionItem>) => {
  reset()
  selections.value = []
  selectedIds.value = []
  selectedList.forEach(element => {
    element.id = element.questionId
    selections.value.push(element)
    selectedIds.value.push(element.questionId)
  })
  getBankList()
  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}
// 获取试题集列表
const getBankList = async () => {
  const res = await listQustionBank(bankParams.value)
  bankList.value = res.list
  if (bankList.value.length > 0) {
    questionParams.value.bankId = bankList.value[0].id
    getQuestionList()
  }
}
// 试题集搜索
const handleBankSearch = () => {
  getBankList()
}
// 点击试题集，刷新试题列表
const handleBankChoose = (bank: BankItem) => {
  visible.value = false
  questionParams.value.bankId = bank.id
  getQuestionList()
}
// 获取试题列表
const getQuestionList = async () => {
  const res = await listQustion(questionParams.value)
  questionList.value = res.list
  questionTotal.value = res.total
  selectFlag.value = true
  setTableSelected()
}
const handleSelectionChange = (selection: QuestionItem[]) => {
  if (!selectFlag.value) {
    // 1、循环选中试题
    // 2、循环当前试题数据如果选中列表中有数据，则判断当前还是否选中,如果未选中则删除
    // 3、循环当前选中试题，如果已选中试题中没有，则添加
    const deleteIds = []
    selections.value?.forEach((selectedQues, index) => {
      let flag = true
      questionList.value.forEach(listQues => {
        // 只有在当前列表展示的才能操作删除
        if (selectedQues.id === listQues.id) {
          flag = false
          selection.forEach(currentQues => {
            if (selectedQues.id === currentQues.id) {
              flag = true
            }
          })
          if (!flag) {
            deleteIds.push(selectedQues.id)
            // selections.value?.splice(index, 1)
          }
        }
      })
    })
    deleteIds.forEach(delId => {
      selections.value?.forEach((selectedQues, index) => {
        if (selectedQues.id === delId) {
          selections.value?.splice(index, 1)
        }
      })
    })
    selection.forEach(currentQues => {
      let contentFlag = false
      selections.value?.forEach((selectedQues, index) => {
        if (selectedQues.id === currentQues.id) {
          contentFlag = true
        }
      })
      if (!contentFlag) {
        selections.value?.push(currentQues)
      }
    })

    // selections.value = selection
    // selectedIds.value = selection.map(item => item.id)
  }
  // multiple.value = !selection.length
  calculateQuestion()
}
const handleRowClick = (row: QuestionItem) => {
  let flag = true
  if (selectedIds.value && selectedIds.value.find(item => item === row.id)) {
    flag = false
  }
  RefTable.value?.toggleRowSelection(row, flag)
}
// 设置表格数据回显
const setTableSelected = () => {
  setTimeout(() => {
    questionList.value.forEach((item, index) => {
      selections.value.forEach(sel => {
        if (sel.id === item.id) {
          sel = item
          RefTable.value.toggleRowSelection(item, true)
        }
      })
      if (index === questionList.value.length - 1) {
        selectFlag.value = false
      }
    })
  }, 0)
}
// 试题搜索
const handleSearch = () => {
  getQuestionList()
}
// 确认选择
const handleConfirm = () => {
  emit('questionChoose', selections.value)
  isVisible.value = false
}
// 试题统计计算
const calculateQuestion = () => {
  statistics.value.single = 0
  statistics.value.multiple = 0
  statistics.value.trueorfalse = 0
  selections.value?.forEach(question => {
    switch (question.type) {
      case 0:
        statistics.value.single++
        break
      case 1:
        statistics.value.multiple++
        break
      case 2:
        statistics.value.trueorfalse++
        break
      default:
        break
    }
  })
}
const getRowKeys = (row) => {
  return row.id
}
// table 选中
// function handleSelectionChange(val: QuestionItem[]) {
//   multipleSelection.value = val
//   statistics.value.single = 0
//   statistics.value.multiple = 0
//   statistics.value.trueorfalse = 0
//   val.forEach(question => {
//     switch (question.type) {
//       case 0:
//         statistics.value.single++
//         break
//       case 1:
//         statistics.value.multiple++
//         break
//       case 2:
//         statistics.value.trueorfalse++
//         break
//       default:
//         break
//     }
//   })
// }

// 监听高度
// function updateHeight() {
//   asideHeight.value = RefTable.value.offsetHeight
// }

// const observer = new ResizeObserver(() => {
//   updateHeight()
// })

// onMounted(() => {
//   updateHeight()
//   observer.observe(RefTable.value)
// })

// onBeforeUnmount(() => {
//   observer.disconnect()
// })

// watch(asideHeight, (newHeight, oldHeight) => {
//   console.log(`Height changed from ${oldHeight}px to ${newHeight}px`)
// })

defineExpose({ handleOpen })
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" width="1100" @close="handleClose">
    <el-row :gutter="20">
      <el-col :span="10" :xs="24">
        <ContentWrap>
          <div class="head-container">
            <el-input v-model="bankParams.name" class="mb-20px" clearable placeholder="Search ..." @change="handleBankSearch">
              <template #prefix>
                <Icon icon="ep:search" />
              </template>
            </el-input>
            <el-scrollbar class="dir-tree" :height="scrollBarHeightFull ? 'calc(100vh - 300px)' : '500px'">
              <el-menu class="wrap-menu" style="border: 0; width: 100%" default-active="0">
                <el-menu-item v-for="(bank, index) in bankList" :key="index" style="height: 32px; padding: 0" :index="`${index}`" @click="handleBankChoose(bank)">
                  <el-tooltip class="box-item" effect="dark" :show-after="500" :hide-after="0" :offset="-10" :content="bank.name" placement="bottom-start">
                    <div class="w-[280px] overflow-ellipsis overflow-hidden">
                      <el-icon><document /></el-icon>
                      <span ref="MenuRef" class="w-full whitespace-nowrap overflow-ellipsis overflow-hidden">
                    {{ bank.name }}
                  </span>
                    </div>
                  </el-tooltip>
                </el-menu-item>
              </el-menu>
            </el-scrollbar>
          </div>
        </ContentWrap>
      </el-col>
      <el-col :span="14" :xs="24">
        <ContentWrap>
          <el-form ref="formRef" class="ml-5" :inline="true" :model="questionParams">
            <el-form-item :label="t('examMgt.paper.content')" prop="content">
              <el-input v-model="questionParams.content" :placeholder="t('common.inputText')" clearable  class="!w-160px" />
            </el-form-item>
            <el-form-item :label="t('examMgt.paper.type')" prop="type">
              <el-select v-model="questionParams.type" :placeholder="t('common.chooseText')" clearable class="!w-160px">
                <el-option :label="t('examMgt.question.singleChoice')" value="0" />
                <el-option :label="t('examMgt.question.multipleChoice')" value="1" />
                <el-option :label="t('examMgt.question.trueOrFalse')" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item class="me-0">
              <el-button type="default" @click="handleSearch">
                <Icon class="mr-5px" icon="ep:search" />
                {{ t('action.search') }}
              </el-button>
            </el-form-item>
          </el-form>
          <el-table ref="RefTable" :data="questionList" border :row-key="getRowKeys" @selection-change="handleSelectionChange" @row-click="handleRowClick">
            <el-table-column type="selection" :reserve-selection="true" width="55" align="center" />
            <el-table-column prop="content" :label="t('examMgt.question.questionContent')" header-align="center">
              <template #default="{ row }">
                <div v-dompurify-html="formatContent(row.content)"></div>
              </template>
            </el-table-column>
            <el-table-column prop="type" :label="t('examMgt.question.questionType')" width="180" align="center">
              <template #default="{ row }">
                {{ typeTitle[row.type] }}
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="questionTotal > 0" v-model:page="questionParams.pageNo" v-model:limit="questionParams.pageSize" :total="questionTotal" @pagination="getQuestionList" />
        </ContentWrap>
      </el-col>
    </el-row>
    <div class="bg-[#E2EBFA] h-[60px] text-center leading-[60px] text-black rounded-[5px]">
      {{ t('examMgt.paper.selectedQuestion') }}
      <span class="font-bold text-[#007943]"> {{ statistics.single }} </span>
      {{ t('examMgt.question.singleChoice') }}
      <span class="font-bold text-[#007943]"> {{ statistics.multiple }} </span>
      {{ t('examMgt.question.multipleChoice') }}
      <span class="font-bold text-[#007943]"> {{ statistics.trueorfalse }} </span>
      {{ t('examMgt.question.trueOrFalse') }}
    </div>

    <template #footer>
      <div>
        <el-button type="primary" @click="handleConfirm"> {{ t('global.confirm') }} </el-button>
        <el-button @click="handleClose"> {{ t('global.cancel') }} </el-button>
      </div>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">
.singleton-tooltip {
  transition: transform 0.3s var(--el-transition-function-fast-bezier);
}
</style>
