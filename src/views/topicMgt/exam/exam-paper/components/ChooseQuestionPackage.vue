<script setup lang="ts" name="newEdit">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'

import { addQustionBank, updateQustionBank } from '@/api/topicMgt/question'

interface rowObject {
  name: string
  classifyId: string
}
const props = defineProps<{ titleName: string; modelValue: boolean; subjectList: any }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'parentEmit'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const labelPosition = ref<FormProps['labelPosition']>('top')
const formRef = ref<FormInstance>()
const loading = ref(false)
const data = reactive<{
  rules: any
}>({
  rules: {
    name: [{ required: true, message: t('examMgt.question.itemTitleRule'), trigger: 'change' }],
    classifyId: [{ required: true, message: t('examMgt.question.choiceSubjectRule'), trigger: 'change' }]
  }
})

const { rules } = toRefs(data)
const form = ref()
const isVisible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
/** 表单重置 */
const reset = () => {
  formRef.value?.resetFields()
}
const handleOpen = () => {
  form.value = {}
  reset()

  isVisible.value = true
}
const handleEdit = (row: rowObject) => {
  form.value = { ...row }
  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}

defineExpose({ handleOpen, handleEdit })
/** 提交按钮 */
const handleConfirm = async () => {
  await formRef.value.validate()
  loading.value = true
  try {
    if (form.value.id !== undefined) {
      await updateQustionBank(form.value)
      message.success(t('global.editSuccess'))
    } else {
      await addQustionBank(form.value)
      message.success(t('global.addSuccess'))
    }
    isVisible.value = false
    emit('parentEmit')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" width="800" class="notice-dialog" @close="handleClose">
    <el-form ref="formRef" style="max-width: 600px" :model="form" label-width="auto" class="demo-dynamic" :label-position="labelPosition" :rules="rules">
      <el-form-item prop="name" :label="t('examMgt.paper.paper')">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item prop="classifyId" :label="t('category.topic.subjectName')">
        <el-select v-model="form.classifyId" :placeholder="t('common.chooseText')" clearable class="w-full">
          <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button type="primary" @click="handleConfirm"> {{ t('global.confirm') }}  </el-button>
        <el-button @click="handleClose"> {{ t('global.cancel') }} </el-button>
      </div>
    </template>
  </Dialog>
</template>
