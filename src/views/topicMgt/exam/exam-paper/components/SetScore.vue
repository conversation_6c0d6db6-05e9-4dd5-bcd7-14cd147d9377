<script setup lang="ts" name="newEdit">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'

interface BatchScore {
  single: number
  multiple: number
  judge: number
}

const props = defineProps<{ modelValue: boolean }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'setScore'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const labelPosition = ref<FormProps['labelPosition']>('top')
const formRef = ref<FormInstance>()
const batchScoreForm = reactive<BatchScore>({
  single: 0,
  multiple: 0,
  judge: 0
})

const isVisible = computed({
  get() {
    reset()
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const reset = () => {
  formRef.value?.resetFields()
}

const handleOpen = (batchScore: BatchScore) => {
  batchScoreForm.judge = batchScore.judge
  batchScoreForm.single = batchScore.single
  batchScoreForm.multiple = batchScore.multiple
  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}
// 确认选择
const handleConfirm = () => {
  emit('setScore', batchScoreForm)
  isVisible.value = false
}

defineExpose({ handleOpen })
</script>

<template>
  <Dialog v-model="isVisible" :title="t('examMgt.paper.setScoreInBatch')" width="400" class="notice-dialog" @close="handleClose">
    <el-form style="max-width: 90%" :model="batchScoreForm" label-width="auto" class="demo-dynamic m-auto" :label-position="labelPosition">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="single" :label="t('examMgt.question.singleChoice')">
            <el-input v-model="batchScoreForm.single" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="multiple" :label="t('examMgt.question.multipleChoice')">
            <el-input v-model="batchScoreForm.multiple" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item prop="judge" :label="t('examMgt.question.trueOrFalse')">
            <el-input v-model="batchScoreForm.judge" type="number" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div>
        <el-button type="primary" @click="handleConfirm"> {{ t('global.confirm') }} </el-button>
        <el-button type="default" @click="handleClose"> {{ t('global.cancel') }} </el-button>
      </div>
    </template>
  </Dialog>
</template>
