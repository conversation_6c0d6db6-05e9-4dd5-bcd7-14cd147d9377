<script setup lang="ts" name="Exam">
// import ExamList from './exam-list/index.vue'
import ExamPaper from './exam-paper/index.vue'

const activeName = ref('paper')
const route = useRoute()

const handleClick = (targetName: string) => {
  console.log(targetName)
}
onMounted(() => {
  activeName.value = route.query.tabSelect ? `${route.query.tabSelect}` : 'paper'
})
</script>

<template>
  <div class="app-container">
    <!-- <el-tabs v-model="activeName" @tab-change="handleTabClick">
      <el-tab-pane label="Exam paper" name="paper"> -->
    <ExamPaper />
    <!-- </el-tab-pane> -->
    <!-- <el-tab-pane label="Exam" name="exam">
        <ExamList />
      </el-tab-pane> -->
    <!-- </el-tabs> -->
  </div>
</template>