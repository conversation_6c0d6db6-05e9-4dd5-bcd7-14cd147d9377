import { EChartsOption } from 'echarts'
const { t } = useI18n()

// 当月每天的观看直播人数
export const viewersNumberOptions = {
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [],
      type: 'bar',
      barWidth: 20, // 设置柱子宽度为固定值
      barCategoryGap: '10px', // 调整柱子之间的距离
      itemStyle: {
        color: 'red'
      },
    }
  ],
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
};
// 当月的排名前十的直播
export const liveRankingOptions = {
  xAxis: {
    max: 'dataMax'
  },
  yAxis: {
    type: 'category',
    data: [],
    inverse: true,
    animationDuration: 300,
    animationDurationUpdate: 300,
    max: 2 // only the largest 3 bars will be displayed
  },
  series: [
    {
      realtimeSort: true,
      name: 'Total Number of Viewers',
      type: 'bar',
      data: [],
      label: {
        show: true,
        position: 'right',
        valueAnimation: true
      },
    }
  ],
  legend: {
    show: true,
    bottom: 'bottom'
  },
  dataZoom: [
    {
      type: 'slider',   // 滑块型 dataZoom
      orient: 'vertical', // 垂直方向的数据区域缩放
      start: 0,         // 初始显示从第一个数据开始
      end: 50,          // 显示前 50% 数据
      width: 20,        // 滚动条宽度
      top: 'top',       // 放置在顶部
      handleSize: '80%' // 手柄大小
    }
  ],
  animationDuration: 0,
  animationDurationUpdate: 3000,
  animationEasing: 'linear',
  animationEasingUpdate: 'linear'
};




