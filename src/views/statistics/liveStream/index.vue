<template>
  <ContentWrap>
    <el-row :gutter="20">
      <el-col :span="8">
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[500px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[350px] text-[14px] text-white font-medium">
            Total Live Stream: {{ liveAggregateInfo?.liveTotal }}
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[500px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[350px] text-[14px] text-white font-medium">
            Total users: {{ liveAggregateInfo?.userTotal }}
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[500px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[350px] text-[14px] text-white font-medium">
            Total time: {{ liveAggregateInfo?.durationTotal }}
          </div>
        </div>
      </el-col>
    </el-row>

  </ContentWrap>
  <!-- 图形展示 -->
  <ContentWrap>
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item name="1">
        <template #title="{ isActive }">
          <div :class="['title-wrapper', { 'is-active': isActive }]" class="flex justify-between w-full">
            <div class="font-bold">Monthly Report</div>
            <div class="h-full flex mt-[12px]">
              <el-button
                class="ms-3 me-3"
                link
                type="primary"
                @click="handleExportReport"
                @click.stop
              >
                Export Report
              </el-button>
            </div>
          </div>
        </template>
        <ContentWrap>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="h-[100px] p-3 rounded-[10px] w-[300px] ms-2 me-2 border border-[#E8B8FF] border-solid">
                <div class="text-[14px]">
                  Total Number of Live Stream
                </div>
                <div class="w-[300px] text-[14px] font-medium">{{ liveAggregateAllInfo?.liveTotal }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="h-[100px] p-3 rounded-[10px] w-[300px] ms-2 me-2 border border-[#E8B8FF] border-solid">
                <div class="text-[14px]">
                  Total Number of Viewers
                </div>
                <div class="text-[14px] font-medium">{{ liveAggregateAllInfo?.userTotal }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="h-[100px] p-3 rounded-[10px] w-[300px] ms-2 me-2 border border-[#E8B8FF] border-solid">
                <div class="text-[14px]">
                  Total Live Stream Duration(min)
                </div>
                <div class="text-[14px] font-medium">{{ liveAggregateAllInfo?.durationTotal	}}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="h-[100px] p-3 rounded-[10px] w-[300px] ms-2 me-2 border border-[#E8B8FF] border-solid">
                <div class="text-[14px]">
                  Average Duration Per Live Stream(min)
                </div>
                <div class="text-[14px] font-medium">{{ liveAggregateAllInfo?.durationTotal }}</div>
              </div>
            </el-col>
          </el-row>
        </ContentWrap>
        <ContentWrap>
          <div ref="viewersNumberRef" class="h-[300px]"></div>

<!--          <Echart :options="viewersNumberOptionsData" :height="300" />-->
        </ContentWrap>
        <ContentWrap>
          <div ref="liveRankingRef" class="h-[300px]"></div>
<!--          <Echart :options="liveRankingOptionsData" :height="300" />-->
        </ContentWrap>
      </el-collapse-item>
    </el-collapse>
  </ContentWrap>

  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="140px"
    >
      <el-form-item label="Live Stream Name" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="Please input"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Status" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.STATISTICS_LIVE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset</el-button>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          Export
        </el-button>
      </el-form-item>

    </el-form>
  </ContentWrap>
<!--  列表信息-->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" row-key="roomId" @selection-change="handleCurrentChange">
      <el-table-column type="selection" :reserve-selection="true" width="55" align="center" />
      <el-table-column label="Live Stream Name" align="center" prop="roomName" min-width="180px" />
      <el-table-column label="Stream Trainer" align="center" prop="name" min-width="180px">
        <template #default="scope">
          {{ scope.row.speakers ? scope.row.speakers.map(item => item.nickname).join(',') : '' }}
        </template>
      </el-table-column>
      <el-table-column label="Start Time" align="center" prop="startTime" min-width="180px" />
      <el-table-column label="End Time" align="center" prop="endTime" min-width="180px" />
      <el-table-column label="Duration" align="center" prop="duration" min-width="180px" />
      <el-table-column label="Status" align="center" prop="trainerScore" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.STATISTICS_LIVE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="Num of student" align="center" prop="studentNum" min-width="180px" />
      <el-table-column label="Attendance" align="center" prop="attendanceRate" min-width="180px" />
      <el-table-column label="Action" align="center" fixed="right" min-width="100px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row.roomId)"
          >
            Details
          </el-button>
        </template>
      </el-table-column>

    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script setup lang="ts" name="LiveStreamStatistics">
import { set } from 'lodash-es'
import { EChartsOption } from 'echarts'
import { viewersNumberOptions, liveRankingOptions } from "@/views/statistics/liveStream/echarts-data"
import {LiveInfoRespVO, LiveStreamApi} from '@/api/statistics/liveStream'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import download from '@/utils/download'
import { getMonthStartAndEnd} from "@/utils/formatDate"
import * as echarts from 'echarts'
import * as ExcelJS from 'exceljs'
import html2canvas from 'html2canvas'
import { saveAs } from 'file-saver'
import { Workbook } from 'exceljs'
import FileSaver from 'file-saver'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const queryRef = ref()
const loading = ref(false)
const exportLoading = ref(false)
const total = ref(0)
const activeName = ref('1')
const tabName = ref('1')
const categoryList = ref([]) // 课程分类信息
const list = ref([])
const allLiveList = ref([]) // 所有直播信息
const queryFormRef = ref()
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
})
const viewersNumberOptionsData = reactive<EChartsOption>(viewersNumberOptions) as EChartsOption
const liveRankingOptionsData = reactive<EChartsOption>(liveRankingOptions) as EChartsOption

const viewersNumberRef = ref<HTMLElement | null>(null)
const liveRankingRef = ref<HTMLElement | null>(null)

const viewersNumberChartInstance = ref()
const iveRankingChartInstance = ref()

const liveAggregateInfo = ref() // 所有直播信息
const liveAggregateAllInfo = ref() // 当月直播信息
const createTime = ref()
const liveIds = ref([])  // 存储直播间id
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.createTime = getMonthStartAndEnd()
  handleQuery()
}
// 获取直播列表信息
const getList = async () => {
  loading.value = true
  try {
    const data = await LiveStreamApi.getLiveInfoPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 获取所有的直播信息
const getAllLiveList = async () => {
  const data = await LiveStreamApi.getLiveInfoPage({pageNo: 1, pageSize: -1})
  allLiveList.value = data.list
}

// 跳转预览详情信息
const handleDetail = (id: number) => {
  router.push({
    name: 'LiveStatisticsDetail',
    query: { id },
    // query: { id: '1897930842064318466' }
  })
}


const handleCurrentChange = (item: LiveInfoRespVO) => {
  liveIds.value = item.map(item => item.roomId)
}

/** 导出图片和所有数据按钮操作 */
const handleExportReport = async () => {
  const workbook = new Workbook()
  const chartImages = getChartImageBase64()
  // 将每个图表插入到不同的 sheet 中
  chartImages.forEach((base64, index) => {
    const worksheet = workbook.addWorksheet(`Chart ${index + 1}`)

    const imageId = workbook.addImage({
      base64: base64,
      extension: 'png'
    })
    worksheet.addImage(imageId, {
      tl: {col: 0, row: 0},
      br: {col: 10, row: 30},
      editAs: 'oneCell'
    })
  })
  // 字段映射关系（根据后端返回的英文字段 -> 中文标题）
  const fieldMap = {
    roomName: "Live Stream Nmae",
    startTime: "Start Time",
    endTime: "End Time",
    duration: "Duration",
    studentNum: "Num of student",
    attendanceRate: "Attendance"
  }

  if (allLiveList.value?.length > 0) {
    // 添加第一个列表 sheet
    const sheetA = workbook.addWorksheet('Live')
    sheetA.addRow(Object.values(fieldMap))
    // 添加每一行数据
    allLiveList.value.forEach(item => {
      const row = Object.keys(fieldMap).map(key => item[key])
      sheetA.addRow(row)
    })
    // 可选：设置列宽
    sheetA.columns.forEach(column => {
      column.width = 20
    })
  }

  // 导出文件
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/octet-stream' })
  FileSaver.saveAs(blob, 'Echarts_export.xlsx')


}
// 导出直播信息
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await LiveStreamApi.exportLiveInfo(liveIds.value?.join(','))
    download.excel(data, 'Live.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
// 统计总的信息
const getLiveAllAggregate = async () => {
  liveAggregateInfo.value = await LiveStreamApi.getLiveInfoAggregate()
}
// 统计当月的信息
const getLiveAggregate = async () => {
  liveAggregateAllInfo.value = await LiveStreamApi.getLiveInfoAggregate({startTime:  createTime.value[0], endTime: createTime.value[1]})
}

// 统计日数据
const getLiveDayAggregate = async () => {
  const data = await LiveStreamApi.getLiveInfoDay({startTime:  createTime.value[0], endTime: createTime.value[1], top: 10})
  // 日期(当月每天的观看直播人数)
  const dateList = data?.dailyViewers?.map(item => item.date)
  viewersNumberOptionsData!.xAxis.data = dateList
  viewersNumberChartInstance.value = echarts.init(viewersNumberRef.value)
  viewersNumberChartInstance.value.setOption(viewersNumberOptionsData)

  // 观看人数
  const viewersCountList = data?.dailyViewers?.map(item => item.viewerCount)
  viewersNumberOptionsData!.series![0].data = viewersCountList
  // 房间名称(当月的排名前十的直播)
  const liveNameList = data?.topRooms?.map(item => item.roomName)
  liveRankingOptionsData!.yAxis.data = liveNameList
  // 观看人数
  const liveViewCountList = data?.topRooms?.map(item => item.viewerCount)
  liveRankingOptionsData!.series![0].data = liveViewCountList
  iveRankingChartInstance.value = echarts.init(liveRankingRef.value)
  iveRankingChartInstance.value.setOption(liveRankingOptionsData)
}

const getChartImageBase64 = () => {
  const images = []
  if (viewersNumberChartInstance.value) {
    images.push(
      viewersNumberChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (iveRankingChartInstance.value) {
    images.push(
      iveRankingChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }

  return images
}

onMounted(() => {
  // 默认是This month
  createTime.value = getMonthStartAndEnd(2)
  getLiveAllAggregate()
  getLiveAggregate()
  getLiveDayAggregate()
  getList()
  getAllLiveList()
})
</script>

<style scoped lang="scss">
</style>
