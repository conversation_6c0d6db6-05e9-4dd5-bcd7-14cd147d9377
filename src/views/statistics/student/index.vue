<script setup lang="ts" name="StudentStatistics">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import moment from 'moment'
import { listTopicAll } from '@/api/category/topic'
import {
  companyEmployeeStatistics,
  deptTreeSelect,
  exportUserLearningStatistics,
  userLearningStatistics
} from '@/api/system/user'
import download from "@/utils/download"
import { listCompany } from "@/api/system/company"
import {DeptRespVO, listDept} from "@/api/system/dept"
import { handlePhaseTree } from "@/utils/tree"
import {useUserStore} from "@/store/modules/user";
interface DeptItem {
  id: string
  value: string
  code: string
  label: string
  children: Array<DeptItem>
}
interface DataItem {
  userId: number
  nickName: string
  badgeNo: string
  email: string
  company: string
  department: string
  section: string
  position: string
  requiredCourseNum: string
  electiveCourseNum: string
  examNum: string
  onboardingNum: string
  companyPolicyNum: string
}
interface StaticsItem {
  bgStyle: string
  companyName: string
  contractorActiveNum: number
  contractorInactiveNum: number
  contractorNum: number
  staffActiveNum: number
  staffInactiveNum: number
  staffNum: number
  totalActiveNum: number
  totalInactiveNum: number
  totalNum: number
}
const { t } = useI18n()
const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const router = useRouter()
const queryRef = ref()
const loading = ref(false)
const staticsBgList = ref(['bg-gradient-to-r from-[#6CA830] to-[#407D04]', 'bg-gradient-to-r from-[#30A772] to-[#08814B]', 'bg-gradient-to-r from-[#2C84A4] to-[#337695]', 'bg-gradient-to-r from-[#2A58A2] to-[#082181]'])

const total = ref(0)
const deptList = ref<Array<DeptItem>>([])
const tableData = ref<Array<DataItem>>([])
const staticsData = ref<Array<StaticsItem>>()
const companyList = ref([])
const departOptions = ref([])
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject'),
  },
])
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    companyId: undefined,
    deptId: undefined,
    name: undefined,
    badge: undefined,
    email: undefined
  },
})
const { queryParams } = toRefs(data)
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  getDepartmentTree(userStore.user.companyId)
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    comId: undefined,
    deptId: undefined,
    name: undefined,
    badge: undefined,
  }
  queryRef.value?.resetFields()
}
// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
const getDeptList = async () => {
  const data = await deptTreeSelect({})
  deptList.value = data[0].children
  formateDeptData(deptList.value)
}
const formateDeptData = (deptData: Array<DeptItem>) => {
  deptData.forEach((dept) => {
    dept.value = dept.id
    if (dept.children) {
      formateDeptData(dept.children)
    }
  })
}
// 获取考试列表
const getList = async () => {
  loading.value = true
  try {
    const res = await userLearningStatistics(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// 查询考试统计
const getStaticsAllExam = async () => {
  const data = await companyEmployeeStatistics()
  staticsData.value = []
  const totalItem = {
    companyName: 'Total',
    bgStyle: staticsBgList.value[0],
    contractorActiveNum: 0,
    contractorInactiveNum: 0,
    contractorNum: 0,
    staffActiveNum: 0,
    staffInactiveNum: 0,
    staffNum: 0,
    totalActiveNum: 0,
    totalInactiveNum: 0,
    totalNum: 0,
  }
  for (let index = 0; index < data?.length; index++) {
    const company = data[index]
    totalItem.totalActiveNum += company.totalActiveNum
    totalItem.totalInactiveNum += company.totalInactiveNum
    totalItem.totalNum += company.totalNum
    if (company.companyName === 'Anton') {
      const antonItem = {
        companyName: 'Anton',
        bgStyle: staticsBgList.value[1],
        contractorActiveNum: 0,
        contractorInactiveNum: 0,
        contractorNum: 0,
        staffActiveNum: 0,
        staffInactiveNum: 0,
        staffNum: 0,
        totalActiveNum: 0,
        totalInactiveNum: 0,
        totalNum: 0,
      }
      const antonContractorItem = {
        companyName: 'Anton Contractor',
        bgStyle: staticsBgList.value[2],
        contractorActiveNum: 0,
        contractorInactiveNum: 0,
        contractorNum: 0,
        staffActiveNum: 0,
        staffInactiveNum: 0,
        staffNum: 0,
        totalActiveNum: 0,
        totalInactiveNum: 0,
        totalNum: 0,
      }
      antonItem.totalActiveNum = company.staffActiveNum
      antonItem.totalInactiveNum = company.staffInactiveNum
      antonItem.totalNum = company.staffNum

      antonContractorItem.totalActiveNum = company.contractorActiveNum
      antonContractorItem.totalInactiveNum = company.contractorInactiveNum
      antonContractorItem.totalNum = company.contractorNum

      staticsData.value.push(antonItem)
      staticsData.value.push(antonContractorItem)
    }
    else {
      company.bgStyle = staticsBgList.value[3]
      if (company.companyName !== null && company.companyName !== 'Others') {
        staticsData.value.push(company)
      }
    }
  }

  staticsData.value.unshift(totalItem)
}
// 查看考试详情
const handleView = (row: DataItem) => {
  router.push({ name: 'StudentStatisticsDetail', query: { userId: row.userId, userName: row.nickName, badgeNumber: row.badgeNo, email: row.email, deptName: row.department, sectNames: row.section, postNames: row.position } })
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportUserLearningStatistics(queryParams.value)
    download.excel(data, `Student statistics-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}
const filterNodeMethod = (value, data) => data.label.includes(value)

// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}
/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}
/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}


onMounted(() => {
  getList()
  getSubjectData()
// getDeptList()
  getStaticsAllExam()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>

<template>
  <div class="app-container">
    <el-scrollbar class="scrollbar-wrapper">
      <div class="flex">
        <div v-for="(company, index) in staticsData" :key="index" class="mb-3 mr-3">
          <div class="h-full flex flex-col justify-between px-[10px] pb-3 rounded-[10px] w-[400px]" :class="company.bgStyle">
            <div class="py-4 flex justify-between items-center text-white font-bold">
              <span class="text-[16px]">{{ `${company.companyName} ${t('statistics.student.student')}` }}</span>
              <span class="text-[22px]">{{ company.totalNum }}</span>
            </div>
            <div class="flex justify-between items-center h-[90px] rounded-[10px] bg-gradient-to-b from-[#E6EFFD] to-[#FFFFFF]">
              <div class="ml-5">
                <div class="text-[#727984] text-[14px]">
                  {{ t('statistics.student.active') }}
                </div>
                <div class="text-[24px] text-[#222222] font-bold">
                  {{ company.totalActiveNum }}
                </div>
              </div>
              <div class="mr-5">
                <div class="text-[#727984] text-[14px]">
                  {{ t('statistics.student.inactive') }}
                </div>
                <div class="text-[24px] text-[#222222] font-bold">
                  {{ company.totalInactiveNum }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- <el-row :gutter="20" class="px-5 inline-block">
        <el-col v-for="(company, index) in staticsData" :key="index" :span="6" class="mb-3">
          <div class="top_item_bg" :class="company.bgStyle">
            <div class="item_title">
              <span class="text-[16px]">{{ `${company.companyName} Students` }}</span>
              <span class="text-[22px]">{{ company.totalNum }}</span>
            </div>
            <div class="item_content">
              <div class="ml-5">
                <div class="text-[#727984] text-[14px]">Active</div>
                <div class="text-[24px] text-[#222222] font-bold">
                  {{ company.totalActiveNum }}
                </div>
              </div>
              <div class="mr-5">
                <div class="text-[#727984] text-[14px]">Inactive</div>
                <div class="text-[24px] text-[#222222] font-bold">
                  {{ company.totalInactiveNum }}
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row> -->
    </el-scrollbar>
    <div>
      <!-- 查询条件 -->
      <ContentWrap>
        <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
          <el-form-item :label="t('examMgt.paper.name')">
            <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable class="!w-180px" @keydown.enter="handleSearch" />
          </el-form-item>
          <el-form-item :label="t('statistics.student.badge')">
            <el-input v-model="queryParams.badge" :placeholder="t('common.inputText')" clearable class="!w-180px" @keydown.enter="handleSearch" />
          </el-form-item>
          <el-form-item label="Company" prop="companyId">
            <el-tree-select
              v-model="queryParams.companyId"
              :data="companyList"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              value-key="id"
              :placeholder="t('sys.user.companyPH')"
              check-strictly
              clearable
              filterable
              class="!w-240px"
              @node-click="handleCompanyClick"
            />
          </el-form-item>
          <el-form-item :label="t('sys.user.department')" prop="deptId">
            <el-tree-select
              v-model="queryParams.deptId"
              :data="departOptions"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              value-key="id"
              :placeholder="t('sys.user.departmentPH')"
              check-strictly
              clearable
              filterable
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item :label="t('sys.user.email')" prop="email">
            <el-input
              v-model="queryParams.email"
              :placeholder="t('sys.user.emailPH')"
              clearable
              class="!w-180px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon class="mr-5px" icon="ep:search" />
              {{ t('action.search') }}
            </el-button>
            <el-button type="default" @click="handleReset">
              <Icon class="mr-5px" icon="ep:refresh" />
              {{ t('action.reset') }}
            </el-button>
            <el-button plain type="primary" @click="handleExport">
              <Icon class="mr-5px" icon="ep:download" />
              {{ t('action.export') }}
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table v-loading="loading" :data="tableData">
          <el-table-column fixed="left" prop="nickName" :label="t('examMgt.paper.name')" min-width="240" />
          <el-table-column prop="badgeNo" :label="t('learningCenter.boarding.badgeNumber')" min-width="240" />
          <el-table-column prop="email" :label="t('learningCenter.course.email')" min-width="240" />
          <el-table-column prop="company" :label="t('learningCenter.course.company')" min-width="240" />
          <el-table-column prop="department" :label="t('learningCenter.course.deptName')" min-width="240" />
          <el-table-column prop="section" :label="t('learningCenter.course.section')" min-width="240" />
          <el-table-column prop="position" :label="t('learningCenter.course.position')" min-width="240" />
          <el-table-column prop="mlcTotal" label="MLC Class" min-width="240" />
          <el-table-column prop="liveTotal" label="Live stream" min-width="240" />
          <el-table-column prop="internalTotal" label="Internal Training" min-width="240" />
          <el-table-column prop="externalTotal" label="External Training" min-width="240" />
          <el-table-column prop="requiredCourseNum" :label="t('statistics.student.mandatoryCourse')" min-width="160" />
          <el-table-column prop="electiveCourseNum" :label="t('statistics.student.electiveCourse')" min-width="140" />
          <el-table-column prop="examNum" :label="t('learningCenter.course.exam')" min-width="140" />
          <el-table-column prop="onboardingNum" :label="t('learningCenter.boarding.boarding')"  min-width="140" />
          <el-table-column prop="companyPolicyNum" :label="t('learningCenter.companyPolicy.companyPolicy')" min-width="140" />
          <el-table-column fixed="right" :label="t('global.action')" width="100" align="center">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row)">
                <Icon class="mr-5px" icon="ep:view" />
                {{ t('action.detail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
      </ContentWrap>
    </div>
  </div>
</template>

<style scoped lang="scss">
.scrollbar-wrapper {
  overflow-x: auto; /* 横向滚动条 */
  white-space: nowrap !important; /* 防止行内元素换行 */
}
.top_item_bg {
  @apply h-full flex flex-col justify-between px-[10px] pb-3 rounded-[10px];

  .item_title {
    @apply py-4 flex justify-between items-center text-white font-bold;
  }

  .item_content {
    @apply flex justify-between items-center h-[90px] rounded-[10px] bg-gradient-to-b from-[#E6EFFD] to-[#FFFFFF];

    .item_number {
      @apply mb-1 text-[24px] text-white font-bold;
    }
    .item_desc {
      @apply text-[14px] text-white font-medium;
    }
  }
}
</style>
