<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="Title" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="Please input"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Type" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INTERNAL_TRAINING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset</el-button>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          Export
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="Course Title EN" align="center" prop="title" min-width="180px" />
      <el-table-column label="Course Title AR" align="center" prop="titleAr" min-width="180px" />
      <el-table-column label="Type" align="center" prop="type" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INTERNAL_TRAINING_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="Place" align="center" prop="place" min-width="180px" />
      <el-table-column label="Company" align="center" prop="implementingCompany" min-width="180px" />
      <el-table-column label="Course Duration" align="center" prop="duration" min-width="180px">
        <template #default="scope">
          {{ scope.row.duration }} days
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InternalApi, InternalRespVO } from '@/api/academy/outsourced/internal'
import { InternalTrainingApi, InternalTrainingReq } from '@/api/statistics/internalTraining'
import { formatDateArray } from "@/utils/formatDate"
/** 国内培训(统计) 列表 */
defineOptions({ name: 'StudentInternalTraining' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const loading = ref(true) // 列表的加载中
const list = ref<InternalTrainingReq[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  type: undefined,
  loginUserId: route.query.userId,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const placeOptions = ref([])
const companyOptions = ref([])
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InternalTrainingApi.getInternalPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const getAllList = async () => {
  const data = await InternalApi.getInternalPage({pageNo: 1, pageSize: -1})
  placeOptions.value = Array.from(new Set(data.list?.map((item: InternalRespVO) => item.place)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
  companyOptions.value = Array.from(new Set(data.list?.map((item: InternalRespVO) => item.implementingCompany)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InternalTrainingApi.exportInternal(queryParams)
    download.excel(data, 'Internal-Training.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


/** 初始化 **/
onMounted(() => {
  getList()
  getAllList()
})
</script>
