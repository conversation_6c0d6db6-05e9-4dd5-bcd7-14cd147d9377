<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="Title" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="Please input"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset</el-button>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          Export
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="Course Title EN" align="center" prop="title" min-width="180px" />
      <el-table-column label="Course Title AR" align="center" prop="titleAr" min-width="180px" />
      <el-table-column label="The receiving Country" align="center" prop="receivingCountry" min-width="180px" />
      <el-table-column label="Travel Date" align="center" prop="travelDate" min-width="180px">
        <template #default="scope">
          {{ formatDateArray(scope.row.travelDate )}}
        </template>
      </el-table-column>
      <el-table-column label="Return Date" align="center" prop="returnDate" min-width="180px">
        <template #default="scope">
          {{ formatDateArray(scope.row.returnDate )}}
        </template>
      </el-table-column>
      <el-table-column label="Admin No." align="center" prop="adminNo" min-width="180px" />
      <el-table-column label="Cost Bearer" align="center" prop="costBearer" min-width="180px" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

</template>

<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { ExternalApi, ExternalRespVO } from '@/api/academy/outsourced/external'
import { ExternalTrainingApi, } from '@/api/statistics/externalTraining'
import { formatDateArray } from "@/utils/formatDate"
import download from "@/utils/download"
/** 国外培训(统计) 列表 */
defineOptions({ name: 'StudentExternalTraining' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const loading = ref(false) // 列表的加载中
const list = ref<ExternalRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  loginUserId: route.query.userId,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const countryOptions = ref([])
const bearerOptions = ref([])
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ExternalTrainingApi.getExternalPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const getAllList = async () => {
  const data = await ExternalApi.getExternalPage({pageNo: 1, pageSize: -1})
  countryOptions.value = Array.from(new Set(data.list?.map((item: ExternalRespVO) => item.receivingCountry)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
  bearerOptions.value = Array.from(new Set(data.list?.map((item: ExternalRespVO) => item.costBearer)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ExternalTrainingApi.exportExternal(queryParams)
    download.excel(data, 'External-Training.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}



/** 初始化 **/
onMounted(() => {
  getList()
  getAllList()
})
</script>
