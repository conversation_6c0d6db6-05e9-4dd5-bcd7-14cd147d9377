<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="Title" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="Please input"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Type" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset</el-button>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          Export
        </el-button>
      </el-form-item>
    </el-form>

  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="Course Title" align="center" prop="courseName" min-width="180px" />
      <el-table-column label="Class Title" align="center" prop="name" min-width="180px" />
      <el-table-column label="Class Type" align="center" prop="type" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="Status" align="center" prop="status" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {QueryTypeEnum, StatisticsClassApi, StatisticsClassResp} from '@/api/statistics/class'
import download from "@/utils/download"
/** 课堂(统计) 列表 */
defineOptions({ name: 'StudentClass' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()
const route = useRoute()
const loading = ref(false) // 列表的加载中
const exportLoading = ref(false)
const list = ref<StatisticsClassResp[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  courseName: undefined,
  type: undefined,
  queryType: QueryTypeEnum.MY_RECORDS,
  status: undefined,
  loginUserId: route.query.userId
})

const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await StatisticsClassApi.getStatisticsClassPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await StatisticsClassApi.exportStatisticsClass(queryParams)
    download.excel(data, 'MLC Training.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
