<script setup lang="ts" name="StudentExam">
import type { ComponentInternalInstance } from 'vue'
import ScoreDetail from '../../../exam/components/ScoreDetail.vue'
import { listExamOfStudent } from '@/api/topicMgt/exam'
import { listTopicAll } from '@/api/category/topic'

interface DataItem {
  assignmentDetailId: number
  name: string
  subject: string
  onSchedule: boolean
  status: number // （0：未考试，1：未通过，2：已通过）
  topScore: number
  operator: string
}
const { t } = useI18n()
const route = useRoute()
const RefScoreDetail = ref()
const isVisible = ref<boolean>(false)
const queryRef = ref()
const loading = ref(false)
const statusList = ref([t('examMgt.exam.notAttend'), t('examMgt.exam.failed'), t('examMgt.exam.pass')])
const total = ref(0)
const tableData = ref<Array<DataItem>>([])
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject'),
  },
])
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    onSchedule: undefined,
  },
})
const { queryParams } = toRefs(data)
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    onSchedule: undefined,
  }
  queryRef.value?.resetFields()
}
// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
// 获取考试列表
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.userId = route.query.userId
    const res = await listExamOfStudent(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// 查看考试详情
const handleView = (row: any) => {
  RefScoreDetail.value.handleOpen(row.assignmentDetailId)
}
// 查看答题详情
const handleAnswerDetail = (val: any) => {
  // console.log(val)
}

onMounted(() => {
  getList()
  getSubjectData()
})
onActivated(() => {
  reset()
  getList()
  getSubjectData()
})
</script>

<template>
  <div>
    <div>
      <!-- 查询条件 -->
      <ContentWrap>
        <el-form ref="queryRef" :inline="true" :model="queryParams" label-width="68px" class="-mb-15px">
          <el-form-item :label="t('category.journey.title')">
            <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable class="!w-180px" @keydown.enter="handleSearch" />
          </el-form-item>
          <el-form-item :label="t('common.status')">
            <el-select v-model="queryParams.status" clearable class="!w-180px">
              <el-option :label="t('examMgt.exam.notAttend')" value="0" />
              <el-option :label="t('examMgt.exam.failed')" value="1" />
              <el-option :label="t('examMgt.exam.pass')" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon class="mr-5px" icon="ep:search" />
              {{ t('action.search') }}
            </el-button>
            <el-button type="default" @click="handleReset">
              <Icon class="mr-5px" icon="ep:refresh" />
              {{ t('action.reset') }}
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table v-loading="loading" :data="tableData">
          <el-table-column fixed="left" prop="name" :label="t('examMgt.exam.examName')" min-width="240" />
          <el-table-column prop="subject" :label="t('category.topic.subjectName')" min-width="240">
            <template #default="{ row }">
              <div>
                {{ row.subject ? row.subject : t('common.noSubject') }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="t('common.status')" min-width="150">
            <template #default="{ row }">
              <div>
                {{ statusList[row.status] }}
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="On schedule" min-width="150">
            <template #default="{ row }">
              <div>
                {{ row.onSchedule !== null ? (row.onSchedule === true ? 'YES' : 'NO') : '' }}
              </div>
            </template>
          </el-table-column> -->
          <el-table-column prop="topScore" :label="t('examMgt.exam.topScore')" min-width="120" />
          <el-table-column prop="operator" :label="t('log.operaLog.operator')" min-width="180" />
          <el-table-column fixed="right" :label="t('global.action')" min-width="140" align="center">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row)">
                <Icon icon="ep:view" />
                {{ t('statistics.exam.scoreDetails') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
      </ContentWrap>
    </div>
    <ScoreDetail ref="RefScoreDetail" v-model="isVisible" :title-name="t('statistics.exam.scoreDetails')" @answer-details="handleAnswerDetail" />
  </div>
</template>

<style scoped lang="scss">
.top_item_bg {
  @apply flex items-center h-[100px] p-5 rounded-[10px];

  .statistic_icon_bg {
    @apply flex items-center justify-center mr-[20px] w-[66px] h-[66px] rounded-[33px] bg-white bg-opacity-30;

    .statistic_icon {
      @apply w-[32px] h-[32px];
    }
  }

  .item_content {
    @apply w-[110px];

    .item_number {
      @apply mb-1 text-[24px] text-white font-bold;
    }
    .item_desc {
      @apply text-[14px] text-white font-medium;
    }
  }
}
</style>
