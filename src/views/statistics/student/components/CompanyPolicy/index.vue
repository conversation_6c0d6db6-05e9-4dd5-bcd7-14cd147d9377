<script setup lang="ts" name="StudentCompanyPloicy">
import type { ComponentInternalInstance } from 'vue'
import { listCompanyPolicyOfStudent } from '@/api/topicMgt/statistics'
import { listTopicAll } from '@/api/category/topic'
import {useI18n} from "vue-i18n";
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

interface DataItem {
  empName: string
  categoryId: number
  categoryName: string
  status: number // （0：Pending，1：In Progress，3：Completed）
  sort: number
  createBy: string
  createId: number
  createTime: string
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const route = useRoute()
const queryRef = ref()
const loading = ref(false)
const total = ref(0)
const tableData = ref<Array<DataItem>>([])
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject'),
  },
])
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    empName: undefined,
    categoryId: undefined,
    ackStatus: undefined,
  },
})
const { queryParams } = toRefs(data)
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    empName: undefined,
    categoryId: undefined,
    ackStatus: undefined,
  }
  queryRef.value?.resetFields()
}
// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
// 获取Company Policy列表
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.userId = route.query.userId
    const res = await listCompanyPolicyOfStudent(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getList()
  getSubjectData()
})
onActivated(() => {
  reset()
  getList()
  getSubjectData()
})
</script>

<template>
  <div>
    <div>
      <!-- 查询条件 -->
      <ContentWrap>
        <el-form ref="queryRef" :inline="true" :model="queryParams" label-width="68px" class="-mb-15px">
          <el-form-item :label="t('category.journey.title')">
            <el-input v-model="queryParams.empName" :placeholder="t('common.inputText')" clearable class="!w-180px" @keydown.enter="handleSearch" />
          </el-form-item>
          <!-- <el-form-item label="Subject">
            <el-select v-model="queryParams.classifyId" placeholder="Please choose" clearable style="width: 240px">
              <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item> -->
          <el-form-item :label="t('common.status')">
            <el-select v-model="queryParams.ackStatus" :placeholder="t('common.chooseText')" clearable class="!w-180px">
              <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_STUDY_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon class="mr-5px" icon="ep:search" />
              {{ t('action.search') }}
            </el-button>
            <el-button type="default" @click="handleReset">
              <Icon class="mr-5px" icon="ep:refresh" />
              {{ t('action.reset') }}
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table v-loading="loading" :data="tableData">
          <el-table-column fixed="left" prop="empName" :label="t('learningCenter.boarding.title')" min-width="320" />
          <!-- <el-table-column prop="subject" label="Subject" min-width="240">
            <template #default="{ row }">
              <div>
                {{ row.subject ? row.subject : 'No Subject' }}
              </div>
            </template>
          </el-table-column> -->
          <el-table-column prop="ackStatus" :label="t('common.status')" min-width="150">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.COURSE_STUDY_STATUS" :value="row.ackStatus" />
            </template>
          </el-table-column>
          <el-table-column prop="createBy" :label="t('category.journey.creator')" min-width="180" />
          <el-table-column prop="createTime" :label="t('category.journey.creationTime')" min-width="180" />
        </el-table>
        <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
      </ContentWrap>

    </div>
  </div>
</template>

<style scoped lang="scss"></style>
