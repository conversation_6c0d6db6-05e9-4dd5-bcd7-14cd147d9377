<script setup lang="ts" name="StudentStatisticsDetail">
import CourseList from '../components/Course/index.vue'
import ExamList from '../components/Exam/index.vue'
import OnboardingList from '../components/Onboarding/index.vue'
import CompanyPolicyList from '../components/CompanyPolicy/index.vue'
import MlcTrainingList from '../components/Training/index.vue'
import InternalTrainingList from '../components/Internal/index.vue'
import ExternalTrainingList from '../components/External/index.vue'
const { t } = useI18n()
const route = useRoute()

const activeName = ref('course')
const handleClick = (targetName: string) => {
  // console.log(targetName)
}
</script>

<template>
  <div class="app-container">
    <!-- 个人信息 -->
    <div class="top_item_bg">
      <div class="top_name">
        {{ route.query.userName }}
      </div>
      <el-row :gutter="10" justify="space-evenly" class="px-[10px] pt-4">
        <el-col :span="5">
          <div class="top_content">
            <div class="top_content_title">
              {{ t('learningCenter.course.badgeNo') }}
            </div>
            <div>
              {{ route.query.badgeNumber }}
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="top_content">
            <div class="top_content_title">
              {{ t('learningCenter.course.email') }}
            </div>
            <div>
              {{ route.query.email }}
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="top_content">
            <div class="top_content_title">
              {{ t('learningCenter.course.deptName') }}
            </div>
            <div>
              {{ route.query.deptName }}
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="top_content">
            <div class="top_content_title">
              {{ t('learningCenter.course.section') }}
            </div>
            <div>
              {{ route.query.sectNames }}
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="top_content">
            <div class="top_content_title">
              {{ t('learningCenter.course.position') }}
            </div>
            <div>
              {{ route.query.postNames }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="mt-4">
      <el-tabs v-model="activeName" @tab-change="handleClick">
        <el-tab-pane :label="t('learningCenter.course.course')" name="course">
          <CourseList />
        </el-tab-pane>
        <el-tab-pane :label="t('learningCenter.course.exam')" name="exam">
          <ExamList />
        </el-tab-pane>
        <el-tab-pane :label="t('learningCenter.boarding.boarding')" name="onboarding">
          <OnboardingList />
        </el-tab-pane>
        <el-tab-pane :label="t('learningCenter.companyPolicy.companyPolicy')" name="companyPolicy">
          <CompanyPolicyList />
        </el-tab-pane>
        <el-tab-pane label="MLC Training" name="mlcTraining">
          <MlcTrainingList />
        </el-tab-pane>
        <el-tab-pane label="Internal Training" name="internalTraining">
          <InternalTrainingList />
        </el-tab-pane>
        <el-tab-pane label="External Training" name="ExternalTraining">
          <ExternalTrainingList />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style scoped lang="scss">
.top_item_bg {
  background: linear-gradient(to right, #32A974, #078049); /* bg-gradient-to-r from-[#32A974] to-[#078049] */
  border-radius: 10px; /* rounded-[10px] */
  padding-bottom: 1rem; /* pb-4 */
}

.top_item_bg .el-col-5 {
  max-width: 20%;
  flex: 0 0 20%;
}

.top_item_bg .top_name {
  color: white;
  font-size: 18px;
  font-weight: bold;
  padding-top: 25px;
  padding-left: 30px;
}

.top_item_bg .top_content {
  height: 100%;
  background-color: white;
  border-radius: 10px;
  padding: 1rem; /* p-4 */
  color: #222222;
  font-size: 16px;
  font-weight: bold;
}

.top_item_bg .top_content .top_content_title {
  font-size: 14px;
  color: #727984;
  padding-bottom: 0.25rem; /* pb-1 */
}
</style>
