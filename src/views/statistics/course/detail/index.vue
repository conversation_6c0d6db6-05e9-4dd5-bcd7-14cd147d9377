<script setup lang="ts" name="CourseStatisticsDetail">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import PicAreaCard from '@/views/statistics/cmp/components/PicAreaCard.vue'
import { detailCountData, detailList, CourseInfoRespVO } from '@/api/statistics/course'
import { ChapterStatus, CourseChapterType, SendStatus } from '@/enums/chapter'

import { ChapterTypeIcons as Icons } from '@/views/learning-center/course/course-form/scripts/ChapterTypeIcons'
import {exportCourseInfo, getCourse, getEmployeeStatistics } from '@/api/topicMgt/elearning'
import { formatImgUrl } from '@/utils/index'
import download from "@/utils/download"
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { useTagsViewStore } from "@/store/modules/tagsView"

interface ResData {
  userId: number
  assignmentDetailId: number
  studentName: string
  badgeNo: string | null
  email: string
  department: string
  section: string | null
  position: string
  type: any
  onSchedule: boolean
  status: number
  star: string | null
  operator: string
  company: string | null
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { push, currentRoute } = useRouter() // 路由
const { delView } = useTagsViewStore() // 视图操作
const countData = ref()
const loading = ref(false)
const tableData = ref<Array<CourseInfoRespVO>>([])
const total = ref(0)
const queryRef = ref()
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    badgeNo: undefined,
    studentName: undefined,
    email: undefined,
    status: undefined,
    type: undefined,
  },
})
const { queryParams } = toRefs(data)
const route = useRoute()
const statusList = [
  { label: t('examMgt.exam.notStarted'), value: ChapterStatus.NotStart, id: '2' },
  { label: t('examMgt.exam.inProcess'), value: ChapterStatus.InProgress, id: '3' },
  { label: t('examMgt.exam.failed'), value: ChapterStatus.Failed, id: '4' },
  { label: t('statistics.course.completed'), value: ChapterStatus.Completed, id: '5' },
]
const EmailStatus: any = {
  [SendStatus.Sending]: t('statistics.course.sending'),
  [SendStatus.Succeeded]: t('statistics.course.sendSuccess'),
  [SendStatus.Failed]: t('statistics.course.sendFail'),
}
/** 顶部的统计样式及数值数据 */
const picCardData = ref()
const selectedUser = ref<ResData>()
const show = ref(false)
const userOrgInfo = computed(() => {
  return [
    {
      icon: 'StatisticsDepartment',
      text: selectedUser.value?.department,
    },
    {
      icon: 'StatisticsSection',
      text: selectedUser.value?.section,
    },
    {
      icon: 'StatisticsPosition',
      text: selectedUser.value?.position,
    },
    {
      icon: 'StatisticsBadge',
      text: selectedUser.value?.badgeNo,
    },
    {
      icon: 'StatisticsEmail',
      text: selectedUser.value?.email,
    },
  ]
})
const statisticsData = ref()
const titleData = ref()
const imgUrl = ref()
const handleViewDetail = async (row: any) => {
  selectedUser.value = row
  show.value = true
  statisticsData.value = await getEmployeeStatistics(queryParams.value.id, row.userId)
}
const handleBack = () => {
  delView(unref(currentRoute))
  push('/statistics/course-statistics')
}
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    badgeNo: undefined,
    studentName: undefined,
    email: undefined,
    status: undefined,
    type: undefined,
  }
  queryRef.value?.resetFields()
}
// }
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.id = route.params.id
    const res = await detailList(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
const getCountData = async () => {
  countData.value = await detailCountData({ id: route.params.id })
  picCardData.value = [
    {
      bg: 'bg-gradient-to-r from-[#6EAA32] to-[#407C03]',
      cBg: 'bg-[#3B88FF]',
      icon: 'OnUser',
      text: 'On Schedule Users',
      number: countData.value?.totalNum ? countData.value?.totalNum : 0,
    },
    {
      bg: 'bg-gradient-to-r from-[#31A873] to-[#067E48]',
      cBg: 'bg-[#752FDD]',
      icon: 'OffUser',
      text: 'Off Schedule Users',
      number: countData.value?.overtimeNum ? countData.value?.overtimeNum : 0,
    },
  ]
}
/** 顶部title和img数据 */
const getCoverTitle = async () => {
  const data = await getCourse(route.params.id)
  titleData.value = data
  if (titleData.value.cover.includes('https')) {
    imgUrl.value = data.cover
  }
  else {
    imgUrl.value = formatImgUrl(titleData?.value.cover)
  }
}
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportCourseInfo(queryParams.value)
    download.excel(data, `Course-${titleData?.value.name}-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}
onMounted(() => {
  getList()
  getCountData()
  getCoverTitle()
})
</script>

<template>
  <div class="app-container">
    <!-- 顶部图片区域 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <!-- <div class="flex gap-5 mr-16">
        <img src="../../../../assets/images/commonImg.png" class="w-[154px] h-[90px] row-span-2">
        <div class="pt-2.5">
          <span class="text-[#23293A] text-xl line-clamp-1 break-all">Welcome to Majnoon Oilfield</span>
          <div class="flex gap-1.5 mt-6">
            <div class="w-5 h-5 bg-primary rounded-[4px] flex items-center justify-center">
              <svg-icon icon-class="CourseTopic" class="text-xs" />
            </div>
          </div>
        </div>
      </div> -->
      <div class="flex gap-5 mr-16">
        <img v-if="imgUrl" :src="imgUrl" class="w-[154px] h-[90px] row-span-2" />
        <img v-else src="../../../../assets/images/commonImg.png" class="w-[154px] h-[90px] row-span-2" />
        <div class="pt-2.5">
          <span class="text-[#23293A] text-xl line-clamp-1 break-all">{{ titleData?.name || t('statistics.course.none') }}</span>
          <div v-if="titleData?.topic" class="flex gap-1.5 mt-6">
            <div class="w-5 h-5 rounded-[4px] flex items-center">
              <svg-icon icon-class="CourseTopic" class="text-xs" />
              <el-tag type="warning">
                {{ titleData?.topic || t('statistics.course.none') }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      <el-button class="ms-auto flex-shrink-0" type="primary" @click="handleBack">
        {{ t('action.back') }}
      </el-button>
    </div>
    <!-- 统计内容 -->
    <div class="mt-[21px] mb-[25px]">
      <PicAreaCard :data="picCardData" />
    </div>
    <!-- Search -->
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
        <el-form-item :label="t('examMgt.paper.name')" prop="studentName">
          <el-input v-model="queryParams.studentName" :placeholder="t('common.inputText')" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.badgeNo')" prop="badgeNo">
          <el-input v-model="queryParams.badgeNo" :placeholder="t('common.inputText')" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.email')" prop="email">
          <el-input v-model="queryParams.email" :placeholder="t('common.inputText')" clearable @keydown.enter="handleSearch" />
        </el-form-item>
        <!-- <el-form-item label="Status" prop="status">
          <el-select v-model="queryParams.status" placeholder="Select" clearable style="width: 200px">
            <el-option v-for="item in statusList" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->
        <el-form-item :label="t('learningCenter.course.type')" prop="type">
          <el-select v-model="queryParams.type" class="!w-36" clearable>
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button plain type="primary" @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />
            {{ t('action.export') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <!-- Table -->
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData" :show-overflow-tooltip="true">
        <el-table-column :label="t('examMgt.paper.name')" prop="studentName" align="left" :width="280" fixed="left" />
        <el-table-column :label="t('learningCenter.course.badgeNo')" prop="badgeNo" :width="180" />
        <el-table-column :label="t('learningCenter.course.email')" prop="email" :width="280" />
        <el-table-column :label="t('global.company')" prop="company" :width="280" />
        <el-table-column :label="t('learningCenter.course.deptName')" prop="department" :width="180" />
        <el-table-column :label="t('learningCenter.course.section')" prop="section" :width="180" />
        <el-table-column :label="t('learningCenter.course.position')" prop="position" :width="180" />
        <el-table-column :label="t('learningCenter.course.type')" prop="type" :width="180">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.COURSE_TYPE" :value="row.type" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="Score" prop="quizScore" :width="180" /> -->
        <el-table-column :label="t('learningCenter.course.star')" prop="star" :width="180" />
        <el-table-column :label="t('learningCenter.course.operator')" prop="operator" show-overflow-tooltip :width="180" />
        <el-table-column :label="t('learningCenter.course.score')" prop="score" show-overflow-tooltip align="center" :width="90">
          <template #default="{ row }">
            {{ row.score === null ? '--' : row.score}}
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.exam.examTimes')" prop="examTimes" show-overflow-tooltip align="center" :width="110">
          <template #default="{ row }">
            {{ row.examTimes === null ? '--' : row.examTimes}}
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.exam.passFail')" prop="examStatus" show-overflow-tooltip align="center" :width="90">
          <template #default="{ row }">
            {{ row.examStatus === null ? '--' : row.examStatus}}
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.sendingStatus')" align="center" prop="operator" fixed="right" width="120px">
          <template #default="{ row }">
            <svg-icon v-if="row.type === '1'" :icon-class="EmailStatus[row.messageStatus ?? 0]" class="text-[22px]" />
            <svg-icon v-if="row.type === '0'" icon-class="SendDisabled" class="text-[22px]" />
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.status')" prop="status" :width="120" fixed="right">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.COURSE_STUDY_STATUS" :value="row.status" />
          </template>
        </el-table-column>

        <el-table-column :width="100" :label="t('global.action')" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link icon="View" @click="handleViewDetail(row)">
              <Icon icon="ep:view" />
              {{ t('sys.dept.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>
    <!-- 抽屉展示学员统计信息 -->
    <el-drawer v-model="show" size="80%">
      <div>
        <!-- 顶部用户信息 -->
        <div class="flex items-center gap-5">
          <span class="text-[#233A35] text-xl">{{ selectedUser?.studentName }}</span>
          <dict-tag :type="DICT_TYPE.COURSE_TYPE" :value="selectedUser?.type" class="w-24 h-8 text-sm text-primary bg-[#DEF4EA] flex items-center justify-center border border-primary rounded-[2px]" />
        </div>
        <!-- 组织结构信息 -->
        <div class="flex gap-5 my-5">
          <div v-for="(item, index) in userOrgInfo" :key="index" class="border border-[#E2E2E2] flex-1 py-4 px-5 rounded-md flex items-center gap-2">
            <div class="w-[30px] h-[30px] rounded-[4px] bg-primary flex items-center justify-center shrink-0">
              <svg-icon :icon-class="item.icon" class="text-base text-white" />
            </div>
            <span class="text-sm line-clamp-1" :title="item.text">{{ item.text }}</span>
          </div>
        </div>
        <!-- 个人统计信息 -->
        <div class="border border-[#E2E2E2] py-5 px-7">
          <span class="text-[#222222] text-xl"> {{ t('learningCenter.course.courseCatalogue') }} </span>
          <div class="w-[125px] h-9 border-[2px] border-primary bg-primary rounded-[4px] px-4 leading-9 my-5">
            <span class="text-white font-bold text-xl">{{ statisticsData?.length }}</span>
            <span class="text-white text-sm ms-0.5">{{ t('learningCenter.task.totalTasks') }}</span>
          </div>
          <!-- 章节信息 -->
          <div>
            <template v-for="(item, index) in statisticsData" :key="index">
              <div class="flex mt-4">
                <!-- 左侧类型图标 -->
                <div class="relative w-[40px] h-[40px]">
                  <svg-icon icon-class="ChapterType" class="text-[40px]" />
                  <svg-icon :icon-class="Icons[item.type as CourseChapterType ]" class="text-base !absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
                </div>
                <!-- 右侧内容 -->
                <div class="flex-1 flex items-center ms-4">
                  <span class="text-sm text-[#233A35]">{{ item.name }}</span>
                  <span v-if="item.type === CourseChapterType.Scorm" class="bg-[#F6E9C5] text-sm text-[#BF9121] px-1 ml-3 rounded-sm">{{ t('statistics.course.scorm') }}</span>

                  <div class="ms-auto flex items-center">
                    <!-- Scrom分数 -->
                    <div v-if="(item.status === ChapterStatus.Completed && item.type === CourseChapterType.Scorm)">
                      <div v-if="item.score" class="ms-auto flex items-center mr-3">
                        <div class="bg-gradient-to-b from-[#0EAFA2] to-[#007943] text-[#ffffff] px-1">
                          <svg-icon icon-class="score" />
                        </div>
                        <div class="bg-[#DEF4EA] text-[#007943] text-sm  py-0.5">
                          <span class="px-1 py-1">{{ t('examMgt.exam.score') }}{{ item.score }}</span>
                        </div>
                      </div>
                      <div v-else class="ms-auto flex items-center mr-3">
                        <div class="bg-[#797979] text-[#F2F2F2] px-1">
                          <svg-icon icon-class="score" />
                        </div>
                        <div class="bg-[#F2F2F2] text-[#797979] text-sm  py-0.5">
                          <span class="px-1 py-1">{{ t('examMgt.exam.score') }}{{ item.score ? item.score : '--' }}</span>
                        </div>
                      </div>
                    </div>
                    <!-- Exam 分数 -->
                    <div v-if="(item.type === CourseChapterType.Exam)">
                      <div v-if="item.status === ChapterStatus.Completed" class="ms-auto flex items-center mr-3">
                        <div class="bg-gradient-to-b from-[#0EAFA2] to-[#007943] text-[#ffffff] px-1">
                          <svg-icon icon-class="score" />
                        </div>
                        <div class="bg-[#DEF4EA] text-[#007943] text-sm  py-0.5">
                          <span class="px-1 py-1">{{ t('examMgt.exam.score') }}{{ item.score }}</span>
                        </div>
                      </div>
                      <div v-else-if="item.status === ChapterStatus.Failed" class="ms-auto flex items-center mr-3">
                        <div class="bg-[#F56C6C] text-[#ffffff] px-1">
                          <svg-icon icon-class="score" />
                        </div>
                        <div class="bg-[#FFE7E7] text-[#F56C6C] text-sm  py-0.5">
                          <span class="px-1 py-1">{{ t('examMgt.exam.score') }}{{ item.score }}</span>
                        </div>
                      </div>
                      <div v-else class="ms-auto flex items-center mr-3">
                        <div class="bg-[#797979] text-[#F2F2F2] px-1">
                          <svg-icon icon-class="score" />
                        </div>
                        <div class="bg-[#F2F2F2] text-[#797979] text-sm  py-0.5">
                          <span class="px-1 py-1">{{ t('examMgt.exam.score') }}{{ item.score ? item.score : '--' }}</span>
                        </div>
                      </div>
                    </div>
                    <!-- 课程状态以及图标 -->
                    <div class="ms-auto flex items-center">
                      <!-- 课程状态为完成或者未完成 -->
                      <div v-if="[null, ChapterStatus.NotStart].includes(item.status) || item.status === ChapterStatus.Completed" class="w-6 h-6 rounded-full" :class="[null, ChapterStatus.NotStart].includes(item.status) ? 'border border-[#AEC2DD]' : 'bg-primary'"></div>
                      <!-- 课程为进行中  -->
                      <svg-icon v-else icon-class="DotUnfilled" class="text-2xl text-[#AEC2DD]" />
                      <!-- 资源总时间（暂无） -->
                      <!-- <span>123</span> -->
                      <!-- <el-button type="primary" class="!w-20 !h-6 ms-2.5">
                          {{ btnText[(item.status || ChapterStatus.NotStart) as ChapterStatus] }}
                        </el-button> -->
                    </div>
                  </div>
                </div>
              </div>
              <!-- 分割线 -->
              <div class="h-[1px] bg-[#DDDDDD] mt-4 ms-14"></div>
            </template>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style scoped lang="scss"></style>
