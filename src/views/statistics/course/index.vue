<script setup lang="ts" name="CourseStatistics">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
// import lineChart from './components/lineChart.vue'
import barChart from './components/barChart.vue'
import PicAreaCard from '@/views/statistics/cmp/components/PicAreaCard.vue'
import SubjectSelect from '@/components/SubjectSelect/index.vue'
import router from '@/router'
import { chartList, countTableData, tableList, exportCourse, CourseTableRespVO } from '@/api/statistics/course'
import { ShelfType } from '@/enums/course'
import { listTopicAll } from '@/api/category/topic'
import { useI18n } from "vue-i18n"
import download from "@/utils/download"

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const countData = ref()
const loading = ref(false)
const tableData = ref<Array<CourseTableRespVO>>([])
const total = ref(0)
const queryRef = ref()
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    topic: undefined,
  },
})
const { queryParams } = toRefs(data)
const ackList = [
  {
    value: ShelfType['On shelf'],
    label: t('action.onShelf'),
  },
  {
    value: ShelfType['Off shelf'],
    label: t('action.offShelf'),
  },
]
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject')
  },
])
/** 顶部的统计样式及数值数据 */
const picCardData = ref()
const chartData = ref()
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    topic: undefined,
  }
  queryRef.value?.resetFields()
}
// 获取subject列表
const getSubjectData = async () => {
  optionsSubject.value = await listTopicAll()
}
/** CompanyPolicy的详情 */
const handleDetail = (row: any) => {
  router.push({ name: 'CourseStatisticsDetail', params: { id: row.id } })
}

/** 获取顶部的统计信息 */
const getCountData = async () => {
  countData.value = await countTableData('')
  picCardData.value = [
    {
      bg: 'bg-gradient-to-r from-[#6FAB34] to-[#3F7B02]',
      cBg: 'bg-[#3B88FF]',
      icon: 'Total',
      text: 'Total Courses',
      number: countData.value?.totalNum ? countData.value?.totalNum : 0,
    },
    {
      bg: 'bg-gradient-to-r from-[#33AA75] to-[#08804A]',
      cBg: 'bg-[#752FDD]',
      icon: 'On',
      text: 'Courses On Shelf',
      number: countData.value?.putOnNum ? countData.value?.putOnNum : 0,
    },
    {
      bg: 'bg-gradient-to-r from-[#2D85A5] to-[#04557D]',
      cBg: 'bg-[#45CC7E]',
      icon: 'Off',
      text: 'Courses Off Shelf',
      number: countData.value?.putOffNum ? countData.value?.putOffNum : 0,
    },
  ]
}
/** 获取Table的数据 */
const getList = async () => {
  loading.value = true
  try {
    const res = await tableList(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
/** 获取折现图的数据 */
const getChartData = async () => {
  chartData.value = await chartList()
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportCourse(queryParams.value)
    download.excel(data, `Course statistics-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}
onMounted(() => {
  getList()
  getCountData()
  getChartData()
  getSubjectData()
})

</script>

<template>
  <div class="app-container">
    <!-- 顶部统计内容 -->
    <ContentWrap>
      <PicAreaCard :data="picCardData" />
    </ContentWrap>
    <!-- 统计图 -->
    <!-- <lineChart :data="chartData" /> -->
    <ContentWrap>
      <barChart :data="chartData" />
    </ContentWrap>

    <!-- Search -->
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
        <el-form-item :label="t('learningCenter.course.title')" prop="title">
          <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable style="width: 240px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('category.topic.subjectName')" prop="topicId">
          <!-- <el-select v-model="queryParams.topicId" placeholder="Please choose" clearable style="width: 240px">
            <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
          </el-select> -->
          <SubjectSelect v-model="queryParams.topicId" :has-no-subject="false" />
        </el-form-item>
        <el-form-item :label="t('common.status')" prop="status">
          <el-select v-model="queryParams.status" :placeholder="t('common.selectText')" clearable style="width: 240px">
            <el-option v-for="item in ackList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button plain type="primary" @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />
            {{ t('action.export') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <!-- Table -->
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData">
        <el-table-column :label="t('learningCenter.course.title')" prop="name" align="left" min-width="280" fixed="left" />
        <el-table-column :label="t('category.topic.subjectName')" prop="topic" min-width="180">
          <template #default="{ row }">
            <el-tooltip
              :content="row.topic" popper-class="tooltip-mywidth"
              effect="dark" placement="top"
            >
              <div class="line-clamp-3">
                {{ row.topic }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :label="t('common.status')" prop="status" min-width="100">
          <template #default="{ row }">
            <el-tag v-if="row.status === 1" type="primary">
              {{ ShelfType[row.status] }}
            </el-tag>
            <el-tag v-if="row.status === 0" type="info">
              {{ ShelfType[row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('statistics.course.requiredUsers')" prop="requiredNum" min-width="125" />
        <el-table-column :label="t('statistics.course.completedUsersOfRequired')" prop="requiredAndCompletedNum" min-width="245" />
        <el-table-column :label="t('statistics.course.uncompletedUsersOfRequired')" prop="requiredAndNotCompletedNum" min-width="245" />
        <el-table-column :label="t('statistics.course.electiveUsers')" prop="electiveNum" min-width="200" />
        <el-table-column :label="t('learningCenter.course.star')" prop="star" min-width="150" />
        <el-table-column fixed="right" align="center" :label="t('global.action')" min-width="100">
          <template #default="{ row }">
            <el-button link type="primary" icon="View" @click="handleDetail(row)">
              <Icon icon="ep:view" />
              {{ t('action.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>
  </div>
</template>

<style  lang="scss">
// 设置tooltip-mywidth时，style不能添加scope
.tooltip-mywidth{
  width: 240px;
}
</style>
