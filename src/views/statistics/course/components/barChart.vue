<script setup lang='ts'>
import type { EChartsCoreOption } from 'echarts'
import { ChartFactory } from '@/views/Home/scripts/chart'
import echarts, { type ECOption } from '@/plugins/echarts'
import type { AdminCourseBar } from '@/typings/dashboard/admin'
const { t } = useI18n()
const props = defineProps<{ data: AdminCourseBar[] | undefined }>()
const loading = ref(true)
const chartRef = ref()
let chart: ChartFactory
function initChart() {
  const xData = props.data?.map(item => item.topicName)
  const yData = props.data?.map(item => item.courseCount)

  const options: ECOption = {
    xAxis: {
      interval: 1,
      axisTick: {
        show: false,
      },
      // axisLabel: {
      //   rotate: 45,
      // },
      data: xData,
      // data: ChartData.map(item => item.topicName),
    },
    grid: {
      left: '30px',
      right: '30px',
      top: '20px',
    },
    tooltip: {},
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [17, 9],
        symbolOffset: [0, -6], // 上部椭圆
        symbolPosition: 'end',
        // @ts-expect-error 需要使用函数方式确定值
        symbol: (value) => {
          if (!value || +value === 0) {
            return 'none'
          }
          return 'circle'
        },
        z: 12,
        color: '#5CB58D',
        data: yData,
        animation: false,
      },
      {
        name: '',
        type: 'pictorialBar',
        symbolSize: [17, 9],
        symbolOffset: [0, 0], // 下部椭圆
        // "barWidth": "20",
        z: 11,
        color: '#007943',
        // @ts-expect-error 需要使用函数方式确定值
        symbol: (value) => {
          if (!value || +value === 0) {
            return 'none'
          }
          return 'circle'
        },
        data: yData,
        animation: false,
      },
      {
        name: 'Course Total',
        type: 'bar',
        barWidth: '17',
        data: yData,
        itemStyle: {
          color: '#007943',
        },
        animation: false,
      },
    ],
  }
  chart = new ChartFactory(chartRef.value, options)
}

// onMounted(() => {
//   initChart()
// })
const watcher = watch(() => props.data, (newValue) => {
  if (newValue) {
    loading.value = false
    initChart()
    watcher()
  }
})
function resize() {
  chart.resize()
}
window.addEventListener('resize', resize)
onDeactivated(() => {
  window.removeEventListener('resize', resize)
})
onActivated(() => {
  window.addEventListener('resize', resize)
})
</script>

<template>
  <div v-loading="loading" class="h-[400px] bg-white rounded-[10px] flex flex-col p-5">
    <div class="flex items-center gap-2.5 mb-5">
      <el-icon color="#007943" :size="20">
        <Histogram />
      </el-icon>
      <span class="text-lg text-[#233a35]">{{ t('statistics.course.subjectCourseStatistics') }}</span>
    </div>
    <div ref="chartRef" class="flex-1 shrink-0"></div>
  </div>
</template>

<style scoped lang='scss'>

</style>
