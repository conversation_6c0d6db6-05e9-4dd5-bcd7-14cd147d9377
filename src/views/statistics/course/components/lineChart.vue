<script setup lang='ts'>
import type { EChartsCoreOption } from 'echarts'
import { ChartFactory } from '@/views/Home/scripts/chart'
import echarts, { type ECOption } from '@/plugins/echarts'
import type { AdminCourseBar } from '@/typings/dashboard/admin'
const { t } = useI18n()
const props = defineProps<{ data: AdminCourseBar[] | undefined }>()
const loading = ref(true)
const chartRef = ref()
let chart: ChartFactory
function initChart() {
  const xData = props.data?.map(item => item.topicName)
  const yData = props.data?.map(item => item.courseCount)

  const options: ECOption = {
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData,
    },
    grid: {
      left: '30px',
      right: '30px',
      top: '20px',
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: 'Course Total',
        type: 'line',
        smooth: true,
        data: yData,
        itemStyle: {
          color: '#007943',
        },
        showSymbol: false, // 在 tooltip hover 的时候显示
        animation: false,
        areaStyle: {},
      },
    ],
  }
  chart = new ChartFactory(chartRef.value, options)
}

// onMounted(() => {
//   initChart()
// })
const watcher = watch(() => props.data, (newValue) => {
  if (newValue) {
    loading.value = false
    initChart()
    watcher()
  }
})
const resize = () => {
  chart.resize()
}
window.addEventListener('resize', resize)
onDeactivated(() => {
  window.removeEventListener('resize', resize)
})
onActivated(() => {
  window.addEventListener('resize', resize)
})
</script>

<template>
  <div v-loading="loading" class="h-[400px] bg-white rounded-[10px] flex flex-col p-5">
    <div class="flex items-center gap-2.5 mb-5">
      <el-icon color="#007943" :size="20">
        <Histogram />
      </el-icon>
      <span class="text-lg text-[#233a35]">{{ t('statistics.course.subjectCourseStatistics') }}</span>
    </div>
    <div ref="chartRef" class="flex-1 shrink-0"></div>
  </div>
</template>

<style scoped lang='scss'>

</style>
