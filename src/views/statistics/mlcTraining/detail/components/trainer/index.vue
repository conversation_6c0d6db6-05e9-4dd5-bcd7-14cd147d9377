<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="140px"
    >
      <el-form-item :label="t('statistics.training.trainerType')" prop="type">
        <el-select
          v-model="queryParams.type"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('statistics.training.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="deptId"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Name" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="Please input"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset</el-button>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          Export
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
<!--      <el-table-column type="selection" width="50" fixed="left" />-->
      <el-table-column label="Trainer Name" align="center" prop="name" min-width="180px" />
      <el-table-column label="Badge Number" align="center" prop="badgeNumber" min-width="180px" />
      <el-table-column label="Trainer Type" align="center" prop="type" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRAINER_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="Company" align="center" prop="companyName" min-width="180px" />
      <el-table-column label="Performance Score" align="center" prop="feedbackAvg" min-width="180px" />
      <el-table-column label="Action" align="center" fixed="right" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
          >
            Detail
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <TrainerDetail
    ref="trainerDetailRef"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TrainerApi, TrainerRespVO } from '@/api/academy/trainer'
import download from "@/utils/download"
import { listCompany } from "@/api/system/company"
import {DeptRespVO, listDept} from "@/api/system/dept"
import { useUserStore } from "@/store/modules/user"
import TrainerDetail from './components/TrainerDetail.vue'

const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()

const loading = ref(false) // 列表的加载中
const exportLoading = ref(false)
const list = ref([]) // 列表的数据
const companyList = ref([])
const departOptions = ref([])
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  companyId:  undefined,
  type: undefined,
  courseId: route.query.id as unknown as number
})
const queryFormRef = ref() // 搜索的表单
const trainerDetailRef = ref()
const formRef = ref()
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TrainerApi.getTrainerPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.userIds = []
  handleQuery()
}

// 导出列表教师信息
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await TrainerApi.exportTrainer(queryParams)
    download.excel(data, 'Trainer.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}

const handleDetail = (item: TrainerRespVO) => {
  trainerDetailRef.value.open(item)
}

/** 初始化 **/
onMounted(() => {
  getList()
  getCompanyTree()
})
</script>
