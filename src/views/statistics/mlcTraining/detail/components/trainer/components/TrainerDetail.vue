<template>
  <el-drawer :title="t('statistics.training.trainerDetail')" v-model="dialogVisible" size="1700">
    <ContentWrap>
      <div class="h-[200px] flex">
        <div class="w-[300px] ms-10 me-10 flex justify-between items-center">
          <el-progress type="dashboard" :percentage="trainerInfo?.feedbackAvg">
            <template #default="{ percentage }">
              <span class="percentage-value">{{ percentage }}</span>
              <span class="percentage-label">{{ t('statistics.training.classEvaluationScore') }}</span>
            </template>
          </el-progress>
        </div>
        <el-divider direction="vertical" />
        <div class="flex flex-col ms-10">
          <span class="mt-10 text-[#BBBBBB]">{{ t('statistics.training.trainerName') }}: {{ trainerInfo?.name }}</span>
          <span class="mt-10 text-[#BBBBBB]">{{ t('statistics.training.courseTitle') }}: {{ courseInfo?.title }}</span>
        </div>
      </div>
    </ContentWrap>
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item :label="t('common.type')" prop="type">
          <el-select
            v-model="queryParams.type"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('common.language')" prop="languages">
          <el-select
            v-model="queryParams.languages"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Time" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="start"
            end-placeholder="end"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="Title" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="Please input"
            clearable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />
            Search
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />
            Reset
          </el-button>
          <el-button type="success" plain @click="handleExport">
            <Icon icon="ep:download" class="mr-5px" />
            Export
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column label="Class Title" align="center" prop="name" min-width="180px" />
        <el-table-column label="Class Type" align="center" prop="type" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="Language" align="center" prop="language" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="scope.row.language" />
          </template>
        </el-table-column>
        <el-table-column label="Classroom" align="center" prop="classRoomName" min-width="180px" />
        <el-table-column label="Date" align="center" prop="startDate" min-width="180px" />
        <el-table-column label="Duration" align="center" prop="duration" min-width="180px">
          <template #default="scope">
            {{ scope.row.startTime }} - {{ scope.row.endTime }}
          </template>
        </el-table-column>
        <el-table-column label="Trainer Evaluation Score" align="center" prop="trainerScore" min-width="180px" />
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
  </el-drawer>


</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi } from '@/api/academy/class'
import download from '@/utils/download'
import { useUserStore } from "@/store/modules/user"
import { TrainerRespVO } from "@/api/academy/trainer"
import { CourseApi } from "@/api/academy/course"
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const userStore = useUserStore()
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  languages: undefined,
  type: undefined,
  createTime: [],
  trainerId: undefined
})
const queryFormRef = ref() // 搜索的表单
const loading = ref(false)
const exportLoading = ref(false)
const dialogVisible = ref(false)
const list = ref([])

const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.getClassInfoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.name = undefined
  queryParams.languages = undefined
  queryParams.type = undefined
  queryParams.createTime = []
  queryParams.trainerId = trainerInfo.value.id
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await ClassInfoApi.exportClassInfo(queryParams)
    download.excel(data, 'TrainerDetail.xlsx')
  } finally {
    exportLoading.value = false
  }
}

const courseInfo = ref()
// 获取课程信息
const getInfo = async (id: number) => {
  courseInfo.value = await CourseApi.getCourse(id)
}
const trainerInfo = ref() // 教师信息
/** 打开弹窗 */
const open = async (item: TrainerRespVO) => {
  // 获取课程信息
  if (route.query.id) {
    getInfo(route.query.id as unknown as number)
  }
  dialogVisible.value = true
  queryParams.trainerId = item.id
  trainerInfo.value = item
  resetQuery()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

onMounted(() => {
})
</script>
<style scoped>
:deep(.el-divider--vertical) {
  height: 200px;
  margin-left: 40px;
}
.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 30px;
}
.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}
.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}
.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>
