<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="140px"
    >
      <el-form-item :label="t('statistics.training.classType')" prop="type">
        <el-select
          v-model="queryParams.type"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('common.language')" prop="languages">
        <el-select
          v-model="queryParams.languages"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('statistics.training.time')" prop="timeType">
        <el-select
          v-model="queryParams.timeType"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
          @change="changeTime"
        >
          <el-option
            v-for="dict in timeTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('action.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('action.reset') }}</el-button>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          {{ t('action.export') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
<!--      <el-table-column type="selection" width="50" fixed="left" />-->
      <el-table-column :label="t('statistics.training.classCode')" align="center" prop="code" min-width="180px" />
      <el-table-column :label="t('statistics.training.trainer')" align="center" prop="trainerName" min-width="180px" />
      <el-table-column :label="t('statistics.training.classType')" align="center" prop="type" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="t('common.language')" align="center" prop="language" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="scope.row.language" />
        </template>
      </el-table-column>
      <el-table-column :label="t('statistics.training.classroom')" align="center" prop="classRoomName" min-width="180px" />
      <el-table-column :label="t('statistics.training.duration')" align="center" prop="duration" min-width="180px">
        <template #default="scope">
          {{ scope.row.startTime }} - {{ scope.row.endTime }}
        </template>
      </el-table-column>
      <el-table-column :label="t('statistics.training.attendance')" align="center" prop="attendRate" min-width="180px">
        <template #default="scope">
          {{ scope.row.attendRate }}%
        </template>
      </el-table-column>
      <el-table-column :label="t('common.status')" align="center" prop="status" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
          >
            {{ t('action.detail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <ClassDetail
    ref="classDetailRef"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { MlcTrainingApi } from "@/api/statistics/mlcTraining"
import { ClassInfoApi, ClassInfoRespVO } from '@/api/academy/class'
import download from "@/utils/download"
import { listCompany } from "@/api/system/company"
import { listDept } from "@/api/system/dept"
import { handlePhaseTree } from "@/utils/tree"
import { useUserStore } from "@/store/modules/user"
import {
  getMonthStartAndEnd,
  getWeekStartAndEnd,
  getYearStartAndEnd
} from "@/utils/formatDate"
import ClassDetail from './components/ClassDetail.vue'

const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const loading = ref(false) // 列表的加载中
const exportLoading = ref(false)
const list = ref([]) // 列表的数据
const companyList = ref([])
const departOptions = ref([])
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  languages: undefined,
  type: undefined,
  timeType: undefined,
  createTime: [],
  courseId: route.query.id as number,
})
const queryFormRef = ref() // 搜索的表单
const formRef = ref()
const classDetailRef = ref()
const timeTypeOptions = computed(() => [
  {
    value: '1',
    label: t('statistics.training.thisWeek')
  },
  {
    value: '2',
    label: t('statistics.training.thisMonth')
  },
  {
    value: '3',
    label: t('statistics.training.thisYear')
  },
])

// 选择时间
const changeTime = (val: number) => {
  switch (Number(val)) {
    case 1:
      // 获取当前天的星期一到星期天的开始时间和结束时间
      queryParams.createTime = getWeekStartAndEnd();
      break;
    case 2:
      // 获取当前天的月的开始时间和结束时间
      queryParams.createTime = getMonthStartAndEnd();
      break;
    case 3:
      // 获取当前年的开始时间和结束时间
      queryParams.createTime = getYearStartAndEnd();
      break;
    default:
      queryParams.createTime = [];
      break;
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.getClassInfoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.createTime = []
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

// 导出列表学生信息
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await ClassInfoApi.exportClassInfo(queryParams)
    download.excel(data, 'Class.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}
/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'deptId')
  }
}

const handleDetail =  (item: ClassInfoRespVO) => {
  classDetailRef.value.open(item)
}


/** 初始化 **/
onMounted(() => {
  getList()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>
