<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="140px"
    >
      <el-form-item :label="t('statistics.training.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
          @node-click="handleCompanyClick"
        />
      </el-form-item>
      <el-form-item :label="t('sys.user.department')" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="departOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.departmentPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('statistics.training.testResult')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('statistics.training.attendanceStatus')" prop="attendanceStatus">
        <el-select
          v-model="queryParams.attendanceStatus"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ATTENDANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('statistics.training.name')" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          :placeholder="t('common.inputText')"
          clearable
          @input="getUserList"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('action.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('action.reset') }}</el-button>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          {{ t('action.export') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
<!--      <el-table-column type="selection" width="50" fixed="left" />-->
      <el-table-column :label="t('statistics.training.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('statistics.training.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('statistics.training.badgeNumber')" align="center" prop="badgeNo" min-width="180px" />
      <el-table-column :label="t('statistics.training.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('statistics.training.classCode')" align="center" prop="classCode" min-width="180px" />
<!--      <el-table-column label="Duration" align="center" prop="roomNumber" min-width="180px" />-->
      <el-table-column :label="t('statistics.training.testResult')" align="center" prop="status" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="t('statistics.training.attendanceStatus')" align="center" prop="attendanceStatus" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ATTENDANCE_STATUS" :value="scope.row.attendanceStatus" />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi } from '@/api/academy/class'
import download from "@/utils/download"
import { listCompany } from "@/api/system/company"
import {DeptRespVO, listDept} from "@/api/system/dept"
import { handlePhaseTree } from "@/utils/tree"
import { useUserStore } from "@/store/modules/user"
import { listUser, UserRespVO } from "@/api/system/user"

const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const exportLoading = ref(false)
const list = ref([]) // 列表的数据
const companyList = ref([])
const departOptions = ref([])
const total = ref(0) // 列表的总页数
const route = useRoute()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 2,
  courseId: route.query.id as number,
  nickname: undefined,
  companyId:  undefined,
  deptId: undefined,
  attendanceStatus: undefined,
  status: undefined,
  userIds: []
})
const queryFormRef = ref() // 搜索的表单
const formRef = ref()
// 用户查询使用form
const queryUserParams = ref({
  pageNo: 1,
  pageSize: 99999,
  nickname: undefined
})
const userList = ref([])
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.getStudentList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
   queryUserParams.value = {
     nickname: undefined
  }
  queryFormRef.value.resetFields()
  queryParams.userIds = []
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

// 导出列表学生信息
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await ClassInfoApi.exportPostponeCompanyDetail(queryParams)
    download.excel(data, 'Student.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}


/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}


/** 查询用户列表 */
const getUserList = async () => {
  queryUserParams.value.nickname = queryParams.nickname
  // 每次调用之前清除一遍用户数据
  queryParams.userIds  = []
  userList.value = await listUser(queryUserParams.value)
  if (userList.value.list) {
    // 取出数据中的id
    queryParams.userIds = userList.value.list.map((item) => {
      return item.userId
    })
  }
}


// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}
/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'deptId')
  }
}


/** 初始化 **/
onMounted(() => {
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
  getList()
})
</script>
