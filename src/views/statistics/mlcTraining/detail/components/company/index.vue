<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="140px"
    >
      <el-form-item>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          {{ t('action.export') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
<!--      <el-table-column type="selection" width="50" fixed="left" />-->
      <el-table-column :label="t('statistics.training.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('statistics.training.studentNumber')" align="center" prop="studentNumber" min-width="180px" />
      <el-table-column :label="t('statistics.training.completionRate')" align="center" prop="completionRate" min-width="180px">
        <template #default="scope">
          {{ scope.row.completionRate }} %
        </template>
      </el-table-column>
      <el-table-column :label="t('statistics.training.attendance')" align="center" prop="attendanceRate" min-width="180px">
        <template #default="scope">
          {{ scope.row.attendanceRate }} %
        </template>
      </el-table-column>
      <el-table-column :label="t('statistics.training.notCompleted')" align="center" prop="notPassed" min-width="180px" />
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
          >
            {{ t('action.detail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <CompanyDetail
    ref="companyDetailRef"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi, PostPoneCompanyRespVO } from '@/api/academy/class'

import download from "@/utils/download"
import { handlePhaseTree } from "@/utils/tree"
import { useUserStore } from "@/store/modules/user"
import CompanyDetail from './components/CompanyDetail.vue'

const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const loading = ref(false) // 列表的加载中
const exportLoading = ref(false)
const list = ref([]) // 列表的数据
const companyList = ref([])
const departOptions = ref([])
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  courseId: route.query.id,
})
const queryFormRef = ref() // 搜索的表单
const companyDetailRef = ref()
const formRef = ref()
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.postponeCompany(queryParams)
    list.value = data
    total.value = data.length
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

// 导出列表公司信息
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await ClassInfoApi.exportPostponeCompany(queryParams)
    download.excel(data, 'Company.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}



const handleDetail = (row: PostPoneCompanyRespVO) => {
  companyDetailRef.value.open(row)
}


/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
