<template>
  <el-drawer :title="t('statistics.training.companyDetail')" v-model="dialogVisible" size="1700">
    <ContentWrap>
      <div class="h-[200px] flex">
        <div class="w-[300px] ms-10 me-10 flex justify-between items-center">
          <el-progress type="dashboard" :percentage="companyInfo?.completionRate">
            <template #default="{ percentage }">
              <span class="percentage-value">{{ percentage }}%</span>
              <span class="percentage-label">{{ t('statistics.training.completionRate') }}</span>
            </template>
          </el-progress>
          <el-progress type="dashboard" :percentage="checkInLength ? parseFloat(((checkInLength / total) * 100).toFixed(2)) : 0">
            <template #default="{ percentage }">
              <span class="percentage-value">{{ percentage }}%</span>
              <span class="percentage-label">{{ t('statistics.training.attendance') }}</span>
            </template>
          </el-progress>
        </div>
        <el-divider direction="vertical" />
        <div class="flex flex-col ms-10">
          <span class="mt-10 text-[#BBBBBB]">{{ t('statistics.training.companyName') }}: {{ companyInfo?.companyName }}</span>
          <span class="mt-10 text-[#BBBBBB]">{{ t('statistics.training.studentNumber') }}: {{ companyInfo?.studentNumber }}</span>
        </div>
      </div>
    </ContentWrap>
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="135px"
      >
        <el-form-item :label="t('sys.user.department')" prop="deptId">
          <el-tree-select
            v-model="queryParams.deptId"
            :data="departOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            :placeholder="t('sys.user.departmentPH')"
            check-strictly
            clearable
            filterable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item :label="t('statistics.training.testResult')" prop="status">
          <el-select
            v-model="queryParams.status"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('statistics.training.attendanceStatus')" prop="attendanceStatus">
          <el-select
            v-model="queryParams.attendanceStatus"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ATTENDANCE_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('statistics.training.name')" prop="nickname">
          <el-input
            v-model="queryParams.nickname"
            :placeholder="t('common.inputText')"
            clearable
            @input="getUserList"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />
            {{ t('common.query') }}
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />
            {{ t('common.reset') }}
          </el-button>
          <el-button type="success" plain @click="handleExport">
            <Icon icon="ep:download" class="mr-5px" />
            {{ t('common.export') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column :label="t('statistics.training.name')" align="center" prop="nickname" min-width="180px" />
        <el-table-column :label="t('statistics.training.companyName')" align="center" prop="companyName" min-width="180px" />
        <el-table-column :label="t('statistics.training.department')" align="center" prop="deptName" min-width="180px" />
        <el-table-column :label="t('statistics.training.badgeNo')" align="center" prop="badgeNo" min-width="180px" />
        <el-table-column :label="t('statistics.training.position')" align="center" prop="positionName" min-width="180px" />
<!--        没必要展示 因为都是同一个class-->
<!--        <el-table-column label="Class Code" align="center" prop="code" min-width="180px" />-->
        <el-table-column :label="t('statistics.training.testResult')" align="center" prop="status" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASS_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="t('statistics.training.checkInOut')" align="center" prop="checkIn" min-width="180px">
          <template #default="scope">
            {{ scope.row.isCheck }}
          </template>
        </el-table-column>
        <el-table-column :label="t('statistics.training.attendanceStatus')" align="center" prop="attendanceStatus" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.ATTENDANCE_STATUS" :value="scope.row.attendanceStatus" />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
  </el-drawer>


</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'

import download from '@/utils/download'
import { listUser, UserRespVO } from "@/api/system/user"
import { listDept } from "@/api/system/dept"
import { handlePhaseTree } from "@/utils/tree"
import { useUserStore } from "@/store/modules/user"
import {ClassInfoApi, PostPoneCompanyRespVO} from '@/api/academy/class'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const userStore = useUserStore()
const checkInLength = ref(0)
const checkOutLength = ref(0)
const completeionRateLength = ref(0)
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  nickname: undefined,
  status: undefined,
  deptId: undefined,
  attendanceStatus: undefined,
  userIds: [],
  courseId: route.query.id
})
const queryFormRef = ref() // 搜索的表单
const loading = ref(false)
const exportLoading = ref(false)
const dialogVisible = ref(false)
const departOptions = ref([])
const userList = ref([])
const list = ref([])
const userIds = ref([])
const companyInfo = ref()
const queryUserParams = ref({
  pageNo: 1,
  pageSize: 99999,
  nickname: undefined
})
/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}

const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.postponeCompanyDetail(queryParams)
    list.value = data.list.map((item) => {
      return {
        ...item,
        isCheck: item.checkInTime && item.checkOutTime  ? true : false,
      }
    })
    if (data) {
      checkInLength.value = data.list.filter(item => item.checkInTime).length
      checkOutLength.value = data.list.filter(item => item.checkOutTime).length
    }
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.nickname = undefined
  queryParams.status = undefined
  queryParams.attendanceStatus = undefined
  queryParams.deptId = undefined
  // 清除一遍值后在重新赋值
  queryParams.userIds = userIds.value
  queryUserParams.value = {
    pageNo: 1,
    pageSize: 99999,
    nickname: undefined
  }
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await ClassInfoApi.exportPostponeCompanyDetail(queryParams)
    download.excel(data, 'Company-Student.xlsx')
  } finally {
    exportLoading.value = false
  }
}


/** 查询用户列表  */
const getUserList = async () => {
  queryUserParams.value.nickname = queryParams.nickname
  userList.value = []
  userList.value = await listUser(queryUserParams.value)
  if (userList.value.list) {
    // 取出数据中的id 取交集
    const ids = userList.value.list.map((item) => {
      return item.userId
    })
    const intersection  = userIds.value.filter(value => {
      ids.includes(value)
    })
    queryParams.userIds = intersection ? intersection : -9999
  }
}

/** 打开弹窗 */
const open = async (item: PostPoneCompanyRespVO) => {
  dialogVisible.value = true
  queryParams.classId = item.id
  // 缓存一份userId
  userIds.value = item.userIds
  companyInfo.value = item
  resetQuery()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

onMounted(() => {
  getDepartmentTree(userStore.user.companyId)
})
</script>
<style scoped>
:deep(.el-divider--vertical) {
  height: 200px;
  margin-left: 40px;
}
.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 30px;
}
.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}
.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}
.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>
