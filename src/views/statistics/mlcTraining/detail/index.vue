<template>
  <div v-loading="loading">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <div class="flex gap-5 mr-16">
<!--        Todo 后面找一个固定图片替换-->
        <el-image src="https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AAOEcdM.img" class="w-[154px] h-[90px] row-span-2" />
        <div class="flex flex-col w-[350px]">
          <span class="text-[#23293A] text-xl break-all">{{ basicInformation?.title }} </span>
          <span class="text-[#23293A] break-all truncate">{{ basicInformation?.code }} </span>
          <div class="text-[#23293A] break-al text-[#BBBBBB] truncate" >{{ basicInformation?.remarks }} </div>
        </div>
      </div>
      <div class="ms-auto flex-shrink-0 flex w-[#200px]">
        <div class="mt-3">
          <el-progress type="dashboard" :percentage="actualAttendanceTotal ? parseFloat(((actualAttendanceTotal / plannedAttendanceTotal) * 100).toFixed(2)) : 0">
            <template #default="{ percentage }">
              <span class="percentage-value">{{ percentage }}%</span>
              <span class="percentage-label">{{ t('statistics.training.attendance') }}</span>
            </template>
          </el-progress>
        </div>
        <div class="flex flex-col text-[#BBBBBB] ms-8 text-m mx-auto my-auto">
          <span>{{ t('statistics.training.plannedAttendanceLabel') }}: {{ plannedAttendanceTotal }}</span>
          <span class="mt-2">{{ t('statistics.training.actualAttendanceLabel') }}: {{ actualAttendanceTotal }}</span>
          <span class="mt-4">{{ t('statistics.training.noShowLabel') }}: {{ noShowTotal }}</span>
        </div>
      </div>
    </div>
    <ContentWrap>
      <el-tabs v-model="activeName">
        <el-tab-pane :label="t('statistics.training.student')" name="1">
          <Student />
        </el-tab-pane>
        <el-tab-pane :label="t('statistics.training.class')" name="2">
          <Class />
        </el-tab-pane>
        <el-tab-pane :label="t('statistics.training.company')" name="3">
          <Company />
        </el-tab-pane>
        <el-tab-pane :label="t('statistics.training.trainer')" name="4">
          <Trainer />
        </el-tab-pane>
      </el-tabs>
    </ContentWrap>
  </div>
</template>
<script setup lang="ts" name="MlcTrainingStatisticsDetail">
import Student from './components/student/index.vue'
import Class from './components/class/index.vue'
import Company from './components/company/index.vue'
import Trainer from './components/trainer/index.vue'
import { useTagsViewStore } from "@/store/modules/tagsView"
import { CourseApi } from "@/api/academy/course"
import {ClassInfoApi} from "@/api/academy/class";
const { delView } = useTagsViewStore() // 视图操作
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const route = useRoute()
const { router, push, currentRoute } = useRouter() // 路由
const classId = route.query.id
const showWithdraw = ref(false)
const formLoading = ref(false)
const loading = ref(false)
const basicInformation = ref({})
const activeName = ref('1') // 默认选择第一个卡片
const plannedAttendanceTotal = ref(0) // 计划出勤人数
const actualAttendanceTotal = ref(0) // 实际出勤人数
const noShowTotal = ref(0) // 没有出勤人数
const getInfo = async (id: number) => {
  loading.value = true
  try {
    basicInformation.value = await CourseApi.getCourse(id)
  } finally {
    loading.value = false
  }
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: -1,
  courseId: route.query.id as number,
})

/** 查询列表 */
const getList = async () => {
  const data = await ClassInfoApi.getStudentList(queryParams)
  plannedAttendanceTotal.value = data.total
  actualAttendanceTotal.value = data.list?.filter(item => item.attendanceStatus === 1).length
  noShowTotal.value = data.list?.filter(item => item.attendanceStatus === 0).length

}

const handleBack = () => {
  delView(unref(currentRoute))
  push('/statistics/mlcTraining')
}

onMounted(() => {
  if (route.query.id) {
    getInfo(route.query.id as unknown as number)
    getList()
  }
})
</script>


<style scoped lang="scss">
:deep(.el-divider--vertical) {
  height: 200px;
  margin-left: 40px;
}
.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 30px;
}
.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}
.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}
.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>
