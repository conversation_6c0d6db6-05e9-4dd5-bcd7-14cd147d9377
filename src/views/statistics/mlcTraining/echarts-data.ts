import { EChartsOption } from 'echarts'
const { t } = useI18n()
// ================ HSE统计数据 =================
// 实际出席人数、缺席人数和总人数
export const percentageOptions = {
  tooltip: {
    trigger: 'item',
  },
  legend: {
    bottom: 'bottom'
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: true,
      label: {
        show: true, // 显示标签
        formatter: '{d}%', // 格式化标签以显示百分比
        fontSize: 12 // 可选：设置字体大小
      },
      emphasis: {
        label: {
          show: true, // 强调状态下也显示标签
          fontSize: '18', // 在强调状态下的字体大小
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: true // 显示引导线
      },
      data: [
        { value: 0, name: 'Actual attendance' },
        { value: 0, name: 'No Show' },
      ]
    }
  ]
};
// 发布的所有证书的数量
export const passportOptions = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    bottom: 'bottom'
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: true,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: false,
          fontSize: 12,
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 0, name: 'E-Passport' },
      ]
    }
  ]
};
// 各个公司没有出勤的人数
export const noShowOptions = {
  tooltip: {
    // trigger: 'axis', // 触发类型，'axis'适用于柱状图等
    // formatter: function (params) {
    //   let tooltipText = ''; // 初始化提示框文本
    //
    //   params.forEach(function (item) {
    //     // 对于每个数据项，构建自定义的提示框内容
    //     tooltipText += item.seriesName + '<br/>'
    //         + item.name + '：' // 显示完整的类别名称
    //         + item.value + '<br/>';
    //   });
    //
    //   return tooltipText; // 返回构建好的提示框文本
    // }
  },
  grid: {
    left: '20%', // 设置绘图区左侧距离整个图表容器的百分比
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      formatter: function (value) {
        // 示例：如果标签长度超过15个字符，则截取前10个字符并加上"..."
        return value.length > 15 ? value.slice(0, 10) + '...' : value;
      },
    }
  },
  series: [{
    type: 'bar',
    barWidth: 10,
    data: []
  }],
  dataZoom: [
    {
      type: 'slider',   // 滑块型 dataZoom
      orient: 'vertical', // 垂直方向的数据区域缩放
      start: 0,         // 初始显示从第一个数据开始
      end: 50,          // 显示前 50% 数据
      width: 20,        // 滚动条宽度
      top: 'top',       // 放置在顶部
      handleSize: '80%' // 手柄大小
    }
  ]
};

// 三个主要公司的参加课程的总人数、实际出勤人数、没有出勤的人数
export const companyOptions = {
  legend: {},
  tooltip: {},
  dataset: {
    source: []
  },
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
  xAxis: { type: 'category' },
  yAxis: {},
  // Declare several bar series, each will be mapped
  // to a column of dataset.source by default.
  series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
};

// 各个课程的总人数、实际出勤人数、没有出勤的人数
export const courseOptions = {
  legend: {},
  tooltip: {},
  dataset: {
    source: []
  },
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
  xAxis: { type: 'category' },
  yAxis: {},
  // Declare several bar series, each will be mapped
  // to a column of dataset.source by default.
  series: [
    {
      type: 'bar',
      itemStyle: {
        color: '#E97132'
      },
      barWidth: '10',
    },
    {
      type: 'bar',
      itemStyle: {
        color: '#0FADDB'
      },
      barWidth: '10',
    },
    {
      type: 'bar',
      itemStyle: {
        color: '#4EA72E'
      },
      barWidth: '10',
    }
  ]
};

// 各个课程course中含有的课堂class的数量
export const noCourseOptions = {
  tooltip: {},
  grid: {
    left: '20%', // 设置绘图区左侧距离整个图表容器的百分比
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: []
  },
  series: [{
    type: 'bar',
    data: []
  }],
  dataZoom: [
    {
      type: 'slider',   // 滑块型 dataZoom
      orient: 'vertical', // 垂直方向的数据区域缩放
      start: 0,         // 初始显示从第一个数据开始
      end: 50,          // 显示前 50% 数据
      width: 20,        // 滚动条宽度
      top: 'top',       // 放置在顶部
      handleSize: '80%' // 手柄大小
    }
  ]
};

// ================ DDT统计数据 =================
export const weeklyOptions = {
  legend: {},
  tooltip: {},
  dataset: {
    source: []
  },
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
  xAxis: { type: 'category' },
  yAxis: {},
  // Declare several bar series, each will be mapped
  // to a column of dataset.source by default.
  series: [
    {
      type: 'bar',
      itemStyle: {
        color: '#E97132'
      },
      barWidth: '10',
    },
    {
      type: 'bar',
      itemStyle: {
        color: '#0FADDB'
      },
      barWidth: '10',
    },
    {
      type: 'bar',
      itemStyle: {
        color: '#4EA72E'
      },
      barWidth: '10',
    }
  ]
};
// 各个公司的缺勤人数
export const companyWeeklyOptions = {
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [],
      type: 'bar',
      barWidth: 20, // 设置柱子宽度为固定值
      barCategoryGap: '10px', // 调整柱子之间的距离
    }
  ],
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
};
// 三个主要公司计划参加课程的总人数、实际出勤人数、缺勤人数
export const companyCourseOptions = {
  legend: {},
  tooltip: {},
  dataset: {
    source: []
  },
  xAxis: { type: 'category' },
  yAxis: {},
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
  // Declare several bar series, each will be mapped
  // to a column of dataset.source by default.
  series: [{ type: 'bar', barWidth: '20', }, { type: 'bar', barWidth: '20', }, { type: 'bar', barWidth: '20' }]
};
