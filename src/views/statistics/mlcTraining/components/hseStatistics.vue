<template>
  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="16" :lg="16" :md="24" :sm="24" :xs="24" class="mb-8px border-2">
      <el-row :gutter="20">
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <ContentWrap class="pie-card_wrapper">
              <el-skeleton :loading="skeletonLoading" animated>
                <div class="h-[120px]">
                  <div class="text-center font-bold mb-2">
                    {{ t('statistics.training.percentageActualAttendanceNoShow') }}
                  </div>
                  <div class="w-[300px] flex mx-auto">
                    <span class="w-[200px] truncate">{{ t('statistics.training.actualAttendance') }}</span>
                    <span class="w-[50px]">{{ checkInfo?.showTotal }}</span>
                    <span class="w-[50px]">{{ percentage(checkInfo?.showTotal,checkInfo?.total) }}%</span>
                  </div>
                  <div class="w-[300px] flex mx-auto mt-2">
                    <span class="w-[200px] truncate">{{ t('statistics.training.noShow') }}</span>
                    <span class="w-[50px]">{{ checkInfo?.notShowTotal }}</span>
                    <span sclass="w-[50px]">{{ percentage(checkInfo?.notShowTotal,checkInfo?.total) }}%</span>
                  </div>
                  <div class="w-[300px] flex mx-auto font-bold mt-2">
                    <span class="w-[200px] truncate">{{ t('statistics.training.total') }}</span>
                    <span class="w-[50px]">{{ checkInfo?.total }}</span>
                    <span class="w-[50px]">{{ checkInfo?.total > 0 ? '100%' : '0%' }}</span>
                  </div>
                </div>
                <div ref="percentageChartHseRef" class="h-[300px]"></div>
              </el-skeleton>
            </ContentWrap>
          </el-col>
          <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
            <ContentWrap class="pie-card_wrapper">
              <el-skeleton :loading="skeletonLoading" animated>
                <div class="h-[120px]">
                  <div class="text-center font-bold mb-2">
                    {{ t('statistics.training.passportIssued') }}
                  </div>
                  <div class="w-[300px] flex mx-auto font-bold mt-2">
                    <span class="w-[250px] truncate">{{ t('statistics.training.total') }}</span>
                    <span class="w-[50px]">{{ checkInfo?.passedTotal }}</span>
                  </div>
                </div>
                <div ref="passportChartHseRef" class="h-[300px]"></div>
              </el-skeleton>
            </ContentWrap>
          </el-col>
        </el-row>
      <el-row :gutter="20">
        <el-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <ContentWrap class="pie-card_wrapper">
            <el-skeleton :loading="skeletonLoading" animated>
              <div class="text-center font-bold mb-2">
                {{ t('statistics.training.bocAosKbr') }}
              </div>
              <div ref="companyChartHseRef" class="h-[300px]"></div>
            </el-skeleton>
            </ContentWrap>
        </el-col>
      </el-row>
    </el-col>
    <el-col :xl="8" :lg="8" :md="24" :sm="24" :xs="24">
      <ContentWrap class="pie-card_wrapper">
        <el-skeleton :loading="skeletonLoading" animated>
          <div class="text-center font-bold mb-2">
            {{ t('statistics.training.noShowReport') }}
          </div>
          <div ref="noShowReportChartHseRef" class="h-[300px]"></div>
        </el-skeleton>
      </ContentWrap>
    </el-col>
  </el-row>
  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="16" :lg="16" :md="24" :sm="24" :xs="24">
      <ContentWrap class="pie-card_wrapper">
        <el-skeleton :loading="skeletonLoading" animated>
          <div class="text-center font-bold mb-2">
            {{ t('statistics.training.coursesDelivered') }}
          </div>
          <div ref="courseDeliveredChartHseRef" class="h-[300px]"></div>
        </el-skeleton>
      </ContentWrap>
    </el-col>
    <el-col :xl="8" :lg="8" :md="24" :sm="24" :xs="24">
      <ContentWrap class="pie-card_wrapper">
        <el-skeleton :loading="skeletonLoading" animated>
          <div class="text-center font-bold mb-2">
            {{ t('statistics.training.noOfCourses') }}
          </div>
          <div ref="noCoursesChartHseRef" class="h-[300px]"></div>
        </el-skeleton>
      </ContentWrap>
    </el-col>
  </el-row>

</template>
<script setup lang="ts" name="HseStatistics">
import { set } from 'lodash-es'
import { EChartsOption } from 'echarts'
import * as echarts from 'echarts'
import * as ExcelJS from 'exceljs'
import html2canvas from 'html2canvas'
import { saveAs } from "file-saver"

import { percentageOptions, passportOptions, companyOptions, noShowOptions, courseOptions,noCourseOptions } from '../echarts-data'
import { MlcTrainingApi } from  '@/api/statistics/mlcTraining'
const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const queryRef = ref()
const loading = ref(false)
const skeletonLoading = ref(true)
const percentageOptionsData = reactive<EChartsOption>(percentageOptions) as EChartsOption
const passportOptionsData = reactive<EChartsOption>(passportOptions) as EChartsOption
const noShowOptionsData = reactive<EChartsOption>(noShowOptions) as EChartsOption
const companyOptionsData = reactive<EChartsOption>(companyOptions) as EChartsOption
const coursesOptionsData = reactive<EChartsOption>(courseOptions) as EChartsOption
const noCourseOptionsData = reactive<EChartsOption>(noCourseOptions) as EChartsOption

const queryForm  = reactive({
  // 分类id
  categoryId: undefined,
  startDate: '',
  endDate: '',
})
const checkInfo = ref() // 存储统计信息-签到统计与通过统计 数据
const percentageChartHseRef = ref<HTMLElement | null>(null)
const passportChartHseRef = ref<HTMLElement | null>(null)
const courseDeliveredChartHseRef = ref<HTMLElement | null>(null)
const companyChartHseRef = ref<HTMLElement | null>(null)
const noShowReportChartHseRef = ref<HTMLElement | null>(null)
const noCoursesChartHseRef = ref<HTMLElement | null>(null)
const percentageChartInstance = ref()
const passportChartInstance = ref()
const companyChartInstance = ref()
const noShowChartInstance = ref()
const coursesChartInstance = ref()
const noCourseChartInstance = ref()
// Percentage of Actual attendance And No Show 图 和 Passport Issued 图
const getSignInAndPass = async (categoryId: number) => {
  queryForm.categoryId = categoryId
  const data = await MlcTrainingApi.getSignInAndPass(queryForm)
  checkInfo.value = data
  percentageOptionsData!.series![0].data[0].value = data.showTotal
  percentageOptionsData!.series![0].data[1].value = data.notShowTotal
  passportOptionsData!.series![0].data[0].value = data.passedTotal
  // 必须先初始化图表 然后再将数据插入到图表中
  percentageChartInstance.value = echarts.init(percentageChartHseRef.value)
  percentageChartInstance.value.setOption(percentageOptionsData)
  passportChartInstance.value = echarts.init(passportChartHseRef.value)
  passportChartInstance.value.setOption(passportOptionsData)
}


// No Show Report 柱状图
const getNoSignIn = async (categoryId: number) => {
  queryForm.categoryId = categoryId
  const data = await MlcTrainingApi.getNoSignIn(queryForm)
  const allKeys = Object.keys(data)
  noShowOptionsData!.yAxis.data = allKeys
  const notShowTotals = Object.values(data).map(item => item.notShowTotal)
  noShowOptionsData!.series![0].data = notShowTotals
  noShowChartInstance.value = echarts.init(noShowReportChartHseRef.value)
  noShowChartInstance.value.setOption(noShowOptionsData)
}

// BOC-AOS-KBR 图
const getCompanySignIn = async (categoryId: number) => {
  queryForm.categoryId = categoryId
  const data = await MlcTrainingApi.getCompanySignIn(queryForm)
  // 转换为二维数组
  const tableData = [
    ['product', 'Total', 'Actual attendance', 'No attendance'],
    ...Object.entries(data)?.map(([key, value]) => [
      key,
      value?.total,
      value?.showTotal,
      value?.notShowTotal
    ])
  ]
  companyOptionsData!.dataset!.source = tableData
  companyChartInstance.value = echarts.init(companyChartHseRef.value)
  companyChartInstance.value.setOption(companyOptionsData)
}
// Courses Delivered 图
const getCourseSignIn = async (categoryId: number) => {
  queryForm.categoryId = categoryId
  const data = await MlcTrainingApi.getCourseSignIn(queryForm)
  // 转换为二维数组
  const tableData = [
    ['product', 'Planed attendance', 'Actual attendance', 'No show'],
    ...Object.entries(data)?.map(([key, value]) => [
      key,
      value?.total,
      value?.showTotal,
      value?.notShowTotal
    ])
  ]
  coursesOptionsData!.dataset!.source = tableData
  coursesChartInstance.value = echarts.init(courseDeliveredChartHseRef.value)
  coursesChartInstance.value.setOption(coursesOptionsData)
}
// No.of courses 图
const getCourseClass = async (categoryId: number) => {
  queryForm.categoryId = categoryId
  const data = await MlcTrainingApi.getCourseClass(queryForm)
  const allKeys = Object.keys(data)
  const allValue = Object.values(data)
  noCourseOptionsData!.yAxis!.data = allKeys
  noCourseOptionsData!.series![0].data = allValue
  noCourseChartInstance.value = echarts.init(noCoursesChartHseRef.value)
  noCourseChartInstance.value.setOption(noCourseOptionsData)
}

// 获取百分比
const percentage = (statusTotal: number,total: number) => {
  return statusTotal ? parseFloat(((statusTotal / total) * 100).toFixed(2)) : 0
}


const getChartImageBase64 = () => {
  const images = []
  if (percentageChartInstance.value) {
    images.push(
      percentageChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (passportChartInstance.value) {
    images.push(
      passportChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (companyChartInstance.value) {
    images.push(
      companyChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (noShowChartInstance.value) {
    images.push(
      noShowChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (coursesChartInstance.value) {
    images.push(
      coursesChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (noCourseChartInstance.value) {
    images.push(
      noCourseChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }

  return images
}

defineExpose({ getSignInAndPass, getNoSignIn, getCompanySignIn, getCourseSignIn, getCourseClass, skeletonLoading, queryForm,getChartImageBase64 })
</script>

<style scoped lang="scss">
</style>
