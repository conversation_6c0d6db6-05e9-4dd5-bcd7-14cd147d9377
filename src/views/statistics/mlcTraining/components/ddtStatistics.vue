<template>
  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24" class="mb-8px border-2">
      <ContentWrap class="pie-card_wrapper">
        <el-skeleton :loading="skeletonLoading" animated>
          <div class="text-center font-bold mb-2">
            {{ t('statistics.training.weeklyReport') }}
          </div>
          <div ref="weeklyReportDdtRef" class="h-[300px]"></div>
        </el-skeleton>
      </ContentWrap>
    </el-col>
    <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24" class="mb-8px border-2">
      <ContentWrap class="pie-card_wrapper">
        <el-skeleton :loading="skeletonLoading" animated>
          <div class="text-center font-bold mb-2">
            {{ t('statistics.training.mlcDdtWeeklyReport') }}
          </div>
          <div ref="companyWeekRef" class="h-[300px]"></div>
        </el-skeleton>
      </ContentWrap>
    </el-col>
  </el-row>
  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24" class="mb-8px border-2">
      <ContentWrap class="pie-card_wrapper">
        <el-skeleton :loading="skeletonLoading" animated>
          <div class="text-center font-bold mb-2">
            {{ t('statistics.training.bocAosKbr') }}
          </div>
          <div ref="companyCourseDdtRef" class="h-[300px]"></div>
        </el-skeleton>
      </ContentWrap>
    </el-col>
  </el-row>
</template>
<script setup lang="ts" name="HseStatistics">
import { set } from 'lodash-es'
import { EChartsOption } from 'echarts'
import {companyWeeklyOptions, weeklyOptions,companyCourseOptions} from "@/views/statistics/mlcTraining/echarts-data"
import { MlcTrainingApi } from  '@/api/statistics/mlcTraining'
import * as echarts from 'echarts'
import * as ExcelJS from 'exceljs'
import html2canvas from 'html2canvas'
import {saveAs} from "file-saver"

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const skeletonLoading = ref(true)
const weeklyOptionsData = reactive<EChartsOption>(weeklyOptions) as EChartsOption
const companyWeeklyOptionsData = reactive<EChartsOption>(companyWeeklyOptions) as EChartsOption
const companyCourseOptionsData = reactive<EChartsOption>(companyCourseOptions) as EChartsOption
const weeklyReportDdtRef = ref<HTMLElement | null>(null)
const companyWeekRef = ref<HTMLElement | null>(null)
const companyCourseDdtRef = ref<HTMLElement | null>(null)

const weeklyChartInstance = ref()
const companyChartInstance = ref()
const companyCourseChartInstance = ref()
const queryForm  = reactive({
  // 分类id
  categoryId: undefined,
  startDate: '',
  endDate: '',
})

// Weekly Report 图
const getCourseSignIn = async (categoryId: number) => {
  queryForm.categoryId = categoryId
  const data = await MlcTrainingApi.getCourseSignIn(queryForm)
  // 转换为二维数组
  const tableData = [
    ['product', 'Planed attendance', 'Actual attendance', 'No show'],
    ...Object.entries(data)?.map(([key, value]) => [
      key,
      value?.total,
      value?.showTotal,
      value?.notShowTotal
    ])
  ]
  weeklyOptionsData!.dataset!.source = tableData
  weeklyChartInstance.value = echarts.init(weeklyReportDdtRef.value)
  weeklyChartInstance.value.setOption(weeklyOptionsData)
}
// MLC-DDT Weekly Report 图
const getNoSignIn = async (categoryId: number) => {
  queryForm.categoryId = categoryId
  const data = await MlcTrainingApi.getNoSignIn(queryForm)
  const allKeys = Object.keys(data)
  companyWeeklyOptionsData!.xAxis.data = allKeys
  const notShowTotals = Object.values(data)?.map(item => item?.notShowTotal)
  companyWeeklyOptionsData!.series![0].data = notShowTotals
  companyChartInstance.value = echarts.init(companyWeekRef.value)
  companyChartInstance.value.setOption(companyWeeklyOptionsData)
}

const getCompanySignIn = async (categoryId: number) => {
  queryForm.categoryId = categoryId
  const data = await MlcTrainingApi.getCompanySignIn(queryForm)
  const convertedData = Object.entries(data).map(([key, value]) =>
    [key, value.total, value.showTotal, value.notShowTotal]
  );
  companyCourseOptionsData!.dataset!.source = convertedData
  companyCourseChartInstance.value = echarts.init(companyCourseDdtRef.value)
  companyCourseChartInstance.value.setOption(companyCourseOptionsData)
}


const getChartImageBase64 = () => {
  const images = []
  if (weeklyChartInstance.value) {
    images.push(
      weeklyChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (companyChartInstance.value) {
    images.push(
      companyChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (companyCourseChartInstance.value) {
    images.push(
      companyCourseChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }

  return images

}
defineExpose({ getCourseSignIn, getNoSignIn, getCompanySignIn,  skeletonLoading, queryForm,getChartImageBase64
})

</script>

<style scoped lang="scss">
</style>
