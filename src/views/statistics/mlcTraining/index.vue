<template>
  <ContentWrap>
    <el-scrollbar class="scrollbar-wrapper">
      <div class="flex">
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] ms-1 me-1">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px]">
            <div class="text-[14px] text-white font-medium">
              {{ t('statistics.training.totalCourses') }}: {{ courseInfoTotal?.total }}
            </div>
            <div class="text-white text-[12px]">
              {{ t('statistics.training.hstDdtCourses') }}: {{ courseInfoTotal?.hseTotal }}/{{ courseInfoTotal?.ddtTotal }}
            </div>
          </div>
        </div>
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] w-[400px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px] text-[14px] text-white font-medium">
            {{ t('statistics.training.offlineClasses') }}: {{ classInfoTotal?.offlineTotal }}
          </div>
        </div>
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] w-[400px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px] text-[14px] text-white font-medium">
            {{ t('statistics.training.virtualClasses') }}: {{ classInfoTotal?.virtualTotal }}
          </div>
        </div>
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] w-[400px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px] text-[14px] text-white font-medium">
            {{ t('statistics.training.hybridClasses') }}: {{ classInfoTotal?.hybridTotal }}
          </div>
        </div>
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] w-[400px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px] text-[14px] text-white font-medium">
            {{ t('statistics.training.totalStudents') }}: {{ studentTotal }}
          </div>
        </div>
      </div>
    </el-scrollbar>

  </ContentWrap>
  <!-- 图形展示 -->
  <ContentWrap>
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item name="1">
        <template #title="{ isActive }">
          <div :class="['title-wrapper', { 'is-active': isActive }]" class="flex justify-between w-full">
            <div class="font-bold">{{ t('statistics.training.report') }}</div>
            <div class="h-full flex mt-[8px]">
              <el-select
                v-model="timeType"
                :placeholder="t('common.selectText')"
                clearable
                class="!w-180px"
                @change="changeTime"
                @click.stop
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.STATISTICS_TRAINING_TIME_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              <el-button
                class="ms-3 me-3"
                link
                type="primary"
                @click="handleExportReport"
                @click.stop
              >
                {{ t('statistics.training.exportReport') }}
              </el-button>
            </div>
          </div>
        </template>
        <el-tabs v-model="tabName" @tab-change="selectActive">
          <el-tab-pane :label="tabList[0]?.name" name="1">
            <HseStatistics ref="hseRef" />
          </el-tab-pane>
          <el-tab-pane :label="tabList[1]?.name" name="2">
            <DdtStatistics ref="ddtRef" />
          </el-tab-pane>
        </el-tabs>
      </el-collapse-item>
    </el-collapse>
  </ContentWrap>

  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item :label="t('statistics.training.category')" prop="categoryId">
        <el-tree-select
          v-model="queryParams.categoryId"
          :data="categoryList"
          :props="defaultProps"
          filterable
          clearable
          check-strictly
          default-expand-all
          value-key="deptId"
          :placeholder="t('common.selectText')"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('common.courseTitle')" prop="courseTitle">
        <el-input
          v-model="queryParams.courseTitle"
          :placeholder="t('common.inputText')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('action.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('action.reset') }}</el-button>
        <el-button
          type="success"
          plain
          :loading="exportLoading"
          @click="handleExport"
        >
          <Icon icon="ep:download" class="mr-5px" />
          {{ t('action.export') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

<!--  列表信息-->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column :label="t('statistics.training.courseTitle')" align="center" prop="courseTitle" min-width="180px" />
      <el-table-column :label="t('statistics.training.courseCode')" align="center" prop="code" min-width="180px">
        <template #default="scope">
          MJN-{{ getCode(scope.row.categoryName) }}-{{ scope.row.code }}
        </template>
      </el-table-column>
      <el-table-column :label="t('statistics.training.category')" align="center" prop="categoryName" min-width="180px" />
      <el-table-column :label="t('statistics.training.noOfClasses')" align="center" prop="classTotal" min-width="180px" />
      <el-table-column :label="t('statistics.training.plannedAttendance')" align="center" prop="plannedTotal" min-width="180px" />
      <el-table-column :label="t('statistics.training.actualAttendance')" align="center" prop="actualTotal" min-width="180px" />
      <el-table-column :label="t('statistics.training.noShow')" align="center" prop="notShowTotal" min-width="180px" />
      <el-table-column :label="t('statistics.training.attendance')" align="center" prop="attendance" min-width="180px">
        <template #default="scope">
          {{ scope.row.attendance }} %
        </template>
      </el-table-column>
      <el-table-column :label="t('statistics.training.classEvaluationScore')" align="center" prop="classScore" min-width="180px" />
      <el-table-column :label="t('statistics.training.trainerEvaluationScore')" align="center" prop="trainerScore" min-width="180px" />
      <el-table-column :label="t('statistics.training.facilityEvaluationScore')" align="center" prop="facilityScore" min-width="180px" />
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row.id)"
          >
            {{ t('statistics.training.details') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script setup lang="ts" name="MlcTrainingStatistics">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { handlePhaseTree, handleTree, defaultProps } from "@/utils/tree"
import HseStatistics from './components/hseStatistics.vue'
import DdtStatistics from './components/ddtStatistics.vue'
import { MlcTrainingApi } from  '@/api/statistics/mlcTraining'
import download from '@/utils/download'
import { listTopic } from "@/api/category/training"
import {
  getMonthStartAndEnd,
  getQuarterTime,
  getWeekStartAndEnd,
  getYearStartAndEnd
} from "@/utils/formatDate"
import { saveAs } from 'file-saver'
import { Workbook } from 'exceljs'
import FileSaver from 'file-saver'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const queryRef = ref()
const loading = ref(false)
const skeletonLoading = ref(false)
const exportLoading = ref(false)
const total = ref(0)
const activeName = ref('1')
const tabName = ref('1')
const timeType = ref(2)
const categoryList = ref([]) // 课程分类信息
const list = ref([])
const queryFormRef = ref()
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  categoryId: undefined,
  courseTitle: undefined
})
const hseRef = ref()
const ddtRef = ref()
const createTime = ref()
const courseInfoTotal = ref()
const classInfoTotal = ref()
const studentTotal = ref(0)
const tabList = ref([])
const hseCourseList = ref([]) // hse课程信息
const ddtCourseList = ref([]) // ddt课程信息

// 获取课程信息
const getCourseList = async () => {
  const data = await MlcTrainingApi.getCoursePage({
    ...queryParams.value,
    // 当首次加载只查询出一级分类下的子级,如果在子类的基础下进行按子类id筛选课程加入一下判断  分类根据顶部的Tab页签进行动态刷新值
    categoryId : tabName.value === '1'
      ? (queryParams.value.categoryId ?? tabList.value[0]?.id)
      : tabName.value === '2'
        ? (queryParams.value.categoryId ?? tabList.value[1]?.id)
        : undefined
  })
  list.value = data.list
}

const handleQuery = () => {
  queryParams.value.pageNo = 1
  getCourseList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  // queryParams.value.categoryId = tabName.value === '1' ? tabList.value[0]?.id : tabList.value[1]?.id
  handleQuery()
}


const getHseTotal = (categoryId: number) => {
  const apiCalls = [
    hseRef.value.getSignInAndPass(categoryId),
    hseRef.value.getNoSignIn(categoryId),
    hseRef.value.getCompanySignIn(categoryId),
    hseRef.value.getCourseSignIn(categoryId),
    hseRef.value.getCourseClass(categoryId)
  ]
  // 并行执行所有接口调用，并等待全部完成
  Promise.all(apiCalls)
    .then(() => {
      // 所有接口成功完成后关闭 loading
      hseRef.value.skeletonLoading = false
    })
    .catch(error => {
      // 错误处理（可选）
      hseRef.value.skeletonLoading = false
    })
}
const getDdtTotal = (categoryId: number) => {
  const apiCalls = [
    ddtRef.value.getCourseSignIn(categoryId),
    ddtRef.value.getNoSignIn(categoryId),
    ddtRef.value.getCompanySignIn(categoryId)
  ]
  // 并行执行所有接口调用，并等待全部完成
  Promise.all(apiCalls)
    .then(() => {
      // 所有接口成功完成后关闭 loading
      ddtRef.value.skeletonLoading = false
    })
    .catch(error => {
      // 错误处理（可选）
      ddtRef.value.skeletonLoading = false
    })
}
// 课程分类信息(type 1: 界面第一次加载 2. 切换Tab页签替换分类树,不单独去取出parentID 为0的数据,如果不加限制，每次切换tab页签数据都会变化)
const getCourserCategory = async (status: number,type?: number) => {
  // existDdt: 是否ddt 状态(0.否 1.是)
  const data = await listTopic({existDdt: status})
  if (type === 1) {
    // 取出parentID 为0的数据
    tabList.value = data?.filter(item => item.parentId === 0)
    // 当Type为1的时候意味着是第一次加载,把hse和ddt的数据全都取出来
    if (tabList.value[0]?.id) {
      getHseCourseList()
    }
    if (tabList.value[1]?.id) {
      getDdtCourseList()
    }
  }
  categoryList.value = handleTree(data)
  if (tabName.value === '1') {
    getHseTotal(tabList.value[0]?.id)
  } else {
    getDdtTotal(tabList.value[1]?.id)
  }



  // 获取课程信息
  getCourseList()
}
// 跳转预览详情信息
const handleDetail = (id: number) => {
  router.push({
    name: 'MlcTrainingStatisticsDetail',
    query: { id }
  })
}

/** 导出图片和所有数据按钮操作 */
const handleExportReport = async () => {
  const workbook = new Workbook();
  const chartHseImages = await hseRef.value.getChartImageBase64()
  const chartDdtImages = await ddtRef.value.getChartImageBase64()
  const chartImages = [...chartHseImages, ...chartDdtImages]
  // 将每个图表插入到不同的 sheet 中
  chartImages.forEach((base64, index) => {
    const worksheet = workbook.addWorksheet(`Chart ${index + 1}`)

    const imageId = workbook.addImage({
      base64: base64,
      extension: 'png'
    })
    worksheet.addImage(imageId, {
      tl: {col: 0, row: 0},
      br: {col: 10, row: 30},
      editAs: 'oneCell'
    })
  })
  // 字段映射关系（根据后端返回的英文字段 -> 中文标题）
  const fieldMap = {
    courseTitle: 'Course Title',
    code: 'Course Code',
    categoryName: 'Category',
    classTotal: 'No.of classes',
    plannedTotal: 'Planned attendance',
    actualTotal: 'Actual attendance',
    notShowTotal: 'No Show',
    attendance: 'Attendance',
    classScore: 'Class Evaluation Score',
    trainerScore: 'Trainer Evaluation Score',
    facilityScore: 'Facility Evaluation Score'
  }


  if (hseCourseList.value.length > 0) {
    // 添加第一个列表 sheet
    const sheetA = workbook.addWorksheet('HSE-Course')
    sheetA.addRow(Object.values(fieldMap))
    // 添加每一行数据
    hseCourseList.value.forEach(item => {
      const row = Object.keys(fieldMap).map(key => item[key])
      sheetA.addRow(row)
    })
    // 可选：设置列宽
    sheetA.columns.forEach(column => {
      column.width = 20
    })
  }

  if (ddtCourseList.value.length > 0) {
    //  添加第二个列表 sheet
    const sheetB = workbook.addWorksheet('DDT-Course')
    sheetB.addRow(Object.values(fieldMap))
    // 添加每一行数据
    ddtCourseList.value.forEach(item => {
      const row = Object.keys(fieldMap).map(key => item[key])
      sheetB.addRow(row)
    })

    // 可选：设置列宽
    sheetB.columns.forEach(column => {
      column.width = 20
    })
  }




  // 导出文件
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/octet-stream' })
  FileSaver.saveAs(blob, 'Echarts_export.xlsx')

}




// 导出列表课程信息
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    exportLoading.value = true
    const data = await MlcTrainingApi.exportCourse(queryParams.value)
    download.excel(data, 'Course.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 选择时间
const changeTime = (val: number) => {
  switch (val) {
    case 1:
      // 获取当前天的星期一到星期天的开始时间和结束时间
      createTime.value = getWeekStartAndEnd(1);
      break;
    case 2:
      // 获取当前天的月的开始时间和结束时间
      createTime.value = getMonthStartAndEnd(1);
      break;
    case 3:
      // 获取当前年的开始时间和结束时间
      createTime.value = getYearStartAndEnd(1);
      break;
    case 4:
      // 获取当前时间的第一季度开始和结束时间
      createTime.value = getQuarterTime(1,1);
      break;
    case 5:
      // 获取当前时间的第二季度开始和结束时间
      createTime.value = getQuarterTime(2,1);
      break;
    case 6:
      // 获取当前时间的第三季度开始和结束时间
      createTime.value = getQuarterTime(3,1);
      break;
    case 7:
      // 获取当前时间的第四季度开始和结束时间
      createTime.value = getQuarterTime(4,1);
      break;
    default:
      createTime.value = []
      break;
  }
  handleTime(tabName.value)
}
// 每次选择时间调用子组件刷新一次
const handleTime = (val: string) => {
  if (val === '1') {
    // 将时间赋值给子组件查询接口
    hseRef.value.queryForm.startDate = createTime.value[0]
    hseRef.value.queryForm.endDate = createTime.value[1]
    getHseTotal(tabList.value[0]?.id)
  } else {
    ddtRef.value.queryForm.startDate = createTime.value[0]
    ddtRef.value.queryForm.endDate = createTime.value[1]
    getDdtTotal(tabList.value[1]?.id)
  }
}

const resetTime = (val: string) => {
  // 默认本月
  timeType.value = 2
  if (val === '1') {
    // 将时间赋值给子组件查询接口
    const time = getMonthStartAndEnd(1)
    hseRef.value.queryForm.startDate = time[0]
    hseRef.value.queryForm.endDate = time[1]
    getHseTotal(tabList.value[0]?.id)
  } else {
    const time = getMonthStartAndEnd(1)
    ddtRef.value.queryForm.startDate = time[0]
    ddtRef.value.queryForm.endDate = time[1]
    getDdtTotal(tabList.value[1]?.id)
  }
  // 清空分类
  queryParams.value.categoryId = undefined
  categoryList.value = []
}
// 每次切换tab页签进行按时间重置
const selectActive = (val: string) => {
  if (val === '1') {
    resetTime(val)
  } else {
    resetTime(val)
  }
  getCourserCategory(val === '1' ? tabList.value[0]?.id : tabList.value[1]?.id,2)
}

const getCourseTotal = async () => {
  courseInfoTotal.value = await MlcTrainingApi.getCourseTotal()
}

const getClassTotal = async () => {
  classInfoTotal.value = await MlcTrainingApi.getClassTotal()
}

const getStudentTotal = async () => {
  studentTotal.value = await MlcTrainingApi.getStudentTotal()
}
// 截取code值(根据分类名称第一个 - 进行截取拼接)
const getCode = (val: string) => {
  const parts = val.split('-')
  return parts.length > 0 ? parts[0] : val
}
// hse课程的所有数据
const getHseCourseList = async () => {
  const data = await MlcTrainingApi.getCoursePage({pageNo: 1, pageSize: -1, categoryId: tabList.value[0]?.id})
  hseCourseList.value = data.list
}
// ddt课程的所有数据
const getDdtCourseList = async () => {
  const data = await MlcTrainingApi.getCoursePage({pageNo: 1, pageSize: -1, categoryId: tabList.value[1]?.id})
  ddtCourseList.value = data.list
}

onMounted(() => {
  // 获取课程分类信息 默认为HSE
  getCourserCategory(undefined,1)
  // 课程总数
  getCourseTotal()
  // 课堂总数
  getClassTotal()
  // 学生总数
  getStudentTotal()
})
</script>

<style scoped lang="scss">
</style>
