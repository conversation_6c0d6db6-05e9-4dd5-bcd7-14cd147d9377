<script setup lang="ts" name="ScoreDetail">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'

import { delPaper, listPaper, updatePaperStatus } from '@/api/topicMgt/paper'
import { studentRecordsExam } from '@/api/topicMgt/exam'
import { dateFormatter } from '@/utils/formatTime'

interface ScoreItem {
  id: number
  assignmentId: number
  answerTime: number
  questionCount: number
  rightCount: number
  wrongCount: number
  totalScore: number
  beginTime: string
  endTime: string
  createTime: string
  updateTime: string
  pass: boolean
}
const props = defineProps<{ titleName: string; modelValue: boolean }>()
const emit = defineEmits(['update:isVisible', 'update:modelValue', 'answerDetails'])
const router = useRouter()
const { t } = useI18n()
const scoreList = ref<Array<ScoreItem>>([])
const formRef = ref()
const RefSingleTable = ref()

const isVisible = computed({
  get() {
    reset()
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
/** 表单重置 */
const reset = () => {
  formRef.value?.resetFields()
}
const handleOpen = (assignmentId: number) => {
  getStudentRecordsList(assignmentId)
  isVisible.value = true
}
const handleClose = () => {
  isVisible.value = false
}
// 获取试题集列表
const getStudentRecordsList = async (assignmentId: number) => {
  scoreList.value = await studentRecordsExam({ assignmentId })
}
// 确认选择
const handleConfirm = () => {
  isVisible.value = false
}
// 查看答题详情
const handleView = (row: any) => {
  router.push({ name: `ExamRecord`, query: { recordId: row.id } })
}

defineExpose({ handleOpen })
</script>

<template>
  <Dialog v-model="isVisible" :title="props.titleName" width="650" class="notice-dialog" @close="handleClose">
    <el-table ref="RefSingleTable" :data="scoreList" border style="width: 100%">
      <el-table-column type="index" :label="t('setting.banner.no')" align="center" width="60" />
      <el-table-column prop="createTime" :formatter="dateFormatter" :label="t('examMgt.exam.submitTime')" width="260" align="center" />
      <el-table-column prop="totalScore" :label="t('learningCenter.course.score')" width="100" align="center" />
      <el-table-column fixed="right" :label="t('global.action')" align="center">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleView(row)">
            <Icon icon="ep:view" />
            {{ t('action.answerDetails') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div>
        <el-button @click="handleConfirm"> {{ t('global.cancel') }} </el-button>
        <!-- <el-button type="primary" @click="handleClose"> Cancel </el-button> -->
      </div>
    </template>
  </Dialog>
</template>
