<script setup lang="ts" name="ExamStatisticsDetail">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import ScoreDetail from './components/ScoreDetail.vue'
import { getExam, infoExam, staticsExam, exportExam } from '@/api/topicMgt/exam'
import { listTopic } from '@/api/category/topic'
import examFailedIcon from '@/assets/images/exam/exam_failed.png'
import examNotAttendIcon from '@/assets/images/exam/exam_not_attend.png'
import examPassedIcon from '@/assets/images/exam/exam_passed.png'
import examTopIcon from '@/assets/images/exam/exam_top_icon.png'
import download from "@/utils/download";
import { dateFormatter } from '@/utils/formatTime'
interface DataItem {
  userId: number
  assignmentId: number
  recordId: number
  studentName: string
  badgeNo: string
  email: string
  department: string
  section: string
  position: string
  onSchedule: boolean
  status: number // 0：未考试，1：未通过，2：已通过）
  submitTime: string
  quizScore: number
  operator: number
  examMax: number
  examNum: number
  company: string
}
interface StaticsItem {
  failedNum: number
  onScheduleNum: number
  overtimeNum: number
  passRate: number
  passedNum: number
  takenNum: number
  totalNum: number
}
const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const RefScoreDetail = ref()
const isVisible = ref<boolean>(false)
const queryRef = ref()
const loading = ref(false)
const statusList = ref([t('examMgt.exam.notAttend'), t('examMgt.exam.failed'), t('examMgt.exam.pass')])
const examStatusList = ref([t('examMgt.exam.notAttend'), t('examMgt.exam.inProcess'), t('examMgt.exam.expired')])
const total = ref(0)
const examInfo = ref()
const tableData = ref<Array<DataItem>>([])
const staticsData = ref<StaticsItem>()
const optionsSubject = ref([
  {
    id: '',
    name: '',
  },
])
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    classifyId: undefined,
    name: undefined,
    status: undefined,
  },
})
const { queryParams } = toRefs(data)
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    classifyId: undefined,
    name: undefined,
    status: undefined,
  }
  queryRef.value?.resetFields()
}
// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopic(null)
  optionsSubject.value = data
}
// exam详情
const getExamInfo = async () => {
  examInfo.value = await getExam(route.query.examId)
}
// 获取考试用户列表
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.id = route.query.examId
   const res = await infoExam(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// 查询考试统计
const getStaticsExam = async () => {
  queryParams.value.id = route.query.examId
  staticsData.value = await staticsExam(queryParams.value)
}
// 查看考试详情
const handleView = (row: any) => {
  RefScoreDetail.value.handleOpen(row.assignmentId)
}
// 查看答题详情
const handleAnswerDetail = (val) => {
  console.log(val)
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportExam(queryParams.value)
    download.excel(data, `Exam-${examInfo?.value.name}-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}

onMounted(() => {
  getExamInfo()
  getList()
  getSubjectData()
  getStaticsExam()
})
</script>

<template>
  <div class="app-container">
    <div class="flex mb-4 bg-[#E3F1EB] rounded-[10px]">
      <el-image class="my-[20px] ml-[30px] w-[60px] h-[75px]" :src="examTopIcon" fit="fill" />
      <div class="ml-[20px]">
        <div class="text-[20px] text-[#23293A] font-medium" style="line-height: 60px">
          {{ examInfo?.name }}
        </div>
        <div class="w-[100px] h-[23px] leading-[23px] rounded-[4px] text-center text-sm text-white bg-[#007943]">
          {{ examStatusList[examInfo?.status] }}
        </div>
      </div>
    </div>
    <el-row :gutter="30" class="p-5">
      <el-col :span="8">
        <div class="top_item_bg bg-gradient-to-r from-[#6FAB34] to-[#3F7B02]">
          <div class="statistic_icon_bg">
            <el-image class="statistic_icon" :src="examNotAttendIcon" fit="fill" />
          </div>
          <div class="item_content">
            <div class="item_desc">
              {{ t('examMgt.exam.notAttend') }}
            </div>
            <div class="item_number">
              {{ staticsData?.totalNum - staticsData?.takenNum }}
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="top_item_bg bg-gradient-to-r from-[#33AA75] to-[#08804A]">
          <div class="statistic_icon_bg">
            <el-image class="statistic_icon" :src="examFailedIcon" fit="fill" />
          </div>
          <div class="item_content">
            <div class="item_desc">
              {{ t('examMgt.exam.failed') }}
            </div>
            <div class="item_number">
              {{ staticsData?.failedNum }}
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="top_item_bg bg-gradient-to-r from-[#2D85A5] to-[#04557D]">
          <div class="statistic_icon_bg">
            <el-image class="statistic_icon" :src="examPassedIcon" fit="fill" />
          </div>
          <div class="item_content">
            <div class="item_desc">
              {{ t('examMgt.exam.pass') }}
            </div>
            <div class="item_number">
              {{ staticsData?.passedNum }}
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div>
      <!-- 查询条件 -->
      <ContentWrap>
        <el-form ref="queryRef" :inline="true" :model="queryParams" label-position="left" class="-mb-15px">
          <el-form-item :label="t('examMgt.paper.name')">
            <el-input v-model="queryParams.studentName" :placeholder="t('common.inputText')" clearable style="width: 180px" @keydown.enter="handleSearch" />
          </el-form-item>
          <el-form-item :label="t('learningCenter.boarding.badgeNumber')">
            <el-input v-model="queryParams.badgeNo" :placeholder="t('common.inputText')" clearable style="width: 180px" @keydown.enter="handleSearch" />
          </el-form-item>
          <el-form-item :label="t('learningCenter.course.email')">
            <el-input v-model="queryParams.email" :placeholder="t('common.inputText')" clearable style="width: 180px" @keydown.enter="handleSearch" />
          </el-form-item>
          <el-form-item :label="t('common.status')">
            <el-select v-model="queryParams.status" :placeholder="t('common.chooseText')" clearable style="width: 180px">
              <el-option :label="t('examMgt.exam.notAttend')" value="0" />
              <el-option :label="t('examMgt.exam.failed')" value="1" />
              <el-option :label="t('examMgt.exam.pass')" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('examMgt.exam.onSchedule')">
            <el-select v-model="queryParams.onSchedule" :placeholder="t('common.chooseText')" clearable style="width: 180px">
              <el-option :label="t('common.yes')" :value="true" />
              <el-option :label="t('common.no')" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon class="mr-5px" icon="ep:search" />
              {{ t('action.search') }}
            </el-button>
            <el-button type="default" @click="handleReset">
              <Icon class="mr-5px" icon="ep:refresh" />
              {{ t('action.reset') }}
            </el-button>
            <el-button plain type="primary" @click="handleExport">
              <Icon class="mr-5px" icon="ep:download" />
              {{ t('action.export') }}
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table v-loading="loading" :data="tableData" style="width: 100%">
          <el-table-column fixed="left" prop="studentName" :label="t('learningCenter.course.studentName')" min-width="240" align="center" />
          <el-table-column prop="badgeNo" :label="t('learningCenter.boarding.badgeNumber')" min-width="240" align="center" />
          <el-table-column prop="email" :label="t('learningCenter.course.email')"  min-width="240" align="center" />
          <el-table-column prop="company" :label="t('learningCenter.course.company')" min-width="240" align="center" />
          <el-table-column prop="department" :label="t('learningCenter.course.deptName')" min-width="240" align="center" />
          <el-table-column prop="section" :label="t('learningCenter.course.section')" min-width="240" align="center" />
          <el-table-column prop="position" :label="t('learningCenter.course.position')" min-width="240" align="center" />
          <el-table-column prop="status" :label="t('common.status')" width="150" align="center">
            <template #default="{ row }">
              <div>
                {{ statusList[row.status] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="onSchedule" :label="t('examMgt.exam.onSchedule')" min-width="240" align="center">
            <template #default="{ row }">
              <div>
                {{ row.status === 2 ? (row.onSchedule ? t('common.yes') : t('common.no')) : '' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="submitTime" :label="t('examMgt.exam.submitTime')" width="150" :formatter="dateFormatter" align="center" />
          <el-table-column prop="quizScore" :label="t('examMgt.exam.topScore')" width="180" align="center" />
          <el-table-column prop="operator" :label="t('log.operaLog.operator')" width="180" align="center" />
          <el-table-column fixed="right" :label="t('global.action')" width="160" align="center">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row)">
                <Icon icon="ep:view" />
                {{ t('statistics.exam.scoreDetails') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
      </ContentWrap>
    </div>
    <ScoreDetail ref="RefScoreDetail" v-model="isVisible" :title-name="t('statistics.exam.scoreDetails')" @answer-details="handleAnswerDetail" />
  </div>
</template>

<style scoped lang="scss">
.top_item_bg {
  display: flex;
  align-items: center;
  height: 100px;
  padding: 1.25rem;
  border-radius: 10px;
}

.top_item_bg .statistic_icon_bg {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  width: 66px;
  height: 66px;
  border-radius: 33px;
  background-color: rgba(255, 255, 255, 0.3);
}

.top_item_bg .statistic_icon_bg .statistic_icon {
  width: 32px;
  height: 32px;
}

.top_item_bg .item_content .item_number {
  margin-bottom: 0.25rem;
  font-size: 24px;
  color: white;
  font-weight: bold;
}

.top_item_bg .item_content .item_desc {
  font-size: 14px;
  color: white;
  font-weight: 500;
}
</style>
