<script setup lang="ts" name="ExamStatistics">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance, FormProps } from 'element-plus'
import { ExamRespVO, ExamStaticsAllRespVO, exportExamInfo, listExam, staticsAllExam } from '@/api/topicMgt/exam'
import { listTopicAll } from '@/api/category/topic'
import SubjectSelect from '@/components/SubjectSelect/index.vue'
import examTotalIcon from '@/assets/images/exam/exam_total.png'
import examNotStartIcon from '@/assets/images/exam/exam_not_started.png'
import examInProgressIcon from '@/assets/images/exam/exam_in_progress.png'
import examExpiredIcon from '@/assets/images/exam/exam_expired.png'
import download from "@/utils/download"

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
// const RefReassign = ref()
// const isVisible = ref<boolean>(false)
const queryRef = ref()
const loading = ref(false)
const statusList = ref([t('examMgt.exam.notStarted'), t('examMgt.exam.inProcess'), t('examMgt.exam.expired')])
const total = ref(0)
const tableData = ref<ExamRespVO[]>([])
const staticsData = ref<ExamStaticsAllRespVO[]>()
const optionsSubject = ref([
  {
    id: 0,
    name: t('common.noSubject'),
  },
])
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    classifyId: undefined,
    name: undefined,
    status: undefined,
  },
})
const { queryParams } = toRefs(data)
// 查找
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    classifyId: undefined,
    name: undefined,
    status: undefined,
  }
  queryRef.value?.resetFields()
}
// 获取subject列表
const getSubjectData = async () => {
  const data = await listTopicAll()
  optionsSubject.value.push(...data)
}
// 获取考试列表
const getList = async () => {
  loading.value = true
  try {
    const res = await listExam(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// 查询考试统计
const getStaticsAllExam = async () => {
  queryParams.value.id = route.params.id
  staticsData.value = await staticsAllExam()
}
// 查看考试详情
const handleView = (row: any) => {
  router.push({ name: 'ExamStatisticsDetail', query: { examId: row.id } })
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportExamInfo(queryParams.value)
    download.excel(data, `Exam statistics-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}
onMounted(() => {
  getList()
  getSubjectData()
  getStaticsAllExam()
})
</script>

<template>
  <div class="app-container">
    <div>
      <ContentWrap>
        <el-row :gutter="30" class="p-5">
          <el-col :span="6">
            <div class="flex items-center h-[100px] p-5 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900]">
              <div class="flex items-center justify-center mr-[20px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
                <el-image class="w-[32px] h-[32px]" :src="examTotalIcon" fit="fill" />
              </div>
              <div class="w-[110px]">
                <div class="text-[14px] text-white font-medium">
                  {{ t('statistics.exam.totalExams') }}
                </div>
                <div class="mb-1 text-[24px] text-white font-bold">
                  {{ staticsData?.examNum }}
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="flex items-center h-[100px] p-5 rounded-[10px] bg-gradient-to-r from-[#34AB76] to-[#027B45]">
              <div class="flex items-center justify-center mr-[20px] w-[66px] h-[66px] rounded-[33px] bg-[#6FC29D] bg-opacity-30;">
                <el-image class="w-[32px] h-[32px]" :src="examNotStartIcon" fit="fill" />
              </div>
              <div class="w-[110px]">
                <div class="text-[14px] text-white font-medium">
                  {{ t('examMgt.exam.notStarted') }}
                </div>
                <div class="mb-1 text-[24px] text-white font-bold">
                  {{ staticsData?.notStartedNum }}
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="flex items-center h-[100px] p-5 rounded-[10px] bg-gradient-to-r from-[#2F87A7] to-[#01527A]">
              <div class="flex items-center justify-center mr-[20px] w-[66px] h-[66px] rounded-[33px] bg-[#69A6BD] bg-opacity-30;">
                <el-image class="w-[32px] h-[32px]" :src="examInProgressIcon" fit="fill" />
              </div>
              <div class="w-[110px]">
                <div class="text-[14px] text-white font-medium">
                  {{ t('examMgt.exam.inProcess') }}
                </div>
                <div class="mb-1 text-[24px] text-white font-bold">
                  {{ staticsData?.inProgressNum }}
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="flex items-center h-[100px] p-5 rounded-[10px] bg-gradient-to-r from-[#2C5BA4] to-[#061E7F]">
              <div class="flex items-center justify-center mr-[20px] w-[66px] h-[66px] rounded-[33px] bg-[#6A89BD] bg-opacity-30;">
                <el-image class="w-[32px] h-[32px]" :src="examExpiredIcon" fit="fill" />
              </div>
              <div class="w-[110px]">
                <div class="text-[14px] text-white font-medium">
                  {{ t('examMgt.exam.expired') }}
                </div>
                <div class="mb-1 text-[24px] text-white font-bold">
                  {{ staticsData?.expiredNum }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

      </ContentWrap>
      <!-- 查询条件 -->
      <ContentWrap>
        <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
          <el-form-item :label="t('setting.banner.title')">
            <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable class="!w-180px" @keydown.enter="handleSearch" />
          </el-form-item>
          <el-form-item :label="t('common.status')">
            <el-select v-model="queryParams.status" :placeholder="t('common.chooseText')" clearable class="!w-180px">
              <el-option :label="t('examMgt.exam.notStarted')" value="0" />
              <el-option :label="t('examMgt.exam.inProcess')" value="1" />
              <el-option :label="t('examMgt.exam.expired')" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('category.topic.subjectName')">
            <!-- <el-select v-model="queryParams.classifyId" placeholder="Please choose" clearable style="width: 240px">
              <el-option v-for="item in optionsSubject" :key="item.id" :label="item.name" :value="item.id" />
            </el-select> -->
            <SubjectSelect v-model="queryParams.classifyId" :has-no-subject="true" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon class="mr-5px" icon="ep:search" />
              {{ t('action.search') }}
            </el-button>
            <el-button type="default" @click="handleReset">
              <Icon class="mr-5px" icon="ep:refresh" />
              {{ t('action.reset') }}
            </el-button>
            <el-button plain type="primary" @click="handleExport">
              <Icon class="mr-5px" icon="ep:download" />
              {{ t('action.export') }}
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <el-table v-loading="loading" :data="tableData">
          <el-table-column fixed="left" prop="name" :label="t('examMgt.exam.examName')" min-width="240" />
          <el-table-column prop="classifyName" :label="t('category.topic.subjectName')" min-width="240">
            <template #default="{ row }">
              {{ !row.classifyId || row.classifyId === 0 ? t('common.noSubject') : row.classifyName }}
            </template>
          </el-table-column>
          <el-table-column prop="type" :label="t('learningCenter.course.type')" min-width="170">
            <template #default="{ row }">
              {{ row.type === 1 ? t('examMgt.paper.autoPaper') : t('learningCenter.exam.customizedPaper') }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="name" label="Department" min-width="240" align="center" /> -->
          <el-table-column prop="department" :label="t('examMgt.exam.examTime')" min-width="180">
            <template #default="{ row }">
              <p>
                {{ row.beginTime }}
              </p>
              <p>
                {{ row.endTime }}
              </p>
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="t('common.status')" width="95">
            <template #default="{ row }">
              <div>
                {{ statusList[row.status] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="totalNum" :label="t('learningCenter.boarding.mandatory')" width="110" />
          <!-- <el-table-column prop="quizScore" label="Required Users" width="180" align="center" /> -->
          <el-table-column prop="completedNum" :label="t('statistics.course.completedUsersOfRequired')" width="240" />
          <el-table-column prop="notCompletedNum" :label="t('statistics.course.uncompletedUsersOfRequired')" width="250" />
          <el-table-column prop="passRate" :label="t('statistics.exam.passRates')" width="100">
            <template #default="{ row }">
              <div>
                {{ `${row.passRate ? row.passRate : 0}%` }}
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" :label="t('global.action')" width="100" align="center">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row)">
                <Icon icon="ep:View" />
                {{ t('action.detail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
      </ContentWrap>
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
