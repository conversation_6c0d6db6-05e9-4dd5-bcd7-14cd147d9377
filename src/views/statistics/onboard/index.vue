<script setup lang="ts" name="OnboardStatistics">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import { setBlockTracking } from 'vue'
import PicCard from './components/PicCard.vue'
import router from '@/router'
import {countTableData, exportData, tableList} from '@/api/statistics/onboarding'
import { getOnboardingCategory } from '@/api/topicMgt/onboarding'
import download from "@/utils/download"
import { dateFormatter } from '@/utils/formatTime'
interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  introduction: string
  departmentId: string
  title: string
  ack: boolean
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const countData = ref()
const loading = ref(false)
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryRef = ref()
const categoryList = ref()
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    title: undefined,
    ack: undefined
  }
})
const { queryParams } = toRefs(data)
const picCardData = ref()
function handleSearch() {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    title: undefined,
    ack: undefined
  }
  queryRef.value?.resetFields()
}
/** Onboarding的详情 */
const handleDetail = (row: DataItem) => {
  router.push({ name: 'OnboardDetailStatis', params: { id: row.id } })
}
/** 查询category列表，没有分页 */
const getOnboardingCategoryList = async () => {
  categoryList.value = await getOnboardingCategory()
}
/** 获取顶部的统计信息 */
const getCountData = async () => {
  const data = await countTableData('')
  countData.value = data
  const resdatalist = data.list
  /** 顶部的统计样式及数值数据:拼接顶部统计的样式及数据展示 */
  const statisData = resdatalist.filter((itt: any) => {
    if (itt.categoryName) {
      return itt.categoryName
    }
  })
  statisData.forEach((item: any) => {
    item.text = item.categoryName
    item.number = item.totalCount ? item.totalCount : 0
    item.tipl = 'Mandatory'
    item.tiplnum = item.mandatoryTotalCount ? item.mandatoryTotalCount : 0
    item.tipr = 'Optional'
    item.tiprnum = item.optionalTotalCount ? item.optionalTotalCount : 0
  })
  statisData.unshift({
    bg: 'bg-gradient-to-r from-[#6EAA32] to-[#407D04]',
    text: 'All Onboarding',
    number: countData.value.allCountInfo.totalCount ? countData.value.allCountInfo.totalCount : 0,
    tipl: 'Mandatory',
    tiplnum: countData.value.allCountInfo.mandatoryTotalCount ? countData.value.allCountInfo.mandatoryTotalCount : 0,
    tipr: 'Optional',
    tiprnum: countData.value.allCountInfo.optionalTotalCount ? countData.value.allCountInfo.optionalTotalCount : 0
  })
  picCardData.value = statisData

}
/** 获取Table的数据 */
const getList = async () => {
  loading.value = true
  try {
    const res = await tableList(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportData(queryParams.value)
    download.excel(data, `Onboarding statistics-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}
onMounted(() => {
  getList()
  getCountData()
  getOnboardingCategoryList()
})
</script>

<template>
  <div class="app-container">
    <!-- 顶部统计内容 -->
    <ContentWrap>
      <PicCard :data="picCardData" />
    </ContentWrap>
    <!-- Search -->
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
        <el-form-item :label="t('category.journey.title')" prop="title">
          <el-input v-model="queryParams.title" :placeholder="t('common.inputText')" clearable class="!w-300px" @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('category.journey.categoryTitle')" prop="categoryId">
          <el-select v-model="queryParams.categoryId" :placeholder="t('common.selectText')" clearable filterable class="!w-240px">
            <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button plain type="primary" @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />
            {{ t('action.export') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData">
        <el-table-column :label="t('learningCenter.boarding.title')" prop="title" align="left" min-width="280" fixed="left" />
        <el-table-column :label="t('learningCenter.journey.categoryTitle')" prop="categoryName" min-width="180" />
        <el-table-column :label="t('setting.banner.sort')" prop="sort" min-width="100" />
        <el-table-column :label="t('category.journey.creator')" min-width="200" />
        <el-table-column :label="t('category.journey.creationTime')" prop="createTime" :formatter="dateFormatter" min-width="200" />
        <el-table-column fixed="right" :label="t('global.action')" min-width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleDetail(row)">
              <Icon icon="ep:view" />
              {{ t('action.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>
  </div>
</template>

<style scoped lang="scss"></style>
