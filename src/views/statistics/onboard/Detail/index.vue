<script setup lang="ts" name="OnboardDetailStatis">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import { OCCStatus } from '@/views/statistics/cmp/enums/status'
import PicAreaCard from '@/views/statistics/cmp/components/PicAreaCard.vue'
import { detailCountData, detailList, exportAssignData, OnboardingDetailPageRespVO } from '@/api/statistics/onboarding'
import { detailOnboarding } from '@/api/topicMgt/onboarding'
import { formatImgUrl } from '@/utils/index'
import download from "@/utils/download"
import {useTagsViewStore} from "@/store/modules/tagsView";
import { dateFormatter } from '@/utils/formatTime'


const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { push, currentRoute } = useRouter() // 路由
const countData = ref()
const loading = ref(false)
const tableData = ref<OnboardingDetailPageRespVO[]>([])
const total = ref(0)
const queryRef = ref()
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    empName: undefined,
    email: undefined,
    status: undefined,
  },
})
const { queryParams } = toRefs(data)
const route = useRoute()
const statusList = [
  { label: t('examMgt.exam.notStarted'), value: OCCStatus.NotStart, id: '2' },
  { label: t('examMgt.exam.inProcess'), value: OCCStatus.InProgress, id: '3' },
  { label: t('statistics.course.completed'), value: OCCStatus.Completed, id: '4' },
]
const titleData = ref()
const imgUrl = ref()
const handleBack = () => {
  delView(unref(currentRoute))
  push('/statistics/onboard-statistics')
}
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    empName: undefined,
    status: undefined,
    email: undefined,
  }
  queryRef.value?.resetFields()
}
/** 顶部title和img数据 */
const getCoverTitle = async () => {
  const data = await detailOnboarding(route.params.id)
  titleData.value = data
  // imgUrl.value = data.cover
  if (titleData.value.cover.includes('https')) {
    imgUrl.value = data.cover
  }
  else {
    imgUrl.value = formatImgUrl(titleData?.value.cover)
  }
}
/** 详情列表数据 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.value.onBoardingId = route.params.id
    const res = await detailList(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
const getCountData = async () => {
  const data = await detailCountData(route.params.id)
  countData.value = data
  countData.value.forEach((item: any) => {
    if (item.status === OCCStatus.NotStart) {
      item.icon = 'NotStart'
      item.bg = 'bg-gradient-to-r from-[#6FAA34] to-[#86AC5F]'
    }
    if (item.status === OCCStatus.Completed) {
      item.icon = 'Complete'
      item.bg = 'bg-gradient-to-r from-[#31A873] to-[#0A824C]'
      item.color = '#2CA36E'
    }
    if (item.status === OCCStatus.InProgress) {
      item.icon = 'Inprocess'
      item.bg = 'bg-gradient-to-r from-[#2A81A2] to-[#0A5C83]'
    }
    item.text = item.statusName	? item.statusName : 'N/A'
    item.number = item.count ? item.count : 0
  })
}
/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportAssignData(queryParams.value)
    download.excel(data, `Onboarding-${titleData?.value.title}-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}
onMounted(() => {
  getList()
  getCountData()
  getCoverTitle()
})
</script>

<template>
  <div class="app-container">
    <!-- 顶部图片区域 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <div class="flex gap-5 mr-16">
        <img v-if="imgUrl" :src="imgUrl" class="w-[154px] h-[90px] row-span-2" />
        <img v-else src="../../../../assets/images/commonImg.png" class="w-[154px] h-[90px] row-span-2" />
        <div class="pt-2.5">
          <span class="text-[#23293A] text-xl line-clamp-1 break-all">{{ titleData?.title }}</span>
          <div v-if="titleData?.attachmentList" class="flex gap-1.5 mt-6">
            <div class="w-5 h-5 rounded-[4px] flex items-center">
              <!-- <svg-icon icon-class="CourseTopic" class="text-xs" /> -->
              <el-tag type="warning">
                {{ titleData?.attachmentList[0].fileType }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
      <el-button class="ms-auto flex-shrink-0" type="primary" @click="handleBack">
        {{ t('action.back') }}
      </el-button>
    </div>
    <!-- 统计内容 -->
    <div class="mt-[21px] mb-[25px]">
      <PicAreaCard :data="countData" />
    </div>
    <!-- Search -->
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
        <el-form-item :label="t('examMgt.paper.name')" prop="empName">
          <el-input v-model="queryParams.empName" :placeholder="t('common.inputText')" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('learningCenter.boarding.badgeNumber')" prop="empCode">
          <el-input v-model="queryParams.empCode" :placeholder="t('common.inputText')" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.email')" prop="email">
          <el-input v-model="queryParams.email" :placeholder="t('common.inputText')" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('common.status')" prop="status">
          <el-select v-model="queryParams.status" :placeholder="t('common.chooseText')" clearable class="!w-200px">
            <el-option v-for="item in statusList" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button plain type="primary" @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />
            {{ t('action.export') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- Table -->
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData" style="width: 100%">
        <el-table-column :label="t('examMgt.paper.name')" prop="empName" align="left" :width="280" fixed="left" />
        <el-table-column :label="t('learningCenter.boarding.badgeNumber')" prop="empCode" :width="180" />
        <el-table-column :label="t('learningCenter.course.email')" prop="email" :min-width="280" />
        <el-table-column :label="t('learningCenter.course.company')" prop="companyName" :width="150" />
        <el-table-column :label="t('learningCenter.course.deptName')" prop="deptName" :width="150" />
        <el-table-column :label="t('learningCenter.course.section')" prop="sectionName" :width="350" />
        <el-table-column :label="t('learningCenter.course.position')" prop="positionName" :width="350" />
        <el-table-column :label="t('category.journey.creator')" prop="createBy" :width="150" />
        <el-table-column :label="t('category.journey.creationTime')" prop="createTime" :formatter="dateFormatter" :width="280" />
        <el-table-column :label="t('common.status')" prop="statusName" width="120" fixed="right" />
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>
  </div>
</template>

<style scoped lang="scss"></style>
