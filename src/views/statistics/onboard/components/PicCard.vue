<script setup lang="ts">
const props = defineProps<{
  data: any
}>()
const staticsBgList = ref(['bg-gradient-to-r from-[#6EAA32] to-[#407D04]', 'bg-gradient-to-r from-[#32A974] to-[#07804A]', 'bg-gradient-to-r from-[#2D85A5] to-[#04567D]', 'bg-gradient-to-r from-[#2B59A3] to-[#071F80]', 'bg-gradient-to-r from-[#BAAB54] to-[#A6AC07]'])

const getBgStyle = (index: number) => {
  if (index > 4) {
    return staticsBgList.value[4]
  }
  return staticsBgList.value[index]
}
</script>

<template>

  <div>
      <el-scrollbar class="scrollbar-wrapper">
        <div class="flex gap-7 mb-3">
          <div v-for="(item, index) in props.data" :key="index" class="shadow-[0_0_20px_0_rgba(19,116,0,0.1)] bg-gradient-to-b pt-[25px] pb-[10px] ps-[10px] pe-[10px] flex-1" style="border-radius: 15px" :class="getBgStyle(index)">
            <div class="w-[400px]">
              <div class="flex justify-between mb-4">
                <span class="text-lg text-white ml-5">{{ item.text }}</span>
                <span class="font-bold text-2xl text-white mr-7">{{ item.number }}</span>
              </div>
              <div class="flex flex-1 bg-white justify-between" style="border-radius: 15px">
                <div class="flex flex-col py-[18px] ml-5">
                  <span class="text-sm text-[#727984]">{{ item.tipl }}</span>
                  <span class="font-bold text-2xl text-[#222] mt-2">{{ item.tiplnum }}</span>
                </div>
                <div class="flex flex-col py-[18px] mr-5">
                  <span class="text-sm text-[#727984]">{{ item.tipr }}</span>
                  <span class="font-bold text-2xl text-[#222] mt-2">{{ item.tiprnum }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
</template>

<style scoped lang="scss"></style>
