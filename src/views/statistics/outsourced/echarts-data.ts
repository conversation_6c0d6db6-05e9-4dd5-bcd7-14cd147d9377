import { EChartsOption } from 'echarts'
const { t } = useI18n()
// ================ Internal统计数据 =================
// 各个Training Type占总External Training的百分比
export const percentageOptions = {
  tooltip: {
    trigger: 'item',
  },
  legend: {
    bottom: 'bottom'
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: true,
      label: {
        show: true, // 显示标签
        formatter: '{d}%', // 格式化标签以显示百分比
        fontSize: 12 // 可选：设置字体大小
      },
      emphasis: {
        label: {
          show: true, // 强调状态下也显示标签
          fontSize: '18', // 在强调状态下的字体大小
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: true // 显示引导线
      },
      data: [
        { value: 0, name: 'Course' },
        { value: 0, name: 'Training' },
        { value: 0, name: 'Visit' },
        { value: 0, name: 'Conference' },
        { value: 0, name: 'Lecture' },
        { value: 0, name: 'Meeting' },
        { value: 0, name: 'Workshop' },
      ]
    }
  ]
};
// 不同状态的学生数量
export const attendingOptions = {
  tooltip: {
    trigger: 'item',
  },
  legend: {
    bottom: 'bottom',
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: true,
      label: {
        show: true, // 显示标签
        formatter: '{d}%', // 格式化标签以显示百分比
        fontSize: 12 // 可选：设置字体大小
      },
      emphasis: {
        label: {
          show: true, // 强调状态下也显示标签
          fontSize: '18', // 在强调状态下的字体大小
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: true // 显示引导线
      },
      data: [
        { value: 0, name: 'Attended' },
        { value: 0, name: 'Cancel Nomination' },
        { value: 0, name: 'Candidate' },
        { value: 0, name: 'Didn\'t Travel' },
        { value: 0, name: 'Report to attend' },
        { value: 0, name: 'Supervisor' },
      ]
    }
  ]
};
// 参加各个公司Internal Training的学生数量 (学员都是BOC的人)
export const companyStudentNumberOptions = {
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [],
      type: 'bar',
      barWidth: 20, // 设置柱子宽度为固定值
      barCategoryGap: '10px', // 调整柱子之间的距离
    }
  ],
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
};
// 参加各个部门Internal Training的学生人数
export const deptStudentNumberOptions = {
  tooltip: {},
  grid: {
    left: '20%', // 设置绘图区左侧距离整个图表容器的百分比
  },
  xAxis: {
    type: 'value',
    data: []
  },
  yAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      formatter: function (value) {
        // 示例：如果标签长度超过15个字符，则截取前10个字符并加上"..."
        return value.length > 15 ? value.slice(0, 10) + '...' : value;
      },
    }
  },
  series: [{
    type: 'bar',
    barWidth: 10,
    data: []
  }],
  dataZoom: [
    {
      type: 'slider',   // 滑块型 dataZoom
      orient: 'vertical', // 垂直方向的数据区域缩放
      start: 0,         // 初始显示从第一个数据开始
      end: 50,          // 显示前 50% 数据
      width: 20,        // 滚动条宽度
      top: 'top',       // 放置在顶部
      handleSize: '80%' // 手柄大小
    }
  ]
};

// ================ External统计数据 =================

export const companyStudentOptions = {
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [],
      type: 'bar',
      barWidth: 20, // 设置柱子宽度为固定值
      barCategoryGap: '10px', // 调整柱子之间的距离
    }
  ],
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
};
export const counterStudentOptions = {
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [],
      type: 'bar',
      barWidth: 20, // 设置柱子宽度为固定值
      barCategoryGap: '10px', // 调整柱子之间的距离
    }
  ],
  dataZoom: [{
    type: 'slider',   // 滑块型 dataZoom
    start: 0,         // 初始显示从第一个数据开始
    end: 40,          // 显示前 40% 数据
    height: 20,       // 滚动条高度
    bottom: 10,       // 放在底部
    handleSize: '80%' // 手柄大小
  }],
};
export const deptStudentOptions = {
  tooltip: {},
  grid: {
    left: '20%', // 设置绘图区左侧距离整个图表容器的百分比
  },
  xAxis: {
    type: 'value',
    data: []
  },
  yAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      formatter: function (value) {
        // 示例：如果标签长度超过15个字符，则截取前10个字符并加上"..."
        return value.length > 15 ? value.slice(0, 10) + '...' : value;
      },
    }
  },
  series: [{
    type: 'bar',
    barWidth: 10,
    data: []
  }],
  dataZoom: [
    {
      type: 'slider',   // 滑块型 dataZoom
      orient: 'vertical', // 垂直方向的数据区域缩放
      start: 0,         // 初始显示从第一个数据开始
      end: 50,          // 显示前 50% 数据
      width: 20,        // 滚动条宽度
      top: 'top',       // 放置在顶部
      handleSize: '80%' // 手柄大小
    }
  ]
};


