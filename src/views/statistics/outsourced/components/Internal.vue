<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="150px"
    >
      <el-form-item label="Type" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INTERNAL_TRAINING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Place" prop="place">
        <el-select
          v-model="queryParams.place"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in placeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Company" prop="implementingCompany">
        <el-select
          v-model="queryParams.implementingCompany"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in companyOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Time" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="Start"
          end-placeholder="End"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Course Title(English)" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="Please input"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="Course Title(Arabic)" prop="titleAr">
        <el-input
          v-model="queryParams.titleAr"
          placeholder="Please input"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="Training Code" align="center" prop="code" min-width="180px" />
      <el-table-column label="Course Title EN" align="center" prop="title" min-width="180px" />
      <el-table-column label="Course Title AR" align="center" prop="titleAr" min-width="180px" />
      <el-table-column label="Type" align="center" prop="type" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INTERNAL_TRAINING_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="Place" align="center" prop="place" min-width="180px" />
      <el-table-column
        label="Start & End Date"
        align="center"
        :formatter="dateFormatter"
        width="180px">
        <template #default="scope">
          <div>
            {{ scope.row.startDate }} - {{ scope.row.endDate }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Company" align="center" prop="implementingCompany" min-width="180px" />
      <el-table-column label="Course Duration" align="center" prop="duration" min-width="180px">
        <template #default="scope">
          {{ scope.row.duration }} days
        </template>
      </el-table-column>
      <el-table-column label="Student Number" align="center" prop="userCount" min-width="180px" />
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

</template>

<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InternalApi, InternalConditionReqVO, InternalRespVO } from '@/api/academy/outsourced/internal'

import { formatDateArray } from "@/utils/formatDate"


const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<InternalRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  titleAr: undefined,
  type: undefined,
  startTime: [],
  endTime: [],
  implementingCompany: undefined,
  place: undefined,
})
const studentNumberRef = ref()
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const placeOptions = ref([])
const companyOptions = ref([])
const internalAllList = ref([])
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InternalApi.getInternalPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const getAllList = async () => {
  const data = await InternalApi.getInternalPage({pageNo: 1, pageSize: -1})
  internalAllList.value = data.list
  placeOptions.value = Array.from(new Set(data.list?.map((item: InternalRespVO) => item.place)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
  companyOptions.value = Array.from(new Set(data.list?.map((item: InternalRespVO) => item.implementingCompany)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}


/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InternalApi.exportInternal(queryParams)
    download.excel(data, 'Internal.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
defineExpose({ internalAllList })

/** 初始化 **/
onMounted(() => {
  getList()
  getAllList()
})
</script>
