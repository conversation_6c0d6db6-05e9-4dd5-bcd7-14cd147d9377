<template>
  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24" class="mb-8px border-2">
      <el-row :gutter="20">
        <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
          <ContentWrap class="pie-card_wrapper h-[600px]">
            <el-skeleton :loading="skeletonLoading" animated>
              <div class="text-center font-bold mb-2">
                Percentage of Training Type
              </div>
              <div class="w-[350px] flex mx-auto">
                <span class="w-[200px] truncate">Course</span>
<!--              0代表下标  1代表的是对象的key值-->
                <span class="w-[100px]">{{ trainingTypeInfo[0]?.['1'] }}</span>
                <span class="w-[50px]">{{ percentage(trainingTypeInfo[0]?.['1'],total) }}%</span>
              </div>
              <div class="w-[350px] flex mx-auto">
                <span class="w-[200px] truncate">Training</span>
                <span class="w-[100px] ">{{ trainingTypeInfo[1]?.['2'] }}</span>
                <span class="w-[50px]">{{ percentage(trainingTypeInfo[1]?.['2'],total) }}%</span>
              </div>
              <div class="w-[350px] flex mx-auto">
                <span class="w-[200px] truncate">Visit</span>
                <span class="w-[100px]">{{ trainingTypeInfo[2]?.['3'] }}</span>
                <span class="w-[50px]">{{ percentage(trainingTypeInfo[2]?.['3'],total) }}%</span>
              </div>
              <div class="w-[350px] flex mx-auto">
                <span class="w-[200px] truncate">Conference</span>
                <span class="w-[100px]">{{ trainingTypeInfo[3]?.['4'] }}</span>
                <span class="w-[50px]">{{ percentage(trainingTypeInfo[3]?.['4'],total) }}%</span>
              </div>
              <div class="w-[350px] flex mx-auto">
                <span class="w-[200px] truncate">Lecture</span>
                <span class="w-[100px]">{{ trainingTypeInfo[4]?.['5'] }}</span>
                <span class="w-[50px]">{{ percentage(trainingTypeInfo[4]?.['5'],total) }}%</span>
              </div>
              <div class="w-[350px] flex mx-auto">
                <span class="w-[200px] truncate">Meeting</span>
                <span class="w-[100px]">{{ trainingTypeInfo[5]?.['6'] }}</span>
                <span class="w-[50px]">{{ percentage(trainingTypeInfo[5]?.['6'],total) }}%</span>
              </div>
              <div class="w-[350px] flex mx-auto">
                <span class="w-[200px] truncate">Workshop</span>
                <span class="w-[100px]">{{ trainingTypeInfo[6]?.['7'] }}</span>
                <span class="w-[50px]">{{ percentage(trainingTypeInfo[6]?.['7'],total) }}%</span>
              </div>
              <div class="w-[350px] flex mx-auto font-bold mb-5">
                <span class="w-[200px] truncate">Total</span>
                <span class="w-[100px]">{{ total }}</span>
                <span class="w-[50px]">{{ total > 0 ? '100%' : '0%'}}</span>
              </div>
              <div ref="percentageRef" class="h-[300px]"></div>
            </el-skeleton>
          </ContentWrap>
        </el-col>
        <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
          <ContentWrap class="pie-card_wrapper h-[600px]">
            <el-skeleton :loading="skeletonLoading" animated>
              <div class="text-center font-bold mb-2">
                Attending Status
              </div>
              <div class="w-[300px] flex mx-auto">
                <span class="w-[200px] truncate">Attended</span>
                <span class="w-[100px] font-bold text-center" >{{ userStatusInfo[0]?.['1'] }}</span>
              </div>
              <div class="w-[300px] flex mx-auto">
                <span class="w-[200px] truncate">Cancel Nomination</span>
                <span class="w-[100px] font-bold text-center">{{ userStatusInfo[1]?.['2'] }}</span>
              </div>
              <div class="w-[300px] flex mx-auto">
                <span class="w-[200px] truncate">Candidate</span>
                <span class="w-[100px] font-bold text-center">{{ userStatusInfo[2]?.['3'] }}</span>
              </div>
              <div class="w-[300px] flex mx-auto">
                <span class="w-[200px] truncate">Didn't Travel</span>
                <span class="w-[100px] font-bold text-center">{{ userStatusInfo[3]?.['4'] }}</span>
              </div>
              <div class="w-[300px] flex mx-auto">
                <span class="w-[200px] truncate">Not subscribed</span>
                <span class="w-[100px] font-bold text-center">{{ userStatusInfo[4]?.['5'] }}</span>
              </div>
              <div class="w-[300px] flex mx-auto mb-18">
                <span class="w-[200px] truncate">Report to attend</span>
                <span class="w-[100px] font-bold text-center">{{ userStatusInfo[5]?.['6'] }}</span>
              </div>
              <div class="w-[300px] flex mx-auto mb-18">
                <span class="w-[200px] truncate">Supervisor</span>
                <span class="w-[100px] font-bold text-center">{{ userStatusInfo[6]?.['7'] }}</span>
              </div>
              <div ref="attendingRef" class="h-[300px]"></div>
            </el-skeleton>
          </ContentWrap>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <ContentWrap class="pie-card_wrapper">
            <el-skeleton :loading="skeletonLoading" animated>
              <div class="text-center font-bold mb-2">
                Company Student Number
              </div>
              <div ref="companyStudentRef" class="h-[300px]"></div>
            </el-skeleton>
          </ContentWrap>
        </el-col>
      </el-row>
    </el-col>
    <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24" class="mb-8px border-2">
      <ContentWrap class="pie-card_wrapper">
        <el-skeleton :loading="skeletonLoading" animated>
          <div class="text-center font-bold mb-2">
            Department Student Number
          </div>
          <div ref="deptStudentRef" class="h-[915px]"></div>
        </el-skeleton>
      </ContentWrap>
    </el-col>
  </el-row>
</template>
<script setup lang="ts" name="InternalStatistics">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { set } from 'lodash-es'
import { EChartsOption } from 'echarts'
import * as echarts from 'echarts'
import * as ExcelJS from 'exceljs'
import html2canvas from 'html2canvas'
import {saveAs} from "file-saver"

import {
  percentageOptions,
  attendingOptions,
  deptStudentNumberOptions, companyStudentNumberOptions
} from "@/views/statistics/outsourced/echarts-data"
import { OutsourcedTrainingApi } from  '@/api/statistics/outsourcedTraining'
import {getMonthStartAndEnd} from "@/utils/formatDate";
const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const skeletonLoading = ref(true)
const percentageOptionsData = reactive<EChartsOption>(percentageOptions) as EChartsOption
const attendingOptionsData = reactive<EChartsOption>(attendingOptions) as EChartsOption
const companyStudentNumberOptionsData = reactive<EChartsOption>(companyStudentNumberOptions) as EChartsOption
const deptStudentNumberOptionsData = reactive<EChartsOption>(deptStudentNumberOptions) as EChartsOption

const percentageRef = ref<HTMLElement | null>(null)
const attendingRef = ref<HTMLElement | null>(null)
const companyStudentRef = ref<HTMLElement | null>(null)
const deptStudentRef = ref<HTMLElement | null>(null)

const percentageChartInstance = ref()
const attendingChartInstance = ref()
const companyStudentChartInstance = ref()
const deptStudentChartInstance = ref()

const queryForm = reactive({
  categoryId: undefined,
  courseTitle: undefined,
  startDate: undefined,
  endDate: undefined
})
const trainingTypeList = ref([])
const trainingTypeInfo = ref([])
const total = ref(0)
const userStatusList = ref([])
const userStatusInfo = ref([])

// 数据组装方法
const percentageList = (list: string[], data: string[]) => {
  // 根据key进行匹配,成功将value值给到对应的对象
  const result = list.map(item => {
    const key = Object.keys(item)[0] // 获取当前对象的 key，例如 "1"
    const value = data[key] !== undefined ? data[key] : item[key] // 如果 data 中有该 key 就用它的值，否则保留默认值
    return { [key]: value }
  })
  return result
}

// Percentage of Training Type 饼图(统计信息-国内培训类型数量)
const getInternalTypeTotal = async () => {
  const data = await OutsourcedTrainingApi.getInternalTypeTotal(queryForm)
  trainingTypeInfo.value = percentageList(trainingTypeList.value, data)
  // 计算所有类型值，将所有的value值加起来
  total.value = trainingTypeInfo.value.reduce((sum, obj) => {
    const value = obj[Object.keys(obj)[0]] // 获取当前对象的 value
    return sum + value
  }, 0)
  // 将数据插入到echarts图表中
  for (let i = 0; i < trainingTypeInfo.value.length; i++) {
    const key = String(i + 1);
    if (trainingTypeInfo.value[i]?.hasOwnProperty(key)) {
      percentageOptionsData!.series![0].data[i].value = trainingTypeInfo.value[i][key];
      percentageChartInstance.value = echarts.init(percentageRef.value)
      percentageChartInstance.value.setOption(percentageOptionsData)
    }
  }
}

// Attending Status 饼图(统计信息-国内培训用户状态数量)
const getInternalUserTotal = async () => {
  const data = await OutsourcedTrainingApi.getInternalUserTotal(queryForm)
  userStatusInfo.value = percentageList(userStatusList.value, data)
  // 将数据插入到echarts图表中
  for (let i = 0; i < userStatusInfo.value.length; i++) {
    const key = String(i + 1);


    if (userStatusInfo.value[i]?.hasOwnProperty(key)) {
      attendingOptionsData!.series![0].data[i].value = userStatusInfo.value[i][key];
      attendingChartInstance.value = echarts.init(attendingRef.value)
      attendingChartInstance.value.setOption(attendingOptionsData)
    }
  }


}
// Company Student Number (统计信息-国内培训公司用户数量)
const getInternalCompanyTotal = async () => {
  const data = await OutsourcedTrainingApi.getInternalCompanyTotal(queryForm)
  const allKeys = Object.keys(data)
  companyStudentNumberOptionsData!.xAxis.data = allKeys
  const allValues = Object.values(data)
  companyStudentNumberOptionsData!.series![0].data = allValues
  companyStudentChartInstance.value = echarts.init(companyStudentRef.value)
  companyStudentChartInstance.value.setOption(companyStudentNumberOptionsData)
}
// Department Student Number (统计信息-国内培训部门用户数量)
const getInternalDeptTotal = async () => {
  const data = await OutsourcedTrainingApi.getInternalDeptTotal(queryForm)
  const allKeys = Object.keys(data)
  const allValues = Object.values(data)
  deptStudentNumberOptionsData!.yAxis.data = allKeys
  deptStudentNumberOptionsData!.series![0].data = allValues
  deptStudentChartInstance.value = echarts.init(deptStudentRef.value)
  deptStudentChartInstance.value.setOption(deptStudentNumberOptionsData)
}
// 获取百分比
const percentage = (statusTotal: number,total: number) => {
  return statusTotal ? parseFloat(((statusTotal / total) * 100).toFixed(2)) : 0
}

const getAllApi = () => {
  const apiCalls = [
    getInternalTypeTotal(),
    getInternalUserTotal(),
    getInternalCompanyTotal(),
    getInternalDeptTotal()
  ]
  // 并行执行所有接口调用，并等待全部完成
  Promise.all(apiCalls)
    .then(() => {
      // 所有接口成功完成后关闭 loading
      skeletonLoading.value = false
    })
    .catch(error => {
      // 错误处理（可选）
      skeletonLoading.value = false
    })
}

const getChartImageBase64 = () => {
  const images = []
  if (percentageChartInstance.value) {
    images.push(
      percentageChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (attendingChartInstance.value) {
    images.push(
      attendingChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (companyStudentChartInstance.value) {
    images.push(
      companyStudentChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (deptStudentChartInstance.value) {
    images.push(
      deptStudentChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }

  return images

}

onMounted(() => {
  // 先获取课程类型字典数据,将字典中的value全都取出来,默认为1 如何使用看getInternalTypeTotal()方法调用
  const trainingTypedata = getIntDictOptions(DICT_TYPE.INTERNAL_TRAINING_TYPE).map((item) => {
    return {
      [item.value]: 0,
    }
  })
  trainingTypeList.value = trainingTypedata
  // 先获取用户状态字典数据,操作同上
  const userStatusData = getStrDictOptions(DICT_TYPE.INTERNAL_USER_STATUS).map((item) => {
    return {
      [item.value]: 0,
    }
  })
  userStatusList.value = userStatusData
  const time = getMonthStartAndEnd(1)
  queryForm.startDate = time[0]
  queryForm.endDate = time[1]
  getAllApi()
})
defineExpose({ queryForm, getAllApi, getChartImageBase64, })
</script>

<style scoped lang="scss">
</style>
