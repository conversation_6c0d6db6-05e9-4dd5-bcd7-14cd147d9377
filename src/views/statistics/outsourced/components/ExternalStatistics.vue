<template>
  <el-row class="mt-8px" :gutter="8" justify="space-between">
    <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24" class="mb-8px border-2">
      <el-row :gutter="20">
        <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
          <ContentWrap class="pie-card_wrapper">
            <el-skeleton :loading="skeletonLoading" animated>
              <div class="text-center font-bold mb-2">
                Company Student Number
              </div>
              <div ref="companyStudentRef" class="h-[400px]"></div>
            </el-skeleton>
          </ContentWrap>
        </el-col>
        <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
          <ContentWrap class="pie-card_wrapper">
            <el-skeleton :loading="skeletonLoading" animated>
              <div class="text-center font-bold mb-2">
                Counter Student Number
              </div>
              <div ref="counterStudentRef" class="h-[400px]"></div>
            </el-skeleton>
          </ContentWrap>
        </el-col>
      </el-row>
    </el-col>
    <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24" class="mb-8px border-2">
      <ContentWrap class="pie-card_wrapper">
        <el-skeleton :loading="skeletonLoading" animated>
          <div class="text-center font-bold mb-2">
            Department Student Number
          </div>
          <div ref="deptStudentRef" class="h-[400px]"></div>
        </el-skeleton>
      </ContentWrap>
    </el-col>
  </el-row>
</template>
<script setup lang="ts" name="InternalStatistics">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { set } from 'lodash-es'
import { EChartsOption } from 'echarts'
import {
  companyStudentOptions, counterStudentOptions,deptStudentOptions
} from "@/views/statistics/outsourced/echarts-data"
import { OutsourcedTrainingApi } from  '@/api/statistics/outsourcedTraining'
import * as echarts from 'echarts'
import * as ExcelJS from 'exceljs'
import html2canvas from 'html2canvas'
import {saveAs} from "file-saver"
import {getMonthStartAndEnd} from "@/utils/formatDate";

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const skeletonLoading = ref(true)
const companyStudentOptionsData = reactive<EChartsOption>(companyStudentOptions) as EChartsOption
const counterStudentOptionsData = reactive<EChartsOption>(counterStudentOptions) as EChartsOption
const deptStudentOptionsData = reactive<EChartsOption>(deptStudentOptions) as EChartsOption

const companyStudentRef = ref<HTMLElement | null>(null)
const counterStudentRef = ref<HTMLElement | null>(null)
const deptStudentRef = ref<HTMLElement | null>(null)

const companyStudentChartInstance = ref()
const counterStudentChartInstance = ref()
const deptStudentChartInstance = ref()

const queryForm = reactive({
  categoryId: undefined,
  courseTitle: undefined,
  startDate: undefined,
  endDate: undefined
})
const total = ref(0)

// Company Student Number (统计信息-国外付费公司用户数量)
const getExternalCompanyTotal = async () => {
  const data = await OutsourcedTrainingApi.getExternalCompanyStudentTotal(queryForm)
  const allKeys = Object.keys(data)
  companyStudentOptionsData!.xAxis.data = allKeys
  const allValues = Object.values(data)
  companyStudentOptionsData!.series![0].data = allValues
  companyStudentChartInstance.value = echarts.init(companyStudentRef.value)
  companyStudentChartInstance.value.setOption(companyStudentOptionsData)
}

// Counter Student Number (统计信息-国外国家用户数量)
const getExternalCountryStudentTotal = async () => {
  const data = await OutsourcedTrainingApi.getExternalCountryStudentTotal(queryForm)
  const allKeys = Object.keys(data)
  counterStudentOptionsData!.xAxis.data = allKeys
  const allValues = Object.values(data)
  counterStudentOptionsData!.series![0].data = allValues
  counterStudentChartInstance.value = echarts.init(counterStudentRef.value)
  counterStudentChartInstance.value.setOption(counterStudentOptionsData)
}
// Department Student Number (统计信息-国外培训部门用户数量)
const getExternalDeptStudentTotal = async () => {
  const data = await OutsourcedTrainingApi.getExternalDeptStudentTotal(queryForm)
  const allKeys = Object.keys(data)
  const allValues = Object.values(data)
  deptStudentOptionsData!.yAxis.data = allKeys
  deptStudentOptionsData!.series![0].data = allValues
  deptStudentChartInstance.value = echarts.init(deptStudentRef.value)
  deptStudentChartInstance.value.setOption(deptStudentOptionsData)
}

const getChartImageBase64 = () => {
  const images = []
  if (companyStudentChartInstance.value) {
    images.push(
      companyStudentChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (counterStudentChartInstance.value) {
    images.push(
      counterStudentChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }
  if (deptStudentChartInstance.value) {
    images.push(
      deptStudentChartInstance.value.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })
    )
  }

  return images
}


const getAllApi = () => {
  const apiCalls = [
    getExternalCompanyTotal(),
    getExternalCountryStudentTotal(),
    getExternalDeptStudentTotal(),
  ]
  // 并行执行所有接口调用，并等待全部完成
  Promise.all(apiCalls)
    .then(() => {
      // 所有接口成功完成后关闭 loading
      skeletonLoading.value = false
    })
    .catch(error => {
      // 错误处理（可选）
      skeletonLoading.value = false
    })
}

defineExpose({ queryForm, getAllApi, getChartImageBase64 })
onMounted(() => {
  const time = getMonthStartAndEnd(1)
  queryForm.startDate = time[0]
  queryForm.endDate = time[1]
  getAllApi()
})
</script>

<style scoped lang="scss">
</style>
