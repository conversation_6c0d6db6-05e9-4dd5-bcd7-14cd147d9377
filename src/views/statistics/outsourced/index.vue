<template>
  <ContentWrap>
    <el-scrollbar class="scrollbar-wrapper">
      <div class="flex">
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px] text-[14px] text-white font-medium">
            Total Courses: {{ courseInfoTotal?.total }}
          </div>
        </div>
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px] text-[14px] text-white font-medium">
            Internal Training: {{ courseInfoTotal?.internalTotal }}
          </div>
        </div>
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px] text-[14px] text-white font-medium">
            External Training: {{ courseInfoTotal?.externalTotal }}
          </div>
        </div>
        <div class="flex items-center h-[100px] p-3 rounded-[10px] bg-gradient-to-r from-[#71AD35] to-[#3D7900] w-[400px] w-[400px] ms-2 me-2">
          <div class="flex items-center justify-center mr-[15px] w-[66px] h-[66px] rounded-[33px] bg-[#97C06C] bg-opacity-30;">
            <svg-icon icon-class="DashboardCourse" class="text-2xl" />
          </div>
          <div class="w-[200px] text-[14px] text-white font-medium">
            Total Students: {{ studentTotal }}
          </div>
        </div>
      </div>
    </el-scrollbar>

  </ContentWrap>
  <!-- 图形展示 -->
  <ContentWrap>
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item name="1">
        <template #title="{ isActive }">
          <div :class="['title-wrapper', { 'is-active': isActive }]" class="flex justify-between w-full">
            <div class="font-bold">Report</div>
            <div class="h-full flex mt-[8px]">
              <el-select
                v-model="timeType"
                placeholder="Please select"
                clearable
                class="!w-180px"
                @change="changeTime"
                @click.stop
              >
                <el-option
                  v-for="dict in timeTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              <el-button
                class="ms-3 me-3"
                link
                type="primary"
                @click="handleExportReport"
                @click.stop
              >
                Export Report
              </el-button>
              <el-date-picker
                v-if="timeType === 5"
                v-model="createTime"
                value-format="YYYY-MM-DD"
                type="daterange"
                start-placeholder="start"
                end-placeholder="end"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-220px"
                @click.stop
                @change="selectTime"
              />
            </div>
          </div>
        </template>
        <el-tabs v-model="tabName" @tab-change="selectActive">
          <el-tab-pane label="Internal" name="1">
            <InternalStatistics ref="internalChartRef"/>
          </el-tab-pane>
          <el-tab-pane label="External" name="2">
            <ExternalStatistics ref="externalChartRef"/>
          </el-tab-pane>
        </el-tabs>
      </el-collapse-item>
    </el-collapse>
  </ContentWrap>

<!--  列表信息-->
  <ContentWrap>
    <el-tabs v-model="tabTrainingName">
      <el-tab-pane label="Internal" name="1">
        <Internal ref="internalRef" />
      </el-tab-pane>
      <el-tab-pane label="External" name="2">
        <External ref="externalRef" />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>
<script setup lang="ts" name="OutsourcedTrainingStatistics">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { handleTree } from "@/utils/tree"
import { getQuarterStartAndEnd } from "@/utils/formatDate"
import { OutsourcedTrainingApi } from  '@/api/statistics/outsourcedTraining'
import InternalStatistics from './components/InternalStatistics.vue'
import ExternalStatistics from './components/ExternalStatistics.vue'
import Internal from './components/Internal.vue'
import External from './components/External.vue'
import download from '@/utils/download'
import { listTopic } from "@/api/category/training"
import {
  getMonthStartAndEnd,
  getWeekStartAndEnd,
  getYearStartAndEnd
} from "@/utils/formatDate"
import { ExternalApi, ExternalRespVO } from "@/api/academy/outsourced/external"
import { saveAs } from 'file-saver'
import { Workbook } from 'exceljs'
import FileSaver from 'file-saver'


const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const queryRef = ref()
const loading = ref(false)
const skeletonLoading = ref(false)
const exportLoading = ref(false)
const total = ref(0)
const activeName = ref('1')
const tabName = ref('1')
const tabTrainingName = ref('1')
const timeType = ref(2)
const categoryList = ref([]) // 课程分类信息
const list = ref([])
const queryFormRef = ref()
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  receivingCountry: undefined,
  travelDate: [],
  returnDate: [],
  costBearer: undefined
})
const timeTypeList = ref([
  {
    label: 'This Week',
    value: 1
  },
  {
    label: 'This Month',
    value: 2
  },
  {
    label: 'This Year',
    value: 3
  },
  {
    label: 'This quarter',
    value: 4
  },
  {
    label: 'Specific Time',
    value: 5
  }
])
// 图表ref
const internalChartRef = ref()
const externalChartRef = ref()
// 列表数据ref
const internalRef = ref()
const externalRef = ref()
const createTime = ref()
const courseInfoTotal = ref()
const studentTotal = ref(0)

// 课程分类信息
const getCourserCategory = async () => {
  const data = await listTopic()
  categoryList.value = handleTree(data)
}

// 跳转预览详情信息 Todo 后端接口缺少id参数
const handleDetail = (id: number) => {
  router.push({
    name: 'MlcTrainingStatisticsDetail',
    query: { id }
  })
}
/** 导出图片和所有数据按钮操作 */
const handleExportReport = async () => {
  const workbook = new Workbook();
  const chartInternalImages = await internalChartRef.value.getChartImageBase64()
  const chartExternalImages = await externalChartRef.value.getChartImageBase64()
  const chartImages = [...chartInternalImages, ...chartExternalImages]
  // const chartImages =await externalChartRef.value.getChartImageBase64()
  // 将每个图表插入到不同的 sheet 中
  chartImages.forEach((base64, index) => {
    const worksheet = workbook.addWorksheet(`Chart ${index + 1}`)

    const imageId = workbook.addImage({
      base64: base64,
      extension: 'png'
    })
    worksheet.addImage(imageId, {
      tl: {col: 0, row: 0},
      br: {col: 10, row: 30},
      editAs: 'oneCell'
    })
  })
  // 字段映射关系（根据后端返回的英文字段 -> 中文标题）国内
  const internalFieldMap = {
    code: "Training Code",
    title: "Course Title EN",
    titleAr: "Course Title AR",
    place: "Place",
    implementingCompany: "Company",
    duration: 'Course Duration',
    userCount: 'Student Number'
  }
  // 国内数据
  if (internalRef.value.internalAllList.length > 0) {
    // 添加第一个列表 sheet
    const sheetA = workbook.addWorksheet('Internal')
    sheetA.addRow(Object.values(internalFieldMap))
    // 添加每一行数据
    internalRef.value.internalAllList.forEach(item => {
      const row = Object.keys(internalFieldMap).map(key => item[key])
      sheetA.addRow(row)
    })
    // 可选：设置列宽
    sheetA.columns.forEach(column => {
      column.width = 20
    })
  }

  // 字段映射关系（根据后端返回的英文字段 -> 中文标题）国外
  const externalFieldMap = {
    code: "Training Code",
    title: "Course Title EN",
    titleAr: "Course Title AR",
    receivingCountry: "The receiving Country",
    travelDate: "Travel Date",
    returnDate: "Return Date",
    adminNo: "Admin No.",
    costBearer: "Cost Bearer",
    userCount: 'Student Number'
  }
  // 国外数据
  if (externalRef.value.externalAllList.length > 0) {
    // 添加第一个列表 sheet
    const sheetB = workbook.addWorksheet('External')
    sheetB.addRow(Object.values(externalFieldMap))
    // 添加每一行数据
    externalRef.value.externalAllList.forEach(item => {
      const row = Object.keys(externalFieldMap).map(key => item[key])
      sheetB.addRow(row)
    })
    // 可选：设置列宽
    sheetB.columns.forEach(column => {
      column.width = 20
    })
  }

  // 导出文件
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/octet-stream' })
  FileSaver.saveAs(blob, 'Echarts_export.xlsx')

}

const selectTime = (val: string[]) => {
  createTime.value = val
  handleTime(tabName.value)
}

// 选择时间
const changeTime = (val: number) => {
  switch (val) {
    case 1:
      // 获取当前天的星期一到星期天的开始时间和结束时间
      createTime.value = getWeekStartAndEnd(1);
      break;
    case 2:
      // 获取当前天的月的开始时间和结束时间
      createTime.value = getMonthStartAndEnd(1);
      break;
    case 3:
      // 获取当前年的开始时间和结束时间
      createTime.value = getYearStartAndEnd(1);
      break;
    case 4:
      // 获取当前天的季度的开始时间和结束时间
      createTime.value = getQuarterStartAndEnd(1);
      break;
    case 5:
      // 特定时间
      createTime.value = []
    default:
      break;
  }
  if (val !== 5) {
    handleTime(tabName.value)
  }
}
// 每次选择时间调用子组件刷新一次
const handleTime = (val: string) => {
  if (val === '1') {
    // 将时间赋值给子组件查询接口
    internalChartRef.value.queryForm.startDate = createTime.value[0]
    internalChartRef.value.queryForm.endDate = createTime.value[1]
    internalChartRef.value.getAllApi()
  } else {
    externalChartRef.value.queryForm.startDate = createTime.value[0]
    externalChartRef.value.queryForm.endDate = createTime.value[1]
    externalChartRef.value.getAllApi()
  }
}
const resetTime = (val: string) => {
  // 默认本月
  timeType.value = 2
  if (val === '1') {
    // 将时间赋值给子组件查询接口
    const time = getMonthStartAndEnd(1)
    internalChartRef.value.queryForm.startDate = time[0]
    internalChartRef.value.queryForm.endDate = time[1]
    internalChartRef.value.getAllApi()
  } else {
    const time = getMonthStartAndEnd(1)
    externalChartRef.value.queryForm.startDate = time[0]
    externalChartRef.value.queryForm.endDate = time[1]
    externalChartRef.value.getAllApi()
  }
}
// 每次切换tab页签进行按时间重置
const selectActive = (val: string) => {
  if (val === '1') {
    resetTime(val)
  } else {
    resetTime(val)
  }
}

const getCourseTotal = async () => {
  courseInfoTotal.value = await OutsourcedTrainingApi.getOutsourcedCoursesTotal()
}


const getStudentTotal = async () => {
  studentTotal.value = await OutsourcedTrainingApi.getStudentTotal()
}
// 截取code值(根据分类名称第一个 - 进行截取拼接)
const getCode = (val: string) => {
  const parts = val.split('-')
  return parts.length > 0 ? parts[0] : val
}

onMounted(() => {
  // 默认是This month
  createTime.value = getMonthStartAndEnd()
  // 课程总数
  getCourseTotal()
  // 学生总数
  getStudentTotal()
  // 获取课程分类信息
  getCourserCategory()


})
</script>

<style scoped lang="scss">
</style>
