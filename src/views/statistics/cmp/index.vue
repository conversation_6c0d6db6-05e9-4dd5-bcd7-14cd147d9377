<script setup lang="ts" name="CompanyStatistics">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import PicAreaCard from './components/PicAreaCard.vue'
import { CompanyPolicyList, countCompanyPolicy, exportCompanyPolicyDetail } from '@/api/statistics/company-policy'
import router from '@/router'
import download from "@/utils/download";
import { dateFormatter } from '@/utils/formatTime'

interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  introduction: string
  departmentId: string
  title: string
  ack: boolean
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const countData = ref()
const loading = ref(false)
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryRef = ref()
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    title: undefined,
    ack: 'Yes',
  },
})
const { queryParams } = toRefs(data)
const ackList = [
  {
    value: 'Yes',
    label: t('common.yes')
  },
  {
    value: 'No',
    label: t('common.no')
  },
]
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    title: undefined,
    ack: 'Yes',
  }
  queryRef.value?.resetFields()
}
/** CompanyPolicy的详情 */
const handleDetail = (row: any) => {
  router.push({ name: 'CompanyPolicyStatisticsDetail', params: { id: row.id } })
}

/** 获取顶部的统计信息 */
const getCountData = async () => {
  countData.value = await countCompanyPolicy('')
}
/** 获取Table的数据 */
const getList = async () => {
  loading.value = true
  try {
    const res = await CompanyPolicyList(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportCompanyPolicyDetail(queryParams.value)
    download.excel(data, `Company policy statistics-${moment().format('YYYY-MM-DD')}.xlsx`)

  } catch {}
}
onMounted(() => {
  getList()
  getCountData()
})
/** 顶部的统计样式及数值数据 */
const picCardData = computed(() => [
  {
    bg: 'bg-gradient-to-r from-[#6EAA32] to-[#87AC61]',
    cBg: 'bg-[#3B88FF]',
    icon: 'All',
    color: '#71aa38',
    text: t('statistics.companyPolicy.allCompanyPolicy'),
    number: countData.value?.totalCount ? countData.value?.totalCount : 0,
  },
  {
    bg: 'bg-gradient-to-r from-[#32A974] to-[#08804A]',
    cBg: 'bg-[#752FDD]',
    icon: 'Ack',
    color: '#ffffff',
    text: t('statistics.companyPolicy.acknowledgement'),
    number: countData.value?.ackTotalCount ? countData.value?.ackTotalCount : 0,
  },
  {
    bg: 'bg-gradient-to-r from-[#2C83A4] to-[#085A81]',
    cBg: 'bg-[#45CC7E]',
    icon: 'Optional',
    color: '#ffffff',
    text: t('statistics.companyPolicy.optional'),
    number: countData.value?.optionalTotalCount ? countData.value?.optionalTotalCount : 0,
  },
])
</script>

<template>
  <div class="app-container">
    <!-- 顶部统计内容 -->
    <ContentWrap>
      <PicAreaCard :data="picCardData" />
    </ContentWrap>
    <!-- Search -->
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="queryParams" @submit.prevent class="-mb-15px">
        <el-form-item :label="t('category.journey.title')" prop="title">
          <el-input v-model="queryParams.title" :placeholder="t('common.inputText')" clearable class="!w-300px" @keyup.enter="handleSearch" />
        </el-form-item>
        <!-- <el-form-item label="Acknowledgement" prop="ack">
          <el-select v-model="queryParams.ack" placeholder="Select" clearable style="width: 300px">
            <el-option v-for="item in ackList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button plain type="primary" @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />
            {{ t('action.export') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <!-- Table -->
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData">
        <el-table-column :label="t('category.journey.title')" prop="title" align="left" min-width="280" fixed="left" />
        <el-table-column :label="t('learningCenter.companyPolicy.acknowledgement')" prop="ack" min-width="180">
          <template #default="{ row }">
            {{ row.ack ? t('common.yes') : t('common.no') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('category.journey.creator')" prop="creator" min-width="200" />
        <el-table-column :label="t('category.journey.creationTime')" prop="createTime" :formatter="dateFormatter" min-width="200" />
        <el-table-column alient="center" fixed="right" :label="t('global.action')" min-width="80">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleDetail(row)">
              <Icon icon="ep:view" />
              {{ t('action.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>
  </div>
</template>

<style scoped lang="scss"></style>
