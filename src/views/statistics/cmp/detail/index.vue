<script setup lang="ts" name="CompanyPolicyStatisticsDetail">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import { OCCStatus } from '../enums/status'
import PicAreaCard from '../components/PicAreaCard.vue'
import { CompanyPolicyDetailList, detailCompanyPolicyCount, exportCompanyPolicy } from '@/api/statistics/company-policy'
import { detailCompanyPolicy } from '@/api/topicMgt/company-policy'
import { formatImgUrl } from '@/utils/index'
import download from "@/utils/download"
import { useTagsViewStore } from "@/store/modules/tagsView"
import { dateFormatter } from '@/utils/formatTime'

interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  introduction: string
  departmentId: string
  title: string
  ack: boolean
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const route = useRoute()
const { push, currentRoute } = useRouter() // 路由
const countData = ref()
const loading = ref(false)
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const title = ref()
const queryRef = ref()
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    companyPolicyId: route.params.id,
    empName: undefined,
    status: undefined,
    empCode: undefined,
    email: undefined,
  },
})
const { queryParams } = toRefs(data)

const statusList = [
  { label: t('examMgt.exam.notStarted'), value: OCCStatus.NotStart, id: '2' },
  { label: t('examMgt.exam.inProcess'), value: OCCStatus.InProgress, id: '3' },
  { label: t('statistics.course.completed'), value: OCCStatus.Completed, id: '4' },
]
const handleBack = () => {
  delView(unref(currentRoute))
  push('/statistics/company-statistics')
}
const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    companyPolicyId: route.params.id,
    empName: undefined,
    status: undefined,
    empCode: undefined,
    email: undefined,
  }
  queryRef.value?.resetFields()
}
// }
const getList = async () => {
  loading.value = true
  try {
    const res = await CompanyPolicyDetailList(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
const getCountData = async () => {
  const data = await detailCompanyPolicyCount(route.params.id)
  countData.value = data
  countData.value.forEach((item: any) => {
    if (item.ackStatus === OCCStatus.NotStart) {
      item.icon = 'NotStart'
      item.bg = 'bg-gradient-to-r from-[#6FAA34] to-[#86AC5F]'
    }
    if (item.ackStatus === OCCStatus.Completed) {
      item.icon = 'Complete'
      item.bg = 'bg-gradient-to-r from-[#31A873] to-[#0A824C]'
      item.color = '#2CA36E'
    }
    if (item.ackStatus === OCCStatus.InProgress) {
      item.icon = 'Inprocess'
      item.bg = 'bg-gradient-to-r from-[#2A81A2] to-[#0A5C83]'
    }
    item.text = item.statusName
    item.number = item.count
  })
}
const getCompanyPolicyDeatil = async () => {
  title.value = await detailCompanyPolicy(route.params.id)
}
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportCompanyPolicy(queryParams.value)
    download.excel(data, `Company policy-${title?.value.title}-${moment().format('YYYY-MM-DD')}.xlsx`)
  } catch {}
}
onMounted(() => {
  getList()
  getCountData()
  getCompanyPolicyDeatil()
})
</script>

<template>
  <div class="app-container">
    <!-- 顶部图片区域 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <div class="flex gap-5 mr-16">
        <img v-if="title?.cover" :src="formatImgUrl(title?.cover)" class="w-[160px] h-[90px]" />
        <img v-else src="../../../../assets/images/commonImg.png" class="w-[154px] h-[90px] row-span-2" />
        <div class="pt-2.5">
          <span class="text-[#23293A] text-xl line-clamp-1 break-all">{{ title?.title }}</span>
          <div class="flex gap-1.5 mt-4">
            <div class="h-6 px-3 bg-[#EFE0B5] text-[#936C0F] text-[14px] rounded-[2px] flex items-center justify-center">
              {{ title?.attachmentList[0].fileType }}
              <!-- <svg-icon icon-class="CourseTopic" class="text-xs" /> -->
            </div>
          </div>
        </div>
      </div>
      <el-button class="ms-auto flex-shrink-0" type="primary" @click="handleBack">
        {{ t('action.back') }}
      </el-button>
      <!-- <el-button class="ms-auto flex-shrink-0" type="primary" @click="handleBack"> Back </el-button> -->
    </div>
    <!-- 统计内容 -->
    <div class="mt-[21px] mb-[25px]">
      <PicAreaCard :data="countData" />
    </div>
    <!-- Search -->
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="queryParams" class="-mb-15px">
        <el-form-item :label="t('examMgt.paper.name')" prop="empName">
          <el-input v-model="queryParams.empName" placeholder="Please Input" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('learningCenter.boarding.badgeNumber')" prop="empCode">
          <el-input v-model="queryParams.empCode" placeholder="Please Input" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.email')" prop="title">
          <el-input v-model="queryParams.email" placeholder="Please Input" clearable class="!w-200px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('common.status')" prop="status">
          <el-select v-model="queryParams.status" placeholder="Select" clearable class="!w-200px">
            <el-option v-for="item in statusList" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button plain type="primary" @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />
            {{ t('action.export') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- Table -->
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData">
        <el-table-column :label="t('examMgt.paper.name')" prop="empName" align="left" :width="280" fixed="left" />
        <el-table-column :label="t('learningCenter.boarding.badgeNumber')" prop="empCode" :width="180" />
        <el-table-column :label="t('learningCenter.course.email')" prop="email" :width="350" />
        <el-table-column :label="t('learningCenter.course.company')" prop="companyName" :width="150" />
        <el-table-column :label="t('learningCenter.course.deptName')" prop="deptName" :width="150" />
        <el-table-column :label="t('learningCenter.course.section')" prop="sectionName" :width="350" />
        <el-table-column :label="t('learningCenter.course.position')" prop="positionName" :width="350" />
        <el-table-column :label="t('category.journey.creator')" prop="createBy" :width="150" />
        <el-table-column :label="t('category.journey.creationTime')" prop="createTime" :formatter="dateFormatter" :width="280" />
        <el-table-column :label="t('common.status')" prop="ackStatusName" width="200" fixed="right" />
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>
  </div>
</template>

<style scoped lang="scss"></style>
