<script setup lang='ts'>
const props = defineProps<{
  data: any
}>()
</script>

<template>
  <div class="flex gap-7" :class="props.data?.length === 1 ? 'w-[520px]' : ''">
    <div
      v-for="(item, index) in props.data" :key="index"
      class="rounded-x shadow-[0_0_20px_0_rgba(19,116,0,0.1)] bg-gradient-to-b flex py-4 ps-5 flex-1" :class="item.bg"
    >
      <div class="flex items-center justify-center relative">
        <div class="w-[66px] h-[66px]  rounded-full bg-[#fff] opacity-50"></div>
        <svg-icon :icon-class="item.icon" :style="{ color: item.color }" class="text-[26px] absolute left-[-46px]" />
      </div>
      <div class="flex flex-col mt-1 ml-2">
        <span class="text-sm text-white">{{ item.text }}</span>
        <span class="font-bold text-2xl text-white mt-2">{{ item.number }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang='scss'>

</style>
