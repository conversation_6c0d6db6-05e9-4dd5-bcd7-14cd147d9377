<script setup lang="ts" name="JourneyForm">
import type { Component, ComponentInternalInstance } from 'vue'
import BasicInformation from './components/BasicInformation/index.vue'
import AssignUser from './components/AssignUser/index.vue'
import { getJourney } from '@/api/topicMgt/journey'
import { formatImgUrl } from '@/utils'
import { useTagsViewStore } from "@/store/modules/tagsView"


const route = useRoute()
// 学习地图id
const journeyId = route.params.id

const menuItems = computed(() => [
  {
    id: 1,
    text: t('learningCenter.course.basicInfo'),
  },
  {
    id: 2,
    text: t('learningCenter.course.assignScope'),
    disabled: !journeyId,
  },
])
const menuActive = ref((route.query.active as string) || '1')
const components: Record<any, Component> = {
  1: BasicInformation,
  2: AssignUser,
}
const loading = ref(false)
const basicInformation = ref<{ name: string, cover: string, topic: string, status: number }>()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const { delView } = useTagsViewStore() // 视图操作
const { router, push, currentRoute } = useRouter() // 路由
const handleSelect = (key: string) => {
  menuActive.value = key
}

const handleToList = () => {
  delView(unref(currentRoute))
  push('/learning-center/journey')
}
/** 获取学习地图基本信息 */
const getInfo = async () => {
  basicInformation.value = await getJourney(journeyId)
}
onMounted(() => {
  if (journeyId) {
    getInfo()
  }
})

</script>

<template>
  <div v-loading="loading">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <span class="text-2xl"> {{ journeyId ? t('learningCenter.journey.editJourney') : t('learningCenter.journey.addJourney') }}</span>
      <!-- c -->
      <div class="ms-auto flex-shrink-0">
        <div>
          <el-button type="primary" class="w-[80px]" @click="handleToList">
            {{ t('action.back') }}
          </el-button>
        </div>
      </div>
    </div>
    <!-- 下方课程设置区域 -->
    <div class="flex mt-6 gap-5">
      <!-- 左侧菜单 -->
      <ContentWrap>
        <div class="w-[220px] shrink-0 rounded-x p-2.5 border border-[#CDDBF1]">
          <el-menu
            v-model="menuActive"
            :default-active="menuActive"
            :style="{
            '--el-menu-item-height': '34px',
          }"
            @select="handleSelect"
          >
            <el-menu-item v-for="menu in menuItems" :key="menu.id" :index="`${menu.id}`" :disabled="menu.disabled">
              {{ menu.text }}
            </el-menu-item>
          </el-menu>
        </div>
      </ContentWrap>
      <!-- 右侧组件内容 -->
      <div class="flex-1 overflow-hidden">
        <component
          :is="components[menuActive]"
          v-bind="{
            journeyId,
            basicInformation,
          }"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-menu) {
  border-right: 0 !important;
  .el-menu-item {
    &.is-active {
      background-color: #E4F4EE;
      border-radius: 4px;
    }
  }
}
</style>
