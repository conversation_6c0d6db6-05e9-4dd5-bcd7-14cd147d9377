<script setup lang="ts" name="AssignUser">
import { differenceBy, union, uniqBy } from 'lodash-es'
import type { ComponentInternalInstance } from 'vue'
import { collapseItemProps } from 'element-plus'
import ScopeSelect from '@/components/ScopeSelect/index.vue'
import type { ScopeConfirm, ScopeData } from '@/components/ScopeSelect/typings/index'
import { AssignFun, AssignedList, delAssign } from '@/api/topicMgt/journey'
import { CourseType } from '@/enums/course'
import type { CourseScope } from '@/typings/views/topicMgt/elearning'

const props = defineProps<{
  journeyId: number
}>()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const route = useRoute()
const scopeRef = ref<InstanceType<typeof ScopeSelect>>()
const scopeList = ref<ScopeData[]>([])
const activeType = ref(CourseType.Elective)
const activeTypeRoute = computed(() => {
  const { query } = route
  if (query.tabActive) {
    return 1
  }
  else {
    return 0
  }
})
const loading = ref(false)

const dialogLoading = ref(false)

const isMounted = ref(false)
/**
 * 转换scopelist为可以回显的格式
 * @param list 需要转换的scopelist
 */
const formatScopeList = (list: CourseScope[]): ScopeData[] => {
  return list.map(item => ({
    relevanceId: item.relevanceId,
    relevanceName: item.relevanceName,
    scope: item.scope,
    id: item.id,
  }))
}
/**
 * 获取当前分配列表
 */
const getList = async (key: any) => {
  activeType.value = key
  const params = {
    journeyId: props.journeyId,
    // scope: formAssign.value.scope,
    type: activeType.value, // 1,
  }
  loading.value = true
  try {
    const data = await AssignedList(params)
    scopeList.value = formatScopeList(data || [])
  } finally {
    loading.value = false
  }
}
/**
 * 提交当前的数据选择
 * @param data 当前组件返回的已选择的用户列表
 */
const handleConfirm = async (data: ScopeConfirm) => {
  const dataParams = data.scopes.map(item => ({
    relevanceId: item.relevanceId,
    relevanceName: item.relevanceName,
    scope: item.scope,
    type: activeType.value,
  }))
  dialogLoading.value = true
  try {
    await AssignFun(+props.journeyId, data.scope, activeType.value, dataParams)
    scopeRef.value?.closeDialog()
    message.success(t('global.editSuccess'))
    await getList(activeType.value)
  } finally {
    dialogLoading.value = false
  }
  // scopeList.value = uniqBy(scopeList.value.concat(list), 'relevanceId')
}
/**
 * 删除分配的用户
 * @param list 当前组件返回的已选择的用户列表
 */
const handleDelete = async (list: CourseScope[] | CourseScope) => {
  let ids: any
  try {
    await message.delConfirm(t('confirm.deleteOption'))
    loading.value = true
    ids = Array.isArray(list) ? list.map(item => item.id).join(',') : list.id
    await delAssign(ids)
    message.success(t('global.deleteSuccess'))
    await getList(activeType.value)
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  isMounted.value = true
  getList(activeTypeRoute.value)
})
</script>

<template>
  <ContentWrap>
    <div class="rounded-x border border-[#CDDBF1] py-5 px-7">
      <span class="text-[#222222] text-xl"> {{ t('learningCenter.course.assignScope') }} </span>
      <el-tabs v-model="activeType" type="card" class="custom-scope-tab" @tab-change="getList">
        <el-tab-pane label="Controlled scope" :name="CourseType.Elective">
          <div :id="`scope-${CourseType.Elective}`"></div>
        </el-tab-pane>
        <el-tab-pane label="Mandatory scope" :name="CourseType.Mandatory">
          <div :id="`scope-${CourseType.Mandatory}`"></div>
        </el-tab-pane>
      </el-tabs>
      <Teleport v-if="isMounted" :to="`#scope-${activeType}`">
        <ScopeSelect
          ref="scopeRef"
          v-model="scopeList"
          v-model:loading="dialogLoading"
          v-loading="loading"
          :employee-table-props="{
          height: 420,
        }"
          @confirm="handleConfirm"
          @delete="handleDelete"
        />
      </Teleport>
    </div>
  </ContentWrap>
</template>

<style scoped lang="scss">
:deep(.custom-scope-tab) {
  margin-top: 1.25rem; /* mt-5 */
}
</style>
