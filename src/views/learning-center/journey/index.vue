<script setup lang="ts" name="Journey">
import type { ComponentInternalInstance } from 'vue'
import { omit } from 'lodash-es'
import {
  delJourney, JourneyRespVO,
  listJourney,
  putOnJourney,
} from '@/api/topicMgt/journey'
import { getJourneyCategoryAll} from '@/api/category/journey'
import { formatImgUrl } from '@/utils'
import { formatSecond, convertToTime } from '@/utils/ruoyi'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'

interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  name: string
  keywords?: string | null
  introduction: string
  departmentId: string
}
const router = useRouter()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryRef = ref()
const ids = ref<number[]>([])
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  categoryId: undefined,
  keywords: undefined,
  status: undefined
})
// 学习地图分类列表
const journeyCategoryList = ref([])
const multiple = ref(true)

const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    title: '',
    categoryId: undefined,
    keywords: '',
    status: undefined
  }
  queryRef.value?.resetFields()
}
/** 选择上架/下架条数  */
const handleSelectionChange = (selection: JourneyRespVO[]) => {
  ids.value = selection.map(item => item.id)
  multiple.value = !selection.length
}
// 批量上下架
const handlePutOn = async (status: number) => {
  if (!ids.value.length)
    return  message.warning(t('global.select'))
  await putOnJourney(ids.value.join(','),status)
  message.success(t('global.editSuccess'))
  getList()
  ids.value = []
}
const handleToForm = (row?: any, active?: number, activeTab?: number) => {
  if (activeTab === 1) {
    router.push({
      name: 'JourneyForm',
      params: {
        id: row.id,
      },
      query: {
        active,
        tabActive: 1,
      },
    })
  }
  else {
    router.push({
      name: 'JourneyForm',
      params: {
        id: row.id,
      },
      query: {
        active,
      },
    })
  }
}
// 删除学习地图
const handleDelete = async (row: JourneyRespVO) => {
  try {
    await message.delConfirm('Are you sure you want to delete the current journey?')
    loading.value = true
    await delJourney(row.id)
    getList()
    message.success(t('global.deleteSuccess'))
  } finally {
    loading.value = false
  }
}
/**
 *
 * @param row 列表内上下架
 */
const handleRowStatus = async (row: any) => {
  try {
    await putOnJourney(row.id, row.status)
    message.success(t('global.editSuccess'))
    getList()
  } catch (e) {}
}
// 获取学习地图列表
const getList = async () => {
  loading.value = true
  try {
    const res = await listJourney(queryParams.value)
    tableData.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

// 学习地图分类
const getListCategory = async () => {
  try {
    journeyCategoryList.value = await getJourneyCategoryAll()
  } catch (e) {}
}
onMounted(() => {
  getList()
  getListCategory()
})
</script>

<template>
  <ContentWrap>
    <el-form ref="queryRef" :inline="true" class="-mb-15px" :model="queryParams" @submit.prevent>
      <el-form-item :label="t('learningCenter.journey.title')">
        <el-input v-model="queryParams.title" :placeholder="t('common.inputText')" clearable class="!w-240px" @keydown.enter="handleSearch" />
      </el-form-item>
      <el-form-item :label="t('learningCenter.journey.categoryTitle')" prop="category">
        <el-select v-model="queryParams.categoryId" clearable class="!w-240px" >
          <el-option v-for="item in journeyCategoryList" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('category.topic.keyWords')">
        <el-input v-model="queryParams.keywords" :placeholder="t('common.inputText')" clearable class="!w-240px" @keydown.enter="handleSearch" />
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.onShelfStatus')" prop="status">
        <el-select v-model="queryParams.status" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('action.search') }}
        </el-button>
        <el-button type="default" @click="handleReset">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('action.reset') }}
        </el-button>
        <el-button plain type="primary" class="mr-2" @click="handleToForm">
          <Icon class="mr-5px" icon="ep:plus" />
          {{ t('action.add') }}
        </el-button>
        <el-button :disabled="multiple" plain type="warning" class="mr-2" @click="handlePutOn(1)">
          <Icon icon="ep:upload" class="mr-5px"/>
          {{ t('action.putOnShelf') }}
        </el-button>
        <el-button :disabled="multiple" plain type="warning" class="mr-2" @click="handlePutOn(0)">
          <Icon class="mr-5px" icon="ep:download" />
          {{ t('action.moveOffShelf') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" :data="tableData" height="600" @selection-change="handleSelectionChange" :show-overflow-tooltip="true">
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column prop="id" width="95" :label="t('learningCenter.journey.uniqueId')" fixed="left" />
      <el-table-column prop="name" :label="t('learningCenter.journey.title')" min-width="316px" fixed="left">
        <template #default="{ row }">
          <div>
            <div class="flex flex-row items-center justify-center">
              <el-image :src="formatImgUrl(row.cover)" class="w-[86px] h-[50px]" />
              <div class="pl-5 w-[230px]">
                <el-tooltip
                  :content="row.title" popper-class="tooltip-mywidth"
                  effect="dark" placement="top"
                >
                  <div class="break-all line-clamp-3">
                    {{ row.title }}
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="categoryName" :label="t('learningCenter.journey.categoryTitle')" width="120" />
      <el-table-column prop="keywords" width="225" :label="t('category.topic.keyWords')">
        <template #default="{ row }">
          <el-tooltip
            :content="row.keywords" popper-class="tooltip-mywidth"
            effect="dark" placement="top"
          >
            <div class="line-clamp-3">
              {{ row.keywords }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="t('learningCenter.course.onShelfStatus')" width="125" align="center">
        <template #default="{ row }">
          <el-switch v-model="row.status" size="large" :active-value="1" :inactive-value="0" @change="handleRowStatus(row)" />
        </template>
      </el-table-column>
      <el-table-column prop="courseCount" :label="t('learningCenter.course.assignedNumber')" width="150">
        <template #default="{ row }">
          <el-link v-if="row.courseCount > 0" type="primary" :underline="false" @click="handleToForm(row, 1)">
            {{ row.courseCount }}
          </el-link>
          <span v-else class="text-primary">{{ row.courseCount }} {{ t('learningCenter.task.tasks') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="t('learningCenter.journey.creationTime')" :formatter="dateFormatter" width="160" />
      <el-table-column fixed="right" :label="t('global.action')" min-width="250">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleToForm(row, 2)">
            <Icon icon="ep:user" />
            {{ t('action.assignCourse') }}
          </el-button>
          <el-button link type="primary" @click="handleToForm(row, 1)">
            <Icon icon="ep:edit" />
            {{ t('action.edit') }}
          </el-button>
          <el-button link type="primary" class="ms-0" @click="handleDelete(row)">
            <Icon icon="ep:delete" />
            {{ t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
  </ContentWrap>
</template>

<style scoped lang="scss">
/** table中superior company的样式 */
:deep .custom-wrapper-disabled .el-input__wrapper{
  border: none;
  box-shadow: none;
  color: #007943;
  @apply cursor-text
}
:deep .custom-wrapper-disabled .el-input__inner {
    color: #fff;
}
:deep .custom-wrapper-disabled .el-input__inner:hover{
  border: none;
  color: #fff;
  cursor: text;
}
:deep .custom-wrapper-disabled .el-input.is-disabled .el-input__inner{
  -webkit-text-fill-color: #000000b3;
}
:deep .el-input__suffix {
    display: none;
}
:deep .custom-wrapper-disabled  .el-input.is-disabled .el-input__wrapper {
    background-color: #fff;
}
:deep .el-table--border .el-table__row:hover {
  background-color:#f5f7fa;
    .custom-wrapper-disabled .el-input__wrapper {
      background-color:#f5f7fa;
  }
}
:deep .el-tag + .el-tag{
  margin-left: 0;
  margin-top:2px
  }
</style>

<style  lang="scss">
// 设置tooltip-mywidth时，style不能添加scope
.tooltip-mywidth{
  width: 240px;
}
</style>

