<script setup lang="ts" name="CourseCenter">
import type { ComponentInternalInstance } from 'vue'
import { omit } from 'lodash-es'
import { useUserStore } from '@/store/modules/user'
import download from '@/utils/download'
import {
  delCourse,
  importCourse,
  isNewCourse,
  listCourse,
  putOffCourse,
  putOnCourse,
  recommendCourse,
  exportCourse
} from '@/api/topicMgt/elearning'
import SearchCascader from '@/components/SearchCascader/index.vue'
import {listTopic, listTopicAll} from '@/api/category/topic'
import { formatImgUrl } from '@/utils'
import { formatSecond, convertToTime } from '@/utils/ruoyi'
import ExcelJS from "exceljs"
import { useI18n } from "vue-i18n"
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import {handlePhaseTree} from "@/utils/tree";

interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNo: number
  pageSize: number
  id: number
  name: string
  keywords?: string | null
  introduction: string
  deptId: string
}
const userStore = useUserStore()
const router = useRouter()

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryRef = ref()
const ids = ref<number[]>([])
const subjectList = ref([])
/** 在批量上架时用到，标记是否选择课程都拥有资源 */
const isAllRes = ref(false)
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    deptId: undefined,
    duration: undefined,
    isSubtitle: undefined,
    source: undefined,
    language: undefined,
    handDuration: undefined,
    topicIdList: []
  },
})
const importCourseForm = ref([])
const { queryParams } = toRefs(data)
const topicList = ref()
// const single = ref(false)
const multiple = ref(true)
const topicId = ref()
const durationList = [
  { id: 1, label: '<15min', durationLower: 0, durationUpper: 900 },
  { id: 2, label: '15 - 30min', durationLower: 900, durationUpper: 1800 },
  { id: 3, label: '30 - 60min', durationLower: 1800, durationUpper: 3600 },
  { id: 4, label: '>60min', durationLower: 3600, durationUpper: 999999 },
]

const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    deptId: undefined,
    topicIdList: [],
    isSubtitle: undefined,
    source: undefined,
    language: undefined,
    handDuration: undefined,
  }
  topicId.value = undefined
  queryRef.value?.resetFields()
}
/** 选择条数  */
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.id)
  multiple.value = !selection.length

  isAllRes.value = selection.every(item => item.chapterNum > 0)
}
const handlePutOn = async () => {
  if (!isAllRes.value) {
    message.warning(t('warning.noResource'))
    return
  }
  if (!ids.value.length)
    return message.warning(t('global.select'))
  await putOnCourse(ids.value.join(','))
  message.success(t('global.editSuccess'))
  await getList()
}
const handlePutOff = async () => {
  if (!ids.value.length)
    return message.warning(t('global.select'))
  await putOffCourse(ids.value.join(','))
  await getList()
}
const handleToForm = (row?: any, active?: number, activeTab?: number) => {
  if (activeTab === 1) {
    router.push({
      name: 'CourseForm',
      params: {
        id: row.id,
      },
      query: {
        active,
        tabActive: 1,
      },
    })
  }
  else {
    router.push({
      name: 'CourseForm',
      params: {
        id: row.id,
      },
      query: {
        active,
      },
    })
  }
}
// 跳转课程导入列表
const jumpImporting = () => {
  router.push({
    name: 'CourseImportingList',
  })
}
const handleDelete = async (row: any) => {
  const noticeIds = row.id || ids.value
  try {
    // 删除的二次确认
    await message.delConfirm()
    loading.value = true
    await delCourse(noticeIds)
    message.success(t('common.delSuccess'))
    await getList()
  } finally {
    loading.value = false
  }
}
/**
 * 推荐/取消推荐
 */
const handleRecommend = async (row: any) => {
  const param = {
    id: row.id,
    isRecommend: row.isRecommend !== false,
  }
  await recommendCourse(param)
  message.success(t('common.updateSuccess'))
  await getList()
}
/**
 * 是否新课
 */
const handleNewCourse = async (row: any) => {
  const param = {
    id: row.id,
    isNew: row.isNew !== false,
  }
  await isNewCourse(param)
  message.success(t('common.updateSuccess'))
  await getList()
}
/**
 *
 * @param row 上下架
 */
const handleRowStatus = async (row: any) => {
  if (row.chapterNum <= 0 && row.status === 1) {
    message.warning(t('warning.noResourceList'))
    row.status = row.status === 1 ? 0 : 1
    return
  }
  if (row.status === 1) {
    try {
      await putOnCourse(row.id)
      message.success(t('common.updateSuccess'))
      await getList()
    } catch (e) {
      row.status = 0
    }
  }
  if (row.status === 0) {
    try {
      await message.confirm(t('confirm.isOffShelf'))
      try {
        await putOffCourse(row.id)
        message.success(t('common.updateSuccess'))
        await getList()
      } catch (e) {
        row.status = 0
      }
    } catch(e) {
      row.status = row.status === 1 ? 0 : 1
    }
  }
}
const getList = async () => {
  loading.value = true
  try {
    if (topicId.value) {
      // queryParams.value.topicId = queryParams.value.topicId[queryParams.value.topicId.length - 1]
      queryParams.value.topicId = topicId.value.join(',')
    }
    const params = {
      ...omit(queryParams.value, ['handDuration']),
      handDurationLower: queryParams.value.handDuration?.durationLower,
      handDurationUpper: queryParams.value.handDuration?.durationUpper,
    }
    const data = await listCourse(params)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const getListTopicAll = async () => {
  topicList.value = await listTopicAll()
}
const showImport = ref(false)
const handleImport = () => {
  importCourseForm.value = []
  showImport.value = true
}
const getImageUrl = (imageUrl: string | string[]) => {
  // 有可能在excel表格中加入超链接里面的数据就是一个对象,取其中一个参数就行
  return typeof imageUrl === 'object' ? imageUrl?.text : imageUrl
}

const parseTimeString = (timeString: string) => {
  // 将时间字符串中的所有空格替换为空字符串
  timeString = timeString.replace(/\s+/g, '')
  // 拆分时间字符串，假设格式为 "HH:MM:SS"
  const [hours, minutes, seconds] = timeString.split(':').map(Number);
  // 计算总秒数
  const totalSeconds = hours * 3600 + minutes * 60 + seconds;
  // 如果总秒数是0默认传值
  if (totalSeconds === 0) {
    return '00:00:00'
  } else {
    // 格式化为HH:MM:SS格式
    const formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    return formattedTime
  }
}
// 判断对象中是否存在这个key
const objKey = (item) => {
  if (item.hasOwnProperty('Estimated Duration')) {
    return true
  }
}

// 导入数据
const beforeUpload = (file) =>{
  const reader = new FileReader();
  reader.onload = (e) => {
    const data = new Uint8Array(e.target.result)
    const workbook = new ExcelJS.Workbook()
    workbook.xlsx
        .load(data.buffer)
        .then(() => {
          const worksheet = workbook.getWorksheet(1); // 假设数据在第一个工作表中
          const headers = worksheet.getRow(1).values // 获取标题行
          // 检查行数是否超过 1000 行
          if (worksheet.rowCount >= 1002) { // 设置大于等于1002的原因是其中包括标题一行,支持一次性导入1000行数据，如果导入的数据是1002行(当前包括了一行标题,数据1001行)，则提示错误(当前包括了标题)
            return message.warning(t('global.importRowLimit'))
            return;
          }
          const result = [];
          for (
              let rowNumber = 2;
              rowNumber <= worksheet.rowCount;
              rowNumber++
          ) {
            const row = worksheet.getRow(rowNumber);
            const rowData = {};
            // 检查当前行是否为空
            let isEmptRow = true
            row.eachCell((cell, colNumber) => {
              const header = headers[colNumber];
              if (cell.value) {
                isEmptRow = false
              }
              if (cell.value instanceof Date) {
                // 先使用toISOString方法转换成字符串再使用正则取出时分秒
                cell.value = cell.value.toISOString().match(/T(\d{2}:\d{2}:\d{2})/)[1];
              }
              rowData[header] = cell.value;
            })
            // 当读取到空行时，跳过该行
            if (isEmptRow) {
              continue
            }
            result.push(rowData);
          }
          // 输出解析后的数据 先合并
          const newList = result.map((item) => {
            return {
              contentTitle: item["Content Title"],
              contentId: item["CONTENTID"],
              assetUuid: item["Asset UUID"],
              category: item["Category - L1"],
              area: item["Area - L2"],
              subject: item["Subject - L3"],
              channel: item["Channel - L4"],
              language: item.Language,
              estimatedDuration: objKey(item) ? parseTimeString(item["Estimated Duration"]) : '00:00:00',
              level: item.Level,
              exam: item["Exam (Yes/No)"],
              subtitle: item["Subtitle (Y/N)"],
              courseSource: item["Local/Cloud"],
              imageUrl: getImageUrl(item["Image Url"]),
              keywords: item.Keywords,
              description: item.Description,
              courseFormat: item["Course Format"],
              courseFileName: item["COURSE FILE NAME"],
              fileLocation: item["FILE LOCATION"],
              // 系统中的用户名称
              creator: userStore.user.nickname
            }
          })
          // 每次表格新加字段都需要往这个数组中添加字段,如果上传的表格不符合里面的则提醒
          // const keyList = ['contentTitle','contentId','assetUuid','category','area','subject','channel','language','estimatedDuration','level','exam','subtitle','courseSource','imageUrl','keywords','description','courseFormat','courseFileName','fileLocation','creator']
          // if (!validateKeys(result, keyList)) {
          //   proxy.$modal.msgWarning(t('global.importRowLimit'))
          //   return
          // }
          importCourseForm.value = newList
          showImport.value = false
          saveImportCourse()
        })
        .catch((error) => {
          console.error("Unable to load Excel file:", error)
        })
  }
  reader.readAsArrayBuffer(file)
  return false; // 阻止默认上传行为
}
// 判断上传的excel表格里面的内容是否符合格式
const validateKeys = (data,keyList) => {
  let isValid = true
  data.forEach((item, index) => {
    for (let key in item) {
      if (!keyList.includes(key)) {
        isValid = false
      }
    }
  });

  return isValid;
}
// 保存导入课程信息
const saveImportCourse = async () => {
  try {
    await message.confirm(t('confirm.importCourse'))
    loading.value = true
    await importCourse(importCourseForm.value)
    await message.success(t('global.importSuccess'))
    await getList()
  } finally {
    loading.value = false
  }
}

// 导出模板
const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportCourse()
    download.excel(data, t('learningCenter.course.courseTemplate'))
  } catch {
  } finally {
  }
}

const getSubjectList = async () => {
  const data = await listTopic({})
  data.forEach((element) => {
    element.value = element.id
    element.label = element.name
  })
  subjectList.value = handlePhaseTree(data, 'id')
}

onMounted(() => {
  getSubjectList()
  getList()
  getListTopicAll()
})
</script>

<template>
  <div class="app-container">
    <ContentWrap>
      <el-form ref="queryRef" :inline="true" :model="queryParams" @submit.prevent label-width="120px">
        <el-form-item :label="t('learningCenter.course.title')">
          <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable class="!w-240px" @keydown.enter="handleSearch" />
        </el-form-item>
        <el-form-item :label="t('category.topic.subjectName')" prop="topic">
          <el-tree-select
            v-model="queryParams.topicIdList"
            :data="subjectList"
            multiple
            :render-after-expand="false"
            show-checkbox
            check-strictly
            check-on-click-node
            class="!w-240px"
          />
        </el-form-item>
        <!--  -->
        <el-form-item :label="t('learningCenter.course.onShelfStatus')" prop="status">
          <el-select v-model="queryParams.status" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="t('learningCenter.course.newCourse')" prop="isNew"
        >
          <el-select v-model="queryParams.isNew" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RECOMMEND_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="t('learningCenter.course.isRecommend')" prop="isRecommend"
        >
          <el-select v-model="queryParams.isRecommend" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RECOMMEND_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="t('learningCenter.course.isAssigned')" prop="assigned"
        >
          <el-select v-model="queryParams.assigned" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_ASSIGN_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="level" :label="t('learningCenter.course.level')">
          <el-select
            v-model="queryParams.level" clearable
            filterable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_LEVEL)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="language" :label="t('learningCenter.course.language')">
          <el-select
            v-model="queryParams.language" clearable
            filterable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="duration" :label="t('learningCenter.course.duration')">
          <el-select v-model="queryParams.handDuration" clearable value-key="id" class="!w-240px">
            <el-option v-for="item in durationList" :key="item.id" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item prop="isSubtitle" :label="t('learningCenter.course.subTitle')">
          <el-select v-model="queryParams.isSubtitle" clearable class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_ASSIGN_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="source" :label="t('learningCenter.course.courseSource')">
          <el-select v-model="queryParams.source" clearable value-key="id" class="!w-240px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_SOURCE)" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <!-- resource_time -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon icon="ep:search" class="mr-5px" />
            {{ t('action.search') }}
          </el-button>
          <el-button type="default" @click="handleReset">
            <Icon icon="ep:refresh" class="mr-5px" />
            {{ t('action.reset') }}
          </el-button>
          <el-button plain type="primary" class="mr-2" @click="handleToForm">
            <Icon icon="ep:plus" class="mr-5px" /> {{ t('action.add') }}
          </el-button>
          <el-button plain type="primary" class="mr-2" @click="handleImport">
            <Icon icon="ep:upload" class="mr-5px"/>
            {{ t('action.import') }}
          </el-button>
          <el-button :disabled="multiple" plain type="warning" class="mr-2" @click="handlePutOn">
            <Icon icon="ep:upload" class="mr-5px"/>
            {{ t('action.putOnShelf') }}
          </el-button>
          <el-button :disabled="multiple" plain type="warning" class="mr-2" @click="handlePutOff">
            <Icon class="mr-5px" icon="ep:download" />
            {{ t('action.moveOffShelf') }}
          </el-button>
          <el-button plain type="warning" class="mr-2" @click="jumpImporting">
            <Icon icon="ep:upload" class="mr-5px"/>
            {{ t('action.importingList') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table v-loading="loading" :data="tableData" height="600" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" fixed="left" />
        <el-table-column prop="id" width="95" :label="t('learningCenter.course.uniqueId')" fixed="left" />
        <el-table-column prop="name" :label="t('learningCenter.course.title')" min-width="316px" fixed="left">
          <template #default="{ row }">
            <div>
              <div class="flex flex-row items-center justify-center">
                <el-image :src="formatImgUrl(row.cover)" class="w-[86px] h-[50px]" />
                <div class="pl-5 w-[230px]">
                  <el-tooltip
                    :content="row.name" popper-class="tooltip-mywidth"
                    effect="dark" placement="top"
                  >
                    <div class="break-all line-clamp-3">
                      {{ row.name }}
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="topic" :label="t('learningCenter.course.subTitle')" min-width="185">
          <template #default="{ row }">
            <el-tooltip
              :content="row.topic" popper-class="tooltip-mywidth"
              effect="dark" placement="top"
            >
              <div class="line-clamp-3">
                {{ row.topic }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="keywords" width="225" :label="t('category.topic.keyWords')">
          <template #default="{ row }">
            <el-tooltip
              :content="row.keywords" popper-class="tooltip-mywidth"
              effect="dark" placement="top"
            >
              <div class="line-clamp-3">
                {{ row.keywords }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.language')" align="left" width="100">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="row.language" />
          </template>
        </el-table-column>
        <el-table-column key="duration" :label="t('learningCenter.course.duration')" align="center" prop="duration" width="120">
          <template #default="{ row }">
            <span>{{ row.handDuration > 0 ? convertToTime(row.handDuration) : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="task" :label="t('learningCenter.course.tasks')" width="90">
          <template #default="{ row }">
            <el-link v-if="row.chapterNum > 0" type="primary" :underline="false" @click="handleToForm(row, 2)">
              {{ row.chapterNum }} Tasks
            </el-link>
            <span v-else class="text-primary">{{ row.chapterNum }} Tasks</span>
          </template>
        </el-table-column>
        <el-table-column prop="examNum" :label="t('learningCenter.course.exams')" width="90">
          <template #default="{ row }">
            <el-link v-if="row.examNum > 0" type="primary" :underline="false" @click="handleToForm(row, 2)">
              {{ row.examNum }} Exams
            </el-link>
            <span v-else class="text-primary">{{ `${row.examNum} ${t('learningCenter.course.exams')}` }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="requiredNum" :label="t('learningCenter.course.assignedNumber')" width="138">
          <template #default="{ row }">
            <el-link v-if="row.requiredNum > 0" type="primary" :underline="false" @click="handleToForm(row, 3, 1)">
              {{ row.requiredNum }}
            </el-link>
            <span v-else class="text-primary">{{ row.requiredNum }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isNew" :label="t('learningCenter.course.newCourse')" width="115">
          <template #default="{ row }">
            <!-- {{ row.isNew === true ? 'Yes' : row.isNew === false ? 'No' : '--' }} -->
            <el-switch v-model="row.isNew" size="large" :active-value="true" :inactive-value="false" @change="handleNewCourse(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="isRecommend" :label="t('learningCenter.course.isRecommend')" width="135" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.isRecommend" size="large" :active-value="true" :inactive-value="false" @change="handleRecommend(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="t('learningCenter.course.onShelfStatus')" width="125" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.status" size="large" :active-value="1" :inactive-value="0" @change="handleRowStatus(row)" />
          </template>
        </el-table-column>
        <el-table-column prop="level" :label="t('learningCenter.course.level')" width="155">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.COURSE_LEVEL" :value="scope.row.level" />
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.subTitle')" align="left" width="90">
          <template #default="{ row }">
            <el-button>{{ row.subtitle ? t('common.yes') : t('common.no') }}</el-button>
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.courseSource')" align="left" width="120">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.COURSE_SOURCE" :value="row.source" />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="t('global.createTime')" :formatter="dateFormatter" width="155" />
        <el-table-column fixed="right" :label="t('global.action')" min-width="330">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleToForm(row, 3)">
              <Icon icon="ep:user" />
              {{ t('action.assignCourse') }}
            </el-button>
            <el-button link type="primary" @click="handleToForm(row, 1)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <!-- <router-link :to="`/topicMgt/elearning-add/index/${row.id}`" class="link-type">

            </router-link> -->
            <el-button link type="primary" @click="handleToForm(row, 4)">
              <Icon icon="ep:view" />
              {{ t('action.statistics') }}
            </el-button>
            <el-button link type="primary" class="ms-0" @click="handleDelete(row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </ContentWrap>
  </div>


  <!-- 课程导入对话框 -->
  <Dialog v-model="showImport" :title="t('dialog.import')" width="400">
    <el-upload
        ref="uploadRef"
        action="#"
        :limit="1"
        accept=".xls,.xlsx"
        :before-upload="beforeUpload"
        drag
    >
      <Icon icon="ep:upload" />
      <div class="el-upload__text">{{ t('action.drag') }} <em>{{ t('action.upload') }}</em></div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <div class="el-upload__tip"> {{ t('action.uploadFormat') }} </div>
          <span>{{ t('action.fileFormat') }}</span>
          <el-link
              :underline="false"
              type="primary"
              @click="handleExport"
          >{{ t('action.downloadTemplate') }}
          </el-link>
        </div>
      </template>
    </el-upload>
    <template #footer>
      <div class="dialog-footer">
<!--        <el-button type="primary" @click="saveImportCourse">-->
<!--          {{ $t('global.confirm') }}-->
<!--        </el-button>-->
        <el-button @click="showImport = false">
          {{ t('global.cancel') }}
        </el-button>
      </div>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">
/** table中superior company的样式 */
:deep .custom-wrapper-disabled .el-input__wrapper{
  border: none;
  box-shadow: none;
  color: #007943;
  @apply cursor-text
}
:deep .custom-wrapper-disabled .el-input__inner {
    color: #fff;
}
:deep .custom-wrapper-disabled .el-input__inner:hover{
  border: none;
  color: #fff;
  cursor: text;
}
:deep .custom-wrapper-disabled .el-input.is-disabled .el-input__inner{
  -webkit-text-fill-color: #000000b3;
}
:deep .el-input__suffix {
    display: none;
}
:deep .custom-wrapper-disabled  .el-input.is-disabled .el-input__wrapper {
    background-color: #fff;
}
:deep .el-table--border .el-table__row:hover {
  background-color:#f5f7fa;
    .custom-wrapper-disabled .el-input__wrapper {
      background-color:#f5f7fa;
  }
}
:deep .el-tag + .el-tag{
  margin-left: 0;
  margin-top:2px
  }
</style>

<style  lang="scss">
// 设置tooltip-mywidth时，style不能添加scope
.tooltip-mywidth{
  width: 240px;
}
</style>
