<script setup lang="ts">
import Required from './components/Employee/Required.vue'
import Elective from './components/Employee/Elective.vue'
import Satisfaction from './components/Employee/Satisfaction.vue'
import StatisticsTable from './components/Employee/StatisticsTable.vue'
import TasksTable from './components/Tasks/TasksTable.vue'
import { getStatistics } from '@/api/topicMgt/elearning'

const props = defineProps<{
  courseId: string
  basicInformation: any
}>()
const { t } = useI18n()
const detail = ref<{
  electiveNum: number
  onScheduleNum: number
  overtimeNum: number
  requiredNum: number
  star: number
  totalNum: number
  requiredAndCompletedNum: number
}>()
const getInfo = async () => {
  detail.value = await getStatistics(props.courseId)
}
onMounted(() => {
  getInfo()
})
</script>

<template>
  <ContentWrap>
    <div>
      <el-tabs class="custom-course-tabs" type="card">
        <el-tab-pane :label="t('learningCenter.course.employee')">
          <div class="p-7">
            <span class="text-[#222222] text-xl"> {{ t('learningCenter.course.courseStatistics') }} </span>
            <div class="flex gap-7 h-[120px] my-5">
              <Required class="custom-bg flex-1 rounded-[10px]" :required-num="detail?.requiredNum || 0" :complete-num="detail?.requiredAndCompletedNum || 0" />
              <Elective class="custom-bg flex-1 rounded-[10px]" :num="detail?.electiveNum" />
              <Satisfaction class="custom-bg flex-1 rounded-[10px]" :num="detail?.star" />
            </div>
            <StatisticsTable :course-id="props.courseId" :course-title="basicInformation?.name" />
          </div>
        </el-tab-pane>
        <el-tab-pane :label="t('learningCenter.task.tasks')">
          <TasksTable :course-id="props.courseId" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </ContentWrap>
</template>

<style scoped lang="scss">
.custom-bg {
  @apply bg-[#E4F4EE];
}
</style>
