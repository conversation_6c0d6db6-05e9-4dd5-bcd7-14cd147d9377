<script setup lang='ts'>
const { t } = useI18n()
const props = withDefaults(defineProps<{
  num: number
}>(), {
  num: 0,
})
</script>

<template>
  <div class="w-full h-full flex justify-between p-5 bg-[#E4F4EE]">
    <div class="flex flex-1 flex-col">
      <span class="text-xl text-[#222222] font-bold">
        {{ props.num || 0 }}
      </span>
      <span class="text-xs text-[#5B6068] mt-2.5">
        {{ t('learningCenter.course.comprehensiveCourse') }}
      </span>
    </div>
    <div class="flex items-center justify-center">
      <el-rate
        :model-value="props.num || 0"
        allow-half
        disabled
        :style="{
          '--el-rate-fill-color': '#FF7F37',
          '--el-rate-icon-size': '38px',
          '--el-rate-void-color': '#FF7F37',
        }"
      />
    </div>
  </div>
</template>

<style scoped lang='scss'>

</style>
