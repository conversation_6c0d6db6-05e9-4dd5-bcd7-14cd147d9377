<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { getTasksStatistics, getTasksStatisticsDetail } from '@/api/topicMgt/elearning'
import { CourseChapterType } from '@/enums/chapter'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
const props = defineProps<{
  courseId: number
}>()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const list = ref()
const show = ref(false)
const selectedTask = ref()
const queryParams = ref({
  username: '',
  badgeNo: '',
  email: '',
  pageNo: 1,
  pageSize: 10,
})
const detailTotal = ref(0)
const detailList = ref()
const queryRef = ref()
const detailLoading = ref(false)
const getList = async () => {
  list.value = await getTasksStatistics(props.courseId)
}
/** 获取当前章节下所有用户的学习信息 */
const getDetailList = async () => {
  detailLoading.value = true
  try {
    const params = {
      courseId: props.courseId,
      courseChapterId: selectedTask.value.id,
      ...queryParams.value,
    }
    const data = await getTasksStatisticsDetail(params)
    detailList.value = data.list
    detailTotal.value = data.total
  } finally {
    detailLoading.value = false
  }
}
const handleViewDetail = (row: any) => {
  selectedTask.value = row
  show.value = true
  getDetailList()
}

const handleQuery = () => {
  queryParams.value.pageNo = 1
  getDetailList()
}
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="p-7">
    <el-table :data="list" border>
      <el-table-column type="index" :label="t('learningCenter.task.taskNo')" width="120" />
      <el-table-column :label="t('learningCenter.task.taskName')" prop="title" />
      <el-table-column :label="t('learningCenter.task.type')" prop="type">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.COURSE_CHAPTER_TYPE" :value="row.type" class="w-20 h-6 text-[#BF9121] text-sm rounded-[2px] flex items-center justify-center" />
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.task.progress')">
        <template #default="{ row }">
          <spna>{{ row.totalCompleteCount }}</spna>/<span>{{ row.totalAssignCount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('global.action')">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleViewDetail(row)">
            <Icon icon="ep:view" />
            {{ t('action.detail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--  -->
    <el-drawer v-model="show" size="80%">
      <div class="flex gap-2.5 items-center mb-5">
        <svg-icon icon-class="TaskDetail" class="text-lg" />
        <span class="text-[#233A35] text-xl">{{ selectedTask.title }}</span>
      </div>
      <el-form ref="queryRef" :model="queryParams" inline>
        <el-form-item :label="t('learningCenter.boarding.name')" prop="username">
          <el-input v-model="queryParams.username" class="!w-52" clearable @keydown.enter="handleQuery" />
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.badgeNo')" prop="badgeNo">
          <el-input v-model="queryParams.badgeNo" class="!w-52" clearable @keydown.enter="handleQuery" />
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.email')" prop="email">
          <el-input v-model="queryParams.email" class="!w-52" clearable @keydown.enter="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="detailLoading" :data="detailList" border>
        <el-table-column :label="t('learningCenter.boarding.name')" prop="userName" />
        <el-table-column :label="t('learningCenter.course.badgeNo')" prop="badgeNo" />
        <el-table-column :label="t('learningCenter.course.email')" prop="email" show-overflow-tooltip />
        <el-table-column :label="t('learningCenter.course.company')" prop="companyName" :width="120" />
        <el-table-column :label="t('learningCenter.course.deptName')" prop="departmentName" :width="120" />
        <el-table-column :label="t('learningCenter.course.section')" prop="sectionName" show-overflow-tooltip />
        <el-table-column :label="t('learningCenter.course.position')" prop="positionName" show-overflow-tooltip />
        <el-table-column v-if="selectedTask.type === CourseChapterType.Scorm || selectedTask.type === CourseChapterType.Exam || selectedTask.type === CourseChapterType.Aicc" :label="t('learningCenter.course.score')" prop="score" show-overflow-tooltip align="center">
          <template #default="{ row }">
            {{ row.score === null ? '--' : row.score }}
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.exam.examTimes')" prop="examTimes" show-overflow-tooltip align="center" width="110">
          <template #default="{ row }">
            {{ row.examTimes === null ? '--' : row.examTimes }}
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.exam.passFail')" prop="examStatus" show-overflow-tooltip align="center" width="90">
          <template #default="{ row }">
            {{ row.examStatus === null ? '--' : row.examStatus }}
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.status')" prop="status" :width="120" fixed="right">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.COURSE_STUDY_STATUS" :value="row.status" />
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="detailTotal > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="detailTotal" @pagination="getDetailList" />
    </el-drawer>
  </div>
</template>

<style scoped lang="scss"></style>
