<script setup lang='ts'>
const { t } = useI18n()
const props = withDefaults(defineProps<{
  requiredAnCompletedNum: number
  requiredNum: number
  completeNum: number
}>(), {
  requiredAnCompletedNum: 0,
  requiredNum: 0,
  completeNum: 0,
})

const calculatePercentage = (numerator: number, denominator: number) => {
  if (denominator === 0)
    return 0 // 防止除以0错误
  const percentage = (numerator / denominator) * 100
  return Math.floor(percentage) // 不四舍五入
}
</script>

<template>
  <div class="w-full h-full flex justify-between p-5 bg-[#E4F4EE]">
    <div class="flex flex-col">
      <span class="text-xl text-[#222222] font-bold">
        {{ props.requiredAnCompletedNum }}/{{ props.requiredNum }}
      </span>
      <span class="text-xs text-[#5B6068] mt-2.5">
        {{ t('learningCenter.course.mandatoryCourse') }}
      </span>
    </div>
    <div class="flex-1 shrink-0 text-right">
      <el-progress :width="90" type="circle" :percentage="calculatePercentage(props.completeNum, props.requiredNum)" :stroke-width="15" color="#0B61E9" stroke-linecap="butt" />
    </div>
  </div>
</template>

<style scoped lang='scss'>
:deep(.el-progress-circle__track){
  stroke: #007943;
}
</style>
