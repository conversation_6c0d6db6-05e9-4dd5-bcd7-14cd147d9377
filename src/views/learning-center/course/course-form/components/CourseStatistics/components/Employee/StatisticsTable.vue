<script setup lang="ts">
import download from '@/utils/download'
import moment from 'moment'
import type { FormInstance } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import { getEmployeeStatistics, listStatistics, exportCourseInfo } from '@/api/topicMgt/elearning'
import type { Statistics } from '@/typings/views/topicMgt/elearning'
import { ChapterTypeIcons as Icons } from '@/views/learning-center/course/course-form/scripts/ChapterTypeIcons'
// import type { CourseChapterType } from '@/enums/chapter'
import { ChapterStatus, CourseChapterType, SendStatus } from '@/enums/chapter'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
interface QueryParams {
  id: string
  studentName: string
  badgeNo: string
  email: string
  type: string
  pageNo: number
  pageSize: number
}

interface ResData {
  userId: number
  assignmentDetailId: number
  studentName: string
  badgeNo: string | null
  email: string
  department: string
  section: string | null
  position: string
  type: any
  onSchedule: boolean
  status: number
  star: string | null
  operator: string
  company: string | null
}
const props = defineProps<{
  courseId: any
  courseTitle: string
}>()
const EmailStatus: any = {
  [SendStatus.Sending]: 'Sending',
  [SendStatus.Succeeded]: 'SendSuccess',
  [SendStatus.Failed]: 'SendFail',
}
const queryRef = ref<FormInstance>()
const queryData = reactive<{
  queryParams: QueryParams
  total: number
  loading: boolean
}>({
  queryParams: {
    id: props.courseId as string,
    studentName: '',
    badgeNo: '',
    email: '',
    type: '',
    pageNo: 1,
    pageSize: 10,
  },
  total: 0,
  loading: false,
})
const { queryParams, total, loading } = toRefs(queryData)
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const list = ref<Statistics[]>()
const show = ref(false)
const selectedUser = ref<ResData>()
const statisticsData = ref()
const userOrgInfo = computed(() => {
  return [
    {
      icon: 'StatisticsDepartment',
      text: selectedUser.value?.department,
    },
    {
      icon: 'StatisticsSection',
      text: selectedUser.value?.section,
    },
    {
      icon: 'StatisticsPosition',
      text: selectedUser.value?.position,
    },
    {
      icon: 'StatisticsBadge',
      text: selectedUser.value?.badgeNo,
    },
    {
      icon: 'StatisticsEmail',
      text: selectedUser.value?.email,
    },
  ]
})
const getList = async () => {
  loading.value = true
  try {
    const res = await listStatistics(queryParams.value)
    if (res && res.list) {
      list.value = res.list
      total.value = res.total
    }
    else {
      list.value = []
      total.value = 0
    }
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
const handleViewDetail = async (row: any) => {
  selectedUser.value = row
  show.value = true
  const data = await getEmployeeStatistics(props.courseId, row.userId)
  statisticsData.value = data
}

const handleExport = async () => {
  try {
    await message.exportConfirm()
    const data = await exportCourseInfo(queryParams.value)
    download.excel(data,`${t('learningCenter.course.course')}-${props.courseTitle}-${moment().format('YYYY-MM-DD')}.xlsx`)
  } finally {}
}
getList()
</script>

<template>
  <div>
    <el-form ref="queryRef" :model="queryParams" inline>
      <el-form-item prop="studentName" :label="t('learningCenter.course.studentName')">
        <el-input v-model="queryParams.studentName" clearable class="!w-52" @keydown.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.badgeNo')" prop="badgeNo">
        <el-input v-model="queryParams.badgeNo" @keydown.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.email')" prop="email">
        <el-input v-model="queryParams.email" clearable class="!w-52" @keydown.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.type')" prop="type">
        <el-select v-model="queryParams.type" class="!w-36" clearable>
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('action.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('action.reset') }}
        </el-button>
        <el-button plain type="primary" @click="handleExport">
          <Icon class="mr-5px" icon="ep:download" />
          {{ t('action.export') }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border>
      <el-table-column :label="t('learningCenter.boarding.name')" prop="studentName" show-overflow-tooltip :width="300" fixed="left" />
      <el-table-column :label="t('learningCenter.course.badgeNo')" prop="badgeNo" :width="120" />
      <el-table-column :label="t('learningCenter.course.email')" prop="email" show-overflow-tooltip :width="260" />
      <el-table-column :label="t('global.company')" prop="company" show-overflow-tooltip :width="260" />
      <el-table-column :label="t('learningCenter.course.deptName')" prop="department" :width="120" />
      <el-table-column :label="t('learningCenter.course.section')" prop="section" show-overflow-tooltip :width="150" />
      <el-table-column :label="t('learningCenter.course.position')" prop="position" show-overflow-tooltip :width="150" />
      <el-table-column :label="t('learningCenter.course.type')" prop="type" :width="100">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.COURSE_TYPE" :value="row.type" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="Schedule" prop="onSchedule" :width="100">
        <template #default="{ row }">
          <span>{{ row.onSchedule ? 'Yes' : 'No' }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="Score" prop="quizScore" /> -->
      <el-table-column :label="t('learningCenter.course.star')" prop="star" />
      <el-table-column :label="t('learningCenter.course.operator')" prop="operator" show-overflow-tooltip />
      <el-table-column :label="t('learningCenter.course.sendingStatus')" align="center" prop="operator" fixed="right" width="120px">
        <template #default="{ row }">
          <svg-icon :icon-class="EmailStatus[row.messageStatus ?? 0]" class="text-[22px]" />
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.course.score')" prop="score" show-overflow-tooltip width="90" align="center">
        <template #default="{ row }">
          {{ row.score === null ? '--' : row.score}}
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.exam.examTimes')" prop="examTimes" show-overflow-tooltip :width="120" align="center">
        <template #default="{ row }">
          {{ row.examTimes === null ? '--' : row.examTimes}}
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.exam.passFail')" prop="examStatus" show-overflow-tooltip align="center" :width="120">
        <template #default="{ row }">
          {{ row.examStatus === null ? '--' : row.examStatus}}
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.course.status')" prop="status" :width="120" fixed="right">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.COURSE_STUDY_STATUS" :value="row.status" />
        </template>
      </el-table-column>
      <el-table-column :width="100" :label="t('global.action')" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleViewDetail(row)">
            <Icon icon="ep:view" />
            {{ t('action.detail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    <!-- 抽屉展示学员统计信息 -->
    <el-drawer v-model="show" size="80%">
      <div>
        <!-- 顶部用户信息 -->
        <div class="flex items-center gap-5">
          <span class="text-[#233A35] text-xl">{{ selectedUser?.studentName }}</span>
          <dict-tag :type="DICT_TYPE.COURSE_TYPE" :value="selectedUser?.type" class="w-24 h-8 text-sm text-primary bg-[#DEF4EA] flex items-center justify-center border border-primary rounded-[2px]" />
        </div>
        <!-- 组织结构信息 -->
        <div class="flex gap-5 my-5">
          <div v-for="(item, index) in userOrgInfo" :key="index" class="border border-[#E2E2E2] flex-1 py-4 px-5 rounded-md flex items-center gap-2">
            <div class="w-[30px] h-[30px] rounded-[4px] bg-primary flex items-center justify-center shrink-0">
              <svg-icon :icon-class="item.icon" class="text-base text-white" />
            </div>
            <span class="text-sm line-clamp-1" :title="item.text">{{ item.text }}</span>
          </div>
        </div>
        <!-- 个人统计信息 -->
        <div class="border border-[#E2E2E2] py-5 px-7">
          <span class="text-[#222222] text-xl"> {{ t('learningCenter.course.courseCatalogue') }} </span>
<!--          <div class="w-[125px] h-9 border-[2px] border-primary bg-primary rounded-[4px] px-4 leading-9 my-5">-->
<!--            <span class="text-white font-bold text-xl">{{ statisticsData?.length }}</span>-->
<!--            <span class="text-white text-sm ms-0.5">Total Tasks</span>-->
<!--          </div>-->
          <!-- 章节信息 -->
          <div>
            <el-table v-loading="loading" :data="statisticsData" border height="600">
              <el-table-column prop="name" :label="t('learningCenter.course.title')" :min-width="278" show-overflow-tooltip>
                <template #default="{ row }">
                  <div class="flex">
                    <div class="relative w-[40px] h-[40px]">
                      <svg-icon icon-class="ChapterType" class="text-[40px]" />
                      <svg-icon :icon-class="Icons[row.type as CourseChapterType]" class="text-base !absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
                    </div>
                    <div flex-1 flex items-center ms-4 class="ms-2 my-2">
                      <span class="text-sm text-[#233A35]">{{ row.name }}</span>
                      <span v-if="row.type === CourseChapterType.Scorm || row.type === CourseChapterType.Aicc" class="bg-[#F6E9C5] text-sm text-[#BF9121] px-1 ml-3 rounded-sm">{{row.type === CourseChapterType.Scorm ? 'SCORM' : 'AICC'}}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="examScore" :label="t('learningCenter.course.score')" width="90" align="center">
                <template #default="{ row }">
                  {{ row.examScore === null ? '--' : row.examScore }}
                </template>
              </el-table-column>
              <el-table-column prop="examTimes" :label="t('learningCenter.exam.examTimes')" width="110" align="center">
                <template #default="{ row }">
                  {{ row.examTimes === null ? '--' : row.examTimes }}
                </template>
              </el-table-column>
              <el-table-column prop="examStatus" :label="t('learningCenter.exam.passFail')" width="90" align="center">
                <template #default="{ row }">
                  {{ row.examStatus === null ? '--' : row.examStatus }}
                </template>
              </el-table-column>
              <el-table-column prop="status" :label="t('learningCenter.course.status')" width="80" align="center">
                <template #default="{ row }">
                  <div class="flex items-center">
                    <!-- Scrom分数 -->
<!--                    <div v-if="(row.status === ChapterStatus.Completed && row.type === CourseChapterType.Scorm)">-->
<!--                      <div v-if="row.score" class="ms-auto flex items-center mr-3">-->
<!--                        <div class="bg-gradient-to-b from-[#0EAFA2] to-[#007943] text-[#ffffff] px-1">-->
<!--                          <svg-icon icon-class="score" />-->
<!--                        </div>-->
<!--                        <div class="bg-[#DEF4EA] text-[#007943] text-sm  py-0.5">-->
<!--                          <span class="px-1 py-1">Score:{{ row.score }}</span>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                      <div v-else class="ms-auto flex items-center mr-3">-->
<!--                        <div class="bg-[#797979] text-[#F2F2F2] px-1">-->
<!--                          <svg-icon icon-class="score" />-->
<!--                        </div>-->
<!--                        <div class="bg-[#F2F2F2] text-[#797979] text-sm  py-0.5">-->
<!--                          <span class="px-1 py-1">Score:{{ row.score ? row.score : '&#45;&#45;' }}</span>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                    </div>-->
                    <!-- Exam 分数 -->
<!--                    <div v-if="(row.type === CourseChapterType.Exam)">-->
<!--                      <div v-if="row.status === ChapterStatus.Completed" class="ms-auto flex items-center mr-3">-->
<!--                        <div class="bg-gradient-to-b from-[#0EAFA2] to-[#007943] text-[#ffffff] px-1">-->
<!--                          <svg-icon icon-class="score" />-->
<!--                        </div>-->
<!--                        <div class="bg-[#DEF4EA] text-[#007943] text-sm  py-0.5">-->
<!--                          <span class="px-1 py-1">Score:{{ row.score }}</span>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                      <div v-else-if="row.status === ChapterStatus.Failed" class="ms-auto flex items-center mr-3">-->
<!--                        <div class="bg-[#F56C6C] text-[#ffffff] px-1">-->
<!--                          <svg-icon icon-class="score" />-->
<!--                        </div>-->
<!--                        <div class="bg-[#FFE7E7] text-[#F56C6C] text-sm  py-0.5">-->
<!--                          <span class="px-1 py-1">Score:{{ row.score }}</span>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                      <div v-else class="ms-auto flex items-center mr-3">-->
<!--                        <div class="bg-[#797979] text-[#F2F2F2] px-1">-->
<!--                          <svg-icon icon-class="score" />-->
<!--                        </div>-->
<!--                        <div class="bg-[#F2F2F2] text-[#797979] text-sm  py-0.5">-->
<!--                          <span class="px-1 py-1">Score:{{ row.score ? row.score : '&#45;&#45;' }}</span>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                    </div>-->
                    <!-- 课程状态以及图标 -->
                    <div class="mx-auto">
                      <!-- 课程状态为完成或者未完成 -->
                      <div v-if="[null, ChapterStatus.NotStart].includes(row.status) || row.status === ChapterStatus.Completed" class="w-6 h-6 rounded-full" :class="[null, ChapterStatus.NotStart].includes(row.status) ? 'border border-[#AEC2DD]' : 'bg-primary'"></div>
                      <!-- 课程为进行中  -->
                      <svg-icon v-else icon-class="DotUnfilled" class="text-2xl text-[#AEC2DD]" />
                      <!-- 资源总时间（暂无） -->
                      <!-- <span>123</span> -->
                      <!-- <el-button type="primary" class="!w-20 !h-6 ms-2.5">
                          {{ btnText[(item.status || ChapterStatus.NotStart) as ChapterStatus] }}
                        </el-button> -->
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-form--inline .el-form-item) {
  @apply mr-3;
}
</style>
