<script setup lang='ts'>
const { t } = useI18n()
const props = withDefaults(defineProps<{
  num: number
}>(), {
  num: 126,
})
</script>

<template>
  <div class="w-full h-full flex justify-between p-5 bg-[#E4F4EE]">
    <div class="flex flex-col">
      <span class="text-xl text-[#222222] font-bold">
        {{ props.num || 0 }}
      </span>
      <span class="text-xs text-[#5B6068] mt-2.5">
        {{ t('learningCenter.course.electiveCourse') }}
      </span>
    </div>
    <div class="flex-1 shrink-0 text-right">
      <svg-icon icon-class="StatisticsElecives" class="!w-[173px] !h-[85px] text-primary" />
    </div>
  </div>
</template>

<style scoped lang='scss'>

</style>
