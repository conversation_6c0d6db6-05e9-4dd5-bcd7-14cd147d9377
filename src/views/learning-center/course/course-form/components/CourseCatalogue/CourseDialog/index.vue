<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import { formatChapterType } from './utils'
import AppendixResource from './components/AppendixResource.vue'
import Appendix from './components/Appendix.vue'
import CourseResource from '@/components/CourseResource/index.vue'
import type { ModelValue } from '@/components/LargeFileUpload/index.vue'
import { InfoChapter, addChapter, editChapter } from '@/api/topicMgt/elearning'
import { Audio, Compressed, EXCEL, PDF, PPT, TXT, Video, WORD } from '@/components/LargeFileUpload/script/FileAllowTypes'
import type { CourseOrigin } from '@/enums/CourseOrigin'
import type { MediaType } from '@/enums/resource'

interface CourseForm {
  formRef: FormInstance | null
  form: {
    fileList: any[]
    title: string
    attachments: []
    type: any
    duration?: number | undefined
    origin?: string | number | undefined
    lang?: string | undefined
    size?: number | undefined
    format?: string | undefined
    mediaType?: string | undefined
  }
  rules: FormRules<CourseForm['form']>
}
interface SelectedResource {
  origin: CourseOrigin
  format: string | undefined
  mediaType: MediaType
  lang: string[]
  duration: number
  fileId: number | undefined
  size: number | undefined
  url: string | undefined
  name: string | undefined
  resourceId: number | undefined
  title: string | undefined
}
const props = defineProps<{
  courseId: any
  taskMaxSort: any
  dataId: any | undefined
}>()
const emits = defineEmits<{
  (e: 'refresh'): void
}>()

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const fileList = ref<ModelValue[]>()
const attachmentList = ref()
const show = ref(false)
const resourceList = ref<SelectedResource[]>([])

const fileValidator = (rule: any, value: any, callback: any) => {
  if (!resourceList.value || !resourceList.value?.length) {
    callback(new Error('Please check the current file status'))
  }
  else {
    callback()
  }
}

const formData = reactive<CourseForm>({
  formRef: null,
  form: {
    fileList: [],
    title: '',
    attachments: [],
    type: undefined,
    lang: undefined,
    duration: undefined,
    fileId: '',
    fileUrl: '',
    fileName: '',
    origin: 0,
    size: 0,
    format: '',
    mediaType: '',
  },
  rules: {
    fileList: [{ required: true, validator: fileValidator }],
    title: [{ required: true, message: t('sys.user.userNicknameRule') }],
  },
})
const loading = ref(false)
const formLoading = ref(false)
const { form, rules, formRef } = toRefs(formData)


const getInfo = async () => {
  resetForm()
  loading.value = true
  try {
    const resData = await InfoChapter(props.dataId)
    form.value = resData
    resourceList.value = [
      {
        // url: resData?.fileUrl,
        // id: resData?.fileId,
        // contentType: resData.contentType,
        // name: resData.fileName,
        // title: resData.title,
        // lang: resData.lang,
        // duration: resData.duration,
        // size: resData.size,
        // format: resData.format ?? resData.fileName.substring(resData.fileName.lastIndexOf('.') + 1),
        // mediaType: resData.type,
        // origin: resData.origin,

        origin: resData.origin,
        format: resData.fileUrl.substring(resData.fileUrl.lastIndexOf('.') + 1),
        mediaType: resData.mediaType,
        lang: resData.lang.split(','),
        duration: resData.duration,
        fileId: resData.fileId,
        size: resData.size,
        url: resData.fileUrl,
        name: resData.fileName,
        resourceId: resData.resourceId,
        title: resData.title,
      },
    ]
    resData.attachments.map((item: any) => {
      attachmentList.value.push({
        fileId: item.fileId,
        name: item.fileName,
        url: item.fileUrl,
        size: item.size,
        format: item.fileUrl?.substring(item.fileUrl.lastIndexOf('.') + 1),
        mediaType: item.mediaType,
        origin: item.origin,
        lang: item.lang,
        duration: item.duration,
        resourceId: item.resourceId,
      })
    })
  } finally {
    loading.value = false
  }
}
/** 表单提交 */
const handleConfirm = () => {
  formRef.value?.validate( async (valid, fields) => {
    if (valid) {
      formLoading.value = true
      // 根据主文件地址，转换后的地址类型
      const chapterType = formatChapterType(resourceList.value[0].url)
      if (chapterType < 0) {
        message.warning(t('warning.unsupportedCourseFileTypes'))
        formLoading.value = false
        return
      }
      // 组织参数，文件参数，与章节类型参数需要特殊处理
      const dataParams = {
        ...form.value,
        attachments: attachmentList.value.map((file: any) => ({ // 提交的附件参数
          fileId: file.id || file.fileId,
          fileName: file.name || file.fileName,
          fileUrl: file.url || file.fileUrl,

          lang: file.lang.toString(),
          duration: file.duration,
          size: file.size,
          format: file.format,
          mediaType: file.mediaType,
          origin: file.origin,
        })),
        // Start 提交的大文件参数
        fileId: resourceList.value![0].fileId,
        fileUrl: resourceList.value![0].url,
        fileName: resourceList.value![0].name,
        // contentType: resourceList.value![0].type,
        type: chapterType,
        courseId: props.courseId,

        lang: (resourceList.value![0].lang).toString(),
        duration: Number(resourceList.value![0].duration),
        mediaType: Number(resourceList.value![0].mediaType),
        origin: resourceList.value![0].origin,
        size: resourceList.value![0].size,
        format: resourceList.value![0].format,
        title: resourceList.value![0].title,
        // End 提交的大文件参数
      }

      if (props.dataId) {
        try {
          await editChapter(dataParams)
          emits('refresh')
          message.success(t('global.editSuccess'))
          show.value = false
        } finally {
          formLoading.value = false
        }
      }
      else {
        dataParams.sort = props.taskMaxSort
        try {
          await addChapter(dataParams)
          emits('refresh')
          message.success(t('global.addSuccess'))
          show.value = false
        } finally {
          formLoading.value = false
        }
      }
    }
  })
}
const resetForm = () => {
  form.value = {
    title: '',
    attachments: [],
    type: undefined,
  }
  formRef.value?.resetFields()
  attachmentList.value = []
  fileList.value = []
  resourceList.value = []
}
watch(show, (newValue) => {
  if (newValue) {
    // 清空表单
    resetForm()
    if (props.dataId) {
      getInfo()
    }
  }
})
watch(fileList, (newValue) => {
  if (newValue) {
    if (fileList.value[0]?.origin) {
      form.value.title = fileList.value[0]?.origin.name
    }
  }
})
watch(() => resourceList, (newProps: any) => {
  fileList.value = newProps
}, {
  immediate: true,
  deep: true,
})
/* watch(() => attachmentList, (newProps: any) => {
  attachmentList.value = newProps
}, {
  immediate: true,
  deep: true,
}) */
defineExpose({ show })
</script>

<template>
  <Dialog v-model="show" :title="t('learningCenter.course.taskMgt')" :width="900">
    <div v-loading="loading" class="min-h-[320px]">
      <div class="w-full min-h-[500px]">
        <el-form ref="formRef" :model="form" :rules="rules" label-position="left">
          <el-form-item prop="fileList" :label="t('learningCenter.course.setTeachingContent')" :label-width="160">
            <CourseResource v-model="resourceList" class="w-full" :page-name-type="[...Compressed, ...Audio, ...Video, ...PDF, ...PPT, ...WORD]" :ooc="false" :limit="1" :edit="!props.dataId" :course="true" :appedixs="false" />
          </el-form-item>

          <el-divider border-style="dashed" />
          <el-form-item :label="t('learningCenter.exam.appendix')" prop="file" :label-width="160">
            <CourseResource v-model="attachmentList" class="w-full" :page-name-type="[...Compressed, ...Audio, ...Video, ...PDF, ...PPT, ...WORD, ...TXT, ...EXCEL]" :ooc="false" :limit="1" :edit="true" :course="false" :appedixs="true" />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div>
        <el-button type="primary" :loading="formLoading" @click="handleConfirm">
          {{ t('action.confirm') }}
        </el-button>
        <el-button @click="show = false">
          {{ t('action.cancel') }}
        </el-button>
      </div>
    </template>
  </Dialog>
</template>

<style scoped lang='scss'>
:deep .el-upload-dragger{
  padding: 0 !important;
  border: 1px solid var(--el-border-color);
}
</style>
