import { formatFileName } from '@/utils'
import { CourseChapterType } from '@/enums/chapter'
/**
 * 求当前值+1后的值，超过下标会重置为0
 * @param currentValue 当前值
 * @param maxValue 范围最大值
 * @returns +1后的值
 */
export const increment = (currentValue: number, maxValue: number) => {
  return (currentValue + 1) % (maxValue + 1)
}
/**
 * 求当前值-1后的值，超过下标会重置为范围最大值
 * @param currentValue 当前值
 * @param maxValue 范围最大值
 * @returns -1后的值
 */
export const decrement = (currentValue: number, maxValue: number) => {
  return (currentValue - 1 + maxValue + 1) % (maxValue + 1)
}
/**
 * 根据文件的URL确定当前的章节类型
 * @param fileUrl 文件url地址
 */
export const formatChapterType = (fileUrl: string) => {
  const fileName = formatFileName(fileUrl)?.split('/').pop() || ''
  if (fileName.match(/\.(doc|docx)$/i)) {
    return CourseChapterType.Document
  }
  else if (fileName.match(/\.(xls|xlsx)$/i)) {
    return CourseChapterType.Document
  }
  else if (fileName.match(/\.(pdf)$/i)) {
    return CourseChapterType.Document
  }
  else if (fileName.match(/\.(zip|rar|tar|gz|7z)$/i)) {
    return CourseChapterType.ZIP // 支持压缩文件
  }
  else if (fileName.match(/\.(ppt|pptx)$/i)) {
    return CourseChapterType.Document // 支持 PPT 文件
  }
  else if (fileName.match(/\.(mov|mp4|avi|mkv)$/i)) {
    return CourseChapterType.Video // 支持视频文件，包括 mov
  }
  else if (fileName.match(/\.(mp3|wav|ogg|flac)$/i)) {
    return CourseChapterType.Audio // 支持音频文件
  }
  else {
    return -1
  }
}
