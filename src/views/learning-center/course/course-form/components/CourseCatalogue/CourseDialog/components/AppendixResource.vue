<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import type { Resource } from '@/typings/views/Resource/list'
import { toUpper, unionBy } from 'lodash-es'
import { getAccessToken } from '@/utils/auth'
import { formatImgUrl } from '@/utils'
import ResourceSelector from '@/components/ResourceSelector/index.vue'
import { formatBytes, formatSecond } from '@/utils/ruoyi'
import { MediaType } from '@/enums/resource'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
const props = defineProps({
  modelValue: [String, Object, Array] as any,
  // 数量限制
  limit: {
    type: Number,
    // default: 5,
    default: 1,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 200,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['doc', 'txt', 'docx', 'pages', 'epub', 'pdf', 'numbers', 'csv', 'xls', 'xlsx', 'keynote', 'ppt', 'pptx'],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
  course: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'paramsfileList'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const number = ref(0)
const uploadList = ref<any[]>([])
const uploadFileUrl = ref(`${import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL}/infra/file/direct/upload`) // 上传文件服务器地址
const uploadData = ref({
  code: import.meta.env.VITE_STORAGE_CODE,
  uuid: 1
})
const headers = ref({ Authorization: `Bearer ${getAccessToken()}` })
const fileList = ref<any[]>([])
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize),
)
const show = ref(false)
const ids = ref([])
const selectedResources = ref<Resource[]>() // 从资源库选择的资源
const resourceList = ref([]) // 已选择或者已上传的资源列表
// const responseFileArray = ref([])
const resourceSelect = ref([])
const loading = ref(false)
const fileUpload = ref()
watch(() => props.modelValue, (val) => {
  if (val) {
    let temp = 1
    // 首先将值转为数组
    const list = Array.isArray(val) ? val : props.modelValue.split(',')
    // 然后将数组转为对象数组
    fileList.value = list.map((item: any) => {
      if (typeof item === 'string') {
        item = { name: item, url: item }
      }
      else {
        item = {
          name: item.fileName || item.name,
          url: item.fileUrl || item.url,
          id: item.id,
          percentage: 100,
        }
      }
      item.uid = item.uid || new Date().getTime() + temp++
      return item
    })
    // 已选择的资源列表
    resourceList.value = list.map((item: any) => {
      item = {
        fileName: item.fileName || item.name,
        fileUrl: item.fileUrl || item.url,
        id: item.id,
        percentage: 100,
        lang: item.lang || '1',
        duration: item.duration,
        mediaType: Number(item.mediaType),
        size: item.size,
        origin: item.origin,
      }
      return item
    })
  }
  else {
    fileList.value = []
    return []
  }
}, { deep: true, immediate: true })

// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.')
    const fileExt = fileName[fileName.length - 1]
    const isTypeOk = props.fileType.includes(fileExt)
    if (!isTypeOk) {
      message.error(t('error.fileTypeError'))
      return false
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 <= props.fileSize
    if (!isLt) {
      message.error(t(`${t('error.fileSizeError')} ${props.fileSize} ${t('error.m')}`))
      return false
    }
  }
  message.loading(t('common.uploadMessage'))
  number.value++
  return true
}

// 文件个数超出
const handleExceed = () => {
  message.error(t(`${t('error.fileLengthError')} ${props.limit}`))
}

// 上传失败
const handleUploadError = (_err: any) => {
  message.error(t('error.failedToUploadFile'))
}

// 上传成功回调
const handleUploadSuccess = (res: any, file: any) => {
  if (res.code === 200) {
    uploadList.value.push({
      name: file.name,
      url: res.data.url,
      id: res.data.id,
      fileType: res.data.fileType,
      percentage: 100,

      fileName: res.data.fileName,
      fileId: res.data.id,
      size: res.data.fileSize,
      lang: '1',
      duration: 300,
      mediaType: 3,
      origin: 0,
      format: res.data.fileType,
    })
    uploadedSuccessfully()
  }
  else {
    number.value--
    message.closeLoading()
    message.error(res.msg)
    fileUpload.value.handleRemove(file)
    uploadedSuccessfully()
  }
}

// 删除文件
const handleDelete = (index: number) => {
  message.confirm(`${t('global.deleteTip')}?`)
  fileList.value.splice(index, 1)
  emit('update:modelValue', fileList.value)
  message.success(t('global.deleteSuccess'))
  return fileList.value
}
/** 资源列表删除 */
const handleRLDelete = (item: any, index: number) => {
  message.confirm(`${t('global.deleteTip')}?`)
  resourceList.value.splice(index, 1)
  ids.value.splice(index, 1)
  fileList.value.splice(index, 1)
  emit('update:modelValue', fileList.value)
  message.success(t('global.deleteSuccess'))
  return resourceList.value
}
// 上传结束处理
const uploadedSuccessfully = () => {
  if (number.value > 0 && uploadList.value.length === number.value) {
    // fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value) // 多个值的情况
    fileList.value = uploadList.value
    // resourceList.value = resourceList.value.concat(fileList.value) // 多个值的情况
    resourceList.value = fileList.value
    uploadList.value = []
    number.value = 0
    // emit('update:modelValue', listToString(fileList.value))
    emit('update:modelValue', fileList.value)
    message.closeLoading()
  }
}

// 获取文件名称
const getFileName = (name: string) => {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.includes('/'))
    return name.slice(name.lastIndexOf('/') + 1)
  else
    return name
}

// 对象转成指定字符串分隔
const listToString = (list: any, separator?: any) => {
  let strs = ''
  separator = separator || ','
  for (const i in list) {
    if (list[i].url)
      strs += list[i].url + separator
  }
  return strs !== '' ? strs.substr(0, strs.length - 1) : ''
}

const handleDownload = (file: any) => {
  window.open(formatImgUrl(file))
}
/**
 * 个性化进度条
 * @param percentage 当前进度数字
 */
const customColor = (percentage: number) => {
  if (percentage >= 100) {
    return '#007943'
  }
  return '#007943'
}

const handleConfirm = (ids: number[] | undefined, resources: Resource[] | undefined) => {
  show.value = false
  // 处理资源
  // selectedResources.value = unionBy([...(selectedResources.value || []), ...(resources || [])], 'id')
  selectedResources.value = unionBy([...(resources || [])], 'id')

  resourceSelect.value = selectedResources.value.map((item) => {
    return {
      // id: `${item.id}`,
      fileId: `${item.fileId}`,
      fileUrl: `${item.address ? item.address : item.scormRunPath ? item.scormRunPath : ''}`,
      fileName: `${item.title ?? item.name}`,
      origin: 1,
      lang: `${item.lang ?? '1'}`,
      duration: `${item.duration}`,
      size: `${item.size}`,
      format: `${item.format ?? getMediaTypeLabel(item.mediaType)}`,
      mediaType: Number(`${item.mediaType}`),
      resource: true,
    }
  })
  // resourceList.value = unionBy([...(resourceSelect.value || []), ...responseFileArray.value], 'id')
  resourceList.value = resourceSelect.value
  // modelValue.value = resourceList.value
  emit('update:modelValue', resourceList.value)
}
const handleSelectResource = () => {
  show.value = true
}

const getMediaTypeLabel = (mediaType: number): string => {
  throw new Error('const not implemented.')
}

</script>

<template>
  <div>
    <div>
      <div class="flex w-full gap-2.5">
        <div
          class="w-[55%]"
        >
          <el-upload
            ref="fileUpload" multiple :action="uploadFileUrl" :before-upload="handleBeforeUpload"
            :file-list="fileList" :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed"
            :on-success="handleUploadSuccess" :show-file-list="false" drag :headers="headers"
            :data="uploadData"
          >
            <div class="my-3.5">
              <svg-icon icon-class="uploadfill" class="text-2xl" />
              <div class="el-upload__text">
                {{ t('error.dropFileHereOr ')}} <em>{{ t('action.upload') }}</em>
              </div>
            </div>
          </el-upload>
        </div>
        <!-- 文件列表 -->
        <!--     <ul name="el-fade-in-linear">
          <li v-for="(file, index) in fileList" :key="file.uid" class="flex items-center">
            <el-tooltip :content="`${file.name}`">
              <span class="cursor-pointer hover:text-primary w-32 line-clamp-1 break-words">
                {{ file.name }}
              </span>
            </el-tooltip>
            <el-progress :percentage="file.percentage" :color="customColor" class="flex-1 mx-2.5" />
            <el-icon class="cursor-pointer" @click="handleDelete(index)">
              <Remove />
            </el-icon>
          </li>
        </ul> -->

        <div class="flex-grow">
          <div class="flex flex-col justify-center gap-2.5 items-center basis-72 border border-dashed border-[#D6DEE3] cursor-pointer active:border-primary duration-300 transition-all rounded p-5" @click="handleSelectResource">
            <svg-icon icon-class="SelectResource" class="text-[22px]" />
            <span class="text-sm text-primary">{{ t('common.fromRepository') }}</span>
          </div>
        </div>
      </div>
      <div v-loading="loading">
        <div v-if="resourceList?.length">
          <div v-for="(item, index) in resourceList" :key="item.id" class="border border-dashed rounded border-[#D6DEE3] mt-1 p-3.5">
            <!-- 第一行 文件名称与删除按钮 -->
            <div class="flex items-center justify-between">
              <span class="text-sm text-[#233A35] line-clamp-1 mr-10">{{ item.fileName }}</span>
              <el-icon :size="14" class="cursor-pointer hover:text-primary" @click="handleRLDelete(item, index)">
                <Delete />
              </el-icon>
            </div>
            <!-- 进度条 -->
            <el-progress :percentage="100" class="flex-1" />
            <!-- 第二行操作行 -->
            <el-form :model="selectedResources" inline class="mt-5">
              <el-form-item prop="source">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="Source" class="text-base" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.source') }}</span>
                  </div>
                </template>
                <dict-tag :type="DICT_TYPE.RESOURCE_LIST_ORIGIN" :value="item.origin" />
              </el-form-item>
              <!-- 语言：从资源库选择的File类型，语言不可编辑 -->
              <el-form-item prop="lang">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="Lang" class="text-base" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.language') }}</span>
                  </div>
                </template>
                <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="item.lang" />
              </el-form-item>
              <!-- 时长：从资源库选择的File类型，时长不可编辑  -->
              <el-form-item v-if="item.resource" prop="duration">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="time" class="text-base text-[#606266]" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.duration') }}</span>
                  </div>
                  <span class="font-normal ms-1.5">{{ formatSecond(item.duration) }}</span>
                </template>
              </el-form-item>
              <!-- 时长：手动上传的File类型，时长需手动输入 -->
              <el-form-item v-else prop="duration">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="time" class="text-base text-[#606266]" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.duration') }}</span>
                  </div>
                </template>
                <span v-if="[MediaType.File].includes(item.mediaType)" class="text-[#606266] ">
                  {{ item.duration ? formatSecond(+(item.duration || 0)) : '' }}
                </span>

                <DurationInput v-else v-model="item.duration" />
              </el-form-item>
              <el-form-item prop="size">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="cube" class="text-base text-[#606266]" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.size') }}</span>
                  </div>
                  <span class="font-normal ms-1.5 whitespace-nowrap">{{ formatBytes(item.size || 0) }}</span>
                </template>
              </el-form-item>
              <el-form-item prop="Format">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="format" class="text-base text-[#606266]" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.format') }}</span>
                  </div>
                  <span class="font-normal ms-1.5 whitespace-nowrap">{{ item.format }}</span>
                </template>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <ResourceSelector v-model:ids="ids" v-model:show="show" type="single" :ooc-value="true" :course="false" @confirm="handleConfirm" />
    </div>
    <div>
      <!-- 上传提示 -->
      <div v-if="showTip">
        <template v-if="fileType">
          <div class="text-[#abacae]">
            {{ t('common.uploadFormat') }} {{ fileType.join("/") }}
          </div>
        </template>
        <template v-if="fileSize">
          <div class="text-[#abacae]">
            {{ t('error.fileSizeError') }} {{ fileSize }}{{ t('error.m') }}
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep .el-upload-dragger{
  padding: 0 !important;
  // border: 1px solid var(--el-border-color);
  @apply border-dashed border-[#D6DEE3] rounded #{!important};
}

.custom-cancel-button{
  margin-left: 10px
}
</style>
