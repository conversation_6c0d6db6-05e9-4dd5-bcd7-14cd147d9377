<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import ExamCourseType from '@/assets/images/exam-course-type.png'
import { InfoChapter, addChapter, editChapter } from '@/api/topicMgt/elearning'
import { addExam, getExam, updateExam } from '@/api/topicMgt/exam'

interface CourseForm {
  formRef: FormInstance | null
  form: {
    title: string
    type: number
  }
  examForm: {
    id: number | undefined
    name: string
    examTimeTemp: Array<Date>
    beginTime: string
    endTime: string
    type: string | undefined
    examMax: number
    passScore?: number | undefined
    answerTime?: number | undefined
    paperId: number | undefined
    paperName: string
    cutCount: number
    optionRandom: boolean
    quesRandom: boolean
    instruction: string
    classifyId?: number | undefined
    classifyName: string | undefined
    usage: number
    courseId: number | undefined
  }
  rules: FormRules<CourseForm['form']>
}
const props = defineProps<{
  courseId: any
  taskMaxSort: any
  dataId: any | undefined
  contentExamId: any | undefined
}>()
const emits = defineEmits<{
  (e: 'refresh'): void
  (e: 'choosePaperAction'): void
}>()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const show = ref(false)

const formData = reactive<CourseForm>({
  formRef: null,
  form: {
    title: '',
    type: 6,
  },
  examForm: {
    id: undefined,
    name: '',
    examTimeTemp: [],
    beginTime: '',
    endTime: '',
    type: undefined,
    examMax: 0,
    passScore: 1,
    answerTime: 1,
    paperId: undefined,
    paperName: '',
    cutCount: 0,
    optionRandom: true,
    quesRandom: true,
    instruction: '',
    classifyId: undefined,
    classifyName: undefined,
    usage: 1,
    courseId: undefined,
  },
  rules: {},
})
const loading = ref(false)
const { form, examForm, formRef } = toRefs(formData)
const examFormRef = ref<FormInstance>()
const examFormRules = ref({
  passScore: [
    {
      required: true,
      message: t('common.inputText'),
      trigger: 'blur',
    },
    { pattern: /^[1-9]\d*$/, message: t('common.inputNumberText'), trigger: 'blur' },
  ],
  answerTime: [
    {
      required: true,
      message: t('common.inputText'),
      trigger: 'blur',
    },
    { pattern: /^[1-9]\d*$/, message: t('common.inputNumberText'), trigger: 'blur' },
  ],
})
const getInfo = async () => {
  resetForm()
  loading.value = true
  try {
    examForm.value = await getExam(props.contentExamId)
  } finally {
    loading.value = false
  }
}
const resetForm = () => {
  form.value = {
    title: '',
    type: 6,
  }
  examForm.value = {
    id: undefined,
    name: '',
    examTimeTemp: [],
    beginTime: '',
    endTime: '',
    type: undefined,
    examMax: 0,
    passScore: 0,
    answerTime: 0,
    paperId: undefined,
    paperName: '',
    cutCount: 0,
    optionRandom: true,
    quesRandom: true,
    instruction: '',
    classifyId: undefined,
    classifyName: undefined,
    courseId: undefined,
  }
  formRef.value?.resetFields()
}
/** 表单提交 */
const handleConfirm = () => {
  // 先添加试卷，然后拿到试卷id再添加task
  // 组织参数，文件参数，与章节类型参数需要特殊处理
  examFormRef.value?.validate( async (valid, fields) => {
    if (valid) {
      examForm.value.usage = 1
      examForm.value.courseId = props.courseId
      if (examForm.value.id) {
        await updateExam(examForm.value)
        await addTask()
      }
      else {
        // 判断是否选择了试卷
        if (!examForm.value.paperId) {
          message.error(t('error.pleaseChooseExamPaper'))
          return
        }
        const data = await addExam(examForm.value)
        examForm.value.id = data
        addTask()
      }
    }
  })
}
const addTask = async () => {
  const dataParams = {
    contentExamId: examForm.value.id,
    title: examForm.value.name,
    type: 6,
    courseId: props.courseId,
  }

  if (props.dataId) {
    dataParams.contentExamId = props.contentExamId
    dataParams.id = props.dataId
    await editExam(dataParams)
    emits('refresh')
    message.success(t('global.editSuccess'))
    show.value = false
  }
  else {
    dataParams.sort = props.taskMaxSort
    await addChapter(dataParams)
    emits('refresh')
    message.success(t('global.addSuccess'))
    show.value = false
  }
}

const handleChoosePaper = () => {
  emits('choosePaperAction')
}
const handlePaperChoosed = (paper: any) => {
  examForm.value.name = paper.name
  examForm.value.classifyId = paper.classifyId
  examForm.value.classifyName = paper.classifyName
  examForm.value.type = paper.random ? '1' : '0'
  examForm.value.paperId = paper.id
}

watch(show, (newValue) => {
  if (newValue) {
    // 清空表单
    resetForm()
    if (props.dataId) {
      getInfo()
    }
  }
})
defineExpose({ handlePaperChoosed, show })
</script>

<template>
  <Dialog v-model="show" :title="props.dataId ? t('learningCenter.exam.editExam') : t('learningCenter.exam.addExam')" :width="800">
    <div v-loading="loading" class="flex min-h-[320px]">
      <div class="w-[100px] flex-none leading-7 text-[14px] text-black">
        {{ t('learningCenter.exam.examPaper') }}
      </div>
      <div class="flex-1">
        <el-button type="primary" plain :disabled="props.dataId" @click="handleChoosePaper">
          {{ t('action.chooseExamPaper') }}
        </el-button>
        <el-form ref="examFormRef" class="border border-[#D6DEE3] border-dotted mt-[10px] p-[15px]" :model="examForm" :rules="examFormRules">
          <el-form-item prop="name">
            <el-input v-model="examForm.name" class="w-full" clearable maxlength="100" show-word-limit />
          </el-form-item>
          <el-form-item class="flex my-[10px] text-[#808080]">
            <div>
              <img :src="ExamCourseType" />
              {{ t('common.subject') }}
              {{ examForm.classifyId === 0 ? 'No Subject' : examForm.classifyId === undefined ? '--' : examForm.classifyName }}
            </div>
            <div class="ml-[20px]">
              <img :src="ExamCourseType" />
              {{ t('common.type') }}
              <span v-if="`${examForm.type}` === '0'">{{ t('learningCenter.exam.customizedPaper') }} </span>
              <span v-else-if="`${examForm.type}` === '1'">{{ t('learningCenter.exam.autoPaper') }} </span>
              <span v-else>--</span>
            </div>
          </el-form-item>
          <div class="flex text-black">
            <el-form-item
              :label="t('learningCenter.exam.passScore')" prop="passScore" class="mt-2"
            >
              <el-input v-model="examForm.passScore" type="number" :min="1" class="!w-160px" :placeholder="t('learningCenter.exam.passScorePH')">
                <template #append>
                  {{ t('learningCenter.exam.points') }}
                </template>
              </el-input>
            </el-form-item>
            <el-form-item class="ml-[20px] mt-2" label="Exam Duration" prop="answerTime">
              <el-input v-model="examForm.answerTime" type="number" :min="1" class="!w-160px" :placeholder="t('learningCenter.exam.examScorePH')">
                <template #append>
                  {{ t('learningCenter.exam.minutes') }}
                </template>
              </el-input>
            </el-form-item>
          </div>
        </el-form>
        <div class="mt-1 text-[#ABACAE]">
          {{ t('learningCenter.exam.editExamTip') }}
        </div>
      </div>
    </div>
    <template #footer>
      <div>
        <el-button type="primary" @click="handleConfirm">
          {{ t('global.confirm') }}
        </el-button>
        <el-button @click="show = false">
          {{ t('global.cancel') }}
        </el-button>
      </div>
    </template>
  </Dialog>
</template>

<style></style>
