<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { getAccessToken } from '@/utils/auth'
import { formatImgUrl } from '@/utils'

const props = defineProps({
  modelValue: [String, Object, Array] as any,
  // 数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 200,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['doc', 'txt', 'docx', 'pages', 'epub', 'pdf', 'numbers', 'csv', 'xls', 'xlsx', 'keynote', 'ppt', 'pptx'],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue', 'paramsfileList'])
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const number = ref(0)
const uploadList = ref<any[]>([])
const baseUrl = import.meta.env.VITE_APP_BASE_API
const uploadFileUrl = ref(`${import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL}/infra/file/direct/upload`) // 上传文件服务器地址
const uploadData = ref({
  code: import.meta.env.VITE_STORAGE_CODE,
  uuid: 1
})
const headers = ref({ Authorization: `Bearer ${getAccessToken()}` })
const fileList = ref<any[]>([])
const fileUpload = ref()
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize),
)

watch(() => props.modelValue, (val) => {
  if (val) {
    let temp = 1
    // 首先将值转为数组
    const list = Array.isArray(val) ? val : props.modelValue.split(',')
    // 然后将数组转为对象数组
    fileList.value = list.map((item: any) => {
      if (typeof item === 'string') {
        item = { name: item, url: item }
      }
      else {
        item = {
          name: item.fileName || item.name,
          url: item.fileUrl || item.url,
          id: item.id,
        }
      }
      item.uid = item.uid || new Date().getTime() + temp++
      return item
    })
  }
  else {
    fileList.value = []
    return []
  }
}, { deep: true, immediate: true })

// 上传前校检格式和大小
const handleBeforeUpload = (file: any) => {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.')
    const fileExt = fileName[fileName.length - 1]
    const isTypeOk = props.fileType.includes(fileExt)
    if (!isTypeOk) {
      message.error(`${t('error.fileFormatIsNotCorrect')} ${props.fileType.join('/')} ${t('error.formatFile')}`)
      return false
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 <= props.fileSize
    if (!isLt) {
      message.error(`${t('error.fileSizeError')} ${props.fileSize} ${t('error.m')}`)
      return false
    }
  }
  message.loading(t('common.uploadMessage'))
  number.value++
  return true
}

// 文件个数超出
const handleExceed = () => {
  message.error(`${t('error.fileLengthError')} ${props.limit}`)
}

// 上传失败
const handleUploadError = (_err: any) => {
  message.error(t('error.failedToUploadFile'))
}

// 上传成功回调
const handleUploadSuccess = (res: any, file: any) => {
  if (res.code === 200) {
    uploadList.value.push({ name: file.name, url: res.data.url, id: res.data.id, fileType: res.data.fileType })
    uploadedSuccessfully()
  }
  else {
    number.value--
    message.closeLoading()
    message.error(res.msg)
    fileUpload.value.handleRemove(file)
    uploadedSuccessfully()
  }
}

// 删除文件
const handleDelete = (index: number) => {
  fileList.value.splice(index, 1)
  // emit('update:modelValue', listToString(fileList.value))
  emit('update:modelValue', fileList.value)
}

// 上传结束处理
const uploadedSuccessfully = () => {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value)
    uploadList.value = []
    number.value = 0
    // emit('update:modelValue', listToString(fileList.value))
    emit('update:modelValue', fileList.value)
    message.closeLoading()
  }
}

// 获取文件名称
const getFileName = (name: string) => {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.includes('/'))
    return name.slice(name.lastIndexOf('/') + 1)
  else
    return name
}

// 对象转成指定字符串分隔
const listToString = (list: any, separator?: any) => {
  let strs = ''
  separator = separator || ','
  for (const i in list) {
    if (list[i].url)
      strs += list[i].url + separator
  }
  return strs !== '' ? strs.substr(0, strs.length - 1) : ''
}

const handleDownload = (file: any) => {
  window.open(formatImgUrl(file))
}
</script>

<template>
  <div class="upload-file">
    <el-upload
      ref="fileUpload" multiple :action="uploadFileUrl" :before-upload="handleBeforeUpload"
      :file-list="fileList" :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed"
      :on-success="handleUploadSuccess" :show-file-list="false" :headers="headers" class="upload-file-uploader"
      :data="uploadData"
    >
      <!-- 上传按钮 -->
      <el-button type="primary" icon="Upload">
        {{ t('action.uploading') }}
      </el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      <!-- Supported format:.rar.zip.doc.pdf, a single file cannot exceed 20MB -->
      <template v-if="fileType">
        {{ t('common.uploadFormat') }} <b>{{ fileType.join("/") }}</b>
      </template>
      <template v-if="fileSize">
        <!--  style="color: #f56c6c" -->
        <div>{{ t('error.fileSizeError') }} <b>{{ fileSize }}{{ t('error.m') }}</b></div>
      </template>
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li v-for="(file, index) in fileList" :key="file.uid" class="el-upload-list__item ele-upload-list__item-content">
        <!-- {{ getFileName(file.name) }} -->
        <el-link :underline="false" target="_blank" @click="handleDownload(file.url)">
          <span class="el-icon-document">  {{ file.name }}</span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(index)">
            {{ t('action.delete') }}
          </el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
