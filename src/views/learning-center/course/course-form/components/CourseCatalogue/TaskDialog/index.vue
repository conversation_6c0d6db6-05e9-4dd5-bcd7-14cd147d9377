<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import Steps from './components/Steps.vue'
import { ChapterTypes, TeachingSteps } from './enums'
import { decrement, formatChapterType, increment } from './utils'
import AppendixUpload from './components/AppendixUpload.vue'
import LargeFileUpload from '@/components/LargeFileUpload/index.vue'
import type { ModelValue } from '@/components/LargeFileUpload/index.vue'
import { formatFileName } from '@/utils'
import { InfoChapter, addChapter, editChapter } from '@/api/topicMgt/elearning'

interface CourseForm {
  formRef: FormInstance | null
  form: {
    fileList: any[]
    title: string
    attachments: []
    type: any
  }
  rules: FormRules<CourseForm['form']>
}
const props = defineProps<{
  courseId: any
  taskMaxSort: any
  dataId: any | undefined
}>()
const emits = defineEmits<{
  (e: 'refresh'): void
}>()

const { t } = useI18n()
const message = useMessage() // 消息弹窗
// const fileList = ref<ModelValue[]>()
// const fileList = computed<ModelValue[]>({
//   get() {
//     if (props.data) {
//       return [{
//         url: props.data?.fileUrl,
//         id: props.data?.fileId,
//         name: formatFileName(props.data.fileUrl),
//       }] as ModelValue[]
//     }
//     return []
//   },
//   set(value) {

//   },
// })
const fileList = ref<ModelValue[]>()
const attachmentList = ref()
const show = defineModel({ default: false })
const steps = [
  {
    index: 0,
    text: t('common.selectTeachingForm')
  },
  {
    index: 1,
    text: t('common.selectTeachingContent')
  },
  {
    index: 2,
    text: t('common.learningSetting')
  }
]
const activeStep = ref(TeachingSteps.TeachingForm)

const courseTypes = [
  {
    type: 1,
    text: t('learningCenter.course.course'),
    icon: 'course_icon'
  }
  // {
  //   type: 6,
  //   text: 'Exam Paper',
  //   icon: 'exampaper_icon',
  // },
]
const activeType = ref(ChapterTypes.Course)
const fileValidator = (rule: any, value: any, callback: any) => {
  if (!fileList.value || !fileList.value?.length) {
    callback(new Error('Please check the current file status'))
  } else {
    callback()
  }
}
const formData = reactive<CourseForm>({
  formRef: null,
  form: {
    fileList: [],
    title: '',
    attachments: [],
    type: undefined
  },
  rules: {
    fileList: [{ required: true, validator: fileValidator }],
    title: [{ required: true, message: t('sys.user.userNicknameRule') }]
  }
})
const loading = ref(false)
const { form, rules, formRef } = toRefs(formData)
const getInfo = async () => {
  resetForm()
  loading.value = true
  try {
    const resData = await InfoChapter(props.dataId)
    form.value = resData
    fileList.value = [
      {
        url: resData?.fileUrl,
        id: resData?.fileId,
        type: resData.contentType,
        // name: formatFileName(resData.fileUrl),
        name: resData.fileName
      }
    ] as ModelValue[]
    attachmentList.value = resData.attachments
    if (!form.value?.type) {
      activeStep.value = TeachingSteps.TeachingForm
    } else {
      activeStep.value = TeachingSteps.TeachingContent
    }
  } finally {
    loading.value = false
  }
}
/**
 * 切换当前激活的章节类型
 * @param type 激活的类型
 */
const handleTypeClick = (type: ChapterTypes) => {
  activeType.value = type
}
const handlePrev = () => {
  activeStep.value = decrement(activeStep.value, steps.length - 1)
}
const handleNext = () => {
  activeStep.value = increment(activeStep.value, steps.length - 1)
}
/** 表单提交 */
const handleConfirm = () => {
  formRef.value?.validate( async (valid, fields) => {
    if (valid) {
      // 根据主文件地址，转换后的地址类型
      const chapterType = formatChapterType(fileList.value![0].url)
      if (chapterType < 0) {
        message.warning(t('warning.unsupportedCourseFileTypes'))
        return
      }
      // 组织参数，文件参数，与章节类型参数需要特殊处理
      const dataParams = {
        ...form.value,
        attachments: attachmentList.value.map((file: any) => ({
          fileId: file.id || file.fileId,
          fileName: file.name || file.fileName,
          fileUrl: file.url || file.fileUrl
        })),
        fileId: fileList.value![0].id,
        fileUrl: fileList.value![0].url,
        fileName: fileList.value![0].name,
        contentType: fileList.value![0].type,
        type: chapterType,
        courseId: props.courseId
      }
      if (props.dataId) {
        await editChapter(dataParams)
        emits('refresh')
        message.success(t('global.editSuccess'))
        show.value = false
      } else {
        dataParams.sort = props.taskMaxSort
        await addChapter(dataParams)
        emits('refresh')
        message.success(t('global.addSuccess'))
        show.value = false
      }
    } else {
      // 验证失败，根据验证的字段结果，激活页面
      //  如果文件未上传，confirm后定位到文件上传页面，否则全部定位到章节设置页面
      if (fields && fields.fileList) {
        activeStep.value = TeachingSteps.TeachingContent
      } else {
        activeStep.value = TeachingSteps.TeachingSetting
      }
    }
  })
}
const resetForm = () => {
  form.value = {
    title: '',
    attachments: [],
    type: undefined
  }
  formRef.value?.resetFields()
  attachmentList.value = []
  fileList.value = []
}
watch(show, newValue => {
  if (newValue) {
    // 清空表单
    resetForm()
    if (props.dataId) {
      getInfo()
    } else {
      activeStep.value = TeachingSteps.TeachingForm
    }
  }
})
</script>

<template>
  <Dialog v-model="show" :title="t('learningCenter.course.taskMgt')" :width="800">
    <div v-loading="loading" class="min-h-[320px]">
      <!-- 步骤选择 -->
      <Steps v-model="activeStep" :steps="steps" class="mb-5" />
      <!-- 步骤对应内容的容器 -->
      <div class="w-full">
        <el-form ref="formRef" :model="form" :rules="rules" :label-width="activeType === ChapterTypes.Course ? 120 : 'auto'" label-position="left">
          <!-- 1、类型选择 -->
          <div v-show="activeStep === TeachingSteps.TeachingForm" class="border flex justify-center py-16 gap-24">
            <div v-for="(item, index) in courseTypes" :key="index" class="w-[140px] h-[140px] rounded-[30px] bg-[#E4F4EE] flex flex-col items-center justify-center gap-3 cursor-pointer" :class="activeType === item.type ? 'border-[3px] border-primary' : undefined" @click="handleTypeClick(item.type)">
              <svg-icon :icon-class="item.icon" class="text-6xl" />
              <span class="text-[#222222] text-base">{{ item.text }}</span>
            </div>
          </div>
          <!-- 2、章节内容设置 -->
          <div v-show="activeStep === TeachingSteps.TeachingContent">
            <!-- 如果内容类型是非试卷类型，展示上传组件 -->
            <el-form-item v-if="activeType !== ChapterTypes.Exam" prop="fileList" label="Upload File">
              <LargeFileUpload v-model="fileList" :limit="1" :multiple="false" class="w-full">
                <template #tip>
                  <span>{{ t('common.uploadFormatMessage') }}</span>
                </template>
              </LargeFileUpload>
            </el-form-item>
            <!-- 试卷类型 -->
            <template v-else>
              <el-form-item prop="" :label="t('action.chooseExamPaper')">
                <el-button plain type="primary"><Icon icon="ep:plus" class="mr-5px" /> {{ t('action.chooseExamPaper') }} </el-button>
              </el-form-item>
              <el-form-item prop="" :label="t('learningCenter.exam.examPaperName')">
                <el-input />
              </el-form-item>
              <el-form-item prop="" :label="t('learningCenter.exam.examDuration')">
                <el-input />
              </el-form-item>
              <el-form-item prop="" :label="t('learningCenter.exam.examAttempts')">
                <el-input />
              </el-form-item>
              <el-form-item prop="" :label="t('learningCenter.exam.passScore')">
                <el-input />
              </el-form-item>
            </template>
          </div>
          <!-- 3、章节信息设置 -->
          <div v-show="activeStep === TeachingSteps.TeachingSetting">
            <template v-if="activeType !== ChapterTypes.Exam">
              <el-form-item :label="t('learningCenter.exam.taskName')" prop="title">
                <el-input v-model="form.title" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.exam.appendix')" prop="file">
                <AppendixUpload v-model="attachmentList" :limit="3" :is-show-tip="true" :file-size="200" />
              </el-form-item>
            </template>
            <template v-else>
              {{ 1 }}
            </template>
          </div>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div>
        <el-button v-if="activeStep !== TeachingSteps.TeachingForm" @click="handlePrev"> {{ t('action.previous') }} </el-button>
        <el-button v-if="activeStep !== TeachingSteps.TeachingSetting" @click="handleNext"> {{ t('common.nextLabel') }} </el-button>
        <el-button v-if="activeStep === TeachingSteps.TeachingSetting" type="primary" @click="handleConfirm"> {{ t('action.confirm') }} </el-button>
        <el-button @click="show = false"> Cancel </el-button>
      </div>
    </template>
  </Dialog>
</template>

<style></style>
