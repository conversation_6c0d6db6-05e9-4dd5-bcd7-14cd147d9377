<script setup lang='ts'>
// import { decrement, increment } from '../utils'

const props = defineProps<{
  steps: { index: number, text: string }[]
}>()

const active = defineModel({ default: 2 })

// function handleAdd() {
//   active.value = decrement(active.value, 2)
// //   active.value = increment(active.value, 2)
// }
</script>

<template>
  <!-- {{ active }}
  <el-button @click="handleAdd">
    active
  </el-button> -->

  <div class="flex max-w-800px] gap-2.5">
    <div v-for="(item, index) in props.steps" :key="item.index" class="flex items-center gap-1.5 basis-2/4">
      <div
        class="w-6 h-6 text-sm text-white border-2  rounded-full flex items-center justify-center shadow-[inset_0_4px_4px_0_rgba(0,0,0,0.25)] shrink-0 duration-300 transition-all"
        :class="active >= item.index ? 'bg-primary border-[#007943]' : 'bg-[#75867E] border-[#495C54]'"
      >
        {{ item.index + 1 }}
      </div>
      <span class="line-clamp-1 text-sm duration-300 transition-colors" :class="active >= item.index ? 'text-primary' : 'text-[#222222]'" :title="item.text">{{ item.text }}</span>
      <div v-if="index !== props.steps.length - 1" class="h-[1px]  flex-1 transition-colors duration-300" :class="active > item.index ? 'bg-primary' : 'bg-[#CDDBF1]'"></div>
    </div>
  </div>
</template>

<style>

</style>
