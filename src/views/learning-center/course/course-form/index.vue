<script setup lang="ts" name="CourseForm">
import type { Component, ComponentInternalInstance } from 'vue'
import BasicInfomation from './components/BasicInformation/index.vue'
import CourseCatalogue from './components/CourseCatalogue/index.vue'
import AssignUser from './components/AssignUser/index.vue'
import CourseStatistics from './components/CourseStatistics/index.vue'
import { getCourse, putOffCourse, putOnCourse, recommendCourse } from '@/api/topicMgt/elearning'
import { formatImgUrl } from '@/utils'
import { useTagsViewStore } from "@/store/modules/tagsView"
const { delView } = useTagsViewStore() // 视图操作
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const route = useRoute()
const { router, push, currentRoute } = useRouter() // 路由
const courseId = route.params.id

const menuItems = computed(() => [
  {
    id: 1,
    text: t('learningCenter.course.basicInfo'),
  },
  {
    id: 2,
    text: t('learningCenter.course.courseCatalogue'),
    disabled: !courseId,
  },
  {
    id: 3,
    text: t('learningCenter.course.assignScope'),
    disabled: !courseId,
  },
  {
    id: 4,
    text: t('learningCenter.course.courseStatistics'),
    disabled: !courseId,
  },
  // {
  //   id: 5,
  //   text: 'Records',
  //   disabled: !courseId,
  // },
])
const menuActive = ref((route.query.active as string) || '1')
// const routeActive = computed(() => route.query.active as string || '1')
const components: Record<any, Component> = {
  1: BasicInfomation,
  2: CourseCatalogue,
  3: AssignUser,
  4: CourseStatistics,
  // 5: Records,
}
const loading = ref(false)
const basicInformation = ref<{ name: string, cover: string, topic: string, status: number }>()
const handleSelect = (key: string) => {
  menuActive.value = key
  // router.replace(`/topicMgt/course-form/index/1263?active=${key}`)
}

const handleToList = () => {
  delView(unref(currentRoute))
  push('/learning-center/course')
}
/**
 * 上架/下架课程
 */
const handleRowStatus = async () => {
  // if (basicInformation.value.status === 1) {
  //   proxy?.$modal.msgWarning('The current course does not have resources and is prohibited from listing')
  //   basicInformation.value.status = basicInformation.value.status === 1 ? 0 : 1
  //   return
  // }
  if (basicInformation.value.status === 0) {
    try {
      await putOnCourse(basicInformation.value.id)
      message.success(t('common.updateSuccess'))
      await getInfo()
    } catch (e) {
      // basicInformation.value.status = 0
    }
  } else if (basicInformation.value.status === 1){
    try {
      await message.confirm(t('confirm.isOffShelf'))
      try {
        await putOffCourse(basicInformation.value.id)
        message.success(t('common.updateSuccess'))
        await getInfo()
      } catch (e) {
        // basicInformation.value.status = 1
      }
    } catch (e) {
      // basicInformation.value.status = basicInformation.value.status === 1 ? 0 : 1
    }
  } else {}
}
/** 获取课程基本信息 */
const getInfo = async () => {
  basicInformation.value = await getCourse(courseId as unknown as number)
}
watch(
  () => route.query,
  (newValue) => {
    if (newValue?.active) {
      menuActive.value = newValue?.active as string
    }
  },
)
onMounted(() => {
  if (courseId) {
    getInfo()
  }
})
</script>

<template>
  <div v-loading="loading">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <template v-if="basicInformation">
        <div class="flex gap-5 mr-16">
          <el-image :src="formatImgUrl(basicInformation.cover)" class="w-[154px] h-[90px] row-span-2" />
          <div class="pt-2.5">
            <span :title="basicInformation?.name" class="text-[#23293A] text-xl break-all">{{ basicInformation?.name }}</span>
            <div v-if="basicInformation.topic" class="flex gap-1.5 mt-1">
              <div class="w-5 h-5 bg-primary rounded-[4px] flex items-center justify-center">
                <svg-icon icon-class="CourseTopic" class="text-xs" />
              </div>
              <span class="text-sm text-primary">{{ basicInformation.topic || 'None' }}</span>
            </div>
          </div>
        </div>
      </template>
      <span v-else class="text-2xl"> {{ t('learningCenter.course.addCourse') }} </span>
      <!-- c -->
      <div v-if="basicInformation?.id" class="ms-auto flex-direction">
        <div class="mb-4">
          <el-button type="primary" class="w-[80px]" @click="handleToList">
            {{ t('action.back') }}
          </el-button>
        </div>
        <div v-if="basicInformation?.status === 1">
          <el-button type="primary" plain class="w-[80px]" @click="handleRowStatus">
            {{ t('action.offShelf') }}
          </el-button>
        </div>
        <div v-else>
          <el-button type="primary" class="w-[80px]" @click="handleRowStatus">
            {{ t('action.onShelf') }}
          </el-button>
        </div>
      </div>
      <div v-else class="ms-auto flex-shrink-0">
        <div>
          <el-button type="primary" class="w-[80px]" @click="handleToList">
            {{ t('action.back') }}
          </el-button>
        </div>
      </div>
    </div>
    <!-- 下方课程设置区域 -->
    <div class="flex mt-6 gap-5">
      <!-- 左侧菜单 -->
      <ContentWrap>
        <div class="w-[220px] shrink-0 rounded-x p-2.5 border border-[#CDDBF1]">
          <el-menu
            v-model="menuActive"
            :default-active="menuActive"
            :style="{
            '--el-menu-item-height': '34px',
          }"
            @select="handleSelect"
          >
            <el-menu-item v-for="menu in menuItems" :key="menu.id" :index="`${menu.id}`" :disabled="menu.disabled">
              {{ menu.text }}
            </el-menu-item>
          </el-menu>
        </div>
      </ContentWrap>
      <!-- 右侧组件内容 -->
        <div class="flex-1 overflow-hidden">
          <component
            :is="components[menuActive]"
            v-bind="{
            courseId,
            basicInformation,
          }"
          />
        </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-menu) {
  border-right: 0 !important;
  .el-menu-item {
    &.is-active {
      background-color: #E4F4EE;
      border-radius: 4px;
    }
  }
}
</style>
