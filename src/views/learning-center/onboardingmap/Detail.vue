<template>
  <div class="app-main-height" v-loading="loading">
    <div class="px-2 my-2 bg-white py-2">
      <el-descriptions
        v-if="onboardingMap"
        title="Onboarding Interactive Map Detail"
        border
      >
        <template #extra>
          <el-button :icon="Back" @click="router.push({name: 'OnboardingMap'})">
            Return
          </el-button>
          <el-button type="primary" :disabled="nodeEditing" :icon="Check" @click="handlePublish">
            Publish
          </el-button>
        </template>
        <el-descriptions-item label="Name">
          {{ onboardingMap?.name }}
        </el-descriptions-item>
        <el-descriptions-item label="Learning Mode">
          <el-radio-group v-model="selectedMode" size="large" :disabled="nodeEditing">
            <el-radio v-for="dict in getIntDictOptions(DICT_TYPE.LEARNING_ONBOARDING_MAP_MODE)" :key="dict.value" :label="dict.label" :value="dict.value"/>
          </el-radio-group>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <Splitpanes class="default-theme !bg-white ">
      <Pane :size="24" class="h-full !bg-transparent">
        <div class="w-full pt-2 px-2">
          <el-button class="w-full" auto-insert-space :icon="Plus" :disabled="nodeEditing"
                     @click="addNode">Add Node
          </el-button>
        </div>
        <draggable
          :draggable="nodeEditing"
          tag="div"
          :list="onboardingMap?.nodes"
          handle=".handle"
          item-key="name"
        >
          <template #item="{ element, index }">
            <el-card class="flex justify-center items-center m-2"
                     shadow="never"
                     :body-class="'w-full h-full cursor-pointer'"
                     :body-style="{padding: '0'}" @click="handleChangeNode(index)">
              <div class="w-full h-full flex justify-between items-center pe-1"
                   :class="{'bg-[#7fbca1] text-white': selectedNodeIndex === index, 'hover:bg-[#e5f1ec]': selectedNodeIndex !== index}">
                <div class="h-[50px] flex justify-center items-center px-2 handle hover:bg-black/5">
                  <el-icon>
                    <Rank/>
                  </el-icon>
                </div>
                <el-divider direction="vertical" class="node-divider m-0"/>
                <div class="h-[50px] flex-1 flex items-center px-2 py-3 space-x-1">
                  <template v-if="!(nodeEditing && selectedNodeIndex === index)">
                    <el-image v-if="selectedNodeIndex === index" :src="defaultNodeImgW"
                              class="w-[25px]"/>
                    <el-image v-else :src="defaultNodeImgB" class="w-[25px]"/>
                  </template>

                  <el-input v-if="nodeEditing && selectedNodeIndex === index"
                            v-model="nodeInputValue" placeholder="Please input the node name"
                            @keydown.stop.prevent.enter="handleSaveNode(element)"/>
                  <span v-else class="line-clamp-1 text-sm font-bold">{{
                      index + 1
                    }}-{{ element.name }}</span>
                </div>
                <div v-if="nodeEditing && selectedNodeIndex === index"
                     class="px-1 space-x-1 flex items-center">
                  <el-icon @click="handleSaveNode(element)"
                           class="hover:text-[var(--el-color-danger)]">
                    <Check/>
                  </el-icon>
                  <el-icon @click="handleCancelNode" class="hover:text-[var(--el-color-danger)]">
                    <Close/>
                  </el-icon>
                </div>
                <el-dropdown v-else :disabled="nodeEditing"
                             @command="($event) => handleCommand($event, index)" trigger="click">
                  <el-button :icon="MoreFilled" :disabled="nodeEditing" circle text
                             @click.stop.prevent="null"/>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="e" :icon="Edit">Edit</el-dropdown-item>
                      <el-dropdown-item command="d" :icon="Delete">Delete</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </el-card>
          </template>
        </draggable>

      </Pane>
      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <div class="p-2">
            <OrgTotalBar :number="onboardingMap?.nodes[selectedNodeIndex].tasks.length || 0"
                         text="Total"/>
            <ContentWrap>
              <el-table
                :data="onboardingMap?.nodes[selectedNodeIndex].tasks"
                row-key="id"
              >
                <el-table-column type="index" label="Index" :width="100"
                                 fixed="left"/>
                <el-table-column prop="bizType" label="Content Type"
                                 width="260">
                  <template #default="scope">
                    <dict-tag :type="DICT_TYPE.LEARNING_ONBOARDING_MAP_CONTENT_TYPE" :value="scope.row.bizType" />
                  </template>
                </el-table-column>

                <el-table-column prop="bizName" label="Content Name" width="260"/>
                <el-table-column prop="duration" label="Duration (min)" width="200">
                  <template #default="{ row }">
                    <span v-if="row.duration !== -1">{{ formatSecond(row.duration) }} min</span>
                    <span v-else>{{ formatSecond(row.duration) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="mandatory" align="center"
                                 label="Mandatory" width="110">
                  <template #default="{ row }">
                    <el-switch v-model="row.mandatory" size="large" :active-value="true" :inactive-value="false" />
                  </template>
                </el-table-column>

                <el-table-column
                  :label="t('global.action')" align="center" class-name="small-padding fixed-width"
                  width="100"
                  fixed="right"
                >
                  <template #default="scope">
                    <el-button link type="primary" :disabled="nodeEditing" @click="handleDeleteTask(scope.$index)">
                      <Icon icon="ep:delete"/>
                      {{ t('action.delete') }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-button class="mt-4 w-full" :disabled="loading || nodeEditing" :icon="Plus" @click="handleAddContent">
                Add Content
              </el-button>
            </ContentWrap>
          </div>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <!-- 内容选择抽屉 -->
    <ContentSelect ref="contentSelectDrawerRef" @select-contents="handleSelectContents" />
  </div>
</template>

<script setup name="Company" lang="ts">
import {Pane, Splitpanes} from 'splitpanes'
import {
  addDept,
  delDept,
  getDept,
  listDept,
  listDeptExcludeChild,
  updateDept
} from '@/api/system/dept'
import 'splitpanes/dist/splitpanes.css'
import {
  OnboardingMapApi,
  OnboardingMapNode,
  OnboardingMapTask,
  OnboardingMapVO
} from '@/api/learning/onboardingmap'
import type {ElTree} from 'element-plus'
import OrgTotalBar from '@/components/OrgTotalBar/index.vue'
import {useI18n} from 'vue-i18n'
import {handlePhaseTree} from '@/utils/tree'
import {DICT_TYPE, getIntDictOptions, getStrDictOptions} from '@/utils/dict'
import {useRouteQuery} from "@vueuse/router"
import draggable from "vuedraggable"
import {Delete, Edit, MoreFilled, Plus, Rank, Check, Close, Back} from "@element-plus/icons-vue";
import defaultNodeImgW from '@/assets/images/onboarding-node-white.png'
import defaultNodeImgB from '@/assets/images/onboarding-node-black.png'
import ContentSelect from './components/ContentSelect.vue'
import {TempContent} from "@/views/learning-center/onboardingmap/components/type";
import router from "@/router";
import { formatSecond } from '@/utils/ruoyi';

defineOptions({name: 'OnboardingMapDetail'})


const id = ref(0)
const onboardingMap = ref<OnboardingMapVO>()
const selectedNodeIndex = ref(0)
const nodeEditing = ref(false)
const nodeInputValue = ref('')
const selectedMode = ref(10)

const contentSelectDrawerRef = ref()

const deptList = ref<any[]>([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref('')
const deptOptions = ref<any[]>([])
const isExpandAll = ref(true)
const onboardingMapTaskTable = ref(true)
const companyTreeRef = ref<InstanceType<typeof ElTree>>()
const queryRef = ref()
const deptRef = ref()
const deptTotal = ref(0)
const {t} = useI18n()
const message = useMessage() // 消息弹窗
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {},
  queryParams: {
    deptName: undefined,
    status: undefined,
    type: undefined,
  },
  rules: {
    parentId: [{required: false, message: t('sys.dept.parentIdRule'), trigger: 'blur'}],
    deptName: [{required: true, message: t('sys.dept.deptNameRule'), trigger: 'blur'}],
    shortName: [{required: true, message: t('sys.dept.shortNameRule'), trigger: 'blur'}],
    orderNum: [{required: true, message: t('sys.dept.orderNumRule'), trigger: 'blur'}],
    deptCode: [{required: false, message: t('sys.dept.deptCodeRule'), trigger: 'blur'}],
    type: [{required: true, message: t('sys.dept.typeRule'), trigger: 'blur'}],
  },
})

const {queryParams, form, rules} = toRefs(data)
const deptName = ref('')
/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await listDept(queryParams.value)
    deptList.value = handlePhaseTree(data, 'deptId')
    deptTotal.value = data.length
  } finally {
    loading.value = false
  }
}
/** 左侧查询公司树结构 */
const getOnboardingMap = async () => {
  try {
    loading.value = true
    onboardingMap.value = await OnboardingMapApi.getOnboardingMap(id.value)
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}

/** 通过条件过滤节点  */
const filterNode = (value: any, data: any) => {
  if (!value)
    return true
  return data.deptName.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  companyTreeRef.value!.filter(val)
})
const handleNodeClick = (node: any) => {
  queryParams.value.companyId = node.deptId
  getList()
}
/** 取消按钮 */
const cancel = () => {
  open.value = false
  reset()
}
/** 表单重置 */
const reset = () => {
  form.value = {
    deptId: undefined,
    parentId: undefined,
    deptName: undefined,
    orderNum: 0,
    status: '0',
    deptCode: undefined,
    type: undefined,
    shortName: undefined,
  }
  deptRef.value?.resetFields()
}
/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}
/** 展开/折叠操作 */
const toggleExpandAll = () => {
  onboardingMapTaskTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    onboardingMapTaskTable.value = true
  })
}
/** 新增按钮操作 */
const handleAdd = async (row: any) => {
  reset()
  try {
    const data = await listDept(queryParams.value)
    deptOptions.value = handlePhaseTree(data, 'deptId')
  } catch (e) {
  }
  if (row !== undefined) {
    form.value.parentId = row.deptId
  }
  open.value = true
  title.value = t('sys.dept.addDept')
}
/** 修改按钮操作 */
const handleUpdate = async (row: any) => {
  reset()
  const data = await listDeptExcludeChild(row.companyId, row.deptId)
  deptOptions.value = handlePhaseTree(data, 'deptId')
  const deptData = await getDept(row.deptId)
  form.value = deptData
  if (form.value.parentId === form.value.companyId) {
    form.value.parentId = null
  }
  open.value = true
  title.value = t('sys.dept.editDept')
}
/** 提交按钮 */
const handlePublish = async () => {
  try {
    if (onboardingMap.value!.nodes.length === 0) {
      message.warning('The Onboarding Interactive Map has no Node.')
      return
    }
    await message.confirm('Sure to publish?')
    loading.value = true
    await OnboardingMapApi.publishOnboardingMap(onboardingMap.value!)
    message.success(t('common.updateSuccess'))
  } finally {
    loading.value = false
  }
}

/** 删除按钮操作 */
const handleDeleteTask = async (index: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  onboardingMap.value!.nodes[selectedNodeIndex.value].tasks.splice(index, 1)
}

/** 处理节点的排序 **/
const handleModelSort = () => {
  // 保存初始数据
  // originalData.value = cloneDeep(props.categoryInfo.modelList)
  // isModelSorting.value = true
  // initSort()
}

const handleSelectContents = (tempContents: TempContent[]) => {
  onboardingMap.value!.nodes[selectedNodeIndex.value]!.tasks = tempContents.map((_tempContent: TempContent) => {
    return {
      nodeId: onboardingMap.value!.nodes[selectedNodeIndex.value].id!,
      bizType: _tempContent.contentType,
      bizId: _tempContent.contentId,
      bizName: _tempContent.contentName,
      duration: 0,
      mandatory: false
    }
  })
}

const handleAddContent = () => {
  // 增加防抖/延迟确保组件挂载
  setTimeout(() => {
    const component = unref(contentSelectDrawerRef)
    if (component && component.open && typeof component.open === 'function') {

      const currentContents = onboardingMap.value!.nodes[selectedNodeIndex.value]!.tasks.map((_task: OnboardingMapTask) => {
        return {
          contentId: _task.bizId,
          contentName: _task.bizName,
          contentType: _task.bizType
        }
      })

      component.open(currentContents) // 示例传参
      // 组装已选内容






    } else {
      console.error('ContentSelect 组件未正确挂载或 open 方法未暴露')
    }
  }, 50)
}

const removeAt = (idx) => {
  // this.list.splice(idx, 1);
}

const addNode = () => {
  const newIndex = Math.max(
    ...onboardingMap.value!.nodes.map((_node: OnboardingMapNode) => _node.sort)
  ) + 1
  onboardingMap.value!.nodes.push({
    mapId: onboardingMap.value!.id,
    name: '',
    icon: '',
    sort: newIndex,
    tasks: []
  })

  // 切换到新的Node
  nextTick(() => {
    nodeEditing.value = true
    nodeInputValue.value = ''
    selectedNodeIndex.value = onboardingMap.value!.nodes.length - 1
  })
}

const handleChangeNode = (index: number) => {
  if (nodeEditing.value) return
  selectedNodeIndex.value = index
}

/** 删除按钮操作 */
const handleDeleteNode = async (index: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  onboardingMap.value!.nodes.splice(index, 1)
}

const handleCommand = (command: 'e' | 'd', index: number) => {
  switch (command) {
    case 'e':
      nodeEditing.value = true
      selectedNodeIndex.value = index
      nodeInputValue.value = onboardingMap.value!.nodes[index].name
      break
    case 'd':
      handleDeleteNode(index)
      break
    default:
      break
  }
}

const handleSaveNode = (node: OnboardingMapNode) => {
  if (nodeInputValue.value === '') return
  node.name = nodeInputValue.value
  nodeInputValue.value = ''
  nodeEditing.value = false
}

const handleCancelNode = () => {
    if (onboardingMap.value!.nodes[selectedNodeIndex.value].name === '' && !onboardingMap.value!.nodes[selectedNodeIndex.value].id){
      onboardingMap.value!.nodes.splice(selectedNodeIndex.value, 1)
      selectedNodeIndex.value = 0
    }
  nodeInputValue.value = ''
  nodeEditing.value = false
}

/** 初始化 */
onMounted(() => {
  id.value = useRouteQuery('id', 0, {transform: Number}).value
  if (id.value) {
    getOnboardingMap()
  } else {
    message.error('Onboarding Interactive Map ID does not exist.')
  }
})

</script>

<style lang="scss" scoped>
:deep(.el-divider.node-divider) {
  margin: 0;
}
</style>
