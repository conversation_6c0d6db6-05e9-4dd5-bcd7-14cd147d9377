<script setup lang="ts" name="CourseCenter">
import type { ComponentInternalInstance } from 'vue'
import { omit } from 'lodash-es'
import { useUserStore } from '@/store/modules/user'
import download from '@/utils/download'
import {
  delCourse,
  importCourse,
  isNewCourse,
  listCourse,
  putOffCourse,
  putOnCourse,
  recommendCourse,
  exportCourse
} from '@/api/topicMgt/elearning'
import SearchCascader from '@/components/SearchCascader/index.vue'
import { listTopicAll } from '@/api/category/topic'
import { formatImgUrl } from '@/utils'
import { formatSecond, convertToTime } from '@/utils/ruoyi'
import ExcelJS from "exceljs"
import { useI18n } from "vue-i18n"
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import {ContentTypeEnum, TempContent} from "@/views/learning-center/onboardingmap/components/type";
import * as _ from 'lodash-es'

interface DataItem {
  createBy: string
  createTime: Date
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNum: number
  pageSize: number
  id: number
  name: string
  keywords?: string | null
  introduction: string
  departmentId: string
}

const courseRef = ref()

// 监听tempContents的改变，刷新table的selection
const changeTempContents = (newTempContents: TempContent[]) => {
  courseRef.value!.clearSelection()

  const rowsToSelect = tableData.value.filter(course =>
    newTempContents.some(temp => Number(temp.contentId) === Number(course.id))
  )
  toggleSelection(rowsToSelect, true) // 选中新项
}

const toggleSelection = (rows: DataItem[], isSelected: boolean = true) => {
  if (rows.length === 0) return
  rows.forEach((row) => {
    courseRef.value!.toggleRowSelection(row, isSelected)
  })
}



const emits = defineEmits<{ (e: 'selectChange')}>()

const selectedContents = ref<Record<number, DataItem[]>>([])


const userStore = useUserStore()
const router = useRouter()

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const loading = ref(false)
const tableData = ref<Array<DataItem>>([])
const total = ref(0)
const queryRef = ref()
const ids = ref<number[]>([])



/** 在批量上架时用到，标记是否选择课程都拥有资源 */
const isAllRes = ref(false)
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    departmentId: undefined,
    duration: undefined,
    subtitle: undefined,
    source: undefined,
    language: undefined,
    handDuration: undefined
  },
})
const importCourseForm = ref([])
const { queryParams } = toRefs(data)
const topicList = ref()
// const single = ref(false)
const multiple = ref(true)
const topicId = ref()
const durationList = [
  { id: 1, label: '<15min', durationLower: 0, durationUpper: 900 },
  { id: 2, label: '15 - 30min', durationLower: 900, durationUpper: 1800 },
  { id: 3, label: '30 - 60min', durationLower: 1800, durationUpper: 3600 },
  { id: 4, label: '>60min', durationLower: 3600, durationUpper: 999999 },
]

let inited = false

defineOptions({name: 'CourseList'})

const handleSearch = () => {
  getList()
}
const handleReset = () => {
  reset()
  getList()
}
/** 表单重置 */
const reset = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    departmentId: undefined,
    topicIdList: undefined,
    subtitle: undefined,
    source: undefined,
    language: undefined,
    handDuration: undefined,
  }
  topicId.value = undefined
  queryRef.value?.resetFields()
}
/** 选择条数  */
const handleSelectionChange = (selection: DataItem[]) => {
  selectedContents.value[data.queryParams.pageNum] = selection
  emits('selectChange')
}
const handleToForm = (row?: any, active?: number, activeTab?: number) => {
  if (activeTab === 1) {
    router.push({
      name: 'CourseForm',
      params: {
        id: row.id,
      },
      query: {
        active,
        tabActive: 1,
      },
    })
  }
  else {
    router.push({
      name: 'CourseForm',
      params: {
        id: row.id,
      },
      query: {
        active,
      },
    })
  }
}
/**
 * 是否新课
 */
const handleNewCourse = async (row: any) => {
  const param = {
    id: row.id,
    isNew: row.isNew !== false,
  }
  await isNewCourse(param)
  message.success(t('common.updateSuccess'))
  await getList()
}
/**
 *
 * @param row 上下架
 */
const handleRowStatus = async (row: any) => {
  if (row.chapterNum <= 0 && row.status === 1) {
    message.warning(t('warning.noResourceList'))
    row.status = row.status === 1 ? 0 : 1
    return
  }
  if (row.status === 1) {
    try {
      await putOnCourse(row.id)
      message.success(t('common.updateSuccess'))
      await getList()
    } catch (e) {
      row.status = 0
    }
  }
  if (row.status === 0) {
    try {
      await message.confirm(t('confirm.isOffShelf'))
      try {
        await putOffCourse(row.id)
        message.success(t('common.updateSuccess'))
        await getList()
      } catch (e) {
        row.status = 0
      }
    } catch(e) {
      row.status = row.status === 1 ? 0 : 1
    }
  }
}
const getList = async () => {
  loading.value = true
  try {
    if (topicId.value) {
      // queryParams.value.topicId = queryParams.value.topicId[queryParams.value.topicId.length - 1]
      queryParams.value.topicIdList = topicId.value.join(',')
    }
    const params = {
      ...omit(queryParams.value, ['handDuration']),
      handDurationLower: queryParams.value.handDuration?.durationLower,
      handDurationUpper: queryParams.value.handDuration?.durationUpper,
    }
    const data = await listCourse(params)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const getListTopicAll = async () => {
  topicList.value = await listTopicAll()
}

// 判断对象中是否存在这个key
const objKey = (item) => {
  if (item.hasOwnProperty('Estimated Duration')) {
    return true
  }
}

const initList = async () => {
  if (!inited) {
    await getList()
    await getListTopicAll()
    inited = true
  }
}

defineExpose({initList, selectedContents: selectedContents.value, changeTempContents})

onMounted(() => {

})
</script>

<template>
  <ContentWrap>
    <el-form ref="queryRef" :inline="true" :model="queryParams" @submit.prevent>
      <el-form-item :label="t('learningCenter.course.title')">
        <el-input v-model="queryParams.name" :placeholder="t('common.inputText')" clearable class="!w-240px" @keydown.enter="handleSearch" />
      </el-form-item>
      <el-form-item :label="t('category.topic.subjectName')" prop="topic">
        <SearchCascader v-model="topicId" :has-no-subject="false" class="!w-260px" />
      </el-form-item>
      <!--  -->
      <el-form-item :label="t('learningCenter.course.onShelfStatus')" prop="status">
        <el-select v-model="queryParams.status" clearable class="!w-273px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('learningCenter.course.newCourse')" prop="isNew"
      >
        <el-select v-model="queryParams.isNew" clearable class="!w-273px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RECOMMEND_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('learningCenter.course.isRecommend')" prop="isRecommend"
      >
        <el-select v-model="queryParams.isRecommend" clearable class="!w-273px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.RECOMMEND_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="t('learningCenter.course.isAssigned')" prop="assigned"
      >
        <el-select v-model="queryParams.assigned" clearable class="!w-273px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_ASSIGN_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="level" :label="t('learningCenter.course.level')">
        <el-select
          v-model="queryParams.level" clearable
          filterable
          class="!w-273px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="language" :label="t('learningCenter.course.language')">
        <el-select
          v-model="queryParams.language" clearable
          filterable
          class="!w-273px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="duration" :label="t('learningCenter.course.duration')">
        <el-select v-model="queryParams.handDuration" clearable value-key="id" class="!w-273px">
          <el-option v-for="item in durationList" :key="item.id" :label="item.label" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item prop="subtitle" :label="t('learningCenter.course.subTitle')">
        <el-select v-model="queryParams.subtitle" clearable class="!w-273px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COURSE_ASSIGN_STATUS)" :key="dict.id" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="source" :label="t('learningCenter.course.courseSource')">
        <el-select v-model="queryParams.source" clearable value-key="id" class="!w-273px">
          <el-option v-for="item in getIntDictOptions(DICT_TYPE.COURSE_SOURCE)" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <!-- resource_time -->
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <Icon icon="ep:search" class="mr-5px" />
          {{ t('action.search') }}
        </el-button>
        <el-button type="default" @click="handleReset">
          <Icon icon="ep:refresh" class="mr-5px" />
          {{ t('action.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" ref="courseRef" :data="tableData" @selection-change="handleSelectionChange" >
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column prop="id" width="95" :label="t('learningCenter.course.uniqueId')" fixed="left" />
      <el-table-column prop="name" :label="t('learningCenter.course.title')" :min-width="278" fixed="left">
        <template #default="{ row }">
          <div>
            <div class="flex flex-row items-center justify-center">
              <el-image :src="formatImgUrl(row.cover)" class="w-[86px] h-[50px]" />
              <div class="pl-5 w-[230px]">
                <el-tooltip
                  :content="row.name" popper-class="tooltip-mywidth"
                  effect="dark" placement="top"
                >
                  <div class="break-all line-clamp-3">
                    {{ row.name }}
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="topic" :label="t('learningCenter.course.subTitle')" min-width="185">
        <template #default="{ row }">
          <el-tooltip
            :content="row.topic" popper-class="tooltip-mywidth"
            effect="dark" placement="top"
          >
            <div class="line-clamp-3">
              {{ row.topic }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="keywords" width="225" :label="t('category.topic.keyWords')">
        <template #default="{ row }">
          <el-tooltip
            :content="row.keywords" popper-class="tooltip-mywidth"
            effect="dark" placement="top"
          >
            <div class="line-clamp-3">
              {{ row.keywords }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.course.language')" align="left" width="100">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="row.language" />
        </template>
      </el-table-column>
      <el-table-column key="duration" :label="t('learningCenter.course.duration')" align="center" prop="duration" width="120">
        <template #default="{ row }">
          <span>{{ row.handDuration > 0 ? convertToTime(row.handDuration) : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="task" :label="t('learningCenter.course.tasks')" width="90">
        <template #default="{ row }">
          <el-link v-if="row.chapterNum > 0" type="primary" :underline="false" @click="handleToForm(row, 2)">
            {{ row.chapterNum }} Tasks
          </el-link>
          <span v-else class="text-primary">{{ row.chapterNum }} Tasks</span>
        </template>
      </el-table-column>
      <el-table-column prop="examNum" :label="t('learningCenter.course.exams')" width="90">
        <template #default="{ row }">
          <el-link v-if="row.examNum > 0" type="primary" :underline="false" @click="handleToForm(row, 2)">
            {{ row.examNum }} Exams
          </el-link>
          <span v-else class="text-primary">{{ `${row.examNum} ${t('learningCenter.course.exams')}` }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="requiredNum" :label="t('learningCenter.course.assignedNumber')" width="138">
        <template #default="{ row }">
          <el-link v-if="row.requiredNum > 0" type="primary" :underline="false" @click="handleToForm(row, 3, 1)">
            {{ row.requiredNum }}
          </el-link>
          <span v-else class="text-primary">{{ row.requiredNum }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isNew" :label="t('learningCenter.course.newCourse')" width="115">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.GLOBAL_BOOLEAN" :value="row.isNew" />
        </template>
      </el-table-column>
      <el-table-column prop="isRecommend" :label="t('learningCenter.course.isRecommend')" width="135" align="center">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.GLOBAL_BOOLEAN" :value="row.isRecommend" />
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="t('learningCenter.course.onShelfStatus')" width="125" align="center">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.GLOBAL_BOOLEAN" :value="row.status" />
        </template>
      </el-table-column>
      <el-table-column prop="level" :label="t('learningCenter.course.level')" width="155">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COURSE_LEVEL" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.course.subTitle')" align="left" width="90">
        <template #default="{ row }">
          <el-button>{{ row.subtitle ? t('common.yes') : t('common.no') }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.course.courseSource')" align="left" width="120">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.COURSE_SOURCE" :value="row.source" />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="t('global.createTime')" width="155" />
    </el-table>
    <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
  </ContentWrap>
</template>

<style scoped lang="scss">
/** table中superior company的样式 */
:deep .custom-wrapper-disabled .el-input__wrapper{
  border: none;
  box-shadow: none;
  color: #007943;
  @apply cursor-text
}
:deep .custom-wrapper-disabled .el-input__inner {
    color: #fff;
}
:deep .custom-wrapper-disabled .el-input__inner:hover{
  border: none;
  color: #fff;
  cursor: text;
}
:deep .custom-wrapper-disabled .el-input.is-disabled .el-input__inner{
  -webkit-text-fill-color: #000000b3;
}
:deep .el-input__suffix {
    display: none;
}
:deep .custom-wrapper-disabled  .el-input.is-disabled .el-input__wrapper {
    background-color: #fff;
}
:deep .el-table--border .el-table__row:hover {
  background-color:#f5f7fa;
    .custom-wrapper-disabled .el-input__wrapper {
      background-color:#f5f7fa;
  }
}
:deep .el-tag + .el-tag{
  margin-left: 0;
  margin-top:2px
  }
</style>

<style  lang="scss">
// 设置tooltip-mywidth时，style不能添加scope
.tooltip-mywidth{
  width: 240px;
}
</style>

