<template>
  <el-drawer v-model="drawerVisible" size="80%" :with-header="false">
    <!--内容类型-->
    <el-affix>
      <div class="bg-white h-[60px] flex justify-center items-center">
        <div class="grid cols-10 gap-2 w-full py-2">
          <template v-for="contentType in contentTypes" :key="contentType.label">
            <div
              class="col-span-2 rounded border-light border-solid border-1 hover:border-stone hover:bg-light cursor-pointer rounded shadow flex justify-between items-center space-y-1 px-2 py-3"
              :class="{'bg-light border-stone': selectedContentTypeValue === contentType.value}"
              @click="changeContentType(contentType.value)">
              <el-text tag="b" size="small" align="center">{{ contentType.label }}</el-text>
              <el-tag size="small" :type="contentType.type ==='online' ? 'primary' : 'warning'" class="content-type-tag">
                {{ contentType.type }}
              </el-tag>
            </div>
          </template>
        </div>
      </div>
    </el-affix>
    <el-divider content-position="center">Select Contents</el-divider>

    <Splitpanes class=" !bg-white h-auto">
      <Pane :size="18" class="!bg-transparent">
<!--        <el-affix :offset="60">-->
        <div class="rounded me-2 p-1 bg-light">
          <template v-for="contentType in contentTypes" :key="contentType.label">
            <div v-if="getTempContents(contentType.value).length > 0" class="">
              <el-text size="small">{{contentType.label}}</el-text>
            </div>
            <div>
              <el-tag
                  v-for="(tempContent) in getTempContents(contentType.value)"
                  :key="tempContent.contentName"
                  closable
                  class="content-tag my-[2px] mx-[1px]"
                  @close="removeTempContent(tempContent)">
                <div class="text-wrap my-1">
                  {{ tempContent.contentName }}
                </div>
              </el-tag>
            </div>
          </template>
          <el-empty v-if="tempSelectedContents.length === 0" description="No content selected"/>
        </div>
<!--        </el-affix>-->
      </Pane>
      <Pane class="!bg-white">
        <!--各个内容模块的选择组件-->
        <div v-show="selectedContentTypeValue === ContentTypeEnum.COURSE">
          <CourseList ref="courseRef"
                      @select-change="handleSelectChange"/>
        </div>
        <div v-show="selectedContentTypeValue === ContentTypeEnum.ONBOARDING">
          <OnboardingList ref="onboardingRef"
                          :temp-contents="getTempContents(ContentTypeEnum.ONBOARDING)"
                          @select-change="handleSelectChange"/>
        </div>
        <div v-show="selectedContentTypeValue === ContentTypeEnum.ORIENTATION">
          <OrientationList ref="orientationRef"
                           :temp-contents="getTempContents(ContentTypeEnum.ORIENTATION)"
                           @select-change="handleSelectChange"/>
        </div>
        <div v-show="selectedContentTypeValue === ContentTypeEnum.COMPANY_POLICY">
          <PolicyList ref="policyRef"
                      :temp-contents="getTempContents(ContentTypeEnum.COMPANY_POLICY)"
                      @select-change="handleSelectChange"/>
        </div>
        <div v-show="selectedContentTypeValue === ContentTypeEnum.TRAINING">
          <TrainingList ref="trainingRef"
                        :temp-contents="getTempContents(ContentTypeEnum.TRAINING)"
                        @select-change="handleSelectChange"/>
        </div>
      </Pane>
    </Splitpanes>

    <template #footer>
      <el-button @click="drawerVisible = false">Cancel</el-button>
      <el-button v-if="formType !== 'view'" @click="addToMap" type="primary"
                 :disabled="formLoading">Confirm
      </el-button>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import CourseList from "@/views/learning-center/onboardingmap/components/CourseList.vue";
import OnboardingList from "@/views/learning-center/onboardingmap/components/OnboardingList.vue";
import OrientationList from "@/views/learning-center/onboardingmap/components/OrientationList.vue";
import PolicyList from "@/views/learning-center/onboardingmap/components/PolicyList.vue";
import TrainingList from "@/views/learning-center/onboardingmap/components/TrainingList.vue";
import {ContentTypeEnum, TempContent} from "@/views/learning-center/onboardingmap/components/type";
import {DICT_TYPE} from "@/utils/dict";
import {parseTime} from "@/utils/ruoyi";
import type {ElTree} from "element-plus";
import {Pane, Splitpanes} from "splitpanes";
import OrgTotalBar from "@/components/OrgTotalBar/index.vue";
import {Check, Close, Delete, Edit, MoreFilled, Plus, Rank} from "@element-plus/icons-vue";
import defaultNodeImgW from "@/assets/images/onboarding-node-white.png";
import defaultNodeImgB from "@/assets/images/onboarding-node-black.png";

/** 内容选择抽屉 */
defineOptions({name: 'ContentSelect'})

const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const drawerVisible = ref(false) // 弹窗的是否展示
const drawerTitle = ref('') // 弹窗的标题

const tempSelectedContents = ref<TempContent[]>([])


const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改 view - 查看
const formData = ref({
  id: undefined,
  name: undefined,
  roomNumber: undefined,
  location: undefined,
  picture: undefined,
  totalSeats: undefined,
  description: undefined
})

const contentTypes = [
  {label: 'Course', value: ContentTypeEnum.COURSE, type: 'online'},
  {label: 'Onboarding', value: ContentTypeEnum.ONBOARDING, type: 'online'},
  {label: 'Orientation', value: ContentTypeEnum.ORIENTATION, type: 'online'},
  {label: 'Company Policy', value: ContentTypeEnum.COMPANY_POLICY, type: 'online'},
  {label: 'Training', value: ContentTypeEnum.TRAINING, type: 'offline'},
]

const selectedContentTypeValue = ref<ContentTypeEnum>(ContentTypeEnum.COURSE)
const courseRef = ref()
const onboardingRef = ref()
const orientationRef = ref()
const policyRef = ref()
const trainingRef = ref()

const handleSelectChange = () => {
  tempSelectedContents.value = []

  // Course
  Object.values(courseRef.value.selectedContents).map(courses => {
    courses.map(course => {
      tempSelectedContents.value.push({
        contentId: course.id,
        contentName: course.name,
        contentType: ContentTypeEnum.COURSE
      })
    })
  })
  // Onboarding
  Object.values(onboardingRef.value.selectedContents).map(onboardings => {
    onboardings.map(onboarding => {
      tempSelectedContents.value.push({
        contentId: onboarding.id,
        contentName: onboarding.title,
        contentType: ContentTypeEnum.ONBOARDING
      })
    })
  })
  // Orientation
  Object.values(orientationRef.value.selectedContents).map(orientations => {
    orientations.map(orientation => {
      tempSelectedContents.value.push({
        contentId: orientation.id,
        contentName: orientation.title,
        contentType: ContentTypeEnum.ORIENTATION
      })
    })
  })
  // Policy
  Object.values(policyRef.value.selectedContents).map(policies => {
    policies.map(policy => {
      tempSelectedContents.value.push({
        contentId: policy.id,
        contentName: policy.title,
        contentType: ContentTypeEnum.COMPANY_POLICY
      })
    })
  })
  // Training
  Object.values(trainingRef.value.selectedContents).map(trainings => {
    trainings.map(training => {
      tempSelectedContents.value.push({
        contentId: training.id,
        contentName: training.title,
        contentType: ContentTypeEnum.TRAINING
      })
    })
  })

}

const getTempContents = (contentType: ContentTypeEnum) => {
  return tempSelectedContents.value.filter(_tempSelectedContent => _tempSelectedContent.contentType === contentType)
}

const removeTempContent = (tempContent: TempContent) => {
  const index = tempSelectedContents.value.findIndex((_tempContent: TempContent) => _tempContent.contentType === tempContent.contentType && _tempContent.contentId === tempContent.contentId)
  if (index > -1) {
    tempSelectedContents.value.splice(index, 1)
    changeContentType(selectedContentTypeValue.value)
  }
}


/** 打开弹窗 */
const open = async (currentContents: TempContent[]) => {
  drawerVisible.value = true
  drawerTitle.value = 'Select Content'
  // 如果还没有临时内容，则将tasks传过来作为已选内容
  if (tempSelectedContents.value.length === 0) {
    tempSelectedContents.value = currentContents
  }

  changeContentType(selectedContentTypeValue.value)
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['select-contents']) // 定义 success 事件，用于操作成功后的回调
const addToMap = async () => {
  emit('select-contents', tempSelectedContents.value)
  drawerVisible.value = false
  tempSelectedContents.value = []
}

const changeContentType = (type: ContentTypeEnum) => {
  selectedContentTypeValue.value = type
  nextTick(() => {
    switch (type) {
      case ContentTypeEnum.COURSE:
        courseRef.value.initList().then(() => {
          courseRef.value.changeTempContents(getTempContents(ContentTypeEnum.COURSE))
        })
        break
      case ContentTypeEnum.ONBOARDING:
        onboardingRef.value.initList().then(() => {
          onboardingRef.value.changeTempContents(getTempContents(ContentTypeEnum.ONBOARDING))
        })
        break
      case ContentTypeEnum.ORIENTATION:
        orientationRef.value.initList().then(() => {
          orientationRef.value.changeTempContents(getTempContents(ContentTypeEnum.ORIENTATION))
        })
        break
      case ContentTypeEnum.COMPANY_POLICY:
        policyRef.value.initList().then(() => {
          policyRef.value.changeTempContents(getTempContents(ContentTypeEnum.COMPANY_POLICY))
        })
        break
      case ContentTypeEnum.TRAINING:
        trainingRef.value.initList().then(() => {
          trainingRef.value.changeTempContents(getTempContents(ContentTypeEnum.TRAINING))
        })
        break
    }
  })
}

</script>

<style scoped>
  :deep(.el-tag.content-tag) {
    height: auto;
  }
  :deep(.el-tag.content-type-tag) {
    margin-top: 0;
  }
</style>
