<script setup lang="ts" name="OnboardAssign">
import type { ComponentInternalInstance } from 'vue'
import ScopeSelect from '@/components/ScopeSelect/index.vue'
import type { ScopeConfirm, ScopeData, ScopeSelectInstance } from '@/components/ScopeSelect/typings'
import { CourseType } from '@/enums/course'
import { AssignFun, AssignedList, delAssign } from '@/api/topicMgt/orientation'

const props = defineProps({
  stepId: {
    type: String,
    default: ''
  }
})
const activeNames = ref(CourseType.Elective)
const scopeTable = ref<ScopeSelectInstance>()
const route = useRoute()
const orientationId = route.params.id ? +route.params.id : +props.stepId
const { t } = useI18n()
const message = useMessage() // 消息弹窗

interface APIScopeData extends ScopeData {
  id: string
}
const scopeList = reactive({
  [CourseType.Elective]: [],
  [CourseType.Mandatory]: []
})
const getList = async () => {
  scopeList[activeNames.value] = await AssignedList({ orientationId, type: activeNames.value })
}
const handleConfirm = async (data: ScopeConfirm) => {
  // // 处理参数为可用形式
  const dataParams: any = data.scopes.map(item => ({
    relevanceId: item.relevanceId,
    relevanceName: item.relevanceName,
    scope: item.scope,
    type: 0 // activeName.value,
  }))
  await AssignFun(orientationId, data.scope, dataParams)
  scopeTable.value?.closeDialog()
  message.success(t('global.successfully'))
  await getList()
}

const handleDelete = async (userList: APIScopeData[] | APIScopeData, refValue: ScopeSelectInstance) => {
  const ids: any = (userList as APIScopeData[])?.length ? (userList as APIScopeData[]).map(item => item.id) : (userList as APIScopeData).id
  try {
    await message.confirm(t('confirm.deleteAssignment'))
    await delAssign(ids)
    message.success(t('global.deleteSuccess'))
    await getList()
    refValue.closeDialog()
  } catch {}
}
onMounted(() => {
  getList()
})
</script>

<template>
  <ContentWrap>
    <div class="w-full py-5 px-7">
      <h2 class="text-xl text-[#222222]">{{ t('learningCenter.course.assignScope') }}</h2>
      <div class="flex flex-template-cols mt-4">
        <div class="border bg-primary text-[#ffffff] bg-[#007943]  w-[160px] h-[40px] leading-[40px] text-center rounded-tl-[10px] rounded-tr-[10px]" style="border: 1px solid #007943">
          {{ t('learningCenter.companyPolicy.controlledScope') }}
        </div>
      </div>
      <div class="border rounded-r-[10px] rounded-br-[10px] rounded-bl-[10px] mb-[26px] p-[30px]" style="border: 1px solid #007943">
        <ScopeSelect
          ref="scopeTable"
          v-model="scopeList[activeNames]"
          :style="{
          '--employee-dialog-height': '100px'
        }"
          :table-props="{
          height: 400
        }"
          :employee-table-props="{
          height: 400
        }"
          @confirm="handleConfirm"
          @delete="handleDelete"
        />
      </div>
    </div>
  </ContentWrap>
</template>

<style scoped></style>
