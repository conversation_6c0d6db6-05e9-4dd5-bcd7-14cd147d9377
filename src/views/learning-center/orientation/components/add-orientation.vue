<script setup lang="ts" name="AddOrientation">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance } from 'element-plus'
import { formatChapterType } from '@/views/learning-center/course/course-form/components/CourseCatalogue/CourseDialog/utils'
import Assign from './assign.vue'
import Editor from '@/layout/components/Editor/index.vue'
import { addOrientation, detailOrientation, editOrientation } from '@/api/topicMgt/orientation'
import { getOrientationCategory } from '@/api/category/orientation'
import { PDF, Video } from '@/components/LargeFileUpload/script/FileAllowTypes'
import CourseResource from '@/components/CourseResource/index.vue'
import type { CourseOrigin } from '@/enums/resource'
import { MediaType } from '@/enums/resource'
import { formatFileType } from '@/views/resource/list/components/BatchUpload/script'
import { useTagsViewStore } from "@/store/modules/tagsView"
import ImageUpload from '@/components/ImageUpload/index.vue'
const { delView } = useTagsViewStore() // 视图操作
const { router, push, currentRoute } = useRouter() // 路由
const route = useRoute()
const formRef = ref<FormInstance>()
const menu_step2_Btndisabled = ref(true)
interface SelectedResource {
  origin: CourseOrigin
  format: string | undefined
  mediaType: MediaType
  lang: string[]
  duration: number
  fileId: number | undefined
  size: number | undefined
  url: string | undefined
  name: string | undefined
  resourceId: number | undefined
}
interface FormData {
  title?: string | undefined
  cover?: string | undefined
  categoryId?: string | undefined
  file?: string | undefined
  sort?: number | null
  description: string | undefined
  content?: string | undefined
  type?: number | undefined
  muploader?: null | undefined
  attachmentList?: [] | undefined
  display?: boolean | undefined
  keywords: string | []
}
const formOrien = ref<FormData>({
  title: '',
  cover: '',
  categoryId: '',
  file: '',
  sort: 1,
  content: '',
  description: '',
  type: 1,
  attachmentList: [],
  display: false,
  muploader: null,
  keywords: [],
  fileId: '',
  fileUrl: '',
  fileName: '',
  origin: 0,
  lang: '1',
  duration: 0,
  size: 0,
  format: '',
  mediaType: '',
})
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const categoryList = ref()
const activeIndex = ref('1')
const resqID = ref()
const detailId = route.params.id ? route.params.id : resqID.value

const inputValue = ref('')
const inputVisible = ref()
const InputRef = ref()
const repeatKeyword = ref(false)
const resourceCompData = ref<SelectedResource[]>([])
const rules = computed(() => ({
  sort: [{ required: false, message: t('category.journey.sortRule'), trigger: 'blur' }],
  title: [{ required: true, message: t('learningCenter.boarding.titleRule'), trigger: 'blur' }],
  display: [{ required: false, message: t('learningCenter.orientation.displayRule'), trigger: 'blur' }],
  categoryId: [{ required: true, message: t('category.journey.titleRule'), trigger: 'change' }],
  cover: [{ required: true, message: t('learningCenter.boarding.coverRule'), trigger: ['blur', 'change'] }],
  muploader: [{ required: true, message: t('learningCenter.boarding.fileRule'), trigger: ['blur', 'change'] }],
}))
// 使用 MediaType 枚举的键来获取对应的标签值
const getMediaTypeLabel = (mediaType: number | string) => {
  return Object.keys(MediaType).find(key => MediaType[key as keyof typeof MediaType] === mediaType) ?? ''
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}
const handleInputConfirm = () => {
  if (formOrien.value.keywords.includes(inputValue.value)) {
    repeatKeyword.value = true
    return
  }
  repeatKeyword.value = false
  if (inputValue.value) {
    formOrien.value.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCloseTag = (tag: string) => {
  formOrien.value.keywords.splice(formOrien.value.keywords.indexOf(tag), 1)
}

/** 表单重置 */
const reset = () => {
  formOrien.value = {
    title: undefined,
    cover: undefined,
    categoryId: undefined,
    file: undefined,
    sort: 1,
    content: undefined,
    description: undefined,
    type: 1,
    attachmentList: [],
    display: false,
    keywords: [],
    muploader: null,

    fileId: '',
    fileName: '',
    fileUrl: '',
    origin: 0,
    lang: '1',
    duration: 0,
    size: 0,
    format: '',
    mediaType: '',
  }
  formRef.value?.resetFields()
}
const handleSelect = (key: string) => {
  if (key === '1') {
    if (route.params.id || resqID.value) {
      const paramId = route.params.id ? route.params.id : resqID.value
      getDetailList(paramId)
    }
  }
  activeIndex.value = key
}
// 上传的文件参数
const fileParamsFun = () => {
  const fileListValue = resourceCompData.value?.map((item: any) => {
    return {
      origin: item?.origin,
      format: item?.format ?? getMediaTypeLabel(item?.mediaType),
      mediaType: formatChapterType(item?.url),
      lang: (item?.lang ?? ['1']).toString(),
      duration: item?.duration, // duration没有值的话，默认赋值5分钟
      fileId: item.fileId ?? item.id,
      size: item?.size,
      fileUrl: item.fileUrl ? item.fileUrl : item.url,
      fileName: item.fileName ? item.fileName : item.name,
      resourceId: item?.resourceId,
      fileType: item?.format, // 学员端列表页图片上的标签值，仅OOC使用
    }
  })
  formOrien.value.attachmentList = fileListValue
  formOrien.value.file = fileListValue[0].fileName
  return formOrien.value
}
// 提交
const handleSubmit = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  try {
    const keywords = formOrien.value.keywords.join(',')
    // keywords有重复的，提交时，去掉重复值
    if (repeatKeyword.value) {
      inputVisible.value = false
      repeatKeyword.value = false
      inputValue.value = ''
    }

    /** Start-提交的上传资源的参数 */
    formOrien.value.origin = resourceCompData.value[0]?.origin
    formOrien.value.format = resourceCompData.value[0]?.format ?? getMediaTypeLabel(resourceCompData.value[0]?.mediaType)
    formOrien.value.mediaType = formatChapterType(resourceCompData.value[0]?.url)
    formOrien.value.lang = (resourceCompData.value[0]?.lang ?? ['1']).toString()
    formOrien.value.duration = resourceCompData.value[0]?.duration
    formOrien.value.fileId = resourceCompData.value[0]?.fileId
    formOrien.value.size = resourceCompData.value[0]?.size
    formOrien.value.fileUrl = resourceCompData.value[0]?.url
    formOrien.value.fileName = resourceCompData.value[0]?.fileName
    formOrien.value.resourceId = resourceCompData.value[0]?.resourceId

    formOrien.value.fileType = resourceCompData.value[0]?.format // 学员端列表页图片上的标签值，仅OOC使用
    /** End-提交的上传资源的参数 */

    // delete formOrien.value.muploader
    if (route.params.id || resqID.value) {
      fileParamsFun()
      await editOrientation({ ...formOrien.value, keywords })
      message.success(t('common.updateSuccess'))
    }
    else {
      fileParamsFun()
      const data = await addOrientation({ ...formOrien.value, keywords })
      message.success(t('common.createSuccess'))
      handleSelect('2')
      resqID.value = data
      menu_step2_Btndisabled.value = false
    }
  } finally {}

}
/** 查看点击后的详情 */
const getDetailList = async (paramId: number) => {
  reset()
  const data = await detailOrientation(paramId)
  formOrien.value = data
  if (data.attachmentList && data.attachmentList.length > 0) {
    formOrien.value.file = data.attachmentList[0].fileName
    /** Start:Upload资源回显 */
    resourceCompData.value = [
      {
        origin: data.origin,
        format: data.format,
        mediaType: data.mediaType,
        lang: data.lang.split(','),
        duration: data.duration,
        fileId: data.attachmentList[0].fileId,
        size: data.attachmentList[0].size,
        url: data.attachmentList[0].fileUrl,
        name: data.attachmentList[0].fileName,
        resourceId: data.attachmentList[0].resourceId,
      },
    ]
    /** End:Upload资源回显 */
  }
  formOrien.value.muploader = resourceCompData.value
  formOrien.value.keywords = data.keywords ? data.keywords.split(',') : []
  menu_step2_Btndisabled.value = false
}
/** 跳转添加页面 */
const handleCancel = () => {
  delView(unref(currentRoute))
  push('/learning-center/orientation')
}
/** 查询category列表，没有分页 */
const getOrientationCategoryList = async () => {
  categoryList.value = await getOrientationCategory()
}
/** 获取封面图片字符串 */
const handleCover = (event: string) => {
  formOrien.value.cover = event
}

watch(() => resourceCompData.value, (newProps: any) => {
  formOrien.value.muploader = newProps
}, {
  immediate: true,
  deep: true,
})
onActivated(() => {
  // 表单重置
  reset()
  resqID.value = ''
  handleSelect('1')
  menu_step2_Btndisabled.value = true
  if (route.params.id) {
    menu_step2_Btndisabled.value = false
  }
})
onMounted(() => {
  handleSelect('1')
  getOrientationCategoryList()
})
</script>

<template>
  <div class="app-container">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <span class="text-2xl">
        {{ detailId ? t('learningCenter.orientation.editOrientation') : t('learningCenter.orientation.addOrientation') }}
      </span>
      <el-button class="ms-auto" type="primary" @click="handleCancel">
        {{ t('action.back') }}
      </el-button>
    </div>
    <div class="flex mt-6 gap-5">
      <ContentWrap>
        <div class="w-[220px] shrink-0 rounded-x p-2.5 border border-[#CDDBF1]">
          <el-menu :default-active="activeIndex" @select="handleSelect">
            <el-menu-item index="1">
              <span>{{ t('learningCenter.course.basicInfo') }}</span>
            </el-menu-item>
            <el-menu-item index="2" :disabled="menu_step2_Btndisabled">
              <span>{{ t('learningCenter.course.assignScope') }}</span>
            </el-menu-item>
          </el-menu>
        </div>
      </ContentWrap>
      <div class="flex-1 rounded-x border border-[#CDDBF1]">
        <ContentWrap v-if="activeIndex === '1'">
          <div class="py-5 px-7">
            <h2 class="text-xl text-[#222222] mt-5 mb-4">
              {{ t('learningCenter.course.basicInfo') }}
            </h2>
            <el-form ref="formRef" :model="formOrien" class="mt-5 w-3/4" :rules="rules" label-width="100px" label-position="left">
              <el-form-item :label="t('learningCenter.journey.title')" prop="title">
                <el-input v-model="formOrien.title" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.journey.cover')" prop="cover">
                <ImageUpload :model-value="formOrien.cover" :limit="1" @update:model-value="handleCover" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.journey.categoryTitle')" prop="categoryId">
                <el-select v-model="formOrien.categoryId" :placeholder="t('common.selectText')" clearable>
                  <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="t('action.uploadFile')" prop="muploader">
                <CourseResource v-model="resourceCompData" class="w-full" :page-name-type="[...Video, ...PDF]" :ooc="true" :limit="1" :edit="menu_step2_Btndisabled" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.orientation.display')" prop="display">
                <el-radio-group v-model="formOrien.display">
                  <el-radio :value="true">
                    {{ t('common.yes') }}
                  </el-radio>
                  <el-radio :value="false">
                    {{ t('common.no') }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="t('setting.banner.sort')" prop="sort">
                <el-input-number v-model="formOrien.sort" type="number" :min="0" :max="9999" clearable controls-position="right" class="w-full" />
              </el-form-item>
              <el-form-item prop="keywords" :label="t('category.topic.keyWords')">
                <div>
                  <div class="flex flex-wrap gap-2">
                    <el-tag v-for="tag in formOrien.keywords" :key="tag" closable :disable-transitions="false" @close="handleCloseTag(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" class="!w-60" size="small" maxlength="50" show-word-limit @keyup.enter="handleInputConfirm" @blur="handleInputConfirm" />
                    <el-button v-else-if="!inputVisible && (formOrien.keywords.length < 10)" class="button-new-tag" size="small" @click="showInput">
                      {{ t('action.addKeyWord') }}
                    </el-button>
                  </div>
                  <div v-if="repeatKeyword" class="text-[#f56c6c] text-xs mt-2">
                    {{ t('common.keyWords') }}
                  </div>
                </div>
              </el-form-item>
              <el-form-item :label="t('learningCenter.boarding.description')" prop="description">
                <el-input v-model="formOrien.description" type="textarea" :rows="5" show-word-limit maxlength="5000" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSubmit">
                  {{ t('global.confirm') }}
                </el-button>
                <el-button type="primary" plain @click="handleCancel">
                  {{ t('global.cancel') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </ContentWrap>
        <Assign v-if="activeIndex === '2'" :step-id="resqID" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.el-menu-item {
  height: 34px;
}
:deep(.el-menu) {
  border-right: 0 !important;
  .el-menu-item {
    &.is-active {
      background-color: #E4F4EE;
      border-radius: 4px;
    }
  }
}
</style>
