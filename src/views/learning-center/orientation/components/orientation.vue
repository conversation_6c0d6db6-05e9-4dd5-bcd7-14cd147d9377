<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance } from 'element-plus'
import { omit, toUpper } from 'lodash-es'
import {deleteOrientation, getOrientation, OrientationRespVO, updateDisplay} from '@/api/topicMgt/orientation'
import { getOrientationCategory } from '@/api/category/orientation'

import { MediaType } from '@/enums/resource'
import { formatSecond } from '@/utils/ruoyi'
import { formatImgUrl } from '@/utils'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
interface Query {
  pageNo: number
  pageSize: number
  title: string
  departmentId: string
  categoryId: string
  display?: boolean | undefined
  lang?: string | undefined
  duration?: number | string | undefined
  mediaType?: number | undefined
}

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const router = useRouter()
const tableList = ref()
const total = ref(0)
const formRef = ref<FormInstance>()
const loading = ref(false)
const queryParams = ref<Query>({
  pageNo: 1,
  pageSize: 10,
  title: '',
  departmentId: '',
  categoryId: '',
  display: undefined,
  lang: '',
  duration: '',
  mediaType: undefined,
})
const categoryList = ref()
const displayList = [
  { label: t('common.yes'), value: true },
  { label: t('common.no'), value: false },
]
const resource_list_type_file_video = [
  {
    label: t('global.video'),
    value: MediaType.Video,
  },
  {
    label: t('global.file'),
    value: MediaType.File,
  },
]
const DURATIONLIST = [
  { id: 1, label: '<15min', durationLower: 0, durationUpper: 900 },
  { id: 2, label: '15 - 30min', durationLower: 900, durationUpper: 1800 },
  { id: 3, label: '30 - 60min', durationLower: 1800, durationUpper: 3600 },
  { id: 4, label: '>60min', durationLower: 3600, durationUpper: 999999 },
]

const formatBg = (type: MediaType) => {
  const color = {
    [MediaType.Video]: 'bg-[#36A5D8]',
    [MediaType.Audio]: 'bg-[#B858F0]',
    [MediaType.File]: 'bg-[#F2A353]',
    [MediaType.Scorm]: 'bg-[#21AC6E]',
  }
  return color[type]
}
/** 获取Orientation列表 */
const getList = async () => {
  loading.value = true
  try {
    const params = {
      ...omit(queryParams.value, ['duration']),
      durationLower: queryParams.value.duration?.durationLower,
      durationUpper: queryParams.value.duration?.durationUpper,
    }
    const res = await getOrientation(params)
    tableList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 跳转添加页面 */
const handleAdd = () => {
  router.push({ name: 'AddOrientation' })
}
/** 跳转编辑页面  */
const handleEdit = (event: OrientationRespVO) => {
  router.push({ name: 'AddOrientation', params: { id: event.id } })
}
const handleSearch = () => {
  getList()
}
const handleRefresh = () => {
  formRef.value?.resetFields()
  queryParams.value.pageNo = 1
  queryParams.value.pageSize = 10
  handleSearch()
}
/** 删除 */
const handleDelete = async (event: OrientationRespVO) => {
  try {
    await message.confirm(`${t('global.deleteTip') + event.title}?`)
    await deleteOrientation(event.id)
    getList()
    message.success(t('global.deleteSuccess'))

  } catch {}
}
/** 查询category列表，没有分页 */
const getOrientationCategoryList = async () => {
  categoryList.value = await getOrientationCategory()
}
/** 列表修改是否 Display */
const handleDisplay = async (row: OrientationRespVO) => {
  const param = {
    id: row.id,
    display: row.display !== false,
  }
  try {
    await updateDisplay(param)
    message.success(t('global.editSuccess'))
    getList()
  } catch {
    row.display = false
  }
}
onMounted(() => {
  getList()
  getOrientationCategoryList()
})
</script>

<template>
  <ContentWrap>
    <el-form ref="formRef" :model="queryParams" inline class="-mb-15px" label-width="120px" @submit.prevent>
      <el-form-item :label="t('learningCenter.boarding.title')" prop="title">
        <el-input v-model="queryParams.title" :placeholder="t('common.inputText')" clearable class="!w-240px" @keyup.enter="handleSearch" />
      </el-form-item>
      <el-form-item :label="t('learningCenter.journey.categoryTitle')" prop="categoryId">
        <el-select v-model="queryParams.categoryId" :placeholder="t('common.selectText')" clearable class="!w-240px">
          <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('learningCenter.orientation.display')" prop="display">
        <el-select v-model="queryParams.display" :placeholder="t('common.selectText')" clearable class="!w-240px">
          <el-option v-for="item in displayList" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.format')" prop="mediaType">
        <el-select v-model="queryParams.mediaType" :placeholder="t('common.selectText')" clearable class="!w-240px">
          <el-option v-for="item in resource_list_type_file_video" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.language')" prop="lang">
        <el-select v-model="queryParams.lang" :placeholder="t('common.selectText')" clearable class="!w-240px">
          <el-option v-for="item in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('learningCenter.course.duration')" prop="duration">
        <el-select v-model="queryParams.duration" clearable value-key="id" class="!w-240px">
          <el-option v-for="item in DURATIONLIST" :key="item.id" :label="item.label" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('action.search') }}
        </el-button>
        <el-button @click="handleRefresh">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('action.reset') }}
        </el-button>
        <el-button plain type="primary" @click="handleAdd">
          <Icon class="mr-5px" icon="ep:plus" />
          {{ t('action.add') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" :data="tableList">
      <el-table-column :label="t('learningCenter.boarding.title')" prop="title" min-width="316px">
        <template #default="{ row }">
          <div>
            <div class="flex flex-row">
              <el-image :src="formatImgUrl(row.cover)" class="w-[86px] h-[50px]" />
              <div class="pl-5 w-[230px]">
                <el-tooltip
                  :content="row.title" popper-class="tooltip-mywidth"
                  effect="dark" placement="top"
                >
                  <div class="break-all line-clamp-3">
                    {{ row.title }}
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.journey.categoryTitle')" prop="categoryName" width="150px" />
      <el-table-column :label="t('learningCenter.course.duration')" align="center" :width="100">
        <template #default="{ row }">
          <span>{{ row.duration !== null && row.duration > 0 ? formatSecond(row.duration) : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.course.language')" align="center" width="100">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="row.lang" />
        </template>
      </el-table-column>
      <el-table-column prop="mediaType" :label="t('learningCenter.course.format')" align="center" :width="90">
        <template #default="{ row }">
          <div
            v-if="row?.mediaType"
            class="size-11 text-sm text-white rounded-full flex items-center justify-center flex-shrink-0 m-auto relative overflow-hidden group"
            :class="formatBg(row.mediaType)"
          >
            <!-- @click="handlePreview(row)" -->
            {{ toUpper(row.format) }}
            <!--   <div class="absolute inset-0 opacity-0 bg-[#131313]/[.5] flex items-center justify-center group-hover:opacity-100 transition-opacity duration-200">
                <el-icon>
                  <View />
                </el-icon>
              </div> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('learningCenter.orientation.display')" prop="display" :width="150">
        <!-- <template #default="{ row }">
          {{ row.display ? 'Yes' : 'No' }}
        </template> -->
        <template #default="{ row }">
          <el-switch v-model="row.display" size="large" :active-value="true" :inactive-value="false" @change="handleDisplay(row)" />
        </template>
      </el-table-column>
      <el-table-column :label="t('setting.banner.sort')" prop="sort" :width="80" />
      <el-table-column :label="t('category.journey.creator')" prop="createBy" :width="130" />
      <el-table-column :label="t('category.journey.creationTime')" prop="creationTime" :formatter="dateFormatter" :width="180" />
      <el-table-column :label="t('global.action')" :width="300" align="center" fixed="right">
        <template #default="{ row }">
          <el-button text type="primary" @click="handleEdit(row)">
            <Icon icon="ep:edit" />
            {{ t('action.edit') }}
          </el-button>
          <el-button text type="primary" @click="handleDelete(row)">
            <Icon icon="ep:delete" />
            {{ t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
  </ContentWrap>
</template>

<style scoped lang="scss">
:deep .el-tag + .el-tag{
  margin-left: 0;
  margin-top:2px
  }
</style>
