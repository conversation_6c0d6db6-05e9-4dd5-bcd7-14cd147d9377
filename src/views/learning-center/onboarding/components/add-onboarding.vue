<script setup lang="ts" name="AddOnboarding">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance } from 'element-plus'
import { formatChapterType } from '../../course/course-form/components/CourseCatalogue/CourseDialog/utils'
import Assign from './assign.vue'
import { getAccessToken } from '@/utils/auth'
import Editor from '@/layout/components/Editor/index.vue'
import { addOnboarding, detailOnboarding, editOnboarding, getOnboardingCategory, OnboardingSaveVO } from '@/api/topicMgt/onboarding'
import CourseResource from '@/components/CourseResource/index.vue'
import { PDF, PPT, Video } from '@/components/LargeFileUpload/script/FileAllowTypes'
import { getDuration, secondToTime, switchSecond } from '@/api/category/tool'
import type { CourseOrigin } from '@/enums/resource'
import { MediaType } from '@/enums/resource'
import { useTagsViewStore } from "@/store/modules/tagsView"
import ImageUpload from '@/components/ImageUpload/index.vue'
const { delView } = useTagsViewStore() // 视图操作
const route = useRoute()
const { router, push, currentRoute } = useRouter() // 路由
const formRef = ref<FormInstance>()
interface SelectedResource {
  origin: CourseOrigin//
  format: string | undefined
  mediaType: MediaType
  lang: string[]
  duration: number
  fileId: number | undefined
  size: number | undefined
  url: string | undefined
  name: string | undefined
  resourceId: number | undefined
}

const form = ref<OnboardingSaveVO>({
  title: '',
  cover: '',
  categoryId: '',
  file: '',
  isMandatory: true,
  sort: 1,
  content: '',
  description: '',
  type: 1,
  attachmentList: [],
  fileList: [],
  keywords: [],
})
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const categoryList = ref()
const menu_step2_Btndisabled = ref(true)
const activeIndex = ref('1')
const canSetDuration = ref(false)
/**
 * isInit 区分新建、编辑;新建true
 * 新建时，需要对时间值进行转换
 * 编辑时，不需要转换，直接赋值即可
 *
 */
const isInit = ref(true)
const resqID = ref()
const detailId = route.params.id ? route.params.id : resqID.value

const inputValue = ref('')
const inputVisible = ref()
const InputRef = ref()
const repeatKeyword = ref(false)
const resourceList = ref<SelectedResource[]>([])

const rules = computed(() => ({
  isMandatory: [{ required: true, message: t('learningCenter.boarding.mandatoryRule'), trigger: 'blur' }],
  sort: [{ required: false, message: t('category.journey.sortRule'), trigger: 'blur' }],
  title: [{ required: true, message: t('learningCenter.boarding.titleRule'), trigger: 'blur' }],
  categoryId: [{ required: true, message: t('category.journey.titleRule'), trigger: 'change' }],
  cover: [{ required: true, message: t('learningCenter.boarding.coverRule'), trigger: ['blur', 'change'] }],
  fileList: [{ required: true, message: t('learningCenter.boarding.fileRule'), trigger: ['blur', 'change'] }],
  duration: [{ required: true, message: t('learningCenter.boarding.durationRule'), trigger: ['blur', 'change'] }],
}))
// 使用 MediaType 枚举的键来获取对应的标签值
const getMediaTypeLabel = (mediaType: string | number ) => {
  return Object.keys(MediaType).find(key => MediaType[key as keyof typeof MediaType] === mediaType) ?? ''
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value!.input!.focus()
  })
}
const handleInputConfirm = () => {
  if (form.value.keywords.includes(inputValue.value)) {
    repeatKeyword.value = true
    return
  }
  repeatKeyword.value = false
  if (inputValue.value) {
    form.value.keywords.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleCloseTag = (tag: string) => {
  form.value.keywords.splice(form.value.keywords.indexOf(tag), 1)
}
/** 表单重置 */
const reset = () => {
  form.value = {
    title: undefined,
    cover: '',
    categoryId: undefined,
    file: undefined,
    sort: 1,
    isMandatory: true,
    content: undefined,
    description: undefined,
    type: 1,
    attachmentList: [],
    fileList: [],
    keywords: [],

    fileId: '',
    fileUrl: '',
    fileName: '',
    origin: 0,
    lang: '1',
    duration: 0,
    size: 0,
    format: '',
    mediaType: '',
  }
  formRef.value?.resetFields()
}
const handleSelect = (key: string) => {
  if (key === '1') {
    if (route.params.id || resqID.value) {
      const paramId = route.params.id ? route.params.id : resqID.value
      getDetailList(paramId)
    }
    else {
      reset()
    }
  }
  else if (key === '2') {
    if (route.params.id || resqID.value) {
      const paramId = route.params.id ? route.params.id : resqID.value
      getDetailList(paramId)
    }
  }
  activeIndex.value = key
}
// 上传的文件参数:内层参数
const fileParamsFun = () => {
  const fileListValue = resourceList.value?.map((item: any) => {
    return {
      origin: item?.origin,
      format: item?.format ?? getMediaTypeLabel(item?.mediaType),
      mediaType: formatChapterType(item?.url),
      lang: (item?.lang ?? ['1']).toString(),
      duration: item?.duration,
      fileId: item.fileId ?? item.id,
      size: item?.size,
      fileUrl: item.fileUrl ? item.fileUrl : item.url,
      fileName: item.fileName ? item.fileName : item.name,
      resourceId: item?.resourceId,
      fileType: item?.format, // 学员端列表页图片上的标签值，仅OOC使用
    }
  })
  form.value.attachmentList = fileListValue
  form.value.file = fileListValue[0].fileName
  return form.value
}
// 提交
const submitAddOnboarding = async () => {
  const valid = await formRef.value.validate()
  if (!valid) return
  try {

    if (!form.value.isMandatory) {
      form.value.sort = null
    }
    const keywords = form.value.keywords.join(',')
    // keywords有重复的，提交时，去掉重复值
    if (repeatKeyword.value) {
      inputVisible.value = false
      repeatKeyword.value = false
      inputValue.value = ''
    }
    /** Start-提交的上传资源的参数 */
    form.value.origin = resourceList.value[0]?.origin
    form.value.format = resourceList.value[0]?.format ?? getMediaTypeLabel(resourceList.value[0]?.mediaType)
    form.value.mediaType = formatChapterType(resourceList.value[0]?.url)
    form.value.lang = (resourceList.value[0]?.lang ?? ['1']).toString()
    form.value.duration = resourceList.value[0]?.duration
    form.value.fileId = resourceList.value[0]?.fileId
    form.value.size = resourceList.value[0]?.size
    form.value.fileUrl = resourceList.value[0]?.url
    form.value.fileName = resourceList.value[0]?.name
    form.value.resourceId = resourceList.value[0]?.resourceId

    form.value.fileType = resourceList.value[0]?.format // 学员端列表页图片上的标签值，仅OOC使用
    /** End-提交的上传资源的参数 */
    // delete form.value.fileList
    if (form.value.ack && !form.value.duration) {
      ackDuration.value = true
      return
    }
    if (route.params.id || resqID.value) {
      fileParamsFun()
      await editOnboarding({ ...form.value, keywords })
      message.success(t('common.updateSuccess'))
    } else {
      fileParamsFun()
      const data = await addOnboarding({ ...form.value, keywords })
      message.success(t('common.createSuccess'))
      handleSelect('2')
      resqID.value = data
      menu_step2_Btndisabled.value = false
    }
  } finally {
  }
}
/** 查看点击后的详情 */
const getDetailList = async (paramId: number) => {
  reset()
  const data = await detailOnboarding(paramId)
  // 重置isInit值，编辑时不再重新计算EstimatedTime值
  isInit.value = true
  form.value = data
  form.value.estimateTimeTemp = secondToTime(form.value.estimateTime)
  if (data.attachmentList && data.attachmentList.length > 0) {
    form.value.file = data.attachmentList[0].fileName
    /** Start:Upload资源回显 */
    resourceList.value = [
      {
        origin: data.origin,
        format: data.format,
        mediaType: data.mediaType,
        lang: data.lang,
        duration: data.duration,
        fileId: data.attachmentList[0].fileId,
        size: data.attachmentList[0].size,
        url: data.attachmentList[0].fileUrl,
        name: data.attachmentList[0].fileName,
        resourceId: data.attachmentList[0].resourceId,
      },
    ]
    /** End:Upload资源回显 */
  }
  form.value.fileList = resourceList.value
  form.value.keywords = data.keywords ? data.keywords.split(',') : []
  menu_step2_Btndisabled.value = false
}
/** 跳转添加页面 */
const handleCancel = () => {
  delView(unref(currentRoute))
  push('/learning-center/onboarding')
}
/** 查询category列表，没有分页 */
const getOnboardingCategoryList = async () => {
  categoryList.value = await getOnboardingCategory()
}

watch(() => resourceList, (newProps: any) => {
  form.value.fileList = newProps
}, {
  immediate: true,
  deep: true,
})

const formateCanSetDuration = async () => {
  if (!form.value.fileList[0].type.includes('pdf')) {
    canSetDuration.value = true
  }
  else {
    canSetDuration.value = false
  }
}
const computationTime = async () => {
  if (!form.value.fileList[0].type.includes('pdf')) {
    canSetDuration.value = true
    const lengthTime = await getDuration(`${form.value.fileList[0].url}?token=${getAccessToken()}`)
    // 四舍五入取整
    const lengthTimeFormate = Math.round((lengthTime * 1 + Number.EPSILON) * 1) / 1
    form.value.fileList[0].duration = lengthTimeFormate
    // 初始化estimateTimeTemp
    form.value.estimateTimeTemp = secondToTime(lengthTimeFormate)
  }
  else {
    form.value.fileList[0].duration = 0
    // 初始化estimateTimeTemp
    form.value.estimateTimeTemp = null
    canSetDuration.value = false
  }
}
onMounted(() => {
  // 表单重置
  reset()
  resqID.value = ''
  handleSelect('1')
  menu_step2_Btndisabled.value = true
  if (route.params.id) {
    menu_step2_Btndisabled.value = false
  }
  getOnboardingCategoryList()
})
</script>

<template>
  <div class="app-container">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <span class="text-2xl">
        {{ detailId ? t('learningCenter.boarding.editBoarding') : t('learningCenter.boarding.addBoarding') }}
      </span>
      <el-button class="ms-auto" type="primary" @click="handleCancel">
        {{ t('action.back') }}
      </el-button>
    </div>
    <div class="flex mt-6 gap-5">
      <ContentWrap>
        <div class="w-[220px] shrink-0 rounded-x p-2.5 border border-[#CDDBF1]">
          <el-menu :default-active="activeIndex" @select="handleSelect">
            <el-menu-item index="1">
              <span>{{ t('learningCenter.course.basicInfo') }}</span>
            </el-menu-item>
            <el-menu-item index="2" :disabled="menu_step2_Btndisabled">
              <span>{{ t('learningCenter.course.assignScope') }}</span>
            </el-menu-item>
          </el-menu>
        </div>
      </ContentWrap>
      <div class="flex-1 rounded-x border border-[#CDDBF1]">
        <ContentWrap v-if="activeIndex === '1'">
          <div class="py-5 px-7">
            <h1 class="text-[#222] text-[20px]">
              {{ t('learningCenter.course.basicInfo') }}
            </h1>
            <el-form ref="formRef" :model="form" :rules="rules" class="mt-5 w-3/4" label-width="130px" label-position="left">
              <el-form-item :label="t('learningCenter.journey.title')" prop="title">
                <el-input v-model="form.title" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.journey.cover')" prop="cover">
                <ImageUpload v-model="form.cover" :limit="1" :file-size="50" :file-type="['jpg', 'png', 'jpeg', 'GIP']" :is-show-tip="true" :tip-text="t('learningCenter.course.coverPH')" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.journey.categoryTitle')" prop="categoryId">
                <el-select v-model="form.categoryId" :placeholder="t('common.selectText')" clearable>
                  <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
              </el-form-item>
              <!-- 暂时注释：TEXT空文本的情况 -->
              <!-- <el-form-item label="Type" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio :value="1">
                File
              </el-radio>
              <el-radio :value="2">
                Text
              </el-radio>
            </el-radio-group>
          </el-form-item> -->
              <el-form-item :label="t('action.uploadFile')" prop="fileList">
                <CourseResource v-model="resourceList" class="w-full" :page-name-type="[...Video, ...PDF]" :ooc="true" :limit="1" :edit="menu_step2_Btndisabled" />
              </el-form-item>
              <el-form-item :label="t('learningCenter.boarding.mandatory')" prop="isMandatory">
                <el-radio-group v-model="form.isMandatory">
                  <el-radio :value="true" size="large">
                    {{ t('common.yes') }}
                  </el-radio>
                  <el-radio :value="false" size="large">
                    {{ t('common.no') }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-show="form.isMandatory" :label="t('setting.banner.sort')" :prop="form.isMandatory ? 'sort' : ''">
                <el-input-number v-model="form.sort" type="number" :min="0" :max="9999" clearable controls-position="right" calss="w-full" />
              </el-form-item>
              <el-form-item prop="keywords" :label="t('category.topic.keyWords')">
                <div>
                  <div class="flex flex-wrap gap-2">
                    <el-tag v-for="tag in form.keywords" :key="tag" closable :disable-transitions="false" @close="handleCloseTag(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" class="!w-60" size="small" maxlength="50" show-word-limit @keyup.enter="handleInputConfirm" @blur="handleInputConfirm" />
                    <el-button v-else-if="!inputVisible && (form.keywords.length < 10)" class="button-new-tag" size="small" @click="showInput">
                      {{ t('action.addKeyWord') }}
                    </el-button>
                  </div>
                  <div v-if="repeatKeyword" class="text-[#f56c6c] text-xs mt-2">
                    {{ t('common.keyWords') }}
                  </div>
                </div>
              </el-form-item>
              <el-form-item :label="t('learningCenter.boarding.description')" prop="description">
                <el-input v-model="form.description" type="textarea" :rows="5" show-word-limit maxlength="5000" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitAddOnboarding">
                  {{ t('global.confirm') }}
                </el-button>
                <el-button type="primary" plain @click="handleCancel">
                  {{ t('global.cancel') }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </ContentWrap>
        <Assign v-if="activeIndex === '2'" :step-id="resqID" />
      </div>
      <el-dialog />
    </div>
  </div>
</template>

<style scoped lang="scss">
.el-menu-item {
  height: 34px;
}
:deep(.el-menu) {
  border-right: 0 !important;
  .el-menu-item {
    &.is-active {
      background-color: #E4F4EE;
      border-radius: 4px;
    }
  }
}
</style>
