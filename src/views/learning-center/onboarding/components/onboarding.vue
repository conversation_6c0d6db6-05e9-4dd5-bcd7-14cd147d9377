<script setup lang="ts" name="Onboarding">
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance } from 'element-plus'
import { omit, toUpper } from 'lodash-es'
import { listDept } from '@/api/system/dept'
import { deleteOnboarding, getOnboarding, getOnboardingCategory } from '@/api/category/onboarding'
import { MediaType } from '@/enums/resource'
import { formatSecond } from '@/utils/ruoyi'
import { formatImgUrl } from '@/utils'
import { handlePhaseTree } from '@/utils/tree'

import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'

interface Query {
  pageNo: number
  pageSize: number
  title: string
  departmentId: string
  categoryId: string
  type?: number | undefined
  isMandatory?: boolean | undefined
  lang?: string | undefined
  duration?: number | string | undefined
  mediaType?: number | undefined
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const router = useRouter()
const tableList = ref()
const total = ref(0)
const formRef = ref<FormInstance>()
const loading = ref(false)
const queryParams = ref<Query>({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  departmentId: undefined,
  categoryId: undefined,
  type: undefined,
  isMandatory: undefined,
  lang: undefined,
  duration: undefined,
  mediaType: undefined,
})
const deptList = ref()
const categoryList = ref()

const isMandatoryList = [
  { value: true, label: t('common.yes') },
  { value: false, label: t('common.no') },
]

const resource_list_type_file_video = [
  {
    label: t('global.video'),
    value: MediaType.Video,
  },
  {
    label: t('global.file'),
    value: MediaType.File,
  },
]
const DURATIONLIST = [
  { id: 1, label: '<15min', durationLower: 0, durationUpper: 900 },
  { id: 2, label: '15 - 30min', durationLower: 900, durationUpper: 1800 },
  { id: 3, label: '30 - 60min', durationLower: 1800, durationUpper: 3600 },
  { id: 4, label: '>60min', durationLower: 3600, durationUpper: 999999 },
]

const formatBg = (type: MediaType) => {
  const color = {
    [MediaType.Video]: 'bg-[#36A5D8]',
    [MediaType.Audio]: 'bg-[#B858F0]',
    [MediaType.File]: 'bg-[#F2A353]',
    [MediaType.Scorm]: 'bg-[#21AC6E]',
  }
  return color[type]
}

/** 查询部门列表 */
const getDeptList = async () => {
  const data = await listDept()
  deptList.value = handlePhaseTree(data, 'id')
}
/** 获取onboarding列表 */
const getOnboardingList = async () => {
  loading.value = true
  try {
    const params = {
      ...omit(queryParams.value, ['duration']),
      durationLower: queryParams.value.duration?.durationLower,
      durationUpper: queryParams.value.duration?.durationUpper,
    }
    const res = await getOnboarding(params)
    tableList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 跳转添加页面 */
const handleAdd = () => {
  router.push({
    name: 'AddOnboarding',
  })
}
/** 跳转学习记录页面 */
const handleLearning = (event: any) => {
  router.push({ name: 'LearningRecords', query: { id: event.id, name: 'onboarding' }})
}
/** 跳转编辑页面  */
const handleEdit = (event: any) => {
  router.push({ name: 'AddOnboarding', params: { id: event.id } })
}
const handleSearch = () => {
  getOnboardingList()
}
const handleRefresh = () => {
  formRef.value?.resetFields()
  queryParams.value.pageNo = 1
  queryParams.value.pageSize = 10
  handleSearch()
}
/** 删除 */
const handleDelete = async (event) => {
  try {
    await message.confirm(`${t('global.deleteTip') + event.title}?`)
    await deleteOnboarding(event.id)
    await getOnboardingList()
    message.success(t('global.deleteSuccess'))
  } catch {}
}
/** 查询category列表，没有分页 */
const getOnboardingCategoryList = async () => {
  categoryList.value = await getOnboardingCategory()
}

onMounted(() => {
  getOnboardingList()
  getOnboardingCategoryList()
})
</script>

<template>
  <div>
    <ContentWrap>
      <el-form ref="formRef" :model="queryParams" class="-mb-15px" inline>
        <el-form-item :label="t('learningCenter.boarding.title')" prop="title" @submit.prevent>
          <el-input v-model="queryParams.title" :placeholder="t('common.inputText')" clearable class="!w-300px" @keyup.enter="handleSearch" />
        </el-form-item>
        <!-- <el-form-item :label="$t('sys.user.department')" prop="departmentId">
          <el-tree-select
            v-model="queryParams.departmentId" :data="deptList"
            :props="{ value: 'deptId', label: 'deptName', children: 'children' }" value-key="deptId" check-strictly
            clearable
            style="width: 260px;"
          />
        </el-form-item> -->
        <el-form-item :label="t('learningCenter.journey.categoryTitle')" prop="categoryId">
          <el-select v-model="queryParams.categoryId" :placeholder="t('common.selectText')" clearable class="!w-300px">
            <el-option v-for="item in categoryList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('learningCenter.boarding.mandatory')" prop="isMandatory">
          <el-select v-model="queryParams.isMandatory" :placeholder="t('common.selectText')" clearable class="!w-300px">
            <el-option v-for="item in isMandatoryList" :key="item.label" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="Type" prop="type">
          <el-select v-model="queryParams.type" placeholder="Select" clearable style="width: 300px">
            <el-option v-for="item in typeList" :key="item.label" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->
        <el-form-item :label="t('learningCenter.course.format')" prop="mediaType">
          <el-select v-model="queryParams.mediaType" :placeholder="t('common.selectText')" clearable class="!w-300px">
            <el-option v-for="item in resource_list_type_file_video" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.language')" prop="lang">
          <el-select v-model="queryParams.lang" :placeholder="t('common.selectText')" clearable class="!w-300px">
            <el-option v-for="item in getIntDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('learningCenter.course.duration')" prop="duration">
          <el-select v-model="queryParams.duration" clearable value-key="id" class="!w-300px">
            <el-option v-for="item in DURATIONLIST" :key="item.id" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <Icon class="mr-5px" icon="ep:search" />
            {{ t('action.search') }}
          </el-button>
          <el-button @click="handleRefresh">
            <Icon class="mr-5px" icon="ep:refresh" />
            {{ t('action.reset') }}
          </el-button>
          <el-button plain type="primary" @click="handleAdd">
            <Icon class="mr-5px" icon="ep:plus" />
            {{ t('action.add') }}
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table v-loading="loading" :data="tableList">
        <el-table-column :label="t('learningCenter.boarding.title')" prop="title" min-width="316px">
          <template #default="{ row }">
            <div>
              <div class="flex flex-row">
                <el-image :src="formatImgUrl(row.cover)" class="w-[86px] h-[50px]" />
                <div class="pl-5 w-[230px]">
                  <el-tooltip
                    :content="row.title" popper-class="tooltip-mywidth"
                    effect="dark" placement="top"
                  >
                    <div class="break-all line-clamp-3">
                      {{ row.title }}
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.journey.categoryTitle')" prop="categoryName" :width="150" />
        <el-table-column :label="t('learningCenter.boarding.mandatory')" prop="isMandatory" :width="110">
          <template #default="{ row }">
            {{ row.isMandatory ? t('common.yes') : t('common.no') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.duration')" align="center" :width="100">
          <template #default="{ row }">
            <span>{{ row.duration !== null && row.duration > 0 ? formatSecond(row.duration) : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('learningCenter.course.language')" align="center" width="100">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="row.lang" />
          </template>
        </el-table-column>
        <el-table-column prop="mediaType" :label="t('learningCenter.course.format')" align="center" :width="90">
          <template #default="{ row }">
            <div
              v-if="row?.mediaType"
              class="size-11 text-sm text-white rounded-full flex items-center justify-center flex-shrink-0 m-auto relative overflow-hidden group"
              :class="formatBg(row.mediaType)"
            >
              <!-- @click="handlePreview(row)" -->
              {{ toUpper(row.format) }}
              <!--   <div class="absolute inset-0 opacity-0 bg-[#131313]/[.5] flex items-center justify-center group-hover:opacity-100 transition-opacity duration-200">
                  <el-icon>
                    <View />
                  </el-icon>
                </div> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('setting.banner.sort')" prop="sort" :width="80" />
        <el-table-column :label="t('category.journey.creator')" prop="createBy" :width="130" />
        <el-table-column :label="t('category.journey.creationTime')" prop="createTime" :formatter="dateFormatter" :width="180" />
        <el-table-column :label="t('global.action')" :width="400" align="center" fixed="right">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleLearning(row)">
              <Icon icon="ep:document" />
              {{ t('learningCenter.boarding.learningRecords') }}
            </el-button>
            <el-button text type="primary" @click="handleEdit(row)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <el-button text type="primary" @click="handleDelete(row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getOnboardingList" />
    </ContentWrap>
  </div>
</template>

<style scoped lang="scss">
:deep .el-tag + .el-tag{
  margin-left: 0;
  margin-top:2px
  }
</style>
