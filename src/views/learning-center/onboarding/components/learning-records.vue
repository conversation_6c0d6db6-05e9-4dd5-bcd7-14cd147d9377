<script setup lang="ts">
import moment from 'moment'
import type { ComponentInternalInstance } from 'vue'
import type { FormInstance } from 'element-plus'
import {detailOnboarding, exportOnboarding, exportPolicy, getLearning} from '@/api/topicMgt/onboarding'
import { CompanyPolicyAssignReqVO, detailCompanyPolicy, getCompanyPolicyAssignment} from '@/api/topicMgt/company-policy'
import { formatImgUrl } from '@/utils/index'
import download from "@/utils/download"
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const table = ref()
const queryParams = ref<CompanyPolicyAssignReqVO>({
  userName: '',
  badgeNo: '',
  email: '',
  status: '',
  pageNo: 1,
  pageSize: 10,
  onBoardingId: route.query.name === 'onboarding' ? route.query.id : undefined,
  companyPolicyId: route.query.name === 'company' ? route.query.id : undefined
})
const loading = ref(false)
const total = ref(0)
const formRef = ref<FormInstance>()
const title = ref()
// onboarding获取学习记录
const getLearningList = async () => {
  loading.value = true
  try {
    const res = await getLearning(queryParams.value)
    table.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
// company policy获取学习记录
const getCompanyPolicyAssignmentList = async () => {
  loading.value = true
  try {
    const data = await getCompanyPolicyAssignment(queryParams.value)
    table.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const getTitle = async () => {
  if (route.query.name === 'onboarding') {
    title.value = await detailOnboarding(route.query.id)
  } else {
    title.value = await detailCompanyPolicy(route.query.id)
  }
}

const handleSearch = () => {
  route.query.name === 'onboarding' ? getLearningList() : getCompanyPolicyAssignmentList()
}

const handleRefresh = () => {
  formRef.value?.resetFields()
  handleSearch()
}

const handleBack = () => {
  router.back()
}
/** 导出按钮操作 */
const handleExport = async () => {
  if (route.query.name === 'onboarding') {
    const data = await exportOnboarding(queryParams.value)
    download.excel(data, `Onboarding-${title?.value.title}-${moment().format('YYYY-MM-DD')}.xlsx`)
  } else {
    const data = await exportPolicy(queryParams.value)
    download.excel(data, `Company policy-${title?.value.title}-${moment().format('YYYY-MM-DD')}.xlsx`)
  }
}


onMounted(() => {
  route.query.name === 'onboarding' ? getLearningList() : getCompanyPolicyAssignmentList()
  getTitle()
})
</script>

<template>
  <div class="app-container">
    <!-- 表头 -->
    <div class="flex justify-between px-5 py-8 bg-[#E4F4EE] rounded-xl">
      <div class="flex">
        <img v-if="title?.cover" :src="formatImgUrl(title?.cover)" class="w-[160px] h-[90px]" />
        <img v-else src="@/assets/images/cover.png" alt="Cover" class="w-[160px] h-[90px]" />
        <div class="ml-5">
          <h1 class="text-[#23293A] text-[20px] mt-4">
            {{ title?.title }}
          </h1>
        </div>
      </div>
      <el-button class="ms-auto" type="primary" @click="handleBack">
        {{ t('action.back') }}
      </el-button>
    </div>
    <div class="mt-[10px]">
      <ContentWrap>
        <el-form ref="formRef" :model="queryParams" class="-mb-15px">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-form-item :label="t('learningCenter.boarding.name')" prop="userName">
                <el-input v-model="queryParams.userName" :placeholder="t('common.inputText')" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item :label="t('learningCenter.boarding.badgeNumber')" prop="badgeNo">
                <el-input v-model="queryParams.badgeNo" :placeholder="t('common.inputText')" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item :label="t('sys.user.email')" prop="email">
                <el-input v-model="queryParams.email" :placeholder="t('common.inputText')" clearable />
              </el-form-item>
            </el-col>
            <el-col v-if="route.query.name === 'onboarding'" :span="5">
              <el-form-item :label="t('sys.user.status')" prop="status">
                <el-select v-model="queryParams.status" :placeholder="t('common.selectText')" style="width: 240px" clearable>
                  <el-option v-for="item in getIntDictOptions(DICT_TYPE.ONBOARDING_ASSIGNMENT_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-else :span="5">
              <el-form-item :label="t('sys.user.status')" prop="status">
                <el-select v-model="queryParams.status" :placeholder="t('common.selectText')" style="width: 240px" clearable>
                  <el-option v-for="item in getIntDictOptions(DICT_TYPE.SYSTEM_COMPANY_POLICY_RECORD)" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="handleSearch">
                <Icon class="mr-5px" icon="ep:search" />
                {{ t('action.search') }}
              </el-button>
              <el-button @click="handleRefresh">
                <Icon class="mr-5px" icon="ep:refresh" />
                {{ t('action.reset') }}
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </ContentWrap>
      <div class="text-right mb-2">
        <el-button plain type="primary" @click="handleExport">
          <Icon class="mr-5px" icon="ep:download" />
          {{ t('action.export') }}
        </el-button>
      </div>
      <ContentWrap>
        <el-table v-loading="loading" :data="table" :show-overflow-tooltip="true">
          <el-table-column :label="t('sys.user.userName')" prop="userName" />
          <el-table-column :label="t('learningCenter.boarding.badgeNumber')" prop="badgeNo" />
          <el-table-column :label="t('sys.user.email')" prop="email" />
          <el-table-column :label="t('sys.user.company')" prop="companyName" />
          <el-table-column :label="t('sys.user.department')" prop="departmentName" />
          <el-table-column :label="t('sys.user.section')" prop="sectionName" />
          <el-table-column :label="t('sys.user.position')" prop="positionName" />
          <el-table-column v-if="route.query.name === 'onboarding'" :label="t('sys.user.status')" prop="status_">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.ONBOARDING_ASSIGNMENT_STATUS" :value="row.status_" />
            </template>
          </el-table-column>
          <el-table-column v-else :label="t('sys.user.status')" prop="status_">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.SYSTEM_COMPANY_POLICY_RECORD" :value="row.status_" />
            </template>
          </el-table-column>
          <el-table-column :label="t('category.journey.creator')" prop="createBy" />
          <el-table-column :label="t('category.journey.creationTime')" :formatter="dateFormatter" prop="createTime" />
        </el-table>
        <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="route.query.name === 'onboarding' ? getLearningList() : getCompanyPolicyAssignmentList()" />
      </ContentWrap>
    </div>
  </div>
</template>

<style scoped lang="scss">
.back {
  @apply bg-primary;
}
</style>
