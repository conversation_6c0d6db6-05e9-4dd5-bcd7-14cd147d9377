<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { ElDialog, type FormInstance } from 'element-plus'
import { EditCategory, addCategory, deleteCategory, detailCategory, getCategory } from '@/api/topicMgt/onboarding'
import { JourneyCategoryRespVO } from "@/api/category/journey"
import { dateFormatter } from '@/utils/formatTime'

interface Query {
  pageNo: number
  pageSize: number
  title: string
}
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const tableList = ref()
const total = ref(0)
const title = ref()
const queryParams = ref<Query>({
  pageNo: 1,
  pageSize: 10,
  title: ''
})
const addDialog = ref(false)
const formRef = ref<FormInstance>()
const dialogRef = ref<FormInstance>()
const dialogForm = ref({
  id: undefined,
  title: '',
  sort: undefined
})
const loading = ref(false)
const rules = ref({
  title: [{ required: true, message: t('category.journey.titleRule'), trigger: 'blur' }],
  sort: [{ required: true, message: t('category.journey.sortRule'), trigger: 'blur' }]
})
/** 获取列表 */
const getCategoryList = async () => {
  loading.value = true
  try {
    const res = await getCategory(queryParams.value)
    tableList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
/** 搜索 */
const handleSearch = () => {
  getCategoryList()
}
/** 重置搜索 */
const handleReset = () => {
  formRef.value?.resetFields()
  handleSearch()
}
/** 点击添加弹窗 */
const handleAdd = () => {
  addDialog.value = true
  title.value = t('category.journey.addCategory')
  dialogRef.value?.resetFields()
  dialogForm.value.id = undefined
}
/** 编辑弹窗 */
const handleEdit = async (row: JourneyCategoryRespVO) => {
  addDialog.value = true
  title.value = t('category.journey.editCategory')
  dialogRef.value?.resetFields()
  dialogForm.value.id = undefined
  dialogForm.value = await detailCategory(row.id)
}
/** 添加/编辑提交 */
const handleSubmit = async () => {
  // 校验表单
  if (!dialogRef) return
  const valid = await dialogRef.value.validate()
  if (!valid) return
  try {
    if (dialogForm.value.id) {
      await EditCategory(dialogForm.value)
      message.success(t('common.updateSuccess'))
    } else {
      await addCategory(dialogForm.value)
      message.success(t('common.createSuccess'))
    }
    await getCategoryList()
  } finally {
    addDialog.value = false
  }
}
/** 删除 */
const handleDelete = async (event: JourneyCategoryRespVO) => {
  try {
    await message.confirm(`${t('global.deleteTip') + event.title}?`)
    await deleteCategory(event.id)
    message.success(t('common.deleteSuccess'))
    await getCategoryList()
  } catch {}
}
onMounted(() => {
  getCategoryList()
})
</script>

<template>
  <div class="p-5">
    <ContentWrap>
      <el-form ref="formRef" :model="queryParams" @submit.prevent>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('category.journey.categoryTitle')" prop="title">
              <el-input v-model="queryParams.title" :placeholder="t('common.inputText')" clearable @keyup.enter="handleSearch" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button @click="handleSearch">
                <Icon class="mr-5px" icon="ep:search" />
                {{ t('action.search') }}
              </el-button>
              <el-button icon="Refresh" @click="handleReset">
                <Icon class="mr-5px" icon="ep:refresh" />
                {{ t('action.reset') }}
              </el-button>
              <el-button icon="Plus" plain type="primary" @click="handleAdd">
                <Icon class="mr-5px" icon="ep:plus" />
                {{ t('action.add') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table v-loading="loading" :data="tableList">
        <el-table-column :label="t('category.journey.title')" prop="title" />
        <el-table-column :label="t('category.journey.sort')" prop="sort" />
        <el-table-column :label="t('category.journey.creator')" prop="createBy" />
        <el-table-column :label="t('category.journey.creationTime')" :formatter="dateFormatter" prop="createTime" />
        <el-table-column :label="t('global.action')" :width="400" align="center" fixed="right">
          <template #default="{ row }">
            <el-button text type="primary" @click="handleEdit(row)">
              <Icon icon="ep:edit" />
              {{ t('action.edit') }}
            </el-button>
            <el-button text type="primary" @click="handleDelete(row)">
              <Icon icon="ep:delete" />
              {{ t('action.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total" @pagination="getCategoryList" />
    </ContentWrap>

    <Dialog v-model="addDialog" :title="title" width="730px">
      <el-form ref="dialogRef" :model="dialogForm" :rules="rules" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('category.journey.categoryTitle')" prop="title">
              <el-input v-model="dialogForm.title" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('category.journey.sort')" prop="sort">
              <el-input-number v-model="dialogForm.sort" type="number" :min="0" :max="9999" clearable controls-position="right" style="width: 240px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="handleSubmit">
          {{ t('global.confirm') }}
        </el-button>
        <el-button @click="addDialog = false">
          {{ t('global.cancel') }}
        </el-button>
      </template>
    </Dialog>
  </div>
</template>

<style scoped lang="scss"></style>
