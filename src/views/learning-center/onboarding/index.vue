<script setup lang='ts'>
import Onboarding from './components/onboarding.vue'
import Category from './components/category.vue'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const activeTab = [
  {
    text: t('learningCenter.boarding.onboarding'),
    id: 1,
  },
  {
    text: t('learningCenter.boarding.Category'),
    id: 2,
  },
]
const activeName = ref(1)
const handleClick = (event: number) => {
  activeName.value = event
}
</script>

<template>
  <div class="app-container">
    <Onboarding />
  </div>
</template>
