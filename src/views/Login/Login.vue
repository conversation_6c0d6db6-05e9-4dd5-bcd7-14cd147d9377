<!--<template>-->
<!--  <div-->
<!--    :class="prefixCls"-->
<!--    class="relative h-[100%] lt-md:px-10px lt-sm:px-10px lt-xl:px-10px lt-xl:px-10px"-->
<!--  >-->
<!--    <div class="relative mx-auto h-full flex">-->
<!--      <div-->
<!--        :class="`${prefixCls}__left flex-1 bg-gray-500 bg-opacity-20 relative p-30px lt-xl:hidden overflow-x-hidden overflow-y-auto`"-->
<!--      >-->
<!--        &lt;!&ndash; 左上角的 logo + 系统标题 &ndash;&gt;-->
<!--        <div class="relative flex items-center text-white">-->
<!--          <img alt="" class="mr-10px h-48px w-48px" src="@/assets/imgs/logo.png" />-->
<!--          <span class="text-20px font-bold">{{ underlineToHump(appStore.getTitle) }}</span>-->
<!--        </div>-->
<!--        &lt;!&ndash; 左边的背景图 + 欢迎语 &ndash;&gt;-->
<!--        <div class="h-[calc(100%-60px)] flex items-center justify-center">-->
<!--          <TransitionGroup-->
<!--            appear-->
<!--            enter-active-class="animate__animated animate__bounceInLeft"-->
<!--            tag="div"-->
<!--          >-->
<!--            <img key="1" alt="" class="w-350px" src="@/assets/svgs/login-box-bg.svg" />-->
<!--            <div key="2" class="text-3xl text-white">{{ t('login.welcome') }}</div>-->
<!--            <div key="3" class="mt-5 text-14px font-normal text-white">-->
<!--              {{ t('login.message') }}-->
<!--            </div>-->
<!--          </TransitionGroup>-->
<!--        </div>-->
<!--      </div>-->
<!--      <div-->
<!--        class="relative flex-1 p-30px dark:bg-[var(&#45;&#45;login-bg-color)] lt-sm:p-10px overflow-x-hidden overflow-y-auto"-->
<!--      >-->
<!--        &lt;!&ndash; 右上角的主题、语言选择 &ndash;&gt;-->
<!--        <div-->
<!--          class="flex items-center justify-between at-2xl:justify-end at-xl:justify-end"-->
<!--          style="color: var(&#45;&#45;el-text-color-primary);"-->
<!--        >-->
<!--          <div class="flex items-center at-2xl:hidden at-xl:hidden">-->
<!--            <img alt="" class="mr-10px h-48px w-48px" src="@/assets/imgs/logo.png" />-->
<!--            <span class="text-20px font-bold" >{{ underlineToHump(appStore.getTitle) }}</span>-->
<!--          </div>-->
<!--          <div class="flex items-center justify-end space-x-10px h-48px">-->
<!--            <ThemeSwitch />-->
<!--            <LocaleDropdown />-->
<!--          </div>-->
<!--        </div>-->
<!--        &lt;!&ndash; 右边的登录界面 &ndash;&gt;-->
<!--        <Transition appear enter-active-class="animate__animated animate__bounceInRight">-->
<!--          <div-->
<!--            class="m-auto h-[calc(100%-60px)] w-[100%] flex items-center at-2xl:max-w-500px at-lg:max-w-500px at-md:max-w-500px at-xl:max-w-500px"-->
<!--          >-->
<!--            &lt;!&ndash; 账号登录 &ndash;&gt;-->
<!--            <LoginForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />-->
<!--            &lt;!&ndash; 手机登录 &ndash;&gt;-->
<!--            <MobileForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />-->
<!--            &lt;!&ndash; 二维码登录 &ndash;&gt;-->
<!--            <QrCodeForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />-->
<!--            &lt;!&ndash; 注册 &ndash;&gt;-->
<!--            <RegisterForm class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />-->
<!--            &lt;!&ndash; 三方登录 &ndash;&gt;-->
<!--            <SSOLoginVue class="m-auto h-auto p-20px lt-xl:(rounded-3xl light:bg-white)" />-->
<!--          </div>-->
<!--        </Transition>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--</template>-->

<template>
  <!--登录容器-->
  <div class="login-container">
    <!--登录头部-->
    <div class="login-header">
      <!--Majnoon油田Logo-->
      <div class="logo-group">
        <el-image class="logo" :src="Logo" />
        <div class="welcome">Online Learning Platform Administration System</div>
      </div>

      <!-- 国际化切换 -->
      <div class="i18n">
        <LocaleDropdown />
      </div>
    </div>

    <!--登录内容区域-->
    <div class="login-content">
      <!--左侧登录插画-->
      <div class="login-cover">
        <el-image :src="LoginCover" />
      </div>

      <!--右侧登录表单-->
      <div class="login-body">
        <!--登录标题-->
        <div class="login-title">
          <LoginFormTitle style="width: 100%" />
        </div>

        <!--登录表单-->
        <div class="login-form">
          <LoginForm @set-ad-login="setLogin" v-show="loginForm" />
          <!--<ADForm @back-login="backLogin" v-show="!loginForm" />-->
          <div class="actions">
            <n-tabs v-model:value="fileType" class="ai-toolkit tab-bar" type="segment" animated>
              <n-tab-pane class="single-tab" name="Login">
                <!--Login-->
                <!--<LoginForm @set-ad-login="setLogin" v-show="loginForm" />-->
                <LoginForm />
              </n-tab-pane>
              <n-tab-pane class="single-tab" name="AD Login"> AD Login </n-tab-pane>
              <n-tab-pane class="single-tab" name="MFA Login">
                <el-button
                  class="login-btn"
                  type="primary"
                  plain
                  @click="mfaHandleLogin"
                >
                  MFA Login
                </el-button>
              </n-tab-pane>
            </n-tabs>
          </div>
        </div>

        <!--忘记密码-->
        <div class="forgot-password">
          <el-text>Forgot Password?</el-text>
        </div>
      </div>
    </div>

    <!--Footer-->
    <div class="footer">
      <el-text>Online Learning Platform 3.1.1</el-text>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { underlineToHump } from '@/utils'
import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { ThemeSwitch } from '@/layout/components/ThemeSwitch'
import { LocaleDropdown } from '@/layout/components/LocaleDropdown'
import { LoginForm, MobileForm, QrCodeForm, RegisterForm, SSOLoginVue } from './components'
import LoginFormTitle from './components/LoginFormTitle.vue'
import Logo from '@/assets/imgs/logo.png'
import LoginCover from '@/assets/svgs/loginCover.png'
import { config } from '@/config/axios/config'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import { loginTypeEnum } from "@/api/login"
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'Login' })

const { t } = useI18n()

const appStore = useAppStore()
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('login')
const { wsCache } = useCache()
// 路由前缀
const { base_url } = config
const userStore = useUserStore()

// MFA登录
const mfaHandleLogin = () => {
  userStore.setUserLoginType(loginTypeEnum.MFA)
  window.location.href = `${base_url}/system/auth/authing/login`
}

</script>

<style lang="scss" scoped>
@import 'src/styles/Login/actions.scss';

.login-btn {
  height: 40px;
  width: 100%;
  border: none;
  border-radius: 8px;
  color: white;
  background-color: rgba(75, 96, 18, 1);

  will-change: transform, background-color; // 性能优化：提前通知浏览器变化属性
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); // 使用贝塞尔曲线添加过渡动画增强交互效果

  // 悬停状态
  &:hover {
    background-color: rgba(75, 96, 18, 0.8); // 颜色变浅20%
  }

  // 点击状态
  &:active {
    transform: scale(0.98); // 缩小2%
    background-color: rgba(75, 96, 18, 0.8);
  }
}

.actions {
  margin-top: 0 !important;
}

/* 容器样式 */
.login-container {
  // 覆盖整个屏幕
  width: 100%;
  height: 100%;

  .logo {
    height: 40px;
    width: auto;
  }

  // 背景颜色渐变
  background: linear-gradient(
    180deg,
    rgba(75, 96, 18, 0.04) 4%,
    // #4B6012 4%透明度
    rgba(75, 96, 18, 1) 100% // #4B6012 100%透明度
  );

  // 保证渐变容器覆盖全屏
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  /* 头部Logo以及语言切换 */
  .login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    padding: 20px 20px 0 20px; // 防止登录内容不居中

    .logo-group{
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 16px;
    }

    // 国际化icon垂直水平居中
    .i18n {
      justify-content: center;
      align-items: center;
    }
  }

  .welcome {
    font-size: 24px;
    color: rgba(36, 52, 63, 1);
  }

  /* 登录页内容区域 */
  .login-content {
    // 定义进入动画
    opacity: 0;
    transform: translateY(20px);
    animation: contentEnter 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    animation-delay: 0.3s; // 延迟启动避免与其他动画冲突

    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;

    height: auto;
    padding: 0 40px;

    // 性能优化
    will-change: transform, opacity;
    backface-visibility: hidden;

    // 登录表单模块
    .login-form {
      flex-direction: column;
      background-color: white;

      padding: 28px;
      margin-top: 40px;
      border-radius: 20px; // 根据UI图设置圆角为20px
      background-color: rgba(255, 255, 255, 0.4); /* 白色带30%透明度 */
      backdrop-filter: blur(10px); /* 关键模糊效果 */
      -webkit-backdrop-filter: blur(10px); /* Safari 兼容 */ /* 添加玻璃光泽效果 */
      border: 1px solid rgba(255, 255, 255, 0.2); /* 玻璃边缘高光 */

      // 对Tab宽度进行单独设置
      :deep(.n-tabs-rail) {
        max-width: 348px;
      }
    }

    // 忘记密码
    .forgot-password {
      display: flex;
      justify-content: center;
      align-items: center;

      margin-top: 8px;
      cursor: pointer;

      // 字体颜色
      :deep(.el-text--default) {
        color: rgba(255, 255, 255, 0.5);
      }

      // 添加Hover效果
      &:hover {
        // 容器整体效果
        transform: translateY(-1px);

        :deep(.el-text--default) {
          color: rgba(255, 255, 255, 1) !important; // 纯白色
          transform: scale(1.02); // 轻微放大
          text-shadow: 0 2px 8px rgba(255, 255, 255, 0.2); // 添加发光效果
        }
      }

      // 点击动效
      &:active {
        transform: translateY(1px);
        transition-duration: 0.1s;
      }
    }

    // 移动端适配
    @media (max-width: 768px) {
      gap: 0; // 移除元素间距

      .login-cover {
        display: none; // 完全隐藏插画
      }

      .login-body {
        padding: 0 8px; // 增加移动端安全边距
      }
    }
  }

  // 添加步入动画
  @keyframes contentEnter {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 底部Footer垂直水平居中 */
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;

    padding: 8px;

    :deep(.el-text--default) {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}
</style>
