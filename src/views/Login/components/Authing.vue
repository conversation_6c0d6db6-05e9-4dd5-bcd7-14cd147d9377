<template>
  <div class="error-page" v-show="failureReminder" :loading="loginLoading">
    <div class="content-con">
      <img alt="" src="@/assets/imgs/error-401.svg" class="error-img" />
      <div class="text-con">
        <p class="text">401</p>
        <p class="text">System exception, please contact the administrator.</p>
      </div>
      <el-link :underline="false" type="primary" @click="jump" class="btn">Return to login</el-link>
    </div>
  </div>
</template>
<script lang="ts" setup>


import { assignWith } from "lodash-es"
import * as LoginApi from "@/api/login"
import * as authUtil from "@/utils/auth"
import { ElLoading } from "element-plus"
import { usePermissionStore } from "@/store/modules/permission"
defineOptions({ name: 'Authing' })

const codeValue = ref('')
const failureReminder = ref(false)
const { t } = useI18n()
const { currentRoute, push } = useRouter()
const permissionStore = usePermissionStore()
const loginInfo = ref({})
const loading = ref() // ElLoading.service 返回的实例
const loginLoading = ref(false)
const redirect = ref<string>('')
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    tenantName: '芋道源码',
    username: 'admin',
    password: 'admin123',
    captchaVerification: '',
    rememberMe: true // 默认记录我。如果不需要，可手动修改
  }
})
const jump = async () => {
  await push({ name: 'Login' })
}
// 获取租户 ID
const getTenantId = async () => {
  if (loginData.tenantEnable === 'true') {
    const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
    authUtil.setTenantId(res)
  }
}
const loginByCode = async (params) => {
  loginLoading.value = true
  try {
    await getTenantId()
    try {
      loginInfo.value = await LoginApi.mfaLogin(codeValue.value)
      loginData.loginForm.captchaVerification = params.captchaVerification
      if (!loginInfo.value) {
        return
      }
      loading.value = ElLoading.service({
        lock: true,
        text: 'Loading into the system...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (loginData.loginForm.rememberMe) {
        authUtil.setLoginForm(loginData.loginForm)
      } else {
        authUtil.removeLoginForm()
      }
      authUtil.setToken(loginInfo.value)
      if (!redirect.value) {
        redirect.value = '/'
      }
      // 跳转
      await push({ path: redirect.value || permissionStore.addRouters[0].path })
    } catch (e) {
      await push({ name: 'authing' })
      failureReminder.value = true
    }
  } finally {
    loginLoading.value = false
    loading.value.close()
  }
}
onMounted(async () => {
  const urlParams=new URLSearchParams(window.location.search)
  if (urlParams.has('code')) {
    codeValue.value = urlParams.get('code')
    if ( codeValue.value ) {
      await loginByCode({})
    }
  }
})

</script>
<style lang="scss" scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* 设置最小高度为视口高度 */
  background-color: #f5f7fa; /* 背景颜色可自定义 */
}

.content-con {
  .error-img {
    max-width: 80%; /* 限制图片最大宽度 */
    max-height: 350px; /* 限制图片最大高度 */
  }
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem; /* 内容之间的间距 */
}
.text-con {
  .text {
    font-size: 30px;font-weight: bold
  }
  text-align: center;
}
.btn {
  font-size: 20px
}
</style>
