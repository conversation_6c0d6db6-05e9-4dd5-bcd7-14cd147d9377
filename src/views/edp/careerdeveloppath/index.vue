<script setup lang="ts">
import { useRouter } from 'vue-router'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import type { ElTree } from 'element-plus'
import { OrgType } from '@/enums/OrgType'
import { getSection, sectTreeSelect } from '@/api/system/section'
import { useI18n } from 'vue-i18n'
import { CareerDevelopApi, CareerDevelop } from '@/api/edp/careerdevelop'
import CareerDevelopForm from './CareerDevelopForm.vue'

interface queryParams {
  postCode: number
  name: string
  status: number
  parentId: number
  deptId: number
  deptName: string
}

const { t } = useI18n()
const router = useRouter()
const message = useMessage() // 消息弹窗
const careerDevelopList = ref<CareerDevelop[]>([])
const careerPaths = ref([]) // 存储处理后的职业发展路径
const loading = ref(true)
const postTotal = ref(0)
const currentPage = ref(1)
const pageSize = ref(30) // 每页显示20个卡片
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {},
  queryParams: {
    postCode: undefined,
    name: undefined,
    status: undefined,
    parentId: undefined,
    deptId: undefined,
    deptName: undefined
  },
  rules: {
    name: [{ required: true, message: t('sys.post.positionNameRule'), trigger: 'blur' }],
    postCode: [{ required: false, message: t('sys.post.positionCodeRule'), trigger: 'blur' }],
    orderNum: [{ required: true, message: t('sys.post.orderNumRule'), trigger: 'blur' }]
  }
})

const { queryParams } = toRefs(data)
const deptName = ref('')
const deptOptions = ref<any[]>([])
const postTreeRef = ref<InstanceType<typeof ElTree>>()
/** 当前选中的部门信息 */
const selDept = ref()
const defaultExpand = ref()
/** 正在删除的节点ID集合 */
const deletingNodes = ref<Set<number>>(new Set())
/** 表单组件引用 */
const formRef = ref()

/** 左侧-查询公司部门下拉树结构 */
const getDeptTree = async () => {
  const data = await sectTreeSelect({ type: 0 })
  console.log('部门树数据:', data)
  deptOptions.value = data
  selDept.value = deptOptions.value[0]
  queryParams.value.compId = deptOptions.value[0].id
  // 展开默认选中节点
  const firstOption = deptOptions.value[0]
  defaultExpand.value = [
    deptOptions.value[0].virtualId,
    firstOption.level === OrgType.Company && firstOption.children
      ? deptOptions.value[0].children[0].virtualId
      : undefined
  ]
  await nextTick(() => {
    postTreeRef.value?.setCurrentNode(deptOptions.value[0])
  })
  // 首次加载时不调用查询路线图列表，直接设置loading为false
  loading.value = false
  // await getList()
}

/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}

/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  postTreeRef.value!.filter(val)
})

// 递归查找父级部门节点
const findParentDepartment = (treeData: any[], targetNodeId: number): any => {
  for (const item of treeData) {
    // 如果当前节点是Department级别，检查其子节点
    if (item.level === OrgType.Department && item.children) {
      for (const child of item.children) {
        if (child.id === targetNodeId && child.level === OrgType.Section) {
          return item // 返回父级Department节点
        }
      }
    }
    // 递归查找子节点
    if (item.children) {
      const result = findParentDepartment(item.children, targetNodeId)
      if (result) return result
    }
  }
  return null
}

// 节点点击事件
const handleNodeClick = async (node: any) => {
  console.log('选中节点:', node)
  selDept.value = node

  // 重置所有参数
  queryParams.value.compId = null
  queryParams.value.deptId = null
  queryParams.value.sectId = null
  queryParams.value.sectName = null
  queryParams.value.positionName = null

  if (node.level === OrgType.Company) {
    queryParams.value.compId = node.id
    // 点击一级节点（公司）时不调用查询路线图列表，清空数据并关闭loading
    careerDevelopList.value = []
    careerPaths.value = []
    postTotal.value = 0
    loading.value = false
    return
  } else if (node.level === OrgType.Department) {
    queryParams.value.deptId = node.id
    queryParams.value.positionName = node.label
  } else if (node.level === OrgType.Section) {
    queryParams.value.sectId = node.id
    queryParams.value.sectName = node.label
    // 通过前端逻辑查找上级Department
    const parentDept = findParentDepartment(deptOptions.value, node.id)
    if (parentDept) {
      queryParams.value.deptId = parentDept.id
      queryParams.value.positionName = parentDept.label
    }
  }

  await getList()
}

/**  查询路线图列表 */
const getList = async (showLoading = true) => {
  if (showLoading) {
    loading.value = true
  }
  try {
    // 同步分页参数
    queryParams.value.pageNo = currentPage.value
    queryParams.value.pageSize = pageSize.value

    careerDevelopList.value = await CareerDevelopApi.getCareerDevelopPath(queryParams.value)
    console.log('查询路线图列表:', careerDevelopList.value)

    // 处理数据为脑图格式
    processCareerPaths()
  } finally {
    if (showLoading) {
      loading.value = false
    }
  }
}

// 处理职业发展路径数据为脑图格式
const processCareerPaths = () => {
  const paths = []

  // 遍历每个根节点
  careerDevelopList.value.forEach((rootItem) => {
    if (rootItem.root) {
      // 从根节点开始构建路径
      const rootPaths = buildPathsFromNode(rootItem, [])
      paths.push(...rootPaths)
    }
  })

  careerPaths.value = paths
  postTotal.value = paths.length
}

// 从节点构建所有可能的路径
const buildPathsFromNode = (node, currentPath) => {
  const newPath = [
    ...currentPath,
    {
      id: node.id,
      positionId: node.positionId,
      positionName: node.positionName,
      parentId: node.parentId,
      root: node.root,
      level: currentPath.length // 层级深度
    }
  ]

  // 如果没有子节点，返回当前路径
  if (!node.children || node.children.length === 0) {
    return [newPath]
  }

  // 如果有子节点，递归构建路径
  const allPaths: any[] = []
  node.children.forEach((child: any) => {
    const childPaths = buildPathsFromNode(child, newPath)
    allPaths.push(...childPaths)
  })

  return allPaths
}

// 获取职位节点的样式类
const getPositionNodeClass = (position: any) => {
  const level = position.level

  if (position.root) {
    // 根节点 - 紫色
    return 'bg-purple-100 border-purple-300 text-purple-800 hover:bg-purple-200'
  } else if (level === 1) {
    // 第一层子节点 - 绿色
    return 'bg-green-100 border-green-300 text-green-800 hover:bg-green-200'
  } else if (level === 2) {
    // 第二层子节点 - 蓝色
    return 'bg-blue-100 border-blue-300 text-blue-800 hover:bg-blue-200'
  } else if (level === 3) {
    // 第三层子节点 - 橙色
    return 'bg-orange-100 border-orange-300 text-orange-800 hover:bg-orange-200'
  } else {
    // 更深层级 - 灰色
    return 'bg-gray-100 border-gray-300 text-gray-800 hover:bg-gray-200'
  }
}

// 打开添加岗位对话框
const handleAddPosition = (pathIndex: number, parentId?: number, firstNodeId?: number) => {
  const params = {
    deptId: queryParams.value.deptId,
    sectId: queryParams.value.sectId,
    positionName: queryParams.value.positionName,
    firstNodeId: firstNodeId, // 传递路径第一个节点ID
    root: false // 添加子节点，不是根节点
  }
  formRef.value?.open(pathIndex, parentId, params)
}

// 添加根岗位节点
const handleAddRootPosition = () => {
  const params = {
    deptId: queryParams.value.deptId,
    sectId: queryParams.value.sectId,
    positionName: queryParams.value.positionName,
    root: true
  }
  // 根节点没有父节点，pathIndex为新路径的索引
  const newPathIndex = careerPaths.value.length
  formRef.value?.open(newPathIndex, 0, params)
}

// 处理表单成功事件
const handleFormSuccess = () => {
  getList(false) // 刷新数据但不显示loading
}

// 删除岗位路径节点
const handleDeleteNode = async (id: number) => {
  try {
    await message.delConfirm()

    // 添加到删除中的节点集合，触发淡出效果
    deletingNodes.value.add(id)

    // 等待一段时间让淡出动画播放
    await new Promise((resolve) => setTimeout(resolve, 300))

    await CareerDevelopApi.deleteCareerDevelopNode(id)
    message.success(t('common.delSuccess'))

    // 从删除中的节点集合移除
    deletingNodes.value.delete(id)

    // 刷新数据（删除操作不显示loading）
    await getList(false)
  } catch (error) {
    // 删除失败时也要从删除中的节点集合移除
    deletingNodes.value.delete(id)
    console.log('删除职位节点失败:', error)
  }
}

// 删除岗位路径
const handleDeletePath = async (rootNodeId: number) => {
  try {
    await message.delConfirm()

    // 接口调用
    await CareerDevelopApi.deleteCareerDevelopPath(rootNodeId)
    message.success(t('common.delSuccess'))

    // 刷新数据（删除操作不显示loading）
    await getList(false)

  } catch (error) {
    console.log('删除职位路径失败:', error)
  }
}

/** 初始化 */
onMounted(() => {
  getDeptTree()
})
</script>

<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <Pane :size="26" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <div class="head-container">
            <el-input
              v-model="deptName"
              :placeholder="t('sys.user.deptNamePH')"
              clearable
              class="mb-[20px]"
            >
              <template #prefix>
                <Icon class="mr-5px" icon="ep:search" />
              </template>
            </el-input>
          </div>
          <div class="head-container">
            <el-tree
              ref="postTreeRef"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :default-expanded-keys="defaultExpand"
              :filter-node-method="filterNode"
              node-key="virtualId"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <div class="flex justify-between">
                  <div class="me-2.5">
                    <el-tag
                      v-if="node.data.level === OrgType.Company"
                      :title="node.data.shortName"
                      :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0'
                      }"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Company' }}
                    </el-tag>
                    <el-tag
                      v-else-if="node.data.level === OrgType.Department"
                      :title="node.data.shortName"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Dept' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Section" type="info">
                      {{ 'Section' }}
                    </el-tag>
                  </div>
                  <span :title="node.label" class="whitespace-normal line-clamp-1 break-all">
                    {{ node.label }}</span
                  >
                </div>
              </template>
            </el-tree>
          </div>
        </el-scrollbar>
      </Pane>

      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <div class="pt-3 flex items-center">
            <div class="p-2">
              <el-tag
                v-if="selDept?.level === OrgType.Company"
                :style="{
                  '--el-tag-text-color': '#630EB8',
                  '--el-tag-bg-color': '#F3F1FF',
                  '--el-tag-border-color': '#D3CEF0'
                }"
              >
                {{ t('global.company') }}
              </el-tag>
              <el-tag v-if="selDept?.level === OrgType.Department">
                {{ t('sys.user.department') }}
              </el-tag>
              <el-tag v-else-if="selDept?.level === OrgType.Section" type="info">
                {{ t('sys.user.section') }}
              </el-tag>
              <span class="ms-2.5">{{ selDept?.label }}</span>
            </div>
          </div>

          <!-- 职业发展路径总数 -->
          <OrgTotalBar :number="postTotal" :text="'Career Development Paths'" />

          <!--职业发展路径-->
          <ContentWrap v-loading="loading" class="min-h-[80vh]">
            <!-- 职业发展路径脑图 -->
            <div v-if="!loading" class="career-mindmap flex flex-col">
              <!-- 添加根节点按钮 - 只在非一级节点时显示 -->
              <div
                v-if="selDept?.level !== OrgType.Company"
                class="w-full p-4 rounded-xl border-2 border-dashed border-gray-300 hover:border-green-500 hover:bg-green-50 cursor-pointer transition-all duration-300 mb-6 flex items-center justify-center"
                @click="handleAddRootPosition"
              >
                <Icon icon="ep:plus" class="text-gray-400 hover:text-green-500 mr-2" />
                <span class="text-gray-500 hover:text-green-600">Create New Path</span>
              </div>

              <!-- 职业发展路径列表 -->
              <div v-if="careerPaths.length > 0">
                <div
                  v-for="(path, pathIndex) in careerPaths"
                  :key="`path-${pathIndex}`"
                  class="career-path-row mb-6 flex items-center flex-wrap gap-2 relative group"
                >
                  <!-- 路径删除按钮 -->
                  <div
                    class="path-delete-btn absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm opacity-0 transition-opacity duration-200 cursor-pointer hover:bg-red-600 z-10"
                    @click.stop="handleDeletePath(path[0]?.id)"
                    title="Delete Path"
                  >
                    <Icon icon="ep:close" />
                  </div>

                  <!-- 路径中的每个节点 -->
                  <div
                    v-for="(position, posIndex) in path"
                    :key="`${pathIndex}-${posIndex}`"
                    class="flex items-center"
                  >
                    <!-- 职位卡片 -->
                    <div
                      class="position-node relative px-4 py-2 rounded-lg border-2 text-sm font-medium transition-all duration-300 hover:shadow-lg cursor-pointer group/node"
                      :class="[
                        getPositionNodeClass(position),
                        { 'deleting-fade-out': deletingNodes.has(position.id) }
                      ]"
                    >
                      {{ position.positionName }}

                      <!-- 节点删除按钮 - 只在路径最后一个节点显示 -->
                      <div
                        v-if="posIndex === path.length - 1"
                        class="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover/node:opacity-100 transition-opacity duration-200 cursor-pointer hover:bg-red-600"
                        @click.stop="handleDeleteNode(position.id)"
                      >
                        <Icon icon="ep:close" size="12" />
                      </div>
                    </div>

                    <!-- 箭头连接器 -->
                    <div
                      v-if="posIndex < path.length - 1"
                      class="arrow-connector ml-3 mr-1 flex items-center justify-center"
                    >
                      <Icon icon="ep:right" class="text-gray-500" />
                    </div>
                  </div>

                  <!-- 路径末尾的加号图标 -->
                  <div
                    class="add-position-btn flex items-center justify-center ml-2 p-2 rounded-lg border-2 border-dashed border-gray-300 hover:border-green-500 hover:bg-green-50 cursor-pointer transition-all duration-300"
                    @click="handleAddPosition(pathIndex, path[path.length - 1]?.id, path[0]?.id)"
                  >
                    <Icon icon="ep:plus" class="text-gray-400 hover:text-green-500" />
                  </div>
                </div>
              </div>

              <!-- 空状态 - 显示在按钮下方 -->
              <div v-else class="flex items-center justify-center text-center py-12 h-[60vh]">
                <el-empty
                  :description="selDept?.level === OrgType.Company ? 'Please select a department to view career development paths' : 'No Career Development Paths'"
                />
              </div>
            </div>
          </ContentWrap>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <!-- 添加职位表单组件 -->
    <CareerDevelopForm ref="formRef" @success="handleFormSuccess" />
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.position-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.position-card:hover {
  transform: translateY(-2px);
}

.career-mindmap {
  padding: 8px;
  border-radius: 8px;
  min-height: 400px;
}

.career-path-row {
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  border-left: 4px solid #e5e7eb;
  position: relative;
}

/* 路径删除按钮显示逻辑 */
.career-path-row:hover .path-delete-btn {
  opacity: 1;
}

/* 当hover到节点时，隐藏路径删除按钮 */
.career-path-row .position-node:hover {
  z-index: 20;
}

.career-path-row:has(.position-node:hover) .path-delete-btn {
  opacity: 0;
}

.career-path-row:nth-child(odd) {
  border-left-color: #8b5cf6;
}

.career-path-row:nth-child(even) {
  border-left-color: #10b981;
}

.position-node {
  min-width: 120px;
  text-align: center;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 删除时的淡出效果 */
.deleting-fade-out {
  opacity: 0.3;
  transform: scale(0.95);
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
  pointer-events: none;
}

.arrow-connector {
  flex-shrink: 0;
}

.add-position-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.add-position-btn:hover {
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .career-path-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .arrow-connector {
    transform: rotate(90deg);
    margin: 8px 0;
  }

  .position-node {
    min-width: 100px;
    margin-bottom: 8px;
  }
}
</style>
