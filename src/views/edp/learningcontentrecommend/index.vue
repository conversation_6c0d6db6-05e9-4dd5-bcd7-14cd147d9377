<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="内容标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入内容标题"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="关键词" prop="keywords">
        <el-input
          v-model="queryParams.keywords"
          placeholder="请输入关键词"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <!--创建时间-->
      <el-form-item label="Create Time" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="Start Date"
          end-placeholder="End Date"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flatList"
      :show-overflow-tooltip="true"
      :span-method="objectSpanMethod"
    >
      <el-table-column label="Title" align="center" prop="title" />
      <el-table-column label="Keywords" align="center">
        <template #default="scope">
          <span v-if="scope.row.keywords && scope.row.keywords.length > 0">
            <el-tag v-for="(keyword, index) in scope.row.keywords" :key="index" class="mr-5px">
              {{ keyword }}
            </el-tag>
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="Difficulty Level" align="center">
        <template #default="scope">
          <span>{{ getDifficultyLabel(scope.row.level) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="Description" align="center" prop="introduction" />

      <!-- 技能层级列 -->
      <el-table-column label="一级技能" align="center" prop="firstName" />
      <el-table-column label="二级技能" align="center" prop="secondName" />
      <el-table-column label="三级技能" align="center">
        <template #default="scope">
          <el-tag v-if="scope.row.skillName" size="small" type="info">
            {{ scope.row.skillName }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 匹配程度和原因只在技能层级显示 -->
      <el-table-column label="Match Degree" align="center">
        <template #default="scope">
          <span v-if="scope.row.matchDegree">{{ scope.row.matchDegree }}%</span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="Match Reason" align="center">
        <template #default="scope">
          <span v-if="scope.row.matchReason">{{ scope.row.matchReason }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button link type="primary" @click="handleMatch(scope.row)"> Match </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <LearningContentRecommendForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import {
  LearningContentRecommendApi,
  LearningContentRecommendVO
} from '@/api/edp/learningcontentrecommend'
import LearningContentRecommendForm from './LearningContentRecommendForm.vue'

/** 学习内容推荐 列表 */
defineOptions({ name: 'LearningContentRecommend' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 扩展 LearningContentRecommendVO 接口，添加 skills 字段
interface ExtendedLearningContentRecommendVO extends LearningContentRecommendVO {
  skills?: {
    [firstId: string]: {
      [secondId: string]: Array<{
        id: number
        name: string
        matchDegree: number
        matchReason: string
      }>
    }
  }
}

// 平铺后的数据接口
interface FlatItem {
  id: string
  title: string
  keywords: string[]
  level: number | null
  introduction: string
  firstName?: string
  secondName?: string
  skillName?: string
  matchDegree?: number
  matchReason?: string
}

const loading = ref(true) // 列表的加载中
const list = ref<ExtendedLearningContentRecommendVO[]>([]) // 列表的数据
const flatList = ref<FlatItem[]>([]) // 平铺数据结构
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  type: undefined,
  typeId: undefined,
  skillId: undefined,
  level: undefined,
  title: undefined,
  keywords: undefined,
  introduction: undefined,
  matchDegree: undefined,
  matchReason: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 难度级别字典
const difficultyOptions = ref([
  { label: '初级', value: 1 },
  { label: '中级', value: 2 },
  { label: '高级', value: 3 }
])

// 获取难度级别标签
const getDifficultyLabel = (level: number | null) => {
  if (!level) return '-'
  const option = difficultyOptions.value.find((item) => item.value === level)
  return option ? option.label : level
}

// 将数据转换为平铺结构
const flattenData = (contentData: ExtendedLearningContentRecommendVO[]): FlatItem[] => {
  const result: FlatItem[] = []

  contentData.forEach((content) => {
    if (!content.skills || Object.keys(content.skills).length === 0) {
      // 没有技能数据的内容直接添加
      result.push({
        id: `content_${content.typeId || 0}`,
        title: content.title || '',
        keywords: Array.isArray(content.keywords) ? content.keywords : [],
        level: content.level,
        introduction: content.introduction || ''
      })
    } else {
      // 处理有技能数据的内容
      Object.entries(content.skills).forEach(([firstLevelSkillKey, firstSkills]) => {
        // 一级技能是否有二级技能
        const hasSecondLevel = Object.keys(firstSkills).length > 0

        if (!hasSecondLevel) {
          // 只有一级技能
          result.push({
            id: `first_${content.typeId}_${firstLevelSkillKey}`,
            title: content.title || '',
            keywords: Array.isArray(content.keywords) ? content.keywords : [],
            level: content.level,
            introduction: content.introduction || '',
            firstName: getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey)
          })
        } else {
          // 处理二级技能
          Object.entries(firstSkills).forEach(([secondLevelSkillKey, skills]) => {
            if (!skills || skills.length === 0) {
              // 只有二级技能，没有三级技能
              result.push({
                id: `second_${content.typeId}_${firstLevelSkillKey}_${secondLevelSkillKey}`,
                title: content.title || '',
                keywords: Array.isArray(content.keywords) ? content.keywords : [],
                level: content.level,
                introduction: content.introduction || '',
                firstName: getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey),
                secondName: getDictLabel(DICT_TYPE.EDP_SECOND_LEVEL_SKILL, secondLevelSkillKey)
              })
            } else {
              // 处理三级技能
              skills.forEach((skill) => {
                result.push({
                  id: `skill_${content.typeId}_${firstLevelSkillKey}_${secondLevelSkillKey}_${skill.id}`,
                  title: content.title || '',
                  keywords: Array.isArray(content.keywords) ? content.keywords : [],
                  level: content.level,
                  introduction: content.introduction || '',
                  firstName: getDictLabel(DICT_TYPE.EDP_FIRST_LEVEL_SKILL, firstLevelSkillKey),
                  secondName: getDictLabel(DICT_TYPE.EDP_SECOND_LEVEL_SKILL, secondLevelSkillKey),
                  skillName: skill.name,
                  matchDegree: skill.matchDegree,
                  matchReason: skill.matchReason
                })
              })
            }
          })
        }
      })
    }
  })

  return result
}

// 单元格合并方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 存储合并信息的对象
  const titleSpanArr: number[] = []
  const firstSkillSpanArr: number[] = []
  const secondSkillSpanArr: number[] = []

  let titlePos = 0
  let firstSkillPos = 0
  let secondSkillPos = 0

  // 计算各列的合并信息
  flatList.value.forEach((item, index) => {
    // 标题列
    if (index === 0) {
      titleSpanArr.push(1)
      titlePos = 0
    } else {
      if (item.title === flatList.value[titlePos].title) {
        titleSpanArr[titlePos] += 1
        titleSpanArr.push(0)
      } else {
        titleSpanArr.push(1)
        titlePos = index
      }
    }

    // 一级技能列
    if (index === 0 || !item.firstName) {
      firstSkillSpanArr.push(item.firstName ? 1 : 0)
      firstSkillPos = index
    } else {
      if (
        item.firstName &&
        item.firstName === flatList.value[firstSkillPos].firstName &&
        item.title === flatList.value[firstSkillPos].title
      ) {
        firstSkillSpanArr[firstSkillPos] += 1
        firstSkillSpanArr.push(0)
      } else {
        firstSkillSpanArr.push(item.firstName ? 1 : 0)
        firstSkillPos = index
      }
    }

    // 二级技能列
    if (index === 0 || !item.secondName) {
      secondSkillSpanArr.push(item.secondName ? 1 : 0)
      secondSkillPos = index
    } else {
      if (
        item.secondName &&
        item.secondName === flatList.value[secondSkillPos].secondName &&
        item.firstName === flatList.value[secondSkillPos].firstName &&
        item.title === flatList.value[secondSkillPos].title
      ) {
        secondSkillSpanArr[secondSkillPos] += 1
        secondSkillSpanArr.push(0)
      } else {
        secondSkillSpanArr.push(item.secondName ? 1 : 0)
        secondSkillPos = index
      }
    }
  })

  // 根据列索引返回对应的合并信息
  if (columnIndex === 0) {
    // 标题列
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 1) {
    // 关键词列
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 2) {
    // 难度级别列
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 3) {
    // 描述列
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 4) {
    // 一级技能列
    const rowspan = firstSkillSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 5) {
    // 二级技能列
    const rowspan = secondSkillSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else if (columnIndex === 9) {
    // 操作列
    const rowspan = titleSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  }
}

// 添加match处理函数
const handleMatch = (row) => {
  // 提取当前行所属的内容信息
  const idParts = row.id.toString().split('_')

  // 获取当前行对应的内容信息
  const contentData = {
    type: list.value.find((item) => item.typeId === Number(idParts[1]))?.type || 1,
    typeId: Number(idParts[1])
  }

  console.log('Match操作:', contentData)
  // 这里可以添加实际的match操作逻辑，例如导航到匹配页面或者打开匹配弹窗
  // 例如：router.push({ path: '/match', query: contentData })
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LearningContentRecommendApi.getLearningContentRecommendPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 转换为平铺结构
    flatList.value = flattenData(data.list)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
