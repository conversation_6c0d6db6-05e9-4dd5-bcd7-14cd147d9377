<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="内容类型" prop="type">
        <el-select v-model="formData.bizType" placeholder="请选择内容类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="内容ID" prop="typeId">
        <el-input v-model="formData.typeId" placeholder="请输入内容ID" />
      </el-form-item>
      <el-form-item label="技能ID" prop="skillId">
        <el-input v-model="formData.skillId" placeholder="请输入技能ID" />
      </el-form-item>
      <el-form-item label="难度等级(Level，0：Suitable for all（默认），1：Beginner，2：Intermediate，3：Advanced)" prop="level">
        <el-input v-model="formData.level" placeholder="请输入难度等级(Level，0：Suitable for all（默认），1：Beginner，2：Intermediate，3：Advanced)" />
      </el-form-item>
      <el-form-item label="内容标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入内容标题" />
      </el-form-item>
      <el-form-item label="关键词" prop="keywords">
        <el-input v-model="formData.keywords" placeholder="请输入关键词" />
      </el-form-item>
      <el-form-item label="介绍" prop="introduction">
        <el-input v-model="formData.introduction" placeholder="请输入介绍" />
      </el-form-item>
      <el-form-item label="匹配度" prop="matchDegree">
        <el-input v-model="formData.matchDegree" placeholder="请输入匹配度" />
      </el-form-item>
      <el-form-item label="匹配原因" prop="matchReason">
        <el-input v-model="formData.matchReason" placeholder="请输入匹配原因" />
      </el-form-item>
      <el-form-item label="状态（1.新增，2保持，3待删除）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { LearningContentRecommendApi, LearningContentRecommendVO } from '@/api/edp/learningcontentrecommend'

/** 学习内容推荐 表单 */
defineOptions({ name: 'LearningContentRecommendForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  bizType: undefined,
  typeId: undefined,
  skillId: undefined,
  level: undefined,
  title: undefined,
  keywords: undefined,
  introduction: undefined,
  matchDegree: undefined,
  matchReason: undefined,
  status: undefined
})
const formRules = reactive({
  type: [{ required: true, message: '内容类型不能为空', trigger: 'change' }],
  typeId: [{ required: true, message: '内容ID不能为空', trigger: 'blur' }],
  skillId: [{ required: true, message: '技能ID不能为空', trigger: 'blur' }],
  level: [{ required: true, message: '难度等级(Level，0：Suitable for all（默认），1：Beginner，2：Intermediate，3：Advanced)不能为空', trigger: 'blur' }],
  matchDegree: [{ required: true, message: '匹配度不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态（1.新增，2保持，3待删除）不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LearningContentRecommendApi.getLearningContentRecommend(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LearningContentRecommendVO
    if (formType.value === 'create') {
      await LearningContentRecommendApi.createLearningContentRecommend(data)
      message.success(t('common.createSuccess'))
    } else {
      await LearningContentRecommendApi.updateLearningContentRecommend(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    bizType: undefined,
    typeId: undefined,
    skillId: undefined,
    level: undefined,
    title: undefined,
    keywords: undefined,
    introduction: undefined,
    matchDegree: undefined,
    matchReason: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>