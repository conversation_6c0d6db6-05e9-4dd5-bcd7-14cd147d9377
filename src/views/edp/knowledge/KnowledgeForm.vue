<template>
  <Dialog :fullscreen="false" title="Review" v-model="dialogVisible">
    <template #default>
      <div class="flex flex-col space-y-2">
        <span> Document Introduction </span>
        <span class="text-sm text-gray-400">
          This role covers Record Management (document creation, archiving, and search), Processing
          Management (archive requests and borrowing/returning workflows), and Warehouse Management
          (multi-level storage and RFID tracking). Key Technical Skills include IT architecture,
          software development, and system configuration. Leadership requires team supervision,
          project management, and strong communication. Problem-solving, compliance, and process
          improvements are essential.
        </span>
      </div>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        class="mt-5"
        label-width="200px"
        v-loading="formLoading"
      >
        <el-form-item label="Department" prop="deptName">
          <p> {{ formData.deptName }} </p>
        </el-form-item>
      </el-form>
    </template>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { KnowledgeApi, KnowledgeVO } from '@/api/edp/knowledge'

/** 知识 表单 */
defineOptions({ name: 'KnowledgeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: undefined,
  content: undefined,
  introduction: undefined,
  keywords: undefined,
  deptId: undefined,
  deptName: undefined,
  positionId: undefined,
  chatId: undefined,
  level: undefined,
  status: undefined,
  listed: undefined,
  approver: undefined,
  approveReason: undefined,
  version: undefined,
  source: undefined
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await KnowledgeApi.getKnowledge(id)
      Object.assign(formData.value, data)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as KnowledgeVO
    if (formType.value === 'create') {
      await KnowledgeApi.createKnowledge(data)
      message.success(t('common.createSuccess'))
    } else {
      await KnowledgeApi.updateKnowledge(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: undefined,
    content: undefined,
    introduction: undefined,
    keywords: undefined,
    deptId: undefined,
    deptName: undefined,
    positionId: undefined,
    chatId: undefined,
    level: undefined,
    status: undefined,
    listed: undefined,
    approver: undefined,
    approveReason: undefined,
    version: undefined,
    source: undefined
  }
  formRef.value?.resetFields()
}
</script>