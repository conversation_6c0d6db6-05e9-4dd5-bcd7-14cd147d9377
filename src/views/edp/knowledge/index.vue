<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import download from '@/utils/download'
import { KnowledgeApi, KnowledgeVO, KnowledgeStatusEnum } from '@/api/edp/knowledge'
import KnowledgeForm from './KnowledgeForm.vue'
import DocIcon from '@/assets/icons/svg/docIcon.svg'
import Employee from '@/assets/icons/svg/employee.svg'
import Supervisor from '@/assets/icons/svg/supervisor.svg'

/** ----- SETUP ----- */
defineOptions({ name: 'Knowledge' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const knowledgeList = ref<KnowledgeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  content: undefined,
  introduction: undefined,
  keywords: undefined,
  deptId: undefined,
  positionId: undefined,
  chatId: undefined,
  level: undefined,
  status: undefined,
  listed: undefined,
  approver: undefined,
  approveReason: undefined,
  version: undefined,
  source: undefined,
  createTime: []
})  // 定义查询参数
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const formRef = ref() // 表单 Ref
// 状态选项
const statusOptions = ref([
  { value: KnowledgeStatusEnum.DRAFT, label: 'Draft' },
  { value: KnowledgeStatusEnum.UNPUBLISHED, label: 'Unpublished' },
  { value: KnowledgeStatusEnum.PUBLISHED, label: 'Published' },
  { value: KnowledgeStatusEnum.REJECTED, label: 'Rejected' }
])

/** ----- FUNCTIONS ----- */
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await KnowledgeApi.getKnowledgePage(queryParams)
    knowledgeList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  // 重置所有查询参数
  Object.assign(queryParams, {
    pageNo: 1,
    pageSize: 10,
    title: undefined,
    content: undefined,
    introduction: undefined,
    keywords: undefined,
    deptId: undefined,
    positionId: undefined,
    chatId: undefined,
    level: undefined,
    status: undefined,
    listed: undefined,
    approver: undefined,
    approveReason: undefined,
    version: undefined,
    source: undefined,
    createTime: []
  })
  // 重新获取数据
  getList()
}

/** 添加/修改操作 */
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await KnowledgeApi.deleteKnowledge(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case KnowledgeStatusEnum.DRAFT: return 'info'     // 草稿 - 灰色
    case KnowledgeStatusEnum.UNPUBLISHED: return 'warning'  // 待发布 - 橙色
    case KnowledgeStatusEnum.PUBLISHED: return 'success'  // 已发布 - 绿色
    case KnowledgeStatusEnum.REJECTED: return 'danger'   // 打回 - 红色
    default: return 'info'
  }
}

/** 获取状态标签文本 */
const getStatusLabel = (status: number) => {
  const option = statusOptions.value.find(item => item.value === status)
  return option ? option.label : 'Unknown'
}

/** 格式化创建时间 mm-dd-yyyy hh:mm */
const formatCreateTime = (dateTime: string | Date) => {
  if (!dateTime) return ''
  return formatDate(new Date(dateTime), 'MM-DD-YYYY HH:mm')
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<template>
  <ContentWrap>
    <!-- 工作栏 -->
    <el-form
      class="-mb-15px space-x-2"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <el-select
          v-model="queryParams.status"
          @update:model-value="handleQuery"
          placeholder="Select Status"
          style="width: 240px"
          clearable
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> Search </el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> Reset </el-button>
        <el-button type="primary" @click="openForm('create')" v-hasPermi="['edp:knowledge:create']">
          <Icon icon="ep:plus" class="mr-5px" /> New
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="knowledgeList" :show-overflow-tooltip="true">
      <!--文档标题-->
      <el-table-column label="Title" prop="title">
        <template #default="scope">
          <div class="flex items-center space-x-2">
            <img :src="DocIcon" class="w-4 h-4 -translate-y-[1px]" />
            <span class="align-middle"> {{ scope.row.title }} </span>
          </div>
        </template>
      </el-table-column>

      <!--文档来源-->
      <el-table-column label="Source" prop="source">
        <template #default="scope">
          <div class="flex items-center space-x-2">
            <img
              :src="scope.row.source === 'Supervisor' ? Supervisor : Employee"
              class="w-4 h-4 flex-shrink-0 -translate-y-[1px]"
            />
            <span class="align-middle">{{ scope.row.source }}</span>
          </div>
        </template>
      </el-table-column>

      <!--作者-->
      <el-table-column label="Author" prop="source" />

      <!--状态-->
      <el-table-column label="Status" prop="status">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!--创建时间-->
      <el-table-column label="Create Time" prop="createTime">
        <template #default="scope">
          {{ formatCreateTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <!--操作-->
      <el-table-column label="Actions" min-width="120px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">
            <Icon icon="ep:finished" />
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> Delete </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <KnowledgeForm ref="formRef" @success="getList" />
</template>