<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="岗位ID" prop="positionId">
        <el-input v-model="formData.positionId" placeholder="请输入岗位ID" />
      </el-form-item>
      <el-form-item label="技能ID" prop="skillId">
        <el-input v-model="formData.skillId" placeholder="请输入技能ID" />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item label="内容标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入内容标题" />
      </el-form-item>
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker
          v-model="formData.startDate"
          type="date"
          value-format="x"
          placeholder="选择开始日期"
        />
      </el-form-item>
      <el-form-item label="介绍" prop="endDate">
        <el-date-picker
          v-model="formData.endDate"
          type="date"
          value-format="x"
          placeholder="选择介绍"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { StudyPlanApi, StudyPlanVO } from '@/api/edp/studyplan'

/** 学习计划 表单 */
defineOptions({ name: 'StudyPlanForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  positionId: undefined,
  skillId: undefined,
  userId: undefined,
  title: undefined,
  startDate: undefined,
  endDate: undefined
})
const formRules = reactive({
  positionId: [{ required: true, message: '岗位ID不能为空', trigger: 'blur' }],
  skillId: [{ required: true, message: '技能ID不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  startDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await StudyPlanApi.getStudyPlan(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as StudyPlanVO
    if (formType.value === 'create') {
      await StudyPlanApi.createStudyPlan(data)
      message.success(t('common.createSuccess'))
    } else {
      await StudyPlanApi.updateStudyPlan(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    positionId: undefined,
    skillId: undefined,
    userId: undefined,
    title: undefined,
    startDate: undefined,
    endDate: undefined
  }
  formRef.value?.resetFields()
}
</script>