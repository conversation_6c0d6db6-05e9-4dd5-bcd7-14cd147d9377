<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import type { TabsPaneContext } from 'element-plus'
import LearningContentTable from '@/views/edp/learningcontentsnapshot/LearningContentTable.vue'

/** ----- SETUP ----- */
const { t } = useI18n()
const route = useRoute()
const positionId = computed(() => {
  return Number(route.query.positionId)
})
const positionName = computed(() => {
  return route.query.positionName as string
})
const matchDegree = computed(() => {
  // 获取用户选择的匹配度，确保转换为正确的数字值
  const value = Number(route.query.matchDegree)
  // 只有在值为NaN时才使用默认值，保留用户选择的值
  if (isNaN(value)) {
    console.warn('匹配度值无效，使用默认值70')
    return 70 // 默认使用70%作为匹配度
  }
  console.log('当前使用的匹配度值:', value)
  return value
})
const currentPhase = ref(1) // 当前选中的阶段
const activeName = ref('phase1')

/** ----- METHODS ----- */
// 添加 tab 切换事件处理函数
const handleTabClick = (tab: TabsPaneContext) => {
  console.log('当前选中的标签页:', tab)
  activeName.value = tab.props.name as string

  // 根据标签页名称设置对应的ID
  switch (tab.props.name) {
    case 'phase1':
      currentPhase.value = 1
      break
    case 'phase2':
      currentPhase.value = 2
      break
    case 'phase3':
      currentPhase.value = 3
      break
    default:
      currentPhase.value = 1
  }
}

/** 初始化 */
onMounted(() => {})
</script>

<template>
  <div class="app-main-height">
    <div class="!app-main-height !bg-white p-4">
      <el-tabs v-model="activeName" @tab-click="handleTabClick" class="learning-tabs">
        <!--Phase 1-->
        <el-tab-pane label="Phase I" name="phase1">
          <LearningContentTable
            :positionId="positionId"
            :positionName="positionName"
            :currentPhase="currentPhase"
            :matchDegree="matchDegree"
          />
        </el-tab-pane>

        <!--Phase 2-->
        <el-tab-pane label="Phase II" name="phase2">
          <LearningContentTable
            :positionId="positionId"
            :positionName="positionName"
            :currentPhase="currentPhase"
            :matchDegree="matchDegree"
          />
        </el-tab-pane>

        <!--Phase 3-->
        <el-tab-pane label="Phase III" name="phase3">
          <LearningContentTable
            :positionId="positionId"
            :positionName="positionName"
            :currentPhase="currentPhase"
            :matchDegree="matchDegree"
          />
        </el-tab-pane>

        <!--学习计划内容管理-->
        <!--<el-tab-pane label="Learning Plan Content" name="content" class="content-tab">-->
        <!--  <StudyPlan />-->
        <!--</el-tab-pane>-->
      </el-tabs>
    </div>
  </div>
</template>

<style scoped>
/* 自定义tabs布局 */
/**
.learning-tabs :deep(.el-tabs__header) {
  display: flex;
}

.learning-tabs :deep(.el-tabs__nav) {
  display: flex;
  width: 100%;
}

.learning-tabs :deep(.el-tabs__item:last-child) {
  margin-left: auto;
}
*/
</style>