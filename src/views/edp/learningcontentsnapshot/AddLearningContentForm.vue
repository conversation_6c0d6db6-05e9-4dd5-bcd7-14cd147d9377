<script lang="ts" setup>
import { ref, watch, computed, nextTick } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import {
  getContentPageVO,
  LearningContentSnapshotApi,
  LearningContentEnum,
  AddLearningContentVO
} from '@/api/edp/learningcontentsnapshot'

// 接收父组件传递的属性
const props = defineProps<{
  phase: number
  positionId: number
  // matchDegree: number
}>()

/** INTERFACE */
interface FlatItem {
  id: string
  skill: string
  contentTitle: string
  bizType: number
  keywords?: string[]
  level: number
  recommendId?: number
} // 扁平化后的数据结构

/** ----- SETUP ----- */
const emit = defineEmits(['refresh-list']) // 定义emit
const loading = ref<boolean>(false)
const centerDialogVisible = ref(false)
const activeName = ref('first')
const activeTab = ref<number>(0) // 当前选中的Tab
const flatList = ref<FlatItem[]>([])
const multipleSelection = ref<FlatItem[]>([]) // 用于存储当前选中的行
const recommendIdSum = ref<number[]>([]) // 用于存储推荐ID的数组
const matchDegree = ref<number>(70) // 匹配度选择
const pageNo = ref(1)
const pageSize = ref(10)
const total = ref(0) // 总数据条数
const searchSkill = ref('')
const tableRef = ref() // 表格引用

// 使用枚举创建tabList
const tabList = ref([
  {
    id: 0,
    label: 'All',
    name: 'first',
    phase: 0
  },
  {
    id: LearningContentEnum.ONLINE_COURSE,
    label: 'Content Course',
    name: 'second',
    phase: LearningContentEnum.ONLINE_COURSE
  },
  {
    id: LearningContentEnum.MLC_TRAINING,
    label: 'MLC Training',
    name: 'third',
    phase: LearningContentEnum.MLC_TRAINING
  },
  {
    id: LearningContentEnum.KNOWLEDGE,
    label: 'Knowledge',
    name: 'fourth',
    phase: LearningContentEnum.KNOWLEDGE
  }
])

/** ----- METHODS ----- */
// 将静态对象改为计算属性
const queryParams = computed<getContentPageVO>(() => {
  const params: getContentPageVO = {
    phase: props.phase,
    positionId: props.positionId,
    matchDegree: matchDegree.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value
  }

  // 只有当activeTab不为0时才添加type参数
  if (activeTab.value !== 0) {
    params.bizType = activeTab.value
  }

  return params
})

// 过滤后的列表
const filteredList = computed(() => {
  if (!searchSkill.value) return flatList.value

  const searchTerm = searchSkill.value.toLowerCase().trim()
  return flatList.value.filter((item) => item.skill.toLowerCase().includes(searchTerm))
})

/** 处理Tab点击 */
const handleTabClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
  // 根据当前选中的tab名称，找到对应的type值并更新activeTab
  const selectedTab = tabList.value.find((item) => item.name === tab.props.name)
  if (selectedTab) {
    activeTab.value = selectedTab.phase
    console.log(
      '当前选中的类型:',
      activeTab.value,
      '当前已选中项数量:',
      recommendIdSum.value.length
    )
    // 切换Tab时重新获取数据
    pageNo.value = 1 // 重置页码
    getLearningContent()
  }
}

/** 获取学习内容列表 */
const getLearningContent = async () => {
  loading.value = true
  try {
    // 接口调用
    const res = await LearningContentSnapshotApi.getContentPage(queryParams.value)
    console.log('获取学习内容参数:', res)

    // 将后端返回的数据格式化为flatList需要的格式
    if (res && res.list) {
      // 直接处理res.list为扁平化数据
      flatList.value = formatData(res.list)
      total.value = res.total || 0 // 设置总数

      // 在数据加载完成后恢复选中状态
      nextTick(() => {
        if (tableRef.value) {
          // 先清除当前页面的所有选择
          tableRef.value.clearSelection()

          // 遍历当前页面数据，找到之前已选中的项
          const matchedRows = flatList.value.filter(
            (row) => row.recommendId && recommendIdSum.value.includes(row.recommendId)
          )

          // 单独处理每一行，确保选中状态正确应用
          matchedRows.forEach((row) => {
            tableRef.value.toggleRowSelection(row, true)
          })

          console.log(
            '恢复选中状态后，当前页面选中项数量:',
            matchedRows.length,
            '总选中项数量:',
            recommendIdSum.value.length
          )
        }
      })
    } else {
      flatList.value = []
      total.value = 0
    }
  } catch (error) {
    console.log('获取学习内容失败:', error)
    flatList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

/** 格式化数据为扁平列表 */
const formatData = (data: any[]) => {
  const result: FlatItem[] = []

  // 遍历后端返回的数据
  data.forEach((item, itemIndex) => {
    if (item.content && Array.isArray(item.content)) {
      // 处理每个内容项
      item.content.forEach((content, contentIndex) => {
        // 处理关键词
        const keywords = content.keywords
          ? String(content.keywords)
              .split(',')
              .filter((k) => k)
              .map((k) => k.trim())
          : []

        // 创建单行数据，包含所有关键词
        result.push({
          id: `item_${itemIndex}_content_${contentIndex}`,
          skill: item.name || '',
          contentTitle: content.contentTitle || '',
          bizType: content.bizType || 0,
          keywords: keywords, // 存储完整的关键词数组
          level: content.level || 0,
          recommendId: content.recommendId
        })
      })
    }
  })

  return result
}

/** 搜索按钮操作 */
const handleQuery = () => {
  pageNo.value = 1
  searchSkill.value = '' // Reset search term
  getLearningContent()
}

/** 处理添加学习内容 */
const handleAdd = async () => {
  centerDialogVisible.value = false // 关闭弹窗
  try {
    // 创建请求体
    const data: AddLearningContentVO = {
      recommendIds: recommendIdSum.value,
      positionId: props.positionId,
      phase: props.phase
    }

    // 调用接口
    await LearningContentSnapshotApi.addLearningContent(data)
    console.log('学习内容添加成功')
    ElMessage.success('Learning content added successfully')

    // 清空选择
    multipleSelection.value = []
    recommendIdSum.value = []
    tableRef.value?.clearSelection()

    // 发射事件通知父组件刷新列表
    emit('refresh-list')
  } catch (error) {
    console.log('学习内容添加失败', error)
    ElMessage.error('Learning content added failed')
  }
}

/** 处理选择变更 */
const handleSelectionChange = (selection: FlatItem[]) => {
  multipleSelection.value = selection

  // 获取当前页面所有recommendId的集合
  const currentPageRecommendIds = flatList.value
    .filter((item) => item.recommendId)
    .map((item) => item.recommendId)

  // 从recommendIdSum中移除当前页面已取消选中的项
  recommendIdSum.value = recommendIdSum.value.filter((id) => {
    // 如果ID不在当前页面，则保留
    if (!currentPageRecommendIds.includes(id)) {
      return true
    }
    // 如果ID在当前页面且在选中项中，则保留
    return selection.some((row) => row.recommendId === id)
  })

  // 添加当前页面新选中的项
  selection.forEach((row) => {
    if (row.recommendId && !recommendIdSum.value.includes(row.recommendId)) {
      recommendIdSum.value.push(row.recommendId)
    }
  })

  console.log(
    '选择变更后，当前选中的recommendId:',
    recommendIdSum.value,
    '总数:',
    recommendIdSum.value.length
  )
}

/** 处理分页事件 */
const handlePagination = ({ page, limit }) => {
  console.log('分页切换, 当前已选中项数量:', recommendIdSum.value.length)
  pageNo.value = page
  pageSize.value = limit
  getLearningContent()
}

/** ----- WATCH ----- */
/** 对话框打开时获取数据 */
watch(centerDialogVisible, (val) => {
  if (val) {
    // 打开对话框时重置页码并获取数据
    pageNo.value = 1
    getLearningContent()
  } else {
    // 关闭对话框时不重置选择状态，保留用户的选择
    console.log('关闭对话框，保留选中状态，当前选中项数量:', recommendIdSum.value.length)
  }
})

/** 监听匹配度变化，重新请求数据 */
watch(matchDegree, (newVal, oldVal) => {
  console.log('匹配度变化:', oldVal, '->', newVal)
  // 只有在对话框打开状态下才重新请求数据
  if (centerDialogVisible.value) {
    pageNo.value = 1 // 重置页码
    getLearningContent()
  }
})

// 对外暴露的方法
defineExpose({
  open: () => {
    centerDialogVisible.value = true
  }
})
</script>

<template>
  <el-button @click="centerDialogVisible = true">
    <Icon icon="ep:plus" />
    <span> Add </span>
  </el-button>

  <el-dialog
    v-model="centerDialogVisible"
    title="Update Learning Map Content"
    width="50%"
    align-center
    class="flex flex-col"
  >
    <!--搜索组-->
    <div class="flex items-center justify-between my-4">
      <!--搜索-->
      <el-input v-model="searchSkill" placeholder="Search for Skill" clearable class="!w-240px">
        <template #suffix>
          <Icon icon="ep:search" />
        </template>
      </el-input>

      <div class="flex items-center justify-between gap-4">
        <el-slider v-model="matchDegree" :step="1" :max="90" :min="70" class="!w-300px" />

        <p> Match Degree: {{ matchDegree }}% </p>
      </div>

      <span :style="{ color: recommendIdSum.length > 0 ? '#017B3D' : '#909399' }"
        >Choose: {{ recommendIdSum.length }}</span
      >
    </div>

    <el-tabs v-model="activeName" @tab-click="handleTabClick" class="add-content-form-tabs">
      <!--类型切换-->
      <el-tab-pane v-for="tab in tabList" :key="tab.id" :label="tab.label" :name="tab.name" />

      <!--待添加的学习内容-->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="filteredList"
        :show-overflow-tooltip="true"
        @selection-change="handleSelectionChange"
      >
        <!--选择列-->
        <el-table-column type="selection" width="55" />
        <!--技能名称-->
        <el-table-column label="Skill" align="center" prop="skill" />
        <el-table-column label="Content Name" align="center" prop="contentTitle" />
        <el-table-column label="Content Tags" align="center">
          <template #default="scope">
            <div class="tag-container">
              <el-tooltip
                v-if="scope.row.keywords && scope.row.keywords.length > 0"
                :content="scope.row.keywords.join(', ')"
                placement="top"
                :show-after="300"
              >
                <div class="flex flex-wrap gap-1">
                  <el-tag
                    v-for="(tag, index) in scope.row.keywords"
                    :key="index"
                    size="small"
                    type="info"
                    class="mx-1 mb-1"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </el-tooltip>
              <span v-else>-</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-tabs>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="pageNo"
      v-model:limit="pageSize"
      @pagination="handlePagination"
    />

    <!--底部操作区-->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="centerDialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleAdd"> Add </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* Override parent styles to prevent inheritance */
.add-content-form-tabs :deep(.el-tabs__header) {
  display: block; /* Override flex display from parent */
}

.add-content-form-tabs :deep(.el-tabs__nav) {
  display: flex;
  width: auto; /* Override width: 100% from parent */
}

.add-content-form-tabs :deep(.el-tabs__item:last-child) {
  margin-left: 0; /* Override margin-left: auto from parent */
}

/* Tags container styling */
.tag-container {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tag-container .flex {
  display: inline-flex;
  max-width: 100%;
}
</style>
