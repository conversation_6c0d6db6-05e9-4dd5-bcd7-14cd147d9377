<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="撮合ID" prop="recommendId">
        <el-input v-model="formData.recommendId" placeholder="请输入撮合ID" />
      </el-form-item>
      <el-form-item label="计划ID" prop="planId">
        <el-input v-model="formData.planId" placeholder="请输入计划ID" />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item label="内容状态：1.正常，2.新增，3.删除" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否跳过 0未跳过，1跳过" prop="skipped">
        <el-select v-model="formData.skipped" placeholder="请选择是否跳过 0未跳过，1跳过">
          <el-option
            v-for="dict in getBoolDictOptions(DICT_TYPE.EDP_LEARNING-PLAN_CONTENT_SKIPPED_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getBoolDictOptions, DICT_TYPE } from '@/utils/dict'
import { PlanContentApi, PlanContentVO } from '@/api/edp/plancontent'

/** 计划内容 表单 */
defineOptions({ name: 'PlanContentForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  recommendId: undefined,
  planId: undefined,
  userId: undefined,
  status: undefined,
  skipped: undefined
})
const formRules = reactive({
  recommendId: [{ required: true, message: '撮合ID不能为空', trigger: 'blur' }],
  planId: [{ required: true, message: '计划ID不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '内容状态：1.正常，2.新增，3.删除不能为空', trigger: 'blur' }],
  skipped: [{ required: true, message: '是否跳过 0未跳过，1跳过不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PlanContentApi.getPlanContent(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as PlanContentVO
    if (formType.value === 'create') {
      await PlanContentApi.createPlanContent(data)
      message.success(t('common.createSuccess'))
    } else {
      await PlanContentApi.updatePlanContent(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    recommendId: undefined,
    planId: undefined,
    userId: undefined,
    status: undefined,
    skipped: undefined
  }
  formRef.value?.resetFields()
}
</script>