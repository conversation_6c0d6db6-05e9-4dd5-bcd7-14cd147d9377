<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { positionLearningMapApi } from '@/api/edp/learningmap'
import {
  LearningContentSnapshotApi,
  LearningContentStatusEnum
} from '@/api/edp/learningcontentsnapshot'
import AddLeaningContent from '@/views/edp/learningcontentsnapshot/AddLearningContentForm.vue'
import GenerationDialog from '@/views/edp/learningmap/GenerationDialog.vue'

/** INTERFACE */
interface ContentItem {
  id: number // content的真实ID
  contentTitle: string
  bizType: number
  recommendId: number
  keywords: string
  level: number
  matchDegree: number
  matchReason: string
  isRequired: number
  status?: LearningContentStatusEnum // 状态
}

interface LearningContentType {
  id: number
  name: string
  firstLevelSkillName: string
  secondLevelSkillName: string
  content: ContentItem[]
}

interface QueryParams {
  positionId: number
  phase: number
  contentTitle?: string
  pageNo: number
  pageSize: number
}
// 扁平化后的数据结构
interface FlatItem {
  id: number // 技能ID
  contentId: number // content的真实ID
  skill: string
  firstLevelSkillName: string
  secondLevelSkillName: string
  contentTitle: string
  bizType: number
  level: number
  recommendId: number
  keyword?: string
  matchDegree: number // 匹配度
  matchReason: string // 匹配原因
  status?: LearningContentStatusEnum // 状态
}

/** 学习内容快照 列表 */
defineOptions({ name: 'LearningContentSnapshot' })

const message = useMessage() // 消息弹窗
const props = defineProps<{
  positionId: number
  positionName: string
  currentPhase: number
  matchDegree: number
}>()
const loading = ref(true)
const publishLoading = ref(false) // 发布按钮的加载状态
const list = ref<LearningContentType[]>([])
const total = ref(0)
const searchSkill = ref('') // Add search term for skill filtering
const flatList = ref<FlatItem[]>([])
const generationDialogRef = ref() // GenerationDialog 引用
const queryParams = reactive<QueryParams>({
  positionId: props.positionId,
  phase: props.currentPhase,
  pageNo: 1,
  pageSize: 10
})
// 过滤后的列表
const filteredList = computed(() => {
  if (!searchSkill.value) return flatList.value

  const searchTerm = searchSkill.value.toLowerCase().trim()
  return flatList.value.filter((item) => item.skill.toLowerCase().includes(searchTerm))
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LearningContentSnapshotApi.getLearningContentSnapshotPage(queryParams)
    list.value = data.list || []
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  searchSkill.value = '' // Reset search term
  getList()
}

/** 处理删除学习内容 */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()

    // 调用接口
    await LearningContentSnapshotApi.batchDeleteContentByIds(id)
    console.log('删除请求的ID:', id)
    console.log('学习内容删除成功！')
    ElMessage.success('Learning content deleted successfully!')

    // 刷新列表
    await getList()
  } catch (error) {
    console.log('学习内容删除失败', error)
  }
}

/** 处理重新生成 */
const handleRegenerate = (id: number, name: string) => {
  console.log('Regenerating learning map for:', name)
  // 直接打开生成对话框，重新生成学习地图
  generationDialogRef.value?.open(id, name)
}

/** 发布学习内容 */
const handlePublish = async () => {
  publishLoading.value = true
  try {
    // 创建请求体
    const data = {
      positionId: props.positionId,
      phase: props.currentPhase
    }

    // 调用API
    const res = await positionLearningMapApi.publishLearningMap(data)
    console.log('发学习内容发布成功😄:', res)
    ElMessage.success('Learning content published successfully!')

    // 刷新列表
    await getList()
  } catch (error) {
    console.log('学习内容发布失败😫:', error)
    ElMessage.error('Failed to publish learning content!')
  } finally {
    publishLoading.value = false
  }
}

// 扁平化数据方法
const flattenData = () => {
  const result: FlatItem[] = []

  // 遍历每个技能
  list.value.forEach((skillItem) => {
    // 遍历技能内容
    skillItem.content.forEach((content) => {
      // 创建扁平化数据项
      result.push({
        id: skillItem.id, // ID
        contentId: content.id, // content的真实ID
        skill: skillItem.name,
        firstLevelSkillName: skillItem.firstLevelSkillName,
        secondLevelSkillName: skillItem.secondLevelSkillName,
        contentTitle: content.contentTitle,
        bizType: content.bizType,
        level: content.level,
        recommendId: content.recommendId,
        keyword: content.keywords,
        matchDegree: content.matchDegree,
        matchReason: content.matchReason,
        status: content.status || LearningContentStatusEnum.DRAFT
      })
    })
  })

  return result
}

// 计算合并单元格方法
const objectSpanMethod = ({ rowIndex, columnIndex }) => {
  // 如果还没有数据，直接返回
  if (flatList.value.length === 0) return { rowspan: 1, colspan: 1 }

  // Action列（第4列）不参与合并，直接返回
  if (columnIndex === 4) {
    return { rowspan: 1, colspan: 1 }
  }

  // 只需要存储skill列的合并信息
  const skillSpanArr: number[] = []
  let skillPos = 0

  // 计算skill列的合并信息
  flatList.value.forEach((item, index) => {
    // 技能列
    if (index === 0) {
      skillSpanArr.push(1)
      skillPos = 0
    } else {
      if (item.skill === flatList.value[skillPos].skill) {
        skillSpanArr[skillPos] += 1
        skillSpanArr.push(0)
      } else {
        skillSpanArr.push(1)
        skillPos = index
      }
    }
  })

  // 只合并skill列（columnIndex为0）
  if (columnIndex === 0) {
    const rowspan = skillSpanArr[rowIndex]
    const colspan = rowspan > 0 ? 1 : 0
    return { rowspan, colspan }
  } else {
    // 其他列不合并
    return { rowspan: 1, colspan: 1 }
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

// 监听岗位ID变化
watch(
  () => props.positionId,
  (newVal) => {
    if (newVal) {
      queryParams.positionId = Number(newVal)
      handleQuery()
    }
  },
  { immediate: true }
)

// 监听标签ID变化
watch(
  () => props.currentPhase,
  (newVal) => {
    if (newVal) {
      queryParams.phase = newVal
      handleQuery()
    }
  },
  { immediate: true }
)

// 初始化时生成扁平化数据
watch(
  () => list.value,
  () => {
    flatList.value = flattenData()
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <!--Phase1标题容器-->
  <div v-if="currentPhase === 1" class="flex flex-col gap-1 my-4">
    <!--标题-->
    <h1> Phase Ⅰ: Getting started and building the basics </h1>

    <!--描述-->
    <span class="text-gray">
      The goal of stage is to understand basic concepts, framework and learning methods of the
      course and establish a solid theoretical foundation.
    </span>
  </div>

  <!--Phase2标题容器-->
  <div v-if="currentPhase === 2" class="flex flex-col gap-1 my-4">
    <!--标题-->
    <h1> Phase II: In-depth learning and skills development </h1>

    <!--描述-->
    <span class="text-gray">
      The goal of this stage is to deeply understand the core knowledge points, master relevant
      professional skills, and be able to carry out initial practical applications.
    </span>
  </div>

  <!--Phase3标题容器-->
  <div v-if="currentPhase === 3" class="flex flex-col gap-1 my-4">
    <!--标题-->
    <h1> Phase III: Comprehensive application and capacity improvement </h1>

    <!--描述-->
    <span class="text-gray">
      The goal is to comprehensively apply the knowledge and skills learned, improve the ability to
      solve complex problems, and prepare for future career development.
    </span>
  </div>

  <!-- 操作区 -->
  <div class="flex items-center justify-between my-4">
    <!--搜索-->
    <el-input v-model="searchSkill" placeholder="Search for Skill" clearable class="!w-240px">
      <template #suffix>
        <Icon icon="ep:search" />
      </template>
    </el-input>

    <div class="flex gap-2">
      <!-- 重新生成按钮 -->
      <el-tooltip
        content="Regenerate"
        placement="top"
      >
        <el-button
          @click="handleRegenerate(position.id, position.name)"
          type="primary"
          link
          size="small"
          class="!px-2 !py-1 !hover:bg-[#017b3d] !hover:text-white !rounded-full transition-all duration-300"
        >
          <Icon icon="ep:refresh" class="w-4 h-4" />
        </el-button>
      </el-tooltip>

      <!--增加学习内容表单弹窗-->
      <AddLeaningContent
        :phase="props.currentPhase"
        :position-id="props.positionId"
        :match-degree="props.matchDegree"
        @refresh-list="getList"
      />

      <!--发布按钮-->
      <el-button type="primary" @click="handlePublish" :loading="publishLoading">
        <Icon v-if="!publishLoading" icon="ep:position" />
        <span> Publish </span>
      </el-button>
    </div>
  </div>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="filteredList"
      :show-overflow-tooltip="true"
      :span-method="objectSpanMethod"
    >
      <!--技能名称-->
      <el-table-column label="Skill" align="center" prop="skill" min-width="200" />

      <!-- 课程名称 -->
      <el-table-column label="Content Title" align="center" prop="contentTitle" min-width="300">
        <template #default="scope">
          <div class="flex items-center justify-between w-full">
            <el-text class="flex-1 text-left truncate mr-2" :title="scope.row.contentTitle">
              {{ scope.row.contentTitle }}
            </el-text>
            <el-popover
              placement="right"
              trigger="hover"
              :width="'auto'"
              :min-width="200"
              popper-class="keyword-popover"
              v-if="scope.row.keyword"
            >
              <template #default>
                <div class="flex flex-wrap gap-1 max-w-[400px]">
                  <el-tag
                    size="small"
                    v-for="(keyword, index) in scope.row.keyword?.split(',')"
                    :key="index"
                  >
                    {{ keyword.trim() }}
                  </el-tag>
                </div>
              </template>
              <template #reference>
                <el-icon class="cursor-pointer text-gray-500 hover:text-blue-500 flex-shrink-0">
                  <InfoFilled />
                </el-icon>
              </template>
            </el-popover>
          </div>
        </template>
      </el-table-column>

      <!--分类-->
      <el-table-column label="Category" align="center" prop="type">
        <template #default="scope">
          <el-tag>
            {{
              getDictLabel(DICT_TYPE.LEARNING_ONBOARDING_MAP_CONTENT_TYPE, scope.row.bizType) || '-'
            }}
          </el-tag>
        </template>
      </el-table-column>

      <!--难度等级-->
      <el-table-column label="Level" align="center" prop="level">
        <template #default="scope">
          <el-tag type="info">
            {{ getDictLabel(DICT_TYPE.EDP_STUDY_PLAN_CONTENT_LEVEL, scope.row.level) || '-' }}
          </el-tag>
        </template>
      </el-table-column>

      <!--状态-->
      <el-table-column label="Status" align="center" prop="status">
        <template #default="scope">
          <el-tag
            :type="scope.row.status === LearningContentStatusEnum.PUBLISHED ? 'success' : 'info'"
          >
            {{
              scope.row.status && scope.row.status === LearningContentStatusEnum.PUBLISHED
                ? 'Published'
                : 'Draft'
            }}
          </el-tag>
        </template>
      </el-table-column>

      <!--匹配度-->
      <el-table-column label="Match Degree" align="center" min-width="120px">
        <template #default="{ row }"> {{ row.matchDegree }}% </template>
      </el-table-column>

      <!--操作-->
      <el-table-column label="Action" align="center" min-width="120px">
        <template #default="scope">
          <el-button link type="danger" @click="handleDelete(scope.row.contentId)">
            <Icon icon="ep:delete" />
            <p class="ml-1">Remove</p>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 岗位名称 -->
  <div v-if="props.positionName" class="my-4">
    <p class="text-gray"> Position: {{ props.positionName }} </p>
  </div>
</template>

<style scoped lang="scss">
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.keyword-popover) {
  max-width: 400px;
  min-width: 200px;
  width: auto !important;
}
</style>
