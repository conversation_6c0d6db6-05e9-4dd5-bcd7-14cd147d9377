<script setup lang="ts">
import { SkillTagsLibraryApi, SkillTagsLibraryVO } from '@/api/edp/skilltagslibrary'
import { SkillTagsLevel1, SkillTagsLevel2, BOC_REQUIRED_VALUE } from '@/enums/edp'

/** 技能标签库 表单 */
defineOptions({ name: 'SkillTagsLibraryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 保存原始枚举值，用于提交时转换回枚举
interface OriginalDataType {
  firstSkill?: number
  secondSkill?: number
}

const originalData = ref<OriginalDataType>({
  firstSkill: undefined,
  secondSkill: undefined
})

// 部门列表数据
interface DepartmentOption {
  value: number
  label: string
}
const departmentOptions = ref<DepartmentOption[]>([])

// 一级技能标签选项
const firstTagOptions = ref<DepartmentOption[]>([])

// 二级技能标签选项
const secondTagOptions = ref<DepartmentOption[]>([])

const formData = ref<SkillTagsLibraryVO>({
  id: undefined,
  deptId: undefined,
  deptName: '',
  firstSkill: undefined,
  secondSkill: undefined,
  thirdSkill: '',
  status: true
})

const formRules = reactive({
  deptId: [{ required: true, message: 'Department can not be empty', trigger: 'change' }],
  firstSkill: [{ required: true, message: 'Level 1 Skill can not be empty', trigger: 'change' }],
  secondSkill: [{ required: true, message: 'Level 2 Skill can not be empty', trigger: 'change' }],
  thirdSkill: [{ required: true, message: 'Level 3 Skill can not be empty', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

// 初始化标签选项
const initTagOptions = () => {
  // 获取一级标签选项
  firstTagOptions.value = Object.keys(SkillTagsLevel1)
    .filter((key) => isNaN(Number(key))) // 只保留非数字的键
    .map((key) => ({
      label: key,
      value: SkillTagsLevel1[key as keyof typeof SkillTagsLevel1]
    }))

  // 初始化二级标签选项 - 默认显示全部
  updateSecondTagOptions()
}

// 一级标签变化时，更新二级标签选项
const handleFirstTagChange = () => {
  formData.value.secondSkill = undefined // 重置二级标签选择
  updateSecondTagOptions()
}

// 根据选择的一级标签，更新二级标签选项
const updateSecondTagOptions = () => {
  // 获取所有二级标签选项
  const allOptions = Object.keys(SkillTagsLevel2)
    .filter((key) => isNaN(Number(key)))
    .map((key) => ({
      label: key,
      value: SkillTagsLevel2[key as keyof typeof SkillTagsLevel2]
    }))

  // 根据选择的一级标签过滤二级标签选项
  switch (formData.value.firstSkill) {
    case 1: // Soft Skill
      // 显示选项 1-3
      secondTagOptions.value = allOptions.filter((opt) => opt.value >= 1 && opt.value <= 3)
      break
    case 2: // Hard Skill
      // 显示选项 4-5
      secondTagOptions.value = allOptions.filter((opt) => opt.value >= 4 && opt.value <= 5)
      break
    case 3: // HSE
      // 显示选项 6
      secondTagOptions.value = allOptions.filter((opt) => opt.value === 6)
      break
    default:
      // 默认不显示任何选项
      secondTagOptions.value = []
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const res = await SkillTagsLibraryApi.getSkillTagsLibraryList({
      pageNo: 1,
      pageSize: 100 // 获取足够多的记录来提取部门
    })
    // 提取不重复的部门数据
    const uniqueDepts = new Map()
    res.list.forEach((dept) => {
      if (dept.deptId && dept.positionName && !uniqueDepts.has(dept.deptId)) {
        uniqueDepts.set(dept.deptId, dept.positionName)
      }
    })

    // 转换为select选项格式
    departmentOptions.value = Array.from(uniqueDepts.entries()).map(([deptId, deptName]) => ({
      value: deptId,
      label: deptName
    }))
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 部门选择变更时同步deptName
const handleDeptChange = (deptId) => {
  const selectedDept = departmentOptions.value.find((dept) => dept.value === deptId)
  if (selectedDept) {
    formData.value.deptName = selectedDept.label
  }
}

/** 打开弹窗 */
const open = async (type: string, rowData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 初始化标签选项
  initTagOptions()

  // 获取部门列表
  await fetchDepartments()

  // 修改时，设置数据
  if (rowData) {
    formLoading.value = true
    try {
      if (typeof rowData === 'number') {
        // 兼容旧的传递方式，如果是数字ID则通过API获取
        const data = await SkillTagsLibraryApi.getSkillTagsLibrary(rowData)

        // 设置表单数据，直接使用枚举值
        formData.value = {
          id: data.id,
          deptId: data.deptId,
          deptName: data.positionName,
          firstSkill: data.firstSkill,
          secondSkill: data.secondSkill,
          thirdSkill: data.thirdSkill,
          status: data.status
        }

        // 更新二级标签选项
        updateSecondTagOptions()
      } else {
        // 直接使用传递的行数据
        formData.value = {
          id: rowData.id,
          deptId: rowData.deptId,
          deptName: rowData.positionName,
          firstSkill: rowData.firstSkill,
          secondSkill: rowData.secondSkill,
          thirdSkill: rowData.thirdSkill,
          status: rowData.status
        }

        // 更新二级标签选项
        updateSecondTagOptions()
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    // 将表单数据转换为枚举值
    let firstSkillValue: number = Number(formData.value.firstSkill)
    let secondSkillValue: number = Number(formData.value.secondSkill)

    // 如果firstSkill是枚举名称字符串，则转换为枚举值
    if (typeof formData.value.firstSkill === 'string') {
      // 查找匹配的枚举值
      for (const [key, value] of Object.entries(SkillTagsLevel1)) {
        // 特殊处理"Boc Required"的情况
        if (formData.value.firstSkill === 'Boc Required') {
          firstSkillValue = BOC_REQUIRED_VALUE
          break
        }

        if (isNaN(Number(key)) && key === formData.value.firstSkill) {
          firstSkillValue = Number(value)
          break
        }
      }
    }

    // 如果secondSkill是枚举名称字符串，则转换为枚举值
    if (typeof formData.value.secondSkill === 'string') {
      // 查找匹配的枚举值
      for (const [key, value] of Object.entries(SkillTagsLevel2)) {
        if (isNaN(Number(key)) && key === formData.value.secondSkill) {
          secondSkillValue = Number(value)
          break
        }
      }
    }

    // 准备提交数据
    const submitData: SkillTagsLibraryVO = {
      deptId: formData.value.deptId as number,
      deptName: formData.value.deptName,
      firstSkill: firstSkillValue,
      secondSkill: secondSkillValue,
      thirdSkill: formData.value.thirdSkill,
      status: formData.value.status
    }

    // 如果是修改操作，需要附加ID
    if (formType.value === 'update' && formData.value.id) {
      submitData.id = formData.value.id
    }

    if (formType.value === 'create') {
      await SkillTagsLibraryApi.createSkillTagsLibrary(submitData)
      message.success(t('common.createSuccess'))
    } else {
      await SkillTagsLibraryApi.updateSkillTagsLibrary(submitData)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    deptId: undefined,
    deptName: '',
    firstSkill: undefined,
    secondSkill: undefined,
    thirdSkill: '',
    status: true
  }
  originalData.value = {
    firstSkill: undefined,
    secondSkill: undefined
  }
  formRef.value?.resetFields()
}
</script>

<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" class="!w-[50%]">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <!-- 部门选择 -->
      <el-form-item label="Department" prop="deptId">
        <el-select
          v-model="formData.deptId"
          placeholder="Select department"
          class="w-full"
          @change="handleDeptChange"
        >
          <el-option
            v-for="item in departmentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 一级技能标签 -->
      <el-form-item label="Level 1 Skill" prop="firstSkill">
        <el-select
          v-model="formData.firstSkill"
          placeholder="Select Level 1 Skill"
          class="w-full"
          @change="handleFirstTagChange"
        >
          <el-option
            v-for="item in firstTagOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 二级技能标签 -->
      <el-form-item label="Level 2 Skill" prop="secondSkill">
        <el-select
          v-model="formData.secondSkill"
          placeholder="Select Level 2 Skill"
          class="w-full"
          :disabled="!formData.firstSkill"
        >
          <el-option
            v-for="item in secondTagOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 三级技能标签 -->
      <el-form-item label="Level 3 Skill" prop="thirdSkill">
        <el-input
          v-model="formData.thirdSkill"
          placeholder="Please input the name of third skill"
        />
      </el-form-item>
    </el-form>

    <!-- 底部按钮组 -->
    <template #footer>
      <!-- 取消按钮 -->
      <el-button @click="dialogVisible = false"> Cancel </el-button>
      <!-- 确认按钮 -->
      <el-button @click="submitForm" type="primary" :disabled="formLoading"> Confirm </el-button>
    </template>
  </Dialog>
</template>