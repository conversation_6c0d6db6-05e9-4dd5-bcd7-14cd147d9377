<script setup lang="ts">
import { useRouter } from 'vue-router'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import { ElMessage, ElTree } from 'element-plus'
import { OrgType } from '@/enums/OrgType'
import { getSection, listSection, sectTreeSelect } from '@/api/system/section'
import { listPosition } from '@/api/system/post'
import { handlePhaseTree } from '@/utils/tree'
import { useI18n } from 'vue-i18n'
import { JobDescApi } from '@/api/edp/jobDesc'

defineOptions({
  name: 'ProMatrix'
})

const { t } = useI18n()
const router = useRouter()
const message = useMessage() // 消息弹窗
const positionList = ref<any[]>([])
const sectionList = ref<any[]>([])
const positionOptions = ref<any[]>([])
const open = ref(false)
const loading = ref(true)
const ids = ref<number[]>([])
const title = ref('')
const postTotal = ref(0)
const sectionRef = ref()
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {},
  queryParams: {
    postCode: undefined,
    name: undefined,
    status: undefined,
    parentId: undefined,
    deptId: undefined,
    deptName: undefined
  },
  rules: {
    name: [{ required: true, message: t('sys.post.positionNameRule'), trigger: 'blur' }],
    postCode: [{ required: false, message: t('sys.post.positionCodeRule'), trigger: 'blur' }],
    orderNum: [{ required: true, message: t('sys.post.orderNumRule'), trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)
const deptName = ref('')
const deptOptions = ref<any[]>([])
const postTreeRef = ref<InstanceType<typeof ElTree>>()
/** 当前选中的部门信息 */
const selDept = ref()
const defaultExpand = ref()
const isExpandAll = ref(false)
const loadingForm = ref(false)

/** 左侧-查询公司部门下拉树结构 */
const getDeptTree = async () => {
  const data = await sectTreeSelect({ type: 0 })
  console.log('部门树数据:', data)
  deptOptions.value = data
  selDept.value = deptOptions.value[0]
  queryParams.value.compId = deptOptions.value[0].id
  // 展开默认选中节点
  const firstOption = deptOptions.value[0]
  defaultExpand.value = [
    deptOptions.value[0].virtualId,
    firstOption.level === OrgType.Company && firstOption.children
      ? deptOptions.value[0].children[0].virtualId
      : undefined
  ]
  await nextTick(() => {
    postTreeRef.value?.setCurrentNode(deptOptions.value[0])
  })
  // 调用handleNodeClick传递第一个节点，模拟点击部门节点
  await handleNodeClick(deptOptions.value[0])
}
/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  postTreeRef.value!.filter(val)
})
/** 查询Position列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await listPosition(queryParams.value)
    positionList.value = handlePhaseTree(data, 'postId')
    postTotal.value = data.length
  } finally {
    loading.value = false
  }
}

// 节点点击事件
const handleNodeClick = async (node: any) => {
  selDept.value = node
  if (node.level === OrgType.Company) {
    queryParams.value.compId = node.id
    queryParams.value.deptId = null
    queryParams.value.sectId = null
    queryParams.value.sectName = null
  }
  if (node.level === OrgType.Department) {
    queryParams.value.positionName = node.label
    queryParams.value.deptId = node.id
    queryParams.value.compId = null
    queryParams.value.sectId = null
    queryParams.value.sectName = null
  }
  if (node.level === OrgType.Section) {
    queryParams.value.sectName = node.label
    queryParams.value.sectId = node.id
    queryParams.value.deptId = null
    queryParams.value.positionName = null
    queryParams.value.compId = null
    const data = await getSection(node.id as number)
    queryParams.value.deptId = data.department.deptId
    queryParams.value.positionName = data.department.positionName
  }
  await getList()
}

// JD生成
const handleJDGenerate = (postId: number, positionName: string) => {
  console.log('Generating JD for position:', positionName)

  // 检查岗位名称是否为空
  if (!positionName) {
    console.warn('Position name is empty!')
    return
  }

  router.push({
    path: '/edp/chatbot',
    query: {
      postId,
      positionName,
      deptName: selDept.value?.label || '',
      createChat: 'true'
    }
  })
}

// 技能生成
const handleSkillGenerate = async (positionId: number, positionName: string) => {
  try {
    console.log('点击的岗位', positionId)
    if (!positionId) return

    const params = {
      pageNo: 1,
      pageSize: 10,
      positionId: positionId
    }

    const res = await JobDescApi.getJDVersionList(params)
    console.log('该岗位JD版本列表:', res)

    // 判断是否有JD版本
    const hasJDVersion = res.list.some((item) => item.id)
    if (!hasJDVersion) {
      ElMessage.warning('Please generate the job description for this position first.')
      return
    }

    // 判断岗位是否有已发布JD版本
    const hasPublishedVersion = res.list.some((item) => item.status === 2)
    if (!hasPublishedVersion) {
      ElMessage.warning('Please generate the job description for this position first.')
      return
    }

    await router.push({
      path: '/edp/skill-generate',
      query: {
        jdId: res.list[0].id,
        positionId,
        positionName,
        deptName: selDept.value?.label || ''
      }
    })
  } catch (error) {
    console.error('Skill Generate跳转失败', error)
  }
}

/** 初始化 */
onMounted(() => {
  getDeptTree()
})
</script>

<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <Pane :size="26" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <div class="head-container">
            <el-input
              v-model="deptName"
              :placeholder="t('sys.user.deptNamePH')"
              clearable
              class="mb-[20px]"
            >
              <template #prefix>
                <Icon class="mr-5px" icon="ep:search" />
              </template>
            </el-input>
          </div>
          <div class="head-container">
            <el-tree
              ref="postTreeRef"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :default-expanded-keys="defaultExpand"
              :filter-node-method="filterNode"
              node-key="virtualId"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <div class="flex justify-between">
                  <div class="me-2.5">
                    <el-tag
                      v-if="node.data.level === OrgType.Company"
                      :title="node.data.shortName"
                      :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0'
                      }"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Company' }}
                    </el-tag>
                    <el-tag
                      v-else-if="node.data.level === OrgType.Department"
                      :title="node.data.shortName"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Dept' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Section" type="info">
                      {{ 'Section' }}
                    </el-tag>
                  </div>
                  <span :title="node.label" class="whitespace-normal line-clamp-1 break-all">
                    {{ node.label }}</span
                  >
                </div>
              </template>
            </el-tree>
          </div>
        </el-scrollbar>
      </Pane>
      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <div class="pt-3 flex items-center">
            <div class="p-2">
              <el-tag
                v-if="selDept?.level === OrgType.Company"
                :style="{
                  '--el-tag-text-color': '#630EB8',
                  '--el-tag-bg-color': '#F3F1FF',
                  '--el-tag-border-color': '#D3CEF0'
                }"
              >
                {{ t('global.company') }}
              </el-tag>
              <el-tag v-if="selDept?.level === OrgType.Department">
                {{ t('sys.user.department') }}
              </el-tag>
              <el-tag v-else-if="selDept?.level === OrgType.Section" type="info">
                {{ t('sys.user.section') }}
              </el-tag>
              <span class="ms-2.5">{{ selDept?.label }}</span>
            </div>
          </div>
          <OrgTotalBar :number="postTotal" :text="t('sys.post.totalTip')" />

          <!--JD列表-->
          <ContentWrap>
            <el-table
              v-loading="loading"
              :data="positionList"
              row-key="postId"
              :default-expand-all="isExpandAll"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <!--岗位名称-->
              <el-table-column :label="t('sys.post.postName')" prop="name" min-width="360" />

              <!--状态-->
              <!--<el-table-column prop="status" :label="t('sys.company.status')" width="200">-->
              <!--  <template #default="{ row }">-->
              <!--    <dict-tag :bizType="DICT_TYPE.SYSTEM_NORMAL_DISABLE" :value="row.status" />-->
              <!--  </template>-->
              <!--</el-table-colaumn>-->

              <!--数据来源-->
              <el-table-column
                prop="dataSource"
                align="center"
                :label="t('sys.company.dataSource')"
                width="110"
              />

              <!--创建者-->
              <el-table-column
                prop="createBy"
                align="center"
                :label="t('sys.company.creator')"
                width="110"
              />

              <!--操作-->
              <el-table-column
                :label="t('global.action')"
                min-width="180"
                align="center"
                class-name="small-padding fixed-width"
                fixed="right"
              >
                <template #default="scope">
                  <!--预览-->
                  <!--<el-button link bizType="primary"> Preview </el-button>-->

                  <!--JD生成-->
                  <el-button
                    link
                    type="primary"
                    @click="handleJDGenerate(scope.row.id, scope.row.name)"
                  >
                    JD Generate
                  </el-button>

                  <!--技能生成-->
                  <el-button
                    link
                    type="primary"
                    @click="handleSkillGenerate(scope.row.id, scope.row.name)"
                  >
                    Skill Generate
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </ContentWrap>
        </el-scrollbar>
      </Pane>
    </Splitpanes>
  </div>
</template>