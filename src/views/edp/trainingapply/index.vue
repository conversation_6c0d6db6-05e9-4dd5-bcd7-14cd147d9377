<script setup lang="ts">
import download from '@/utils/download'
import HandleApprove from './HandleApprove.vue'
import ViewDetail from './ViewDetail.vue'
import {
  TrainingApplyApi,
  TrainingApplyPageReqVO,
  TrainingNeedStatusEnum
} from '@/api/edp/trainingapply'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { useUserStore } from '@/store/modules/user'

/** 培训申请 列表 */
defineOptions({ name: 'TrainingApply' })

/** ----- INTERFACE ----- */
interface ContentItem {
  id: number
  applyId: number
  skillName: string
  trainingType: number
  contentCatalog: string
  status: number
}

interface ApplyItem {
  id: number
  title: string
  content?: ContentItem[]
  status: number
  approveReason: string | null
  applicantName?: string
  createTime?: string | number
}

interface ProcessedTableItem {
  id: number
  title: string
  status: number
  approveReason: string | null
  applicantName?: string
  createTime?: string | number
  skillName: string
  trainingType?: number
  contentCatalog: string
  _titleIndex: number
  _titleTotal: number
}

interface SpanInfo {
  position: number
  rowspan: number
}

/** ----- SETUP -----*/
const userStore = useUserStore() // 用户信息
const { t } = useI18n() // 国际化
const applyList = ref<ApplyItem[]>([]) // 培训申请列表
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const queryParams = reactive<TrainingApplyPageReqVO>({
  pageNo: 1,
  pageSize: 10,
  title: '',
  statusList: undefined,
  trainingType: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const spanData = ref<Record<string, SpanInfo>>({}) // 存储每行数据的合并信息
const applyStatus = ref([
  {
    value: TrainingNeedStatusEnum.SUBMITTED,
    label: 'Submitted'
  },
  {
    value: TrainingNeedStatusEnum.APPROVED,
    label: 'Approved'
  },
  {
    value: TrainingNeedStatusEnum.REJECTED,
    label: 'Rejected'
  }
])

// 处理后的表格数据
const processedTableData = computed<ProcessedTableItem[]>(() => {
  const data: ProcessedTableItem[] = []

  // 将嵌套数据展开为平铺结构
  applyList.value.forEach((apply) => {
    if (apply.content && apply.content.length > 0) {
      apply.content.forEach((item, index) => {
        data.push({
          id: apply.id,
          title: apply.title,
          status: apply.status,
          approveReason: apply.approveReason,
          applicantName: apply.applicantName,
          createTime: apply.createTime,
          skillName: item.skillName,
          trainingType: item.trainingType,
          contentCatalog: item.contentCatalog,
          _titleIndex: index, // 记录在同一个title下的索引位置
          _titleTotal: apply.content?.length || 1 // 记录同一个title下的总数量
        })
      })
    } else {
      // 没有content的情况，仍然添加一行
      data.push({
        id: apply.id,
        title: apply.title,
        status: apply.status,
        approveReason: apply.approveReason,
        applicantName: apply.applicantName,
        createTime: apply.createTime,
        skillName: '',
        trainingType: undefined,
        contentCatalog: '',
        _titleIndex: 0,
        _titleTotal: 1
      })
    }
  })

  // 计算每个title的合并信息
  const spanInfo: Record<string, SpanInfo> = {}
  let position = 0

  data.forEach((item) => {
    if (!spanInfo[item.title]) {
      spanInfo[item.title] = {
        position,
        rowspan: item._titleTotal
      }
    }
    position += 1
  })

  spanData.value = spanInfo
  return data
})

/** 表格合并单元格方法 */
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 获取当前行所属的title信息
  const titleInfo = Object.values(spanData.value).find(
    (info) => rowIndex >= info.position && rowIndex < info.position + info.rowspan
  )

  if (!titleInfo) {
    return {
      rowspan: 1,
      colspan: 1
    }
  }

  // 这些列需要合并：标题(0)、申请人(3)、创建时间(4)、状态(5)、操作(6)
  // 这些列不需要合并：技能(1)、培训类型(2)
  const columnsToMerge = [0, 3, 4, 5, 6]

  if (columnsToMerge.includes(columnIndex)) {
    if (rowIndex === titleInfo.position) {
      return {
        rowspan: titleInfo.rowspan,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }

  // 其他列不合并
  return {
    rowspan: 1,
    colspan: 1
  }
}

/** 格式化日期为 mm-dd-yyyy hh:mm */
const formatDate = (timestamp: number | string) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const year = date.getFullYear()
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${month}-${day}-${year} ${hours}:${minutes}`
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await TrainingApplyApi.getTrainingApplyPage(queryParams)
    applyList.value = res.list || []
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  // 重置所有筛选参数
  queryParams.title = ''
  queryParams.statusList = undefined
  queryParams.trainingType = undefined
  handleQuery()
}

const handleExport = async () => {
  try {
    // 调用API
    const res = await TrainingApplyApi.exportTrainingApply(queryParams)
    console.log('导出培训申请成功😊:', res)

    // 获取当前用户信息
    const user = userStore.getUser
    const userName = user.nickname || 'export'

    // 下载Excel
    download.excel(res, `TrainingNeed_${userName}.xlsx`)
  } catch (error) {
    console.log('导出培训申请失败😫:', error)
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<template>
  <ContentWrap>
    <!-- 工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!--状态-->
      <el-form-item prop="status">
        <el-select
          v-model="queryParams.statusList"
          placeholder="Status"
          clearable
          class="!w-240px"
          @change="handleQuery"
        >
          <el-option
            v-for="item in applyStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!--标题搜索框-->
      <el-form-item prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="Search for title"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <!--按钮组-->
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> Search
        </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" /> Reset </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading">
          <Icon icon="ep:download" class="mr-5px" />
          <span> Export </span>
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="processedTableData"
      :show-overflow-tooltip="true"
      :span-method="objectSpanMethod"
    >
      <!--标题-->
      <el-table-column label="Title" align="center" prop="title" min-width="120" />

      <!--技能-->
      <el-table-column label="Skill" align="center" min-width="120">
        <template #default="scope">
          {{ scope.row.skillName }}
        </template>
      </el-table-column>

      <!--培训类型-->
      <el-table-column label="Training Type" align="center" min-width="120">
        <template #default="scope">
          <el-tag>
            {{ getDictLabel(DICT_TYPE.EDP_TRAINING_TYPE, scope.row.trainingType) || '-' }}
          </el-tag>
        </template>
      </el-table-column>

      <!--申请人-->
      <el-table-column label="Applicant" align="center" prop="applicantName" min-width="120" />

      <!--创建时间-->
      <el-table-column label="Submit Time" align="center" min-width="120">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>

      <!--状态-->
      <el-table-column label="Status" align="center" width="120">
        <template #default="scope">
          <el-tag>
            {{ getDictLabel(DICT_TYPE.EDP_TRAINING_NEED_STATUS, scope.row.status) || '-' }}
          </el-tag>
        </template>
      </el-table-column>

      <!--操作列-->
      <el-table-column fixed="right" label="Actions" align="center" width="200">
        <template #default="scope">
          <!-- 已处理状态：显示查看详情 -->
          <ViewDetail v-if="scope.row.status === TrainingNeedStatusEnum.APPROVED || scope.row.status === TrainingNeedStatusEnum.REJECTED" :id="scope.row.id" />

          <!-- 待审批状态：显示审批按钮 -->
          <template v-if="scope.row.status === TrainingNeedStatusEnum.SUBMITTED">
            <!-- 审批处理 - 通过 -->
            <HandleApprove :id="scope.row.id" action="approve" @success="getList">
              <template #default="{ openDialog }">
                <el-button
                  link
                  type="primary"
                  @click="openDialog"
                >
                  <Icon icon="ep:check" class="mr-1"/>
                  <p>Approve</p>
                </el-button>
              </template>
            </HandleApprove>

            <!-- 审批处理 - 拒绝 -->
            <HandleApprove :id="scope.row.id" action="reject" @success="getList">
              <template #default="{ openDialog }">
                <el-button
                  link
                  type="danger"
                  @click="openDialog"
                >
                  <Icon icon="ep:close" class="mr-1" />
                  <p>Reject</p>
                </el-button>
              </template>
            </HandleApprove>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>
