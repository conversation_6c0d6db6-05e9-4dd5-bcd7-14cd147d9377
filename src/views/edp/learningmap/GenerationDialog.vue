<script lang="ts" setup>
import { ref } from 'vue'
import { positionLearningMapApi } from '@/api/edp/learningmap'
import { LearningContentSnapshotApi, regenrateByPhase } from '@/api/edp/learningcontentsnapshot'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

/** ----- SETUP ----- */
const props = defineProps<{
  dialogVisible?: boolean
  phase?: number
  positionId?: number
  positionName?: string
}>() // 接收岗位ID
const router = useRouter()
const currentPositionId = ref<number>() // 岗位ID内部变量
const currentPositionName = ref<string>('') // 岗位名称内部变量
const dialogVisible = ref<boolean>(props.dialogVisible || false) // 对话框控制器
const activeStep = ref<number>(1) // 当前步骤
const matchDegree = ref<number | undefined>(undefined) // 匹配度选择

// 生成进度列表
const progressList = ref([
  {
    id: 1,
    icon: 'ep:loading',
    value: 'Smart matching Online Courses...',
    successValue: 'Smart matching Online Course success!',
    status: 'pending' // 三种状态：pending, processing, done
  },
  {
    id: 2,
    icon: 'ep:loading',
    value: 'Smart matching MLC Training Course',
    successValue: 'Smart matching MLC Training success!',
    status: 'pending'
  },
  {
    id: 3,
    icon: 'ep:loading',
    value: 'Smart matching Knowledge Docs',
    successValue: 'Smart matching Knowledge Docs success!',
    status: 'pending'
  }
])

// 当前正在处理的进度索引
const currentProgressIndex = ref(-1)

// 存储定时器ID，用于清理
const timers = ref<NodeJS.Timeout[]>([])

/** ----- METHODS ----- */
/** 下一步 */
const next = () => {
  if (activeStep.value++ > 2) activeStep.value = 1
}

/** 处理初始化岗位学习地图 || 生成功能 */
const handleInit = async () => {
  // 逻辑校验
  if (!currentPositionId.value) {
    // 岗位ID为空时结束
    console.error('岗位ID不能为空')
    return
  } else if (!matchDegree.value) {
    // 匹配度为空时结束
    console.error('匹配度不能为空')
    return
  }

  try {
    // 创建请求体
    const data = {
      positionId: currentPositionId.value,
      matchDegree: matchDegree.value
    }

    // 如果有phase参数则传递该参数
    if (props.phase) {
      data.phase = props.phase

      // 调用重新生成API
      await LearningContentSnapshotApi.regenrateByPhase(data)
    } else {
      // 调用API进行岗位学习地图初始化
      await positionLearningMapApi.initLearningMap(data)
    }

    next() // 执行下一步

    // 重置进度状态
    progressList.value.forEach((item) => {
      item.status = 'pending'
      item.icon = 'ep:loading'
    })
    currentProgressIndex.value = -1

    // 开始生成进度展示
    handleGenerate()
  } catch (error) {
    console.error('岗位学习地图初始化错误:', error)
    dialogVisible.value = false
    ElMessage.error('This position has no generated skills')
  }
}

/** 处理生成进度展示 */
const handleGenerate = () => {
  // 如果已经显示完所有进度，跳转到下一步
  if (currentProgressIndex.value >= progressList.value.length - 1) {
    const timer = setTimeout(() => {
      next() // 跳转到step 3
    }, 1000)
    timers.value.push(timer)
    return
  }

  // 更新索引到下一个进度
  currentProgressIndex.value++

  // 设置当前进度为处理中
  const currentProgress = progressList.value[currentProgressIndex.value]
  currentProgress.status = 'processing'

  // 5秒后将当前进度设为完成，并开始下一个进度
  const timer = setTimeout(() => {
    // 更新当前进度为完成
    currentProgress.status = 'done'
    currentProgress.icon = 'ep:check'

    // 继续处理下一个进度
    handleGenerate()
  }, 5000)
  timers.value.push(timer)
}

/** 清理所有定时器 */
const clearAllTimers = () => {
  timers.value.forEach((timer) => {
    clearTimeout(timer)
  })
  timers.value = []
}

/** 关闭对话框 */
const handleCancel = () => {
  // 清理所有定时器
  clearAllTimers()

  dialogVisible.value = false
  activeStep.value = 1 // 重置步骤
  matchDegree.value = undefined // 重置匹配度

  // 重置进度索引
  currentProgressIndex.value = -1

  // 重置进度列表状态
  progressList.value.forEach((item) => {
    item.status = 'pending'
    item.icon = 'ep:loading'
  })
}

/** 处理导航到学习内容快照 */
function toLearningContent() {
  dialogVisible.value = false
  if (currentPositionId.value) {
    router.push({
      path: `/edp/learning-content`,
      query: {
        positionId: currentPositionId.value,
        positionName: currentPositionName.value,
        matchDegree: matchDegree.value
      }
    })
  }
}

/** 打开弹窗 */
const open = (positionId: number, positionName: string) => {
  console.log('Opening dialog for position:', positionName, 'ID:', positionId)

  // 清理所有定时器
  clearAllTimers()

  currentPositionId.value = positionId
  currentPositionName.value = positionName
  dialogVisible.value = true

  // 重置状态
  activeStep.value = 1
  matchDegree.value = undefined
  currentProgressIndex.value = -1

  // 重置进度列表状态
  progressList.value.forEach((item) => {
    item.status = 'pending'
    item.icon = 'ep:loading'
  })
}

// 暴露方法给父组件
defineExpose({
  open
})

/** ON MOUNTED */
onMounted(() => {
  // 从props获取岗位ID
  currentPositionId.value = props.positionId

  // 初始化匹配度为undefined
  matchDegree.value = undefined
})
</script>

<template>
  <el-dialog v-model="dialogVisible" width="800" align-center class="!rounded-xl !p-6">
    <template #header>
      <div class="flex flex-col items-center justify-center text-center p-8 !pr-1 !pb-0">
        <span class="text-lg font-semibold text-black">
          {{ currentPositionName }} Map Generator
        </span>
        <span class="text-gray-500 mt-2">
          AI will automatically generate a job learning map<br />base on your company's job titles
          and skills.
        </span>
      </div>
    </template>

    <template #default>
      <el-steps
        class="max-w-[720px] mx-8 my-8"
        direction="vertical"
        :active="activeStep"
        finish-status="success"
        align-center
      >
        <!--Step1: 匹配度选择-->
        <el-step title="Step 1">
          <template #description>
            <div class="flex flex-col justify-between gap-2 mb-10">
              <span class="flex align-center justify-start w-full text-gray-500">
                Matching Range: {{ matchDegree ? matchDegree + '%' : 'Select the matching range' }}
              </span>

              <el-slider
                v-if="activeStep === 1"
                v-model="matchDegree"
                :step="1"
                :max="90"
                :min="70"
              />

              <!-- 匹配度卡片 -->
              <div class="flex gap-4 mt-4">
                <!-- Under Challenge 卡片 -->
                <el-card
                  :class="[
                    'flex-1 !rounded-lg transition-all duration-300',
                    matchDegree && matchDegree >= 70 && matchDegree < 80
                      ? '!border-[#017B3D] border-2 !shadow-[0_2px_8px_rgba(1,123,61,0.15)]'
                      : ''
                  ]"
                  shadow="never"
                >
                  <div class="text-center">
                    <div
                      :class="[
                        'text-sm font-medium mb-2 space-y-1',
                        matchDegree && matchDegree >= 70 && matchDegree < 80
                          ? 'text-[#017B3D]'
                          : 'text-gray-800'
                      ]"
                    >
                      <p>Broader Matching</p>
                      <p>70% - 79%</p>
                    </div>
                    <div class="text-xs text-gray-600"
                      >"Courses with a match degree greater than 70% will be add in learning map.
                      You will have a broader course list but less relevant to the position."</div
                    >
                  </div>
                </el-card>

                <!-- Optimal Match 卡片 -->
                <el-card
                  :class="[
                    'flex-1 !rounded-lg transition-all duration-300',
                    matchDegree && matchDegree >= 80 && matchDegree < 90
                      ? '!border-[#017B3D] border-2 !shadow-[0_2px_8px_rgba(1,123,61,0.15)]'
                      : ''
                  ]"
                  shadow="never"
                >
                  <div class="text-center">
                    <div
                      :class="[
                        'text-sm font-medium mb-2 space-y-1',
                        matchDegree && matchDegree >= 80 && matchDegree < 90
                          ? 'text-[#017B3D]'
                          : 'text-gray-800'
                      ]"
                    >
                      <p>Balanced Matching</p>
                      <p>80% - 89%</p>
                    </div>
                    <div class="text-xs text-gray-600"
                      >"Courses with a match degree between 80%-89% will be recommended. You will
                      have course list balanced quantity and quality."</div
                    >
                  </div>
                </el-card>

                <!-- Highly Challenging 卡片 -->
                <el-card
                  :class="[
                    'flex-1 !rounded-lg transition-all duration-300',
                    matchDegree && matchDegree >= 90
                      ? '!border-[#017B3D] border-2 !shadow-[0_2px_8px_rgba(1,123,61,0.15)]'
                      : ''
                  ]"
                  shadow="never"
                >
                  <div class="text-center">
                    <div
                      :class="[
                        'text-sm font-medium mb-2 space-y-1',
                        matchDegree && matchDegree >= 90 ? 'text-[#017B3D]' : 'text-gray-800'
                      ]"
                    >
                      <p>Accurate Matching</p>
                      <p>≥ 90%</p>
                    </div>
                    <div class="text-xs text-gray-600"
                      >"Courses with a match degree greater than 90% will be recommended. You will
                      have a fewer but more relevant course list."</div
                    >
                  </div>
                </el-card>
              </div>
            </div>
          </template>
        </el-step>

        <!--Step2: 生成岗位学习地图进度-->
        <el-step title="Step 2">
          <template #description>
            <div class="text-gray-500 mb-5">
              <span>
                The {{ currentPositionName }} job map is being automatically generated. Please wait
                patiently.
              </span>
            </div>

            <div class="mb-10 flex flex-col gap-2">
              <div
                v-for="(progress, index) in progressList"
                :key="progress.id"
                class="flex items-center gap-2 py-2 rounded transition-all"
                :class="{
                  'opacity-50': progress.status === 'pending',
                  'text-black': progress.status === 'processing',
                  'text-[#017b3d]': progress.status === 'done'
                }"
                v-show="index <= currentProgressIndex"
              >
                <el-icon
                  class="text-xl"
                  :class="{ 'animate-spin': progress.status === 'processing' }"
                >
                  <!--加载Icon-->
                  <Icon v-if="progress.icon === 'ep:loading'" icon="ep:loading" />
                  <!--完成Icon-->
                  <Icon v-else-if="progress.icon === 'ep:check'" icon="ep:check" />
                </el-icon>
                <span>{{
                  progress.status === 'done' ? progress.successValue : progress.value
                }}</span>
              </div>
            </div>
          </template>
        </el-step>

        <!--Step3: 提示完成生成确认-->
        <el-step title="Step 3">
          <template #description>
            <div v-if="activeStep === 3" class="text-gray-500 mb-5">
              <span> Generate successfully, please click continue to view. </span>
            </div>
          </template>
        </el-step>
      </el-steps>
    </template>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">Cancel</el-button>
        <el-button v-if="activeStep === 1" type="primary" @click="handleInit"> Generate </el-button>
        <el-button
          v-if="activeStep != 1"
          type="primary"
          :disabled="activeStep === 2"
          @click="toLearningContent"
        >
          Continue
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-step__description) {
  padding-right: 1% !important;
}
</style>
