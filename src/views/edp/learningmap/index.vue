<script setup lang="ts">
import { useRouter } from 'vue-router'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import type { ElTree } from 'element-plus'
import { OrgType } from '@/enums/OrgType'
import { getSection, sectTreeSelect } from '@/api/system/section'
import { handlePhaseTree } from '@/utils/tree'
import { useI18n } from 'vue-i18n'
import GenerationDialog from '@/views/edp/learningmap/GenerationDialog.vue'
import { Position, positionLearningMapApi } from '@/api/edp/learningmap'

const { t } = useI18n()
const router = useRouter()
const message = useMessage() // 消息弹窗
const positionList = ref<Position[]>([])
const loading = ref(true)
const postTotal = ref(0)
const currentPage = ref(1)
const pageSize = ref(30) // 每页显示20个卡片
const generationDialogRef = ref() // GenerationDialog 引用
const data = reactive<{
  form: any
  queryParams: any
  rules: any
}>({
  form: {},
  queryParams: {
    postCode: undefined,
    name: undefined,
    status: undefined,
    parentId: undefined,
    deptId: undefined,
    deptName: undefined,
    pageNo: 1,
    pageSize: 30
  },
  rules: {
    name: [{ required: true, message: t('sys.post.positionNameRule'), trigger: 'blur' }],
    postCode: [{ required: false, message: t('sys.post.positionCodeRule'), trigger: 'blur' }],
    orderNum: [{ required: true, message: t('sys.post.orderNumRule'), trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)
const deptName = ref('')
const deptOptions = ref<any[]>([])
const postTreeRef = ref<InstanceType<typeof ElTree>>()
/** 当前选中的部门信息 */
const selDept = ref()
const defaultExpand = ref()

/** 左侧-查询公司部门下拉树结构 */
const getDeptTree = async () => {
  const data = await sectTreeSelect({ type: 0 })
  console.log('部门树数据:', data)
  deptOptions.value = data
  selDept.value = deptOptions.value[0]
  queryParams.value.compId = deptOptions.value[0].id
  // 展开默认选中节点
  const firstOption = deptOptions.value[0]
  defaultExpand.value = [
    deptOptions.value[0].virtualId,
    firstOption.level === OrgType.Company && firstOption.children
      ? deptOptions.value[0].children[0].virtualId
      : undefined
  ]
  await nextTick(() => {
    postTreeRef.value?.setCurrentNode(deptOptions.value[0])
  })

  // 调用handleNodeClick传递第一个节点，模拟点击部门节点
  await handleNodeClick(deptOptions.value[0])
}
/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  postTreeRef.value!.filter(val)
})
/** 查询Position列表 */
const getList = async () => {
  loading.value = true
  try {
    // 同步分页参数
    queryParams.value.pageNo = currentPage.value
    queryParams.value.pageSize = pageSize.value

    const data = await positionLearningMapApi.getPositionList(queryParams.value)

    // 如果返回的是分页数据结构
    if (data.list && data.total !== undefined) {
      positionList.value = data.list
      postTotal.value = data.total
    } else {
      // 如果返回的是数组，进行客户端分页
      const allData = handlePhaseTree(data, 'postId')
      postTotal.value = allData.length

      const startIndex = (currentPage.value - 1) * pageSize.value
      const endIndex = startIndex + pageSize.value
      positionList.value = allData.slice(startIndex, endIndex)
    }
  } finally {
    loading.value = false
  }
}

// 节点点击事件
const handleNodeClick = async (node: any) => {
  console.log('node:', node)
  selDept.value = node
  if (node.level === OrgType.Company) {
    queryParams.value.compId = node.id
    queryParams.value.deptId = null
    queryParams.value.sectId = null
    queryParams.value.sectName = null
  }
  if (node.level === OrgType.Department) {
    queryParams.value.positionName = node.label
    queryParams.value.deptId = node.id
    queryParams.value.compId = null
    queryParams.value.sectId = null
    queryParams.value.sectName = null
  }
  if (node.level === OrgType.Section) {
    queryParams.value.sectName = node.label
    queryParams.value.sectId = node.id
    queryParams.value.deptId = null
    queryParams.value.positionName = null
    queryParams.value.compId = null
    const data = await getSection(node.id as number)
    queryParams.value.deptId = data.department.id
    queryParams.value.positionName = data.department.name
  }
  await getList()
}

// JD生成
const handleJDGenerate = (id: number, positionName: string) => {
  console.log('Generating JD for position:', positionName)

  // 检查岗位名称是否为空
  if (!positionName) {
    console.warn('Position name is empty!')
    return
  }

  router.push({
    path: '/edp/chatbot',
    query: {
      id,
      positionName,
      deptName: selDept.value?.label || '',
      createChat: 'true'
    }
  })
}

// 处理Generate按钮点击，打开弹窗
const handleGenerate = (id: number, name: string, initialized: boolean, published: boolean) => {
  console.log('Opening generation dialog for:', name, 'initialized:', initialized)

  // 检查岗位名称是否为空
  if (!name) {
    console.warn('Position name is empty!')
    return
  }

  // 检查岗位是否已初始化
  if (initialized) {
    // 如果已初始化，显示确认对话框
    message
      .confirm(t('error.positionAlreadyGenerated'), t('common.reminder'))
      .then(() => {
        // 用户确认后，直接跳转到学习内容页面
        toLearningContent(id, name)
      })
      .catch(() => {
        // 用户取消，不做任何操作
        console.log('User cancelled navigation')
      })
    return
  }

  // 打开GenerationDialog弹窗
  generationDialogRef.value?.open(id, name)
}

// 跳转到学习内容页面
const toLearningContent = (positionId: number, positionName: string) => {
  router.push({
    path: `/edp/learning-content`,
    query: { positionId, positionName }
  })
}

// 处理重新生成
const handleRegenerate = (id: number, name: string) => {
  console.log('Regenerating learning map for:', name)
  // 直接打开生成对话框，重新生成学习地图
  generationDialogRef.value?.open(id, name)
}

/** 分页处理 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  getList()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getList()
}

/** 初始化 */
onMounted(() => {
  // 同步分页参数
  queryParams.value.pageNo = currentPage.value
  queryParams.value.pageSize = pageSize.value
  getDeptTree()
})
</script>

<template>
  <div class="app-main-height">
    <Splitpanes class="default-theme">
      <Pane :size="26" class="!bg-white">
        <el-scrollbar class="!app-main-height p-2.5" height="calc(100vh)">
          <div class="head-container">
            <el-input
              v-model="deptName"
              :placeholder="t('sys.user.deptNamePH')"
              clearable
              class="mb-[20px]"
            >
              <template #prefix>
                <Icon class="mr-5px" icon="ep:search" />
              </template>
            </el-input>
          </div>
          <div class="head-container">
            <el-tree
              ref="postTreeRef"
              :data="deptOptions"
              :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false"
              :default-expanded-keys="defaultExpand"
              :filter-node-method="filterNode"
              node-key="virtualId"
              highlight-current
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <div class="flex justify-between">
                  <div class="me-2.5">
                    <el-tag
                      v-if="node.data.level === OrgType.Company"
                      :title="node.data.shortName"
                      :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0'
                      }"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Company' }}
                    </el-tag>
                    <el-tag
                      v-else-if="node.data.level === OrgType.Department"
                      :title="node.data.shortName"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Dept' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Section" type="info">
                      {{ 'Section' }}
                    </el-tag>
                  </div>
                  <span :title="node.label" class="whitespace-normal line-clamp-1 break-all">
                    {{ node.label }}</span
                  >
                </div>
              </template>
            </el-tree>
          </div>
        </el-scrollbar>
      </Pane>

      <Pane class="!app-main-height !bg-white">
        <el-scrollbar class="app-main-height p-2.5" height="calc(100vh)">
          <div class="pt-3 flex items-center">
            <div class="p-2">
              <el-tag
                v-if="selDept?.level === OrgType.Company"
                :style="{
                  '--el-tag-text-color': '#630EB8',
                  '--el-tag-bg-color': '#F3F1FF',
                  '--el-tag-border-color': '#D3CEF0'
                }"
              >
                {{ t('global.company') }}
              </el-tag>
              <el-tag v-if="selDept?.level === OrgType.Department">
                {{ t('sys.user.department') }}
              </el-tag>
              <el-tag v-else-if="selDept?.level === OrgType.Section" type="info">
                {{ t('sys.user.section') }}
              </el-tag>
              <span class="ms-2.5">{{ selDept?.label }}</span>
            </div>
          </div>
          <OrgTotalBar :number="postTotal" :text="t('sys.post.totalTip')" />

          <!--JD卡片列表-->
          <ContentWrap v-loading="loading" class="min-h-[20vh]">
            <div
              class="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5"
            >
              <el-card
                v-for="position in positionList"
                :key="position.id"
                class="position-card"
                shadow="hover"
                :body-style="{
                  padding: '16px',
                  height: '120px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between'
                }"
              >
                <!-- 岗位名称 -->
                <div class="text-sm font-medium text-gray-900 line-clamp-2 mb-2">
                  {{ position.name }}
                </div>

                <!-- 底部信息和按钮 -->
                <div class="flex justify-between items-center w-full">
                  <!-- 左侧：skillSum和Regenerate按钮 -->
                  <div class="flex items-center gap-2">
                    <!-- skillSum信息 -->
                    <div class="flex items-center gap-1 text-xs text-gray-600">
                      <Icon icon="ep:collection" class="w-2 h-2" />
                      <span>{{ position.skillSum || 0 }}</span>
                    </div>

                    <!-- Regenerate按钮 -->
                    <el-tooltip
                      v-if="!position.published && position.initialized"
                      content="Regenerate"
                      placement="top"
                    >
                      <el-button
                        @click="handleRegenerate(position.id, position.name)"
                        type="primary"
                        link
                        size="small"
                        class="!p-1 !hover:bg-[#017b3d] !hover:text-white !rounded-full transition-all duration-300"
                      >
                        <Icon icon="ep:refresh" class="w-4 h-4" />
                      </el-button>
                    </el-tooltip>
                  </div>

                  <!-- 右侧：View或Generate按钮 -->
                  <el-button
                    v-if="position.published || position.initialized"
                    @click="toLearningContent(position.id, position.name)"
                    type="primary"
                    link
                    size="small"
                    class="group !px-2 !py-1 !hover:bg-[#017b3d] !hover:text-white !rounded-full transition-transform duration-300 ease-in-out space-x-2"
                  >
                    <span>View</span>
                    <Icon
                      icon="ep:right"
                      class="w-3 h-3 transition-transform duration-300 ease-in-out group-hover:translate-x-[2px]"
                    />
                  </el-button>

                  <el-button
                    v-else
                    @click="
                      handleGenerate(
                        position.id,
                        position.name,
                        position.initialized,
                        position.published
                      )
                    "
                    type="primary"
                    link
                    size="small"
                    class="group !px-2 !py-1 !hover:bg-[#017b3d] !hover:text-white !rounded-full transition-transform duration-300 ease-in-out space-x-2"
                  >
                    <span>Generate</span>
                    <Icon
                      icon="ep:right"
                      class="w-3 h-3 transition-transform duration-300 ease-in-out group-hover:translate-x-[2px]"
                    />
                  </el-button>
                </div>
              </el-card>
            </div>

            <!-- 空状态 -->
            <div v-if="!loading && positionList.length === 0" class="text-center py-12">
              <Icon icon="ep:document" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">{{ t('common.noData') }}</p>
            </div>

            <!-- 分页 -->
            <div v-if="postTotal > 0" class="flex justify-end mt-6">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 15, 20, 30, 50]"
                :total="postTotal"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                background
              />
            </div>
          </ContentWrap>
        </el-scrollbar>
      </Pane>
    </Splitpanes>

    <GenerationDialog ref="generationDialogRef" />
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.position-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.position-card:hover {
  transform: translateY(-2px);
}
</style>
