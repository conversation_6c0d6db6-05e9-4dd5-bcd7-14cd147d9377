<template>
  <el-button text class="!w-8" @click="dialogVisible = true" :disabled="!content || props.disabled">
    <Icon icon="ep:view" />
  </el-button>

  <el-dialog v-model="dialogVisible" title="Preview" align-center width="50%">
    <div class="markdown-preview" v-html="renderedContent" :dir="textDirection"></div>
    <template #footer>
      <div class="flex gap-3 justify-end">
        <el-button @click="dialogVisible = false"> Cancel </el-button>
        <!-- <el-button bizType="primary" @click="dialogVisible = false"> Download </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import MarkdownIt from 'markdown-it'
import markdownItSanitizer from 'markdown-it-sanitizer'
import { Languages } from '@/enums/edp'

const dialogVisible = ref(false)
const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  language: {
    type: Number,
    default: Languages.English
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// 创建Markdown解析器
const markdown = new MarkdownIt({
  html: false,
  linkify: true,
  typographer: true
}).use(markdownItSanitizer)

// 根据语言设置文本方向
const textDirection = computed(() => {
  return props.language === Languages.Arabic ? 'rtl' : 'ltr'
})

// 将内容转换为HTML - 如果已经是HTML则保持不变
const renderedContent = computed(() => {
  if (!props.content) return ''

  // 如果内容已经是HTML格式，直接返回
  if (props.content.startsWith('<') && props.content.includes('</')) {
    return props.content
  }

  // 否则作为Markdown渲染
  return markdown.render(props.content)
})
</script>

<style lang="scss" scoped>
.markdown-preview {
  max-height: 70vh;
  overflow-y: auto;
  padding: 1rem;
}

/* Markdown 样式 */
:deep(.markdown-preview) {
  line-height: 1.6;
  width: 100%;
  overflow-wrap: break-word;
}

:deep(.markdown-preview p) {
  margin: 0.5em 0;
}

:deep(.markdown-preview a) {
  color: #017b3d;
  text-decoration: none;
}

:deep(.markdown-preview code) {
  background-color: #f5f5f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

:deep(.markdown-preview pre) {
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
}

:deep(.markdown-preview ul, .markdown-preview ol) {
  padding-left: 2em;
  margin: 0.5em 0;
  box-sizing: border-box;
}

:deep(.markdown-preview ol) {
  list-style-position: inside;
}

:deep(.markdown-preview li) {
  margin-bottom: 0.3em;
  position: relative;
}

:deep(.markdown-preview blockquote) {
  border-left: 4px solid #ddd;
  padding-left: 1em;
  margin-left: 0;
  color: #666;
}

:deep(.markdown-preview h1),
:deep(.markdown-preview h2),
:deep(.markdown-preview h3),
:deep(.markdown-preview h4),
:deep(.markdown-preview h5),
:deep(.markdown-preview h6) {
  line-height: 1.1;
  font-weight: 600;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

:deep(.markdown-preview h1) {
  font-size: 2em;
}

:deep(.markdown-preview h2) {
  font-size: 1.5em;
}

:deep(.markdown-preview h3) {
  font-size: 1.25em;
}

:deep(.markdown-preview table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

:deep(.markdown-preview table td),
:deep(.markdown-preview table th) {
  border: 1px solid #ddd;
  padding: 0.5rem;
}

:deep(.markdown-preview table th) {
  background-color: #f5f5f5;
  font-weight: bold;
}

:deep(.markdown-preview img) {
  max-width: 100%;
  height: auto;
}

:deep(.markdown-preview strong) {
  font-weight: bold;
}

:deep(.markdown-preview em) {
  font-style: italic;
}
</style>