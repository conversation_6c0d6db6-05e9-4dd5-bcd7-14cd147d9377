<script setup lang="ts">
import { SuperUpload } from '@/components/SuperUpload'
import { FileInfo } from '@/components/SuperUpload/src/config'
import { checkFile } from '@/utils/fileUtil'
import { nanoid } from 'nanoid'
import { UploadResult } from '@/components/SuperUpload/src/config'
const fileInfoList = ref<FileInfo[]>([])
const emits = defineEmits(['uploadSuccess'])
// 限制的文件格式
const fileTypes = ['JPG', 'JPEG', 'PNG', 'txt', 'doc', 'docx']
const message = useMessage()
const uploading = ref(false)
const isHovered = ref(false)
const handlePrepared = (value: UploadResult) => {
  uploading.value = true
}
const handleError = () => {
  uploading.value = false
}
const handleComplete = (value: UploadResult) => {
  emits('uploadSuccess', value)
  setTimeout(() => {
    // 模拟上传时间
    uploading.value = false
  }, 5000)
}
const fileInputRef = ref<HTMLInputElement | null>(null)
// 手动触发文件选择框
const triggerFileSelect = () => {
  fileInputRef.value?.click()
}

const handleFileChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // 处理文件上传逻辑
    uploadFile(file)
  }
}
const uploadFile = async (file: File) => {
  try {
    // 检测文件
    const checkResult = await checkFile(file, fileTypes)
    // 检测文件大小
    if (file.size > 200 * 1024 * 1024) {
      message.notifyWarning('The file size exceeds the limit')
      return false
    }
    // 检查通过，创建上传文件信息
    if (checkResult.can) {
      const fileInfo: FileInfo = {
        uid: nanoid(10),
        file: file,
        bizType: checkResult.type,
        folderId: 0, // 例如目录id，默认根目录
        relativePath: '' // 例如相对路径，默认无相对路径
      }
      // 添加到上传文件列表
      fileInfoList.value.push(fileInfo)
    }
  } catch {
  } finally {
    uploading.value = false
  }
  return false
}
const handleDragOver = (event: DragEvent) => {
  // 当文件拖入区域时设置悬浮效果
  event.preventDefault()
  isHovered.value = true
}

const handleDragLeave = () => {
  // 当文件离开上传区域时恢复样式
  isHovered.value = false
}

const handleDrop = (event: DragEvent) => {
  // 文件被拖拽到上传区域时触发
  isHovered.value = false
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    const file = files[0]
    uploadFile(file)
  }
}
onMounted(() => {})
</script>

<template>
  <el-button
    class="!w-6 !h-6 !rounded-full !border-none !p-0 flex items-center justify-center"
    @click="triggerFileSelect"
    @dragover.prevent="handleDragOver"
    @dragleave="handleDragLeave"
    @drop.prevent="handleDrop"
  >
    <Icon icon="ep:link" :size="14" />
  </el-button>

  <!-- 添加缺失的文件输入元素 -->
  <input
    type="file"
    ref="fileInputRef"
    style="display: none"
    @change="handleFileChange"
    accept=".jpg,.jpeg,.png,.txt,.doc,.docx"
  />

  <SuperUpload
    v-for="fileInfo of fileInfoList"
    :key="fileInfo.uid"
    :file-info="fileInfo"
    @prepared="handlePrepared"
    @complete="handleComplete"
    @error="handleError"
  />
</template>