<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { ChatMessageApi, ChatReqVO, JDOptimizeApi } from '@/api/edp/chat'
import { useUserStore } from '@/store/modules/user'
import { useRoute, useRouter } from 'vue-router'
import avatarImg from '@/assets/imgs/avatar.gif'
import { generateUUID } from '@/utils'
import { Loading } from '@element-plus/icons-vue'
import MarkdownIt from 'markdown-it'
import markdownItSanitizer from 'markdown-it-sanitizer'
import TurnDownService from 'turndown'
import Editor from '@/views/edp/chatbot/Editor/index.vue'
import { ElMessage } from 'element-plus'
import { JobDescApi } from '@/api/edp/jobDesc'
import { FileTypes, JDPublishStatus, Languages, TaskTypes } from '@/enums/edp'
import { useClipboard } from '@vueuse/core'
import Preview from '@/views/edp/chatbot/previewDialog/index.vue'
import { BubbleMenu } from '@tiptap/vue-3'
import download from '@/utils/download'

/* SET UP */
const route = useRoute()
const router = useRouter()
const chatHistoryList = ref<any[]>([]) // 历史记录记录列表
const tempChatHistory = ref<any>(null) // 临时聊天记录
const userStore = useUserStore()
const avatar = computed(() => userStore.user.avatar || avatarImg)
const userName = computed(() => userStore.user.nickname ?? 'Admin')
const conversationId = ref<number>() // 会话ID
const conversationIdCopy = ref<number>()
const chatId = ref<number>() // 聊天ID
const welcome = ref<string>('') // 欢迎语
const positionIdCopy = ref(route.query.postId || null)
const positionNameCopy = ref((route.query.positionName as string) || '')
const positionId = ref(route.query.postId || undefined)
const positionName = ref((route.query.positionName as string) || '')
const deptName = ref((route.query.deptName as string) || '')
const shouldCreateChat = ref(route.query.createChat === 'true')
const JDVersionList = ref<any[]>([]) // JD版本列表
const currentJDVersion = ref<any>(null) // 当前JD版本
const currentVersionId = ref<number | null>(null) // 当前选中的版本ID
const isLoading = ref(false) // 加载状态
const sending = ref(false) // 发送状态
const optimizing = ref(false) // 优化状态
const conversationInProgress = ref(false) // 对话是否正在进行中。目前只有【发送】消息时，会更新为 true，避免切换对话、删除对话等操作
const optimizationInProgress = ref(false) // 优化是否正在进行中
const conversationInAbortController = ref<any>() // 对话进行中 abort 控制器(控制 stream 对话)
const optimizationInAbortController = ref<any>() // 优化进行中 abort 控制器(控制 stream 对话)
const hasData = ref(false) // 是否有数据标志
const chatScrollRef = ref<any>(null) // 聊天区域滚动引用
const typingQueue = ref<string[]>([]) // 打字队列
const isTyping = ref(false) // 是否正在打字
const taskType = ref<number>(TaskTypes.casualChat)
const pageNo = ref(1) // 当前页码
const pageSize = ref(40) // 每页大小
const isLoadingMore = ref(false) // 是否正在加载更多
const hasMoreHistory = ref(true) // 是否还有更多历史记录
const historyScrollbarRef = ref<any>(null) // 历史记录滚动区域引用
const targetLanguage = ref<Languages>(Languages.English) // 目标语言
const editorRef = ref(null) // 用于存储编辑器内容
const optimizeAnswer = ref<string>('') // 优化后的内容
const editorSelectedContent = ref<string>('') // 编辑器选中的内容
const previewEnglishContent = ref<string>('') // 英语预览内容
const previewChineseContent = ref<string>('') // 中文预览内容
const previewArabicContent = ref<string>('') // 阿拉伯语预览内容
const fileType = ref(FileTypes.pdf) // 添加文件类型，默认为Markdown
const JDEditTime = ref<string>('') // JD编辑时间
const { copy } = useClipboard()
const isCopied = ref(false)
const isPreviewLoading = ref(false) // 预览区域加载状态
const lastOptimizePrompt = ref<string>('') // Store last optimization prompt for regeneration
const activeCollapseNames = ref(['jdVersion']) // JD版本collapse默认打开

// 翻译语言列表
const targetLanguageList = [
  { id: Languages.English, label: 'English', value: 'en' },
  { id: Languages.Arabic, label: 'Arabic', value: 'ar' },
  { id: Languages.Chinese, label: 'Chinese', value: 'zh' }
]

// 创建Markdown解析器
const markdown = new MarkdownIt({
  html: false, // 禁用HTML标签
  linkify: true, // 自动转换URL为链接
  typographer: true // 美化排版
}).use(markdownItSanitizer) // 添加XSS防护插件

// 创建Markdown转换器
const turnDownService = new TurnDownService({
  headingStyle: 'atx', // 使用ATX风格的标题
  bulletListMarker: '-', // 使用短横线作为无序列表标记
  codeBlockStyle: 'fenced' // 使用围栏样式的代码块
})

// 对话消息历史
const messageHistory = ref([
  {
    answer: welcome.value,
    fullAnswer: welcome.value
  }
])

// 发送消息参数
const queryParams = reactive({
  positionId: '',
  positionName: '',
  conversationId: '',
  question: '',
  optimizeQuestion: '',
  contextAbove: '',
  content: '',
  contextBelow: '',
  source: 1,
  type: 1
})

// Add a new computed property after hasDraftVersion
// Check if the current JD version is a draft
const isCurrentVersionDraft = computed(() => {
  return currentJDVersion.value?.status === JDPublishStatus.draft
})

// Add a new computed property after isCurrentVersionDraft
// Check if the current JD version is published
const isCurrentVersionPublished = computed(() => {
  return currentJDVersion.value?.status === JDPublishStatus.publish
})

// 只显示当前岗位下的历史记录
const filteredChatHistoryList = computed(() =>
  chatHistoryList.value.filter((chat) => chat.positionId == positionId.value)
)

/* METHODS */
// 检查所有语言预览内容是否为空
const isAllPreviewEmpty = computed(() => {
  return !previewEnglishContent.value && !previewChineseContent.value && !previewArabicContent.value
})

// 检查是否有当前JD版本
const hasCurrentJDVersion = computed(() => {
  return !currentJDVersion.value
})

// 检查是否有草稿版本
const hasDraftVersion = computed(() => {
  return JDVersionList.value.some((version) => version.status === JDPublishStatus.draft)
})

// 清理编辑器和预览内容
const clearEditorAndPreview = () => {
  // 清空编辑器内容
  if (editorRef.value) {
    const editorInstance = editorRef.value as any
    if (editorInstance.editor) {
      editorInstance.editor.commands.setContent('')
    }
  }
  // 清空预览内容
  previewEnglishContent.value = ''
  previewChineseContent.value = ''
  previewArabicContent.value = ''
  // 清空编辑时间
  JDEditTime.value = ''
}

// 日期格式化函数
const formatDate = (timestamp) => {
  if (!timestamp) return ''

  const date = new Date(timestamp)

  // 补零函数
  const padZero = (num) => {
    return num.toString().padStart(2, '0')
  }

  const day = padZero(date.getDate())
  const month = padZero(date.getMonth() + 1) // 月份从0开始
  const year = date.getFullYear()

  return `${day}-${month}-${year}`
}

// 获取聊天会话记录列表
const getChatHistoryList = async (isLoadMore = false) => {
  if (
    isLoadingMore.value ||
    (!isLoadMore && isLoading.value) ||
    (isLoadMore && !hasMoreHistory.value)
  ) {
    return
  }

  isLoadMore ? (isLoadingMore.value = true) : (isLoading.value = true)

  try {
    const params = {
      pageNo: isLoadMore ? pageNo.value : 1,
      pageSize: pageSize.value,
      type: 1,
      source: 1,
      positionId: positionId?.value,
      conversationNo: 1
    }

    const res = await ChatMessageApi.getChatList(params)

    // 创建一个Map来存储每个conversationId对应的最新记录
    const uniqueChats = new Map()

    // 如果是加载更多，保留之前的结果
    if (isLoadMore && chatHistoryList.value.length > 0) {
      chatHistoryList.value.forEach((chat) => {
        uniqueChats.set(chat.conversationId, chat)
      })
    }

    // 添加新加载的结果
    res.list.forEach((chat) => {
      uniqueChats.set(chat.conversationId, chat)
    })

    // 将获取到的聊天记录赋值给响应式变量
    chatHistoryList.value = Array.from(uniqueChats.values())

    // 检查是否还有更多数据
    hasMoreHistory.value = res.list.length >= pageSize.value

    // 如果是加载更多，则递增页码
    if (isLoadMore) {
      pageNo.value++
    } else {
      pageNo.value = 2 // 第一页加载完后，下一页从第2页开始
    }

    console.log('Chat List:', chatHistoryList.value)

    // 检查是否需要继续加载更多数据来填满容器
    nextTick(() => {
      checkAndLoadMoreIfNeeded()
    })
  } catch (error) {
    console.error('Failed to load chat history:', error)
  } finally {
    isLoadMore ? (isLoadingMore.value = false) : (isLoading.value = false)
  }
}

// 检查并在必要时加载更多历史记录以填满容器
const checkAndLoadMoreIfNeeded = () => {
  if (!hasMoreHistory.value || isLoadingMore.value) return

  if (!historyScrollbarRef.value) return

  const scrollbarWrap = historyScrollbarRef.value.$el.querySelector('.el-scrollbar__wrap')
  if (!scrollbarWrap) return

  const { scrollHeight, clientHeight } = scrollbarWrap

  // 如果内容高度小于或接近容器高度，且还有更多数据可加载，则自动加载更多
  if (scrollHeight <= clientHeight + 20) {
    getChatHistoryList(true)
  }
}

// 监听历史记录滚动
const handleHistoryScroll = () => {
  if (!historyScrollbarRef.value) return

  const scrollbarWrap = historyScrollbarRef.value.$el.querySelector('.el-scrollbar__wrap')
  if (!scrollbarWrap) return

  // 当滚动到接近底部时加载更多
  const { scrollTop, scrollHeight, clientHeight } = scrollbarWrap
  const threshold = 50 // 滚动到距离底部50px时触发加载

  if (scrollHeight - scrollTop - clientHeight < threshold) {
    getChatHistoryList(true)
  }
}

// 历史记录点击处理
const clickHistoryItem = async (chat: any) => {
  // 如果点击的不是占位 new chat，且当前存在占位 new chat，则移除占位项
  if (
    chat.conversationId !== 0 &&
    filteredChatHistoryList.value.some((c) => c.conversationId === 0)
  ) {
    const idx = chatHistoryList.value.findIndex((c) => c.conversationId === 0)
    if (idx !== -1) chatHistoryList.value.splice(idx, 1)
  }
  // 如果点击的是临时记录，不执行任何操作
  if (chat.conversationId === 0) {
    return
  }

  // 设置加载状态
  isPreviewLoading.value = true

  // 将目标语言初始化为英语
  targetLanguage.value = Languages.English

  conversationId.value = chat.conversationId
  positionId.value = chat.positionId // 更新positionId

  // 如果有岗位名称，则更新岗位名称
  if (chat.positionName) {
    positionName.value = chat.positionName
  }

  // 根据历史记录返回的会话类型更新taskType
  if (chat.type === 1) {
    taskType.value = TaskTypes.generateJD
  } else {
    taskType.value = TaskTypes.casualChat
  }

  await getMessageHistoryById(chat.conversationId)

  // 如果没有职位ID，无法获取JD版本
  if (!positionId.value) {
    isPreviewLoading.value = false
    return
  }

  try {
    await getJDVersion() // 获取新的JD版本列表

    // 确保在获取版本列表后预览区有内容
    if (JDVersionList.value.length > 0 && !previewEnglishContent.value) {
      await getVersionById(JDVersionList.value[0].id)
    }
  } finally {
    // 确保加载状态最终被关闭
    isPreviewLoading.value = false
  }
}

// 根据ID获取聊天记录
const getMessageHistoryById = async (conversationId: number) => {
  try {
    const params = {
      conversationId,
      pageNo: 1,
      pageSize: 20
    }
    const res = await ChatMessageApi.getChatHistory(params)

    // 保留欢迎消息并替换其余消息历史
    if (res.list && res.list.length > 0) {
      // 先添加欢迎消息
      messageHistory.value = [
        {
          answer: welcome.value,
          fullAnswer: welcome.value
        }
      ]
      // 再添加历史消息
      messageHistory.value = messageHistory.value.concat(res.list)
    } else {
      // 如果没有历史消息，只显示欢迎消息
      messageHistory.value = [
        {
          answer: welcome.value,
          fullAnswer: welcome.value
        }
      ]
    }

    console.log('Message History loaded:', messageHistory.value)
    scrollToBottom() // 自动滚动到底部
  } catch (error) {
    console.log('获取聊天记录失败', error)
  }
}

// 处理新会话创建
const handleInitialChat = () => {
  console.log('Handling initial chat. Query params:', route.query)
  console.log('Position name:', positionName.value, 'Should create chat:', shouldCreateChat.value)

  // 如果URL中指定了创建新会话并且有职位名称，则创建新会话
  if (positionName.value && shouldCreateChat.value) {
    // 创建会话
    createConv(positionId.value, positionName.value)
  }
}

// 创建会话
const createConv = async (positionId?: number, positionName?: string) => {
  // 如果已存在占位new chat且当前高亮就是它，则不操作
  if (
    filteredChatHistoryList.value.some((chat) => chat.conversationId === 0) &&
    conversationId.value === 0
  ) {
    return
  }

  console.log('开始创建会话')
  console.log('Position ID:', positionId)
  console.log('Position Name:', positionName)

  // 创建临时历史记录
  tempChatHistory.value = {
    conversationId: 0,
    title: 'New Chat',
    createTime: new Date().toISOString(),
    positionId: positionId,
    positionName: positionName
  }

  // 将临时记录添加到历史记录列表的开头
  chatHistoryList.value.unshift(tempChatHistory.value)

  // 立即高亮新建的临时记录
  conversationId.value = tempChatHistory.value.conversationId

  try {
    isLoading.value = true

    // 构造请求参数
    const params = {
      // title: title || 'New Chat',  // 标题优先级：传入参数 > 默认值
      positionName: positionName || ''
    }

    const res = await ChatMessageApi.createChat(params)
    conversationIdCopy.value = res.session_id // 保存会话ID
    console.log('res', res)
    welcome.value = res.answer // 保存欢迎语
    messageHistory.value = [
      {
        answer: welcome.value,
        fullAnswer: welcome.value
      }
    ] // 初始化消息历史

    console.log('Conversation created successfully', res)
    console.log('Conversation ID:', conversationId.value)
  } catch (error) {
    console.error('Error creating chat:', error)
    // 发生错误时移除临时记录
    if (tempChatHistory.value) {
      const index = chatHistoryList.value.findIndex(
        (chat) => chat.conversationId === tempChatHistory.value.conversationId
      )
      if (index !== -1) {
        chatHistoryList.value.splice(index, 1)
      }
    }
  } finally {
    isLoading.value = false
    tempChatHistory.value = null // 清除临时记录引用
  }
}

// 删除会话
const deleteConversation = async (conversationId: number) => {
  try {
    await ChatMessageApi.deleteChat(conversationId)
    console.log('会话删除成功')
  } catch (error) {
    console.log('会话删除失败', error)
  }
}

// 处理删除会话按钮点击
const handleDeleteConversation = async (targetConversationId: number) => {
  try {
    // 确认删除
    await ElMessageBox.confirm(
      'Are you sure you want to delete this conversation?',
      'Delete Conversation',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 执行删除
    await deleteConversation(targetConversationId)

    // 如果删除的是当前选中的会话，清空当前会话
    if (targetConversationId === conversationId.value) {
      conversationId.value = undefined
      messageHistory.value = []
    }

    // 重新请求历史记录
    await getChatHistoryList()

    ElMessage.success('Conversation deleted successfully')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除会话失败:', error)
      ElMessage.error('Failed to delete conversation')
    }
  }
}

// Chat发送预处理
const btnSendMessage = () => {
  if (queryParams.question && !hasData.value) {
    sendMessage()
  }
}

// Chat网络请求发送
const sendMessage = async () => {
  if (queryParams.question === '') return // 如果输入为空则不发送
  sending.value = true // 设置发送状态
  conversationInProgress.value = true

  // 添加新消息到消息列表
  const newMessage: any = {
    id: generateUUID(),
    question: queryParams.question,
    answer: '',
    fullAnswer: '',
    source: 3,
    type: 2
  }
  console.log('sdkljhflksdjf', queryParams.question)

  messageHistory.value.push(newMessage) // 将用户输入的消息添加到消息历史中

  // 添加loading气泡作为占位
  const loadingMessage: any = {
    id: generateUUID(),
    question: '',
    answer: '',
    fullAnswer: '',
    source: 1,
    type: 1,
    isLoading: true // 标记为loading消息
  }
  messageHistory.value.push(loadingMessage) // 添加loading气泡

  queryParams.question = '' // 清空输入框

  console.log('清空后', newMessage.question)
  scrollToBottom() // 滚动到底部

  // 调用 API 发送流式消息
  conversationInAbortController.value = new AbortController()

  let lastContent = '' // 上一次收到的完整内容

  const onMessage = (event: MessageEvent) => {
    const data: ChatReqVO = JSON.parse(event.data)
    const rawContent = data.data?.answer || ''

    console.log('task bizType', data.data.task_type)
    console.log('这是数据', data)

    // 检查特殊条件：当data.data为true时，更新chatId
    if (data?.data && data?.id) {
      console.log('接收到新的会话ID', data.id)
      chatId.value = data.id
    }

    // 更新会话类型
    if (data.data?.task_type === 'generate_jd') taskType.value = TaskTypes.generateJD
    console.log('更新会话类型', taskType.value)

    // 跳过系统日志信息和空白行
    if (rawContent.includes('is running...') || rawContent.trim() === '') {
      return // 直接跳过这些消息
    }

    console.log('Content after filtering:', rawContent)

    // 如果是第一次接收内容，检查是否需要清理前导空白
    if (lastContent === '') {
      // 移除loading气泡
      const loadingIndex = messageHistory.value.findIndex((msg) => msg.isLoading)
      if (loadingIndex !== -1) {
        messageHistory.value.splice(loadingIndex, 1)
      }

      // 处理第一条消息 - 清除前导空格
      const trimmedContent = rawContent.trimStart()
      lastContent = trimmedContent

      const lastMessage = messageHistory.value[messageHistory.value.length - 1]
      if (lastMessage) {
        if (!lastMessage.fullAnswer) {
          lastMessage.fullAnswer = ''
        }

        lastMessage.fullAnswer = trimmedContent

        if (trimmedContent.length > 0) {
          typingQueue.value.push(trimmedContent)
        }

        if (!isTyping.value) {
          processTypingQueue(lastMessage)
        }
      }
      return // 直接返回，不做后续处理
    }

    // 获取新增部分
    if (rawContent.startsWith(lastContent)) {
      // 正确计算新增部分
      const newPart = rawContent.slice(lastContent.length)
      if (newPart.length === 0) return // 没有新内容，直接返回

      lastContent = rawContent // 更新lastContent为最新的完整内容

      const lastMessage = messageHistory.value[messageHistory.value.length - 1]
      if (lastMessage) {
        lastMessage.fullAnswer += newPart // 追加新内容

        if (newPart.length > 0) {
          typingQueue.value.push(newPart) // 只将新增部分加入打字队列
        }

        if (!isTyping.value) {
          processTypingQueue(lastMessage)
        }
      }
    } else {
      lastContent = rawContent

      const lastMessage = messageHistory.value[messageHistory.value.length - 1]
      if (lastMessage) {
        lastMessage.fullAnswer = rawContent // 重置为当前完整内容

        // 清空现有队列
        typingQueue.value = []
        lastMessage.answer = ''

        // 将全部内容加入队列重新打印
        typingQueue.value.push(rawContent)

        if (!isTyping.value) {
          processTypingQueue(lastMessage)
        }
      }
    }
  }

  const onError = (error: Event) => {
    console.error('Stream error:', error)
    stopStream()
  }

  const onClose = () => {
    console.log('Stream closed')
    stopStream()
  }

  // 调用流式消息接口
  const requestData: any = {
    ...queryParams,
    positionId: positionId.value,
    positionName: positionName.value,
    question: newMessage.question || ''
  }

  // 只有在conversationId存在时才添加
  if (conversationIdCopy.value) {
    requestData.conversationId = conversationIdCopy.value
  }

  await ChatMessageApi.getStreamAnswer(
    requestData,
    conversationInAbortController.value,
    onMessage,
    onError,
    onClose
  )

  sending.value = false // 发送完成，重置发送状态
}

// 处理打字队列
const processTypingQueue = async (message: any) => {
  if (typingQueue.value.length === 0) {
    // 确保显示完整内容
    if (message.fullAnswer && message.answer !== message.fullAnswer) {
      message.answer = message.fullAnswer
      scrollToBottom()
    }
    isTyping.value = false
    return
  }

  isTyping.value = true
  const text = typingQueue.value.shift() || ''

  // 逐字打印文本
  for (let i = 0; i < text.length; i++) {
    message.answer += text.charAt(i)
    // 每添加一个字符就滚动到底部
    await nextTick(() => {
      scrollToBottom()
    })
    await new Promise((resolve) => setTimeout(resolve, 20)) // 略微加快打字速度
  }

  // 处理队列中的下一条消息
  await processTypingQueue(message)
}

// 停止 stream 流式调用
const stopStream = async () => {
  // tip：如果 stream 进行中的 message，就需要调用 controller 结束
  if (conversationInAbortController.value) {
    conversationInAbortController.value.abort()
  }

  // 移除可能残留的loading气泡
  const loadingIndex = messageHistory.value.findIndex((msg) => msg.isLoading)
  if (loadingIndex !== -1) {
    messageHistory.value.splice(loadingIndex, 1)
  }

  // 等待正在进行的打字完成
  if (isTyping.value) {
    const lastMessage = messageHistory.value[messageHistory.value.length - 1]

    // 清空队列，但确保完整内容被显示
    if (lastMessage && lastMessage.fullAnswer) {
      typingQueue.value = []
      lastMessage.answer = lastMessage.fullAnswer
    }

    // 等待当前打字完成
    await new Promise((resolve) => {
      const checkTyping = () => {
        if (!isTyping.value) {
          resolve(null)
        } else {
          setTimeout(checkTyping, 100)
        }
      }
      checkTyping()
    })
  }

  // 设置为 false
  conversationInProgress.value = false
  sending.value = false

  // 恢复真正的会话ID
  conversationId.value = conversationIdCopy.value

  // 重新请求历史记录列表
  getChatHistoryList()
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    const chatScrollbarEl = chatScrollRef.value?.$el.querySelector('.el-scrollbar__wrap')
    if (chatScrollbarEl) {
      // 使用平滑滚动效果
      chatScrollbarEl.scrollTo({
        top: chatScrollbarEl.scrollHeight,
        behavior: 'smooth'
      })
    }

    // 在优化回复区域内查找滚动容器
    const optimizeScrollbar = document.querySelector('.bg-white.p-3.rounded-3 .el-scrollbar')
    if (optimizeScrollbar) {
      const scrollbarWrap = optimizeScrollbar.querySelector('.el-scrollbar__wrap')
      if (scrollbarWrap) {
        scrollbarWrap.scrollTop = scrollbarWrap.scrollHeight
      }
    }
  })
}

// 将 Markdown 转换为 HTML 的方法
const renderMarkdown = (text) => {
  if (!text) return ''
  const html = markdown.render(text)
  return `<div class="markdown-content">${html}</div>`
}

// 处理应用方法
const handleApply = async (message) => {
  try {
    targetLanguage.value = Languages.English // 默认设置为英语
    isPreviewLoading.value = true // 开始加载
    // 如果消息中有id属性，则存储到chatId中
    if (message.id) {
      console.log('从消息中获取chatId:', message.id)
      chatId.value = message.id
    }

    // 获取消息内容 - 这是Markdown格式的内容
    const content = message.answer || ''
    console.log('获取到的内容:', content)

    // 确保内容是HTML格式，以便在编辑器中正确显示
    const htmlContent = markdown.render(content)

    // 先更新预览内容，再设置编辑器内容
    switch (targetLanguage.value) {
      case Languages.English:
        previewEnglishContent.value = htmlContent
        break
      case Languages.Chinese:
        previewChineseContent.value = htmlContent
        break
      case Languages.Arabic:
        previewArabicContent.value = htmlContent
        break
    }

    // 将内容设置到编辑器中
    await nextTick()
    if (editorRef.value) {
      const editorInstance = editorRef.value as any
      if (editorInstance.editor) {
        editorInstance.editor.commands.setContent(htmlContent)
        ElMessage.success('Content applied successfully!') // 显示成功消息
      }
    }
  } catch (error) {
    console.error('应用内容失败:', error)
    ElMessage.error('Content application failed!') // 显示错误消息
  } finally {
    // 确保内容完全加载后再关闭loading
    setTimeout(() => {
      isPreviewLoading.value = false // 结束加载
    }, 300)
  }
}

// 重新生成
const handleRegenerate = async (message) => {
  if (!message || !message.question || sending.value) return // 如果没有消息、问题，或正在发送则不处理

  // 保存原始问题
  const originalQuestion = queryParams.question

  // 设置要重新生成的问题
  queryParams.question = message.question

  // 调用已有的发送方法
  await sendMessage()

  // 恢复原始问题（以防止影响用户可能正在编辑的内容）
  queryParams.question = originalQuestion
}

// 获取JD版本分页
const getJDVersion = async () => {
  try {
    if (!positionId.value) return

    const params = {
      pageNo: 1,
      pageSize: 10,
      positionId: positionId.value
    }

    const res = await JobDescApi.getJDVersionList(params)
    // 保存原始列表
    const originalList = res.list || []

    // 按照发布状态和创建时间处理版本列表
    const processedList = []

    // 找出最新的草稿版本（如果有）
    const draftVersions = originalList.filter((item) => item.status === JDPublishStatus.draft)
    const latestDraft =
      draftVersions.length > 0
        ? draftVersions.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))[0]
        : null

    // 如果有草稿版本，将其放在第一位
    if (latestDraft) {
      processedList.push(latestDraft)
    }

    // 添加所有已发布的版本
    const publishedVersions = originalList.filter((item) => item.status === JDPublishStatus.publish)
    processedList.push(...publishedVersions)

    // 更新JD版本列表
    JDVersionList.value = processedList

    // 如果有版本，自动加载第一个版本
    if (processedList.length > 0) {
      getVersionById(processedList[0].id)
    } else {
      // 清空编辑器内容
      if (editorRef.value) {
        const editorInstance = editorRef.value as any
        if (editorInstance.editor) {
          editorInstance.editor.commands.setContent('')
        }
      }
      // 清空预览内容
      previewEnglishContent.value = ''
      previewChineseContent.value = ''
      previewArabicContent.value = ''
    }
  } catch (error) {
    console.error('获取JD版本失败', error)
  }
}

// 根据ID获取JD版本相应内容
const getVersionById = async (id: number) => {
  try {
    console.log('Getting JD version by ID:', id)
    isPreviewLoading.value = true // 开始加载
    currentVersionId.value = id // 设置当前选中的版本ID
    const res = await JobDescApi.getJDVersionById(id)
    currentJDVersion.value = res
    console.log('JD Version by ID:', res)

    // 根据当前选择的语言将内容保存到相应的响应式变量
    if (res) {
      let content = ''

      // 根据当前选择的语言获取相应的内容
      switch (targetLanguage.value) {
        case Languages.English:
          content = res.enContent || ''
          break
        case Languages.Chinese:
          content = res.chContent || ''
          break
        case Languages.Arabic:
          content = res.arContent || ''
          break
      }

      // 将Markdown内容转换为HTML
      if (content) {
        const htmlContent = markdown.render(content)
        console.log(`Converted content to HTML for language: ${targetLanguage.value}`)

        // 更新预览内容
        switch (targetLanguage.value) {
          case Languages.English:
            previewEnglishContent.value = htmlContent
            break
          case Languages.Chinese:
            previewChineseContent.value = htmlContent
            break
          case Languages.Arabic:
            previewArabicContent.value = htmlContent
            break
        }

        // 将内容设置到编辑器中
        if (editorRef.value) {
          const editorInstance = editorRef.value as any
          if (editorInstance.editor) {
            // 确保编辑器已初始化
            nextTick(() => {
              editorInstance.editor.commands.setContent(htmlContent)
              console.log(`Content set to editor for language: ${targetLanguage.value}`)
            })
          }
        }
      } else {
        console.log('No content available for the selected language')
        clearEditorAndPreview()
      }
    } else {
      console.log('No content received for JD version')
      clearEditorAndPreview()
    }

    // 保存当前JD编辑时间，并格式化为dd-mm-yyyy
    if (res && res.lastUpdateTime) {
      JDEditTime.value = formatDate(res.lastUpdateTime)
      console.log('JD Edit Time set to:', JDEditTime.value)
    } else {
      JDEditTime.value = ''
    }
  } catch (error) {
    console.error('获取JD版本失败', error)
    clearEditorAndPreview()
  } finally {
    isPreviewLoading.value = false // 结束加载
  }
}

// 删除JD版本
const deleteJDVersion = async (id: number) => {
  try {
    await JobDescApi.deleteJD(id)
  } catch (error) {
    console.error('删除JD版本失败', error)
  }
}

// 处理删除JD版本按钮点击
const handleDeleteJDVersion = async (versionId: number) => {
  try {
    // 确认删除
    await ElMessageBox.confirm(
      'Are you sure you want to delete this JD version?',
      'Delete JD Version',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 执行删除
    await deleteJDVersion(versionId)

    // 如果删除的是当前选中的版本，清空当前版本状态
    if (versionId === currentVersionId.value) {
      currentJDVersion.value = null
      previewEnglishContent.value = ''
      previewChineseContent.value = ''
    }

    // 重新获取JD版本列表
    await getJDVersion()

    ElMessage.success('JD version deleted successfully')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除JD版本失败:', error)
      ElMessage.error('Failed to delete JD version')
    }
  }
}

// 下载JD
const handleJDDownload = async () => {
  try {
    // 定义请求参数
    const params = {
      id: currentJDVersion.value.id,
      language: targetLanguage.value,
      fileType: fileType.value
    }

    // 直接获取文件流
    const res = await JobDescApi.downloadJD(params)
    console.log('获取到的PDF流', res)

    // 下载PDF
    download.pdf(
      res,
      `JD_${positionName.value || 'JD'}_${currentJDVersion.value?.version || ''}.pdf`
    )
  } catch (error) {
    console.error('Error downloading file:', error)
  }
}

// 保存JD版本为草稿
const handleJDSave = async () => {
  try {
    // 检查岗位名称是否为空
    if (!positionName.value) {
      ElMessage.warning('Position name cannot be empty')
      return
    }

    // 获取当前编辑器最新内容
    let currentEditorContent = ''
    if (editorRef.value) {
      const editorInstance = editorRef.value as any
      if (editorInstance.editor) {
        currentEditorContent = editorInstance.editor.getHTML()

        // 更新相应语言的预览内容
        switch (targetLanguage.value) {
          case Languages.English:
            previewEnglishContent.value = currentEditorContent
            break
          case Languages.Chinese:
            previewChineseContent.value = currentEditorContent
            break
          case Languages.Arabic:
            previewArabicContent.value = currentEditorContent
            break
        }
      }
    }

    // 检查内容是否为空
    if (!previewEnglishContent.value) {
      ElMessage.warning('Please generate JD in the chat or edit before saving draft')
      return
    }

    // 定义请求体
    const params = {
      positionId: positionId.value,
      positionName: positionName.value,
      status: JDPublishStatus.draft,
      enContent: turnDownService.turndown(previewEnglishContent.value),
      chContent: turnDownService.turndown(previewChineseContent.value),
      arContent: turnDownService.turndown(previewArabicContent.value)
    }

    // 接口调用
    const res = await JobDescApi.publishJD(params)
    console.log('Save successful', res)

    // 提示用户保存成功
    ElMessage.success('Save successful')

    // 重新获取JD版本列表
    await getJDVersion()
  } catch (error) {
    ElMessage.error('Save failed')
    console.log('保存JD版本失败', error)
  }
}

// 更新JD版本草稿
const handleJDUpdate = async () => {
  try {
    // 获取当前编辑器最新内容
    let currentEditorContent = ''
    if (editorRef.value) {
      const editorInstance = editorRef.value as any
      if (editorInstance.editor) {
        currentEditorContent = editorInstance.editor.getHTML()

        // 更新相应语言的预览内容
        switch (targetLanguage.value) {
          case Languages.English:
            previewEnglishContent.value = currentEditorContent
            break
          case Languages.Chinese:
            previewChineseContent.value = currentEditorContent
            break
          case Languages.Arabic:
            previewArabicContent.value = currentEditorContent
            break
        }
      }
    }

    // 检查内容是否为空
    if (!previewEnglishContent.value) {
      ElMessage.warning('Please generate JD in the chat or edit before saving draft')
      return
    }

    // 定义请求体
    const params = {
      id: currentJDVersion.value.id,
      positionId: positionId.value,
      positionName: positionName.value,
      status: JDPublishStatus.draft,
      enContent: turnDownService.turndown(previewEnglishContent.value),
      chContent: turnDownService.turndown(previewChineseContent.value),
      arContent: turnDownService.turndown(previewArabicContent.value)
    }

    // 接口调用
    const res = await JobDescApi.saveJD(params)
    console.log('Update successful', res)

    // 提示用户更新成功
    ElMessage.success('Update successful')

    // 重新获取JD版本列表
    await getJDVersion()
  } catch (error) {
    ElMessage.error('Update failed')
    console.log('更新JD版本失败', error)
  }
}

// 发布JD版本
const handleJDPublish = async () => {
  try {
    // 获取当前编辑器最新内容
    let currentEditorContent = ''
    if (editorRef.value) {
      const editorInstance = editorRef.value as any
      if (editorInstance.editor) {
        currentEditorContent = editorInstance.editor.getHTML()

        // 更新相应语言的预览内容
        switch (targetLanguage.value) {
          case Languages.English:
            previewEnglishContent.value = currentEditorContent
            break
          case Languages.Chinese:
            previewChineseContent.value = currentEditorContent
            break
          case Languages.Arabic:
            previewArabicContent.value = currentEditorContent
            break
        }
      }
    }

    // 检查内容是否为空
    if (!previewEnglishContent.value) {
      ElMessage.warning('Please generate JD in the chat or edit before publishing')
      return
    }

    // 定义请求体
    const params = {
      // chatId: chatId.value,
      positionId: positionId.value,
      positionName: positionName.value,
      status: JDPublishStatus.publish,
      enContent: turnDownService.turndown(previewEnglishContent.value),
      chContent: turnDownService.turndown(previewChineseContent.value),
      arContent: turnDownService.turndown(previewArabicContent.value)
    }

    // 接口调用
    const res = await JobDescApi.publishJD(params)
    console.log('Publish successful', res, 'Chat ID:', chatId.value)

    // 提示用户发布成功
    ElMessage.success('Publish successful')

    // 重新获取JD版本列表
    await getJDVersion()
  } catch (error) {
    ElMessage.error('Publish failed')
    console.log('发布JD版本失败', error)
  }
}

// 处理复制方法
const handleCopy = async () => {
  if (editorRef.value) {
    const editorInstance = editorRef.value as any
    if (editorInstance.editor) {
      const content = editorInstance.editor.getHTML()
      // 创建一个临时的div元素来解析HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = content

      // 获取所有p标签的内容并合并
      const paragraphs = tempDiv.getElementsByTagName('p')
      let plainText = ''
      for (let i = 0; i < paragraphs.length; i++) {
        // 获取当前段落的内容
        const text = paragraphs[i].textContent || ''
        // 如果不是最后一个段落，添加一个换行符
        if (i < paragraphs.length - 1) {
          plainText += text + '\n'
        } else {
          plainText += text
        }
      }

      // 去除首尾空白字符
      plainText = plainText.trim()

      await copy(plainText)
      isCopied.value = true
      ElMessage.success('Copy successful')
      setTimeout(() => {
        isCopied.value = false
      }, 2000) // 2秒后恢复原始图标
    }
  }
}

// 处理优化复制方法
const handleCopyOptimize = async () => {
  if (!optimizeAnswer.value) return // 如果没有优化内容则返回

  try {
    // 将优化内容（markdown）先转为HTML
    const optimizedHtml = renderMarkdown(optimizeAnswer.value)

    // 创建一个临时的div元素来解析HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = optimizedHtml

    // 获取所有p标签的内容并合并
    const paragraphs = tempDiv.getElementsByTagName('p')
    let plainText = ''
    for (let i = 0; i < paragraphs.length; i++) {
      // 获取当前段落的内容
      const text = paragraphs[i].textContent || ''
      // 如果不是最后一个段落，添加一个换行符
      if (i < paragraphs.length - 1) {
        plainText += text + '\n'
      } else {
        plainText += text
      }
    }

    // 去除首尾空白字符
    plainText = plainText.trim()

    await copy(plainText)
    isCopied.value = true
    ElMessage.success('Optimization content copied')
    setTimeout(() => {
      isCopied.value = false
    }, 2000) // 2秒后恢复原始图标
  } catch (error) {
    console.error('Failed to copy optimization content:', error)
    ElMessage.error('Copy failed')
  }
}

// 获取当前编辑器的内容
const getCurrentEditorContent = () => {
  if (editorRef.value) {
    const editorInstance = editorRef.value as any
    if (editorInstance.editor) {
      return editorInstance.editor.getHTML()
    }
  }
  return ''
}

// JD优化发送预处理
const btnJDOptimize = () => {
  if (queryParams.optimizeQuestion) {
    handleJDOptimize()
  }
}

// 处理JD优化
const handleJDOptimize = async () => {
  if (!editorSelectedContent.value || queryParams.optimizeQuestion === '') return // 如果没有选中内容或输入为空则不发送

  optimizing.value = true // 设置优化状态
  optimizationInProgress.value = true // 设置优化进行中状态

  const optimizeInput = queryParams.optimizeQuestion
  // Store the optimization prompt before clearing the input field
  lastOptimizePrompt.value = optimizeInput
  queryParams.optimizeQuestion = '' // 清空输入框

  // 初始化优化答案
  optimizeAnswer.value = ''

  // 调用 API 发送流式消息
  optimizationInAbortController.value = new AbortController()

  let lastContent = '' // 上一次收到的完整内容

  const onMessage = (event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data)
      console.log('优化数据:', data)

      // 获取返回的内容
      const rawContent = data.data.answer || ''

      // 跳过系统日志和空白行
      if (rawContent.includes('is running...') || rawContent.trim() === '') {
        return
      }

      console.log('优化内容过滤后:', rawContent)

      // 直接更新完整内容
      optimizeAnswer.value = rawContent
      lastContent = rawContent

      // 内容更新后滚动到底部，确保用户始终看到最新内容
      scrollToBottom()
    } catch (error) {
      console.error('处理优化返回数据失败:', error)
    }
  }

  const onError = (error: Event) => {
    console.error('优化请求错误:', error)
    ElMessage.error('Optimization failed')
    stopOptimization()
  }

  const onClose = () => {
    console.log('优化请求完成')
    stopOptimization()
  }

  try {
    const requestData: any = {
      positionId: positionId.value,
      positionName: positionName.value,
      content: editorSelectedContent.value, // 选中的内容
      question: optimizeInput, // 优化提示词，根据API文档命名
      source: 2, // 来源，假设为2
      type: 2 // 类型，假设为2
    }

    // 调用JD优化接口
    await JDOptimizeApi.getJDOptimization(
      requestData,
      optimizationInAbortController.value,
      onMessage,
      onError,
      onClose
    )
  } catch (error) {
    console.error('优化过程发生错误:', error)
    ElMessage.error('Optimization process failed')
    stopOptimization()
  }
}

// 停止优化流
const stopOptimization = () => {
  // 如果优化进行中，调用controller结束
  if (optimizationInAbortController.value) {
    optimizationInAbortController.value.abort()
  }

  // 设置为 false
  optimizationInProgress.value = false
  optimizing.value = false
}

// JD优化内容替换
const handleReplace = () => {
  if (!optimizeAnswer.value || !editorRef.value) return

  try {
    const editorInstance = editorRef.value as any
    if (editorInstance.editor && editorSelectedContent.value) {
      // 获取当前编辑器内容
      const currentContent = editorInstance.editor.getHTML()

      // 将优化的内容转换为HTML格式
      const optimizedHtml = renderMarkdown(optimizeAnswer.value)
        .replace('<div class="markdown-content">', '')
        .replace('</div>', '')

      // 替换选中内容
      // 先删除选中的内容
      editorInstance.editor.commands.deleteSelection()

      // 在光标位置插入优化后的内容
      editorInstance.editor.commands.insertContent(optimizedHtml)

      // 获取更新后的内容并同步到预览变量中
      const updatedContent = editorInstance.editor.getHTML()

      // 更新相应语言的预览内容
      switch (targetLanguage.value) {
        case Languages.English:
          previewEnglishContent.value = updatedContent
          break
        case Languages.Chinese:
          previewChineseContent.value = updatedContent
          break
        case Languages.Arabic:
          previewArabicContent.value = updatedContent
          break
      }

      // 清空选中内容和优化答案，表示替换操作已完成
      editorSelectedContent.value = ''
      optimizeAnswer.value = ''

      ElMessage.success('Content replaced successfully')
    } else {
      ElMessage.warning('Editor not initialized or no selected content')
    }
  } catch (error) {
    console.error('替换内容失败', error)
    ElMessage.error('Content replacement failed')
  }
}

// 重新生成JD优化内容
const handleRegenerateOptimize = async () => {
  // Check if there's selected content and a stored prompt
  if (!editorSelectedContent.value) {
    ElMessage.warning('Please select content to optimize')
    return
  }

  if (!lastOptimizePrompt.value && !queryParams.optimizeQuestion) {
    ElMessage.warning('No previous optimization prompt found')
    return
  }

  if (optimizing.value) return // Don't allow when already optimizing

  // Save current prompt if available, otherwise use the stored one
  if (queryParams.optimizeQuestion) {
    lastOptimizePrompt.value = queryParams.optimizeQuestion
    queryParams.optimizeQuestion = ''
  }

  // Show message that we're regenerating
  ElMessage.info('Regenerating optimization...')

  // 清空优化答案
  optimizeAnswer.value = ''

  // 临时设置优化问题并调用优化方法
  const tempQuestion = queryParams.optimizeQuestion
  queryParams.optimizeQuestion = lastOptimizePrompt.value

  // 调用原有的优化方法
  await handleJDOptimize()

  // 恢复输入框状态
  queryParams.optimizeQuestion = tempQuestion
}

// JD翻译
const handleTranslateJD = async () => {
  try {
    // 检查内容是否为空
    if (!previewEnglishContent.value) {
      ElMessage.warning('Please generate JD in the chat or edit before translating')
      return
    }

    // 确保目标语言不是英语（源语言）
    if (targetLanguage.value === Languages.English) {
      // 如果是英语，直接设置内容不需要翻译
      if (editorRef.value) {
        const editorInstance = editorRef.value as any
        if (editorInstance.editor) {
          editorInstance.editor.commands.setContent(previewEnglishContent.value)
        }
      }
      return
    }

    // 显示加载状态 - 这里不设置，因为在watch中已经设置了
    // isPreviewLoading.value = true
    ElMessage.info('Translating content...')

    // 获取当前内容的md格式
    const content = turnDownService.turndown(previewEnglishContent.value)

    // 检查内容是否为空
    if (!content) {
      ElMessage.warning('No content available for translation')
      return
    }

    // 定义请求体
    const params = {
      content: content,
      originLanguage: Languages.English,
      targetLanguage: targetLanguage.value
    }

    // 如果有当前版本ID，添加到参数中
    if (currentJDVersion.value?.id) {
      params.jdId = currentJDVersion.value.id
    }

    console.log('Translation params:', params)

    // 接口调用
    const res = await JobDescApi.translateJD(params)
    console.log('Translation response:', res)

    // 提示用户翻译成功
    ElMessage.success('Translation successful')

    // 更新预览内容
    switch (targetLanguage.value) {
      case Languages.Chinese:
        previewChineseContent.value = res.transContent
        break
      case Languages.Arabic:
        previewArabicContent.value = res.transContent
        break
    }

    // 确保编辑器内容更新 - 添加延迟确保DOM渲染完成
    await nextTick()
    if (editorRef.value) {
      const editorInstance = editorRef.value as any
      if (editorInstance.editor) {
        const translatedContent = res.transContent
        // 将Markdown内容转换为HTML再设置到编辑器
        const htmlContent = markdown.render(translatedContent)
        editorInstance.editor.commands.setContent(htmlContent)

        // 确认编辑器内容已更新
        await nextTick()
        console.log('Editor content updated with translation')
      }
    }
  } catch (error) {
    console.error('Translation error:', error)
    ElMessage.error('Translation failed')
  } finally {
    // 无论成功还是失败，都结束加载状态
    setTimeout(() => {
      isPreviewLoading.value = false
    }, 5000)
  }
}

/* Mounted */
onMounted(async () => {
  await getChatHistoryList() // 获取对话记录列表

  // 获取JD版本列表
  await getJDVersion()

  // 如果有版本，模拟点击第一个版本
  if (JDVersionList.value.length > 0) {
    const firstVersion = JDVersionList.value[0]
    console.log('Simulating click on first JD version:', firstVersion.id)
    await getVersionById(Number(firstVersion.id))
  } else {
    console.log('No JD versions available')
    // 清空编辑器内容
    if (editorRef.value) {
      const editorInstance = editorRef.value as any
      if (editorInstance.editor) {
        editorInstance.editor.commands.setContent('')
      }
    }
    // 清空预览内容
    previewEnglishContent.value = ''
    previewChineseContent.value = ''
    previewArabicContent.value = ''
  }

  handleInitialChat() // 如果有查询参数，创建新会话
  scrollToBottom() // 初始加载后滚动到底部

  // 设置延时执行，确保编辑器已加载
  nextTick(() => {
    // 监听编辑器内容变化
    if (editorRef.value) {
      const editorInstance = editorRef.value as any
      if (editorInstance.editor) {
        editorInstance.editor.on('update', () => {
          // 当编辑器内容变化时，更新对应的预览内容
          const currentContent = editorInstance.editor.getHTML()

          switch (targetLanguage.value) {
            case Languages.English:
              previewEnglishContent.value = currentContent
              break
            case Languages.Chinese:
              previewChineseContent.value = currentContent
              break
            case Languages.Arabic:
              previewArabicContent.value = currentContent
              break
          }
        })
      }
    }
  })
})

/* WATCH */
// 添加对targetLanguage的监听，当语言切换时更新编辑器内容
watch(targetLanguage, async (newLanguage) => {
  if (!currentJDVersion.value) return

  // 显示加载状态
  isPreviewLoading.value = true

  try {
    // 如果切换到英语，直接使用现有内容，不需要翻译
    if (newLanguage === Languages.English) {
      // 如果有英文内容，直接使用
      if (previewEnglishContent.value) {
        if (editorRef.value) {
          const editorInstance = editorRef.value as any
          if (editorInstance.editor) {
            await nextTick()
            editorInstance.editor.commands.setContent(previewEnglishContent.value)
            console.log('Editor content updated with English content')
          }
        }
      }
      // 如果没有英文内容但有JD版本，从JD版本获取
      else if (currentJDVersion.value.enContent) {
        const content = markdown.render(currentJDVersion.value.enContent)
        if (editorRef.value) {
          const editorInstance = editorRef.value as any
          if (editorInstance.editor) {
            await nextTick()
            editorInstance.editor.commands.setContent(content)
            previewEnglishContent.value = content
          }
        }
      }
    }
    // 如果切换到其他语言，调用翻译
    else if (previewEnglishContent.value) {
      await handleTranslateJD()
    }
    // 如果没有英文内容，尝试从JD版本获取对应语言的内容
    else {
      let content = ''
      switch (newLanguage) {
        case Languages.Chinese:
          content = currentJDVersion.value.chContent
            ? markdown.render(currentJDVersion.value.chContent)
            : ''
          break
        case Languages.Arabic:
          content = currentJDVersion.value.arContent
            ? markdown.render(currentJDVersion.value.arContent)
            : ''
          break
      }

      if (content && editorRef.value) {
        const editorInstance = editorRef.value as any
        if (editorInstance.editor) {
          await nextTick()
          editorInstance.editor.commands.setContent(content)

          // 更新对应语言的预览内容
          switch (newLanguage) {
            case Languages.Chinese:
              previewChineseContent.value = content
              break
            case Languages.Arabic:
              previewArabicContent.value = content
              break
          }
        }
      }
    }
  } catch (error) {
    console.error('Error during language change:', error)
    ElMessage.error('Failed to update content for selected language')
  } finally {
    // 无论成功还是失败，最终结束加载状态
    setTimeout(() => {
      isPreviewLoading.value = false
    }, 2000) // 使用2秒延迟，提供足够的视觉反馈但不过长
  }
})

// 跳转到Skills Tags页面
const toSkillsGenerate = () => {
  // 保存当前JD版本ID，以便在Skills Tags页面使用
  if (currentJDVersion.value?.id) {
    router.push({
      path: '/edp/skill-generate',
      query: {
        jdId: currentJDVersion.value.id,
        positionId: positionId.value,
        positionName: positionName.value,
        deptName: deptName.value
      }
    })
  } else {
    ElMessage.warning('Please save the JD first to generate skills tags')
  }
}
</script>

<template>
  <ContentWrap class="!h-[85vh]">
    <el-row class="relative flex items-stretch">
      <!-- History -->
      <el-col :span="4" class="flex !h-[50vh]">
        <!--新建会话按钮-->
        <div class="p-3 pb-0">
          <el-button
            type="primary"
            class="w-full !h-10"
            @click="() => createConv(positionIdCopy, positionNameCopy)"
          >
            <Icon icon="ep:plus" />
            <span>New Chat</span>
          </el-button>
        </div>

        <!--历史记录滚动区域-->
        <el-scrollbar
          class="!h-[55vh]!mx-[-12px] px-3 mt-1"
          ref="historyScrollbarRef"
          @scroll="handleHistoryScroll"
        >
          <div
            v-for="(chat, chatIndex) in filteredChatHistoryList"
            :key="chat.conversationId"
            class="relative flex justify-start items-center gap-3 h-16 my-2 p-3 rounded-1 hover:bg-gray-100 cursor-pointer group"
            :class="{ 'bg-gray-100': conversationId === chat.conversationId }"
            @click="clickHistoryItem(chat)"
          >
            <Icon icon="ep:chat-dot-square" />

            <div
              class="flex flex-col justify-between gap-1 truncate overflow-hidden whitespace-nowrap flex-1"
            >
              <!--对话标题-->
              <span class="truncate overflow-hidden whitespace-nowrap">
                {{ chat.title || 'Chat' + (chatIndex + 1) }}
              </span>

              <span class="flex justify-start w-full text-xs text-gray-4">{{
                formatDate(chat.createTime)
              }}</span>
            </div>

            <!-- 删除按钮 -->
            <Icon
              v-if="chat.conversationId && chat.conversationId !== 0"
              icon="ep:close"
              class="!absolute right-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-200 !w-4 !h-4 text-gray-400 hover:!text-red-500 cursor-pointer"
              @click.stop="handleDeleteConversation(chat.conversationId)"
            />
          </div>

          <!-- 加载更多指示器 -->
          <div v-if="isLoadingMore" class="flex justify-center items-center py-2">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span class="ml-2">Loading...</span>
          </div>

          <!-- 没有更多数据提示 -->
          <div
            v-if="!hasMoreHistory && chatHistoryList.length > 0"
            class="text-center text-gray-400 py-2 text-xs"
          >
            No more chats
          </div>
        </el-scrollbar>

        <!--JD列表-->
        <div class="collapse mt-5">
          <el-collapse class="px-3" v-model="activeCollapseNames">
            <el-collapse-item title="JD Version" name="jdVersion">
              <el-scrollbar height="200px">
                <!--当存在JD版本时显示-->
                <div class="flex flex-col gap-2">
                  <div
                    v-show="JDVersionList.length > 0"
                    v-for="version in JDVersionList"
                    :key="version.id"
                    class="relative cursor-pointer p-2 hover:bg-gray-100 flex items-center justify-between rounded-1 group"
                    :class="{ 'bg-gray-100': currentVersionId === version.id }"
                    @click="
                      () => {
                        console.log('Clicked version ID:', version.id)
                        getVersionById(Number(version.id))
                      }
                    "
                  >
                    <div class="flex gap-2 flex-1">
                      <span>{{ version.positionName }}</span>
                      <span v-if="version.status === JDPublishStatus.publish">
                        {{ `v` + parseFloat(version.version).toFixed(1) }}
                      </span>
                    </div>

                    <div class="flex items-center gap-2">
                      <!-- 草稿状态显示黄点 -->
                      <!--<span v-if="version.status === JDPublishStatus.draft" class="draft-dot"></span>-->
                      <el-tag v-if="version.status === JDPublishStatus.draft" type="warning">
                        Draft
                      </el-tag>

                      <!-- 删除按钮 -->
                      <Icon
                        v-if="version.id"
                        icon="ep:close"
                        class="opacity-0 group-hover:opacity-100 transition-all duration-200 !w-4 !h-4 text-gray-400 hover:!text-red-500 cursor-pointer mx-1"
                        @click.stop="handleDeleteJDVersion(version.id)"
                      />
                    </div>
                  </div>
                </div>

                <!--当JD版本为空时显示-->
                <div
                  v-show="JDVersionList.length === 0"
                  class="flex items-center justify-center w-full"
                >
                  <span class="text-gray">- No JD Version -</span>
                </div>
              </el-scrollbar>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-col>

      <!-- Chat -->
      <el-col
        :span="12"
        class="h-[85vh] relative"
        style="border-left: 1px solid var(--el-border-color-lighter)"
      >
        <!--标题-->
        <div
          class="absolute top-0 left-0 right-0 z-10 py-3 px-3"
          style="
            background: linear-gradient(
              to bottom,
              rgba(255, 255, 255, 1) 0%,
              rgba(255, 255, 255, 0.95) 20%,
              rgba(255, 255, 255, 0.8) 40%,
              rgba(255, 255, 255, 0.6) 60%,
              rgba(255, 255, 255, 0.3) 80%,
              rgba(255, 255, 255, 0) 100%
            );
            backdrop-filter: blur(2px);
            padding-bottom: 2.5rem;
          "
        >
          <div class="flex items-baseline justify-start">
            <h1> {{ positionName }} </h1>
          </div>
        </div>

        <div class="h-full flex flex-col gap-4">
          <el-scrollbar ref="chatScrollRef" class="flex-1 p-3">
            <!--对话界面-->
            <div class="flex flex-col gap-4 mt-17">
              <!-- 遍历消息历史 -->
              <template v-for="(message, index) in messageHistory" :key="index">
                <!-- 用户消息 -->
                <div v-if="message.question" class="flex flex-col gap-2 self-end w-full">
                  <div class="flex items-center self-end gap-2">
                    <!--用户名-->
                    <span class="text-14px text-gray <lg:hidden">
                      {{ userName }}
                    </span>

                    <!--头像-->
                    <ElAvatar :src="avatar" alt="" class="!w-7 !h-7 rounded-full" />
                  </div>

                  <!--消息气泡-->
                  <div
                    class="flex flex-col gap-2 p-3 rounded-1 bg-[#017B3D] ml-auto max-w-[80%] break-words"
                  >
                    <span class="text-white">{{ message.question }}</span>
                  </div>
                </div>

                <!-- AI消息 -->
                <div
                  v-if="message.answer || message.isLoading"
                  class="flex flex-col gap-2 self-start max-w-[80%]"
                >
                  <div class="flex gap-2">
                    <!--AI助手头像-->
                    <div
                      class="flex justify-center align-center pt-1 border-solid border-[1px] border-gray-200 rounded-full w-7 h-7"
                    >
                      <Icon icon="ep:promotion" color="#017B3D" />
                    </div>

                    <!--名称-->
                    <span class="flex justify-center align-center text-gray <lg:hidden"
                      >AI Assistant</span
                    >
                  </div>

                  <!--消息气泡-->
                  <div
                    class="flex flex-col gap-2 p-3 border-solid border-[1px] border-gray-200 rounded-1 markdown-bubble"
                    :class="{ 'w-fit': message.isLoading }"
                  >
                    <!-- Loading状态 -->
                    <div v-if="message.isLoading" class="flex items-center gap-2 animate-pulse">
                      <div class="flex space-x-1 items-center">
                        <div class="w-1.5 h-1.5 bg-gray-300 rounded-full loading-dot"></div>
                        <div
                          class="w-1.5 h-1.5 bg-gray-300 rounded-full loading-dot"
                          style="animation-delay: 0.2s"
                        ></div>
                        <div
                          class="w-1.5 h-1.5 bg-gray-300 rounded-full loading-dot"
                          style="animation-delay: 0.4s"
                        ></div>
                      </div>
                    </div>

                    <!-- 正常消息内容 -->
                    <div v-else-if="message.answer" class="markdown-wrapper">
                      <div v-html="renderMarkdown(message.answer)"></div>
                    </div>

                    <!--操作按钮组-->
                    <div v-if="message.answer && index > 0 && !isTyping" class="flex gap-2">
                      <el-button
                        v-if="!message.answer.includes('outage')"
                        class="!border-none !bg-zinc-100 !px-2"
                        @click="handleApply(message)"
                      >
                        <Icon icon="ep:check" />
                        <span>Apply</span>
                      </el-button>

                      <el-button
                        class="!border-none !bg-zinc-100 !px-2"
                        @click="handleRegenerate(message)"
                      >
                        <Icon icon="ep:refresh" />
                        <span>Regenerate</span>
                      </el-button>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </el-scrollbar>

          <!--Chat输入框-->
          <div class="sticky bottom-3 !w-full px-3">
            <div
              class="flex flex-col bg-white border-solid border-[#EFEFEF] border-[1px] rounded-3 shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)]"
            >
              <div class="flex items-center">
                <el-input
                  class="chat-input toolbar-input !h-14 flex-1 !py-3"
                  placeholder="Type message"
                  v-model="queryParams.question"
                  maxlength="500"
                  show-word-limit
                  @keydown.enter.exact.prevent="btnSendMessage"
                />

                <!--发送按钮-->
                <div class="flex justify-center items-center mr-3">
                  <el-button
                    v-if="!sending"
                    type="primary"
                    :class="[
                      '!w-8 !h-8 !rounded-full !border-none flex items-center justify-center',
                      queryParams.question.trim() ? '!bg-[#017B3D]' : '!bg-gray-300'
                    ]"
                    @click="btnSendMessage"
                  >
                    <Icon icon="ep:top" />
                  </el-button>

                  <el-button
                    v-if="sending"
                    :class="[
                      '!w-8 !h-8 !rounded-full !border-none flex items-center justify-center',
                      queryParams.question.trim() ? '!bg-[#017B3D]' : '!bg-gray-300'
                    ]"
                  >
                    <el-icon class="is-loading"><Loading /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- Preview -->
      <el-col
        :span="8"
        class="h-[85vh] p-3"
        style="border-left: 1px solid var(--el-border-color-lighter)"
      >
        <div class="flex flex-col gap-4">
          <!--标题-->
          <div class="flex justify-between items-center">
            <!--标题与编辑时间-->
            <div class="flex gap-2 justify-start items-baseline">
              <h1> Preview </h1>

              <!--编辑时间-->
              <span v-if="JDEditTime" class="text-xs text-gray"> {{ JDEditTime }} </span>
            </div>

            <!--按钮组-->
            <div class="flex gap-2">
              <!--预览-->
              <Preview
                :content="getCurrentEditorContent()"
                :language="targetLanguage"
                :disabled="isPreviewLoading"
              />

              <!--复制-->
              <el-button
                text
                class="!w-8"
                :disabled="isAllPreviewEmpty || isPreviewLoading"
                @click="handleCopy"
              >
                <Icon :icon="isCopied ? 'ep:check' : 'ep:copy-document'" />
              </el-button>

              <!--下载-->
              <el-button
                text
                class="!w-8"
                :disabled="hasCurrentJDVersion || isCurrentVersionDraft || isPreviewLoading"
                @click="handleJDDownload"
              >
                <Icon icon="ep:download" />
              </el-button>
            </div>
          </div>

          <!--Actions-->
          <div class="flex justify-between">
            <!--翻译语言选择器-->
            <el-select
              v-model="targetLanguage"
              palaceholder="Translated Language"
              class="!w-24"
              :disabled="hasCurrentJDVersion || isCurrentVersionDraft || isPreviewLoading"
            >
              <el-option
                v-for="item in targetLanguageList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>

            <!--按钮组-->
            <div class="flex gap-2 ml-2">
              <!--技能标签生成-->
              <el-button
                class="!px-2"
                @click="toSkillsGenerate"
                :disabled="hasCurrentJDVersion || isCurrentVersionDraft || isPreviewLoading"
              >
                <icon icon="ep:price-tag" />
                <span>Skills Tags</span>
              </el-button>

              <!--保存草稿按钮 - 当没有ID且没有草稿版本时显示-->
              <el-button
                v-if="(!currentJDVersion?.id || !hasDraftVersion) && !isCurrentVersionPublished"
                :disabled="isPreviewLoading"
                class="!px-2"
                @click="handleJDSave"
              >
                <icon icon="ep:document" />
                <span> Save </span>
              </el-button>

              <!-- 更新草稿按钮 - 当有草稿版本时显示 -->
              <el-button
                v-if="hasDraftVersion && !isCurrentVersionPublished"
                :disable="isPreviewLoading"
                class="!px-2"
                @click="handleJDUpdate"
              >
                <icon icon="ep:document" />
                <span> Save </span>
              </el-button>

              <!--发布按钮-->
              <el-button
                type="primary"
                :disabled="isPreviewLoading"
                class="flex items-center justify-center !px-2"
                @click="handleJDPublish"
              >
                <icon icon="ep:promotion" />
                <span> Publish </span>
              </el-button>
            </div>
          </div>

          <!--编辑器-->
          <div class="tiptap-editor-container relative">
            <!--当存在JD时显示-->
            <el-scrollbar
              v-if="previewEnglishContent != ''"
              height="74vh"
              v-loading="isPreviewLoading"
            >
              <!--编辑器组件-->
              <Editor
                ref="editorRef"
                v-model:selectedContent="editorSelectedContent"
                :dir="targetLanguage === Languages.Arabic ? 'rtl' : 'ltr'"
              />

              <!-- ToolBar -->
              <bubble-menu
                v-if="editorRef?.editor"
                class="flex flex-col sticky bottom-5 !w-full bg-white border-solid border-[#EFEFEF] border-[1px] rounded-3 shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)]"
                :tippy-options="{ duration: 100 }"
                :editor="editorRef.editor"
              >
                <!--AI回复-->
                <div v-if="optimizeAnswer" class="bg-white p-3 rounded-3">
                  <!--AI回复内容-->
                  <el-scrollbar class="!h-32 mb-2">
                    <div v-html="renderMarkdown(optimizeAnswer)" class="markdown-wrapper"></div>
                  </el-scrollbar>

                  <!--Actions-->
                  <div class="flex justify-between h-full">
                    <el-button
                      type="primary"
                      class="!px-2 !bg-[#017B3D] !border-none flex items-center justify-center"
                      @click="handleReplace"
                    >
                      <Icon icon="ep:top" />
                      <span> Replace </span>
                    </el-button>

                    <div class="flex gap-1">
                      <!--复制到剪贴板-->
                      <el-button text class="!w-8" @click="handleCopyOptimize">
                        <Icon :icon="isCopied ? 'ep:check' : 'ep:copy-document'" />
                      </el-button>

                      <!--重新生成-->
                      <el-button text class="!w-8" @click="handleRegenerateOptimize">
                        <Icon icon="ep:refresh" />
                      </el-button>
                    </div>
                  </div>
                </div>

                <!--分割线-->
                <el-divider v-if="optimizeAnswer" class="!border-t-[#EFEFEF] !m-0" />

                <!--JD优化提示词输入框-->
                <div class="flex items-center">
                  <el-input
                    class="chat-input toolbar-input !h-14 flex-1 !py-3"
                    placeholder="Type message"
                    v-model="queryParams.optimizeQuestion"
                    maxlength="500"
                    show-word-limit
                    @keydown.enter.exact.prevent="btnJDOptimize"
                  />

                  <!--发送按钮-->
                  <div class="flex justify-center items-center mr-3">
                    <el-button
                      v-if="!optimizing"
                      type="primary"
                      :class="[
                        '!w-8 !h-8 !rounded-full !border-none flex items-center justify-center',
                        queryParams.optimizeQuestion.trim() && !optimizing
                          ? '!bg-[#017B3D]'
                          : '!bg-gray-300'
                      ]"
                      @click="btnJDOptimize"
                    >
                      <Icon icon="ep:top" />
                      <Icon v-if="optimizing" icon="ep:loading" />
                    </el-button>

                    <el-button
                      v-if="optimizing"
                      type="primary"
                      :class="[
                        '!w-8 !h-8 !rounded-full !border-none flex items-center justify-center',
                        queryParams.optimizeQuestion.trim() && !optimizing
                          ? '!bg-[#017B3D]'
                          : '!bg-gray-300'
                      ]"
                    >
                      <el-icon class="is-loading"><Loading /></el-icon>
                    </el-button>
                  </div>
                </div>
              </bubble-menu>
            </el-scrollbar>

            <!--空JD提示-->
            <div
              v-if="previewEnglishContent === ''"
              class="flex flex-col gap-3 items-center justify-center h-[74vh]"
              v-loading="isPreviewLoading"
            >
              <Icon icon="ep:smoking" :size="48" color="gray" />

              <!--提示语-->
              <div class="flex flex-col items-center justify-center">
                <span class="items-center justify-center text-gray text-sm">
                  There is currently no job description.
                </span>

                <span class="items-center justify-center text-gray text-sm">
                  You can talk to the AI Assistant to generate a job description.
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </ContentWrap>
</template>

<style scoped>
/* 去除按钮左外边距 */
:deep(.el-button + .el-button) {
  margin-left: 0 !important;
}

/* 去除el-card__body内边距 */
:deep(.el-card__body) {
  padding: 0 !important;
}

/* JD版本草稿状态黄点样式 */
.draft-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #f9a825; /* 黄色 */
}

/* Apply border-radius to the input */
.chat-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 1px solid #e7e7e7;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

/* Apply border only when focused */
.chat-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid #017b3d !important;
}

/* Remove border from collapse component */
.border-none :deep(.el-collapse-item__header),
.border-none :deep(.el-collapse-item__wrap) {
  border: none;
}

/* Markdown 样式 */
:deep(.markdown-content) {
  line-height: 1.6;
  width: 100%;
  overflow-wrap: break-word;
}

:deep(.markdown-content p) {
  margin: 0.5em 0;
}

:deep(.markdown-content a) {
  color: #017b3d;
  text-decoration: none;
}

:deep(.markdown-content code) {
  background-color: #f5f5f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

:deep(.markdown-content pre) {
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
}

:deep(.markdown-content ul, .markdown-content ol) {
  padding-left: 2em;
  margin: 0.5em 0;
  box-sizing: border-box;
}

:deep(.markdown-content ol) {
  list-style-position: inside;
}

:deep(.markdown-content li) {
  margin-bottom: 0.3em;
  position: relative;
}

:deep(.markdown-content blockquote) {
  border-left: 4px solid #ddd;
  padding-left: 1em;
  margin-left: 0;
  color: #666;
}

/* 确保编辑器内容区域有足够的内边距 */
.tiptap-editor-container :deep(.ProseMirror) {
  padding: 0 !important;
  outline: none !important; /* 去除蓝色边框 */
}

/* 移除所有编辑区域的原生蓝色边框 */
.tiptap-editor-container :deep(*:focus),
.tiptap-editor-container :deep(*:focus-visible) {
  outline: none !important;
  box-shadow: none !important;
  border-color: transparent !important;
}

/* 移除特定工具栏输入框的边框和阴影 */
.toolbar-input :deep(.el-input__wrapper) {
  border: none !important;
  box-shadow: none !important;
  border-radius: 12px;
}

.toolbar-input :deep(.el-input__wrapper.is-focus) {
  border: none !important;
  box-shadow: none !important;
}

/* Loading dots 波浪动画 */
.loading-dot {
  animation: wave 1s ease-in-out infinite;
}

@keyframes wave {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}
</style>
