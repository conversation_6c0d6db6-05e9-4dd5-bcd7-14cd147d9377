<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import { BubbleMenu } from '@tiptap/vue-3'
import { FloatingMenu } from '@tiptap/vue-3'
import MarkdownIt from 'markdown-it'
import markdownItSanitizer from 'markdown-it-sanitizer'
import type { Editor as EditorType } from '@tiptap/vue-3'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  selectedContent: {
    type: String,
    default: ''
  },
  dir: {
    type: String,
    default: 'ltr'
  }
})

const emit = defineEmits(['update:modelValue', 'update:selectedContent'])

// 使用ref创建编辑器实例，并明确指定类型
const editor = ref<EditorType | null>(null)

// 创建Markdown解析器，与父组件保持一致
const markdown = new MarkdownIt({
  html: false,
  linkify: true,
  typographer: true
}).use(markdownItSanitizer)

// 初始化编辑器
onMounted(() => {
  // 检查内容是否为HTML格式
  const isHtml = props.modelValue.startsWith('<')

  // 初始化编辑器
  editor.value = new Editor({
    content: isHtml ? props.modelValue : '',
    extensions: [
      StarterKit.configure({
        // 只禁用不需要的扩展，保留必要的扩展
        heading: {
          levels: [1, 2, 3, 4, 5, 6]
        }
      }),
      Link.configure({
        openOnClick: false
      }),
      Table.configure({
        resizable: true
      }),
      TableRow,
      TableCell,
      TableHeader
    ],
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection
      if (from !== to) {
        const selectedText = editor.state.doc.textBetween(from, to)
        emit('update:selectedContent', selectedText)
      } else {
        emit('update:selectedContent', '')
      }
    }
  })
})

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (editor.value && newValue !== editor.value.getHTML()) {
      editor.value.commands.setContent(newValue)
    }
  }
)

// 监听dir属性变化，更新编辑器文本方向
watch(
  () => props.dir,
  (newDir) => {
    if (editor.value) {
      const element = editor.value.view.dom
      if (element) {
        element.setAttribute('dir', newDir)
      }
    }
  },
  { immediate: true }
)

// 组件卸载时销毁编辑器
onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// 暴露编辑器实例给父组件
defineExpose({
  editor
})
</script>

<template>
  <div class="tiptap-editor" :dir="props.dir">
    <editor-content :editor="editor" />
  </div>
</template>

<style lang="scss">
/* 增强基本编辑器样式以支持更好的Markdown显示 */
/* Basic editor styles */
.tiptap {
  :first-child {
    margin-top: 0;
  }

  /* List styles */
  ul,
  ol {
    padding: 0 1rem;
    margin: 1.25rem 1rem 1.25rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  /* Heading styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.1;
    margin-top: 2.5rem;
    text-wrap: pretty;
  }

  h1,
  h2 {
    margin-top: 3.5rem;
    margin-bottom: 1.5rem;
  }

  h1 {
    font-size: 1.4rem;
  }

  h2 {
    font-size: 1.2rem;
  }

  h3 {
    font-size: 1.1rem;
  }

  h4,
  h5,
  h6 {
    font-size: 1rem;
  }

  /* Code and preformatted text styles */
  code {
    background-color: var(--purple-light);
    border-radius: 0.4rem;
    color: var(--black);
    font-size: 0.85rem;
    padding: 0.25em 0.3em;
  }

  pre {
    background: var(--black);
    border-radius: 0.5rem;
    color: var(--white);
    font-family: 'JetBrainsMono', monospace;
    margin: 1.5rem 0;
    padding: 0.75rem 1rem;

    code {
      background: none;
      color: inherit;
      font-size: 0.8rem;
      padding: 0;
    }
  }

  blockquote {
    border-left: 3px solid var(--gray-3);
    margin: 1.5rem 0;
    padding-left: 1rem;
  }

  hr {
    border: none;
    border-top: 1px solid var(--gray-2);
    margin: 2rem 0;
  }
}

.tiptap-editor {
  width: 100%;
  height: 100%;
}

.tiptap-editor .ProseMirror {
  height: 100%;
  padding: 1rem;
  outline: none;
}

.tiptap-editor .ProseMirror p {
  margin: 0.5em 0;
}

.tiptap-editor .ProseMirror h1,
.tiptap-editor .ProseMirror h2,
.tiptap-editor .ProseMirror h3,
.tiptap-editor .ProseMirror h4,
.tiptap-editor .ProseMirror h5,
.tiptap-editor .ProseMirror h6 {
  margin: 0.5em 0;
  line-height: 1.2;
}

.tiptap-editor .ProseMirror ul,
.tiptap-editor .ProseMirror ol {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

.tiptap-editor .ProseMirror blockquote {
  border-left: 3px solid #ccc;
  margin-left: 0;
  margin-right: 0;
  padding-left: 1em;
  color: #666;
}

.tiptap-editor .ProseMirror pre {
  background: #f4f4f4;
  padding: 0.5em;
  border-radius: 4px;
  overflow-x: auto;
}

.tiptap-editor .ProseMirror code {
  background: #f4f4f4;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.tiptap-editor .ProseMirror table {
  border-collapse: collapse;
  margin: 0.5em 0;
  width: 100%;
}

.tiptap-editor .ProseMirror th,
.tiptap-editor .ProseMirror td {
  border: 1px solid #ccc;
  padding: 0.5em;
}

.tiptap-editor .ProseMirror th {
  background-color: #f4f4f4;
  font-weight: bold;
}

.tiptap-editor .ProseMirror a {
  color: #017b3d;
  text-decoration: none;
}

.tiptap-editor .ProseMirror a:hover {
  text-decoration: underline;
}
</style>