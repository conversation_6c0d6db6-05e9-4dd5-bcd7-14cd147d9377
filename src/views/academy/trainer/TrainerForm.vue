<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="formLoading"
      label-position="top"
    >
      <el-form-item :label="t('academy.trainer.trainerName')" prop="name">
        <el-input v-model="formData.name" :placeholder="t('academy.trainer.trainerNamePlaceholder')">
          <template #suffix>
            <Icon icon="ep:more" class="mr-5px cursor-pointer" @click="openUser" v-show="!formData.id" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="t('academy.trainer.trainerType')" prop="type">
        <el-select v-model="formData.type" :placeholder="t('academy.trainer.trainerTypePlaceholder')">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <el-descriptions :column="1" border class="mt-5">
      <el-descriptions-item :label="t('academy.trainer.badge')">
        {{ formData.badgeNumber }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.trainer.email')">
        {{ formData.Email }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.trainer.company')">
        {{ formData.companyName }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.trainer.department')">
        {{ formData.deptName }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.trainer.position')">
        {{ formData.positionName }}
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('academy.trainer.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('academy.trainer.save') }}</el-button>
    </template>
  </el-drawer>

  <!-- 人员选择弹框 -->
  <EmployeeSelect
    ref="selectEmployee"
    @confirm="employeeConfirm"
  />
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TrainerApi,TrainerSaveVO } from '@/api/academy/trainer'
import EmployeeSelect from '@/components/EmployeeExclusiveSelect/index.vue'
/** 教师信息 表单 */
defineOptions({ name: 'TrainerForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  userId: undefined,
  name: undefined,
  type: undefined
})
const formRules = reactive({
  name: [{ required: true, message: t('academy.trainer.trainerNameRequired'), trigger: 'blur' }],
  type: [{ required: true, message: t('academy.trainer.trainerTypeRequired'), trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
const selectEmployee = ref()
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
     const data = await TrainerApi.getTrainer(id)
      formData.value = data
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  if (!formData.value.userId) {
    message.error(t('academy.trainer.pleaseSelectUser'))
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TrainerSaveVO
    if (formType.value === 'create') {
      const { id,userId,type, name } = data
      const dataToSubmit = {
        id,
        userId,
        type,
        name
      }
      await TrainerApi.createTrainer(dataToSubmit)
      message.success(t('common.createSuccess'))
    } else {
      await TrainerApi.updateTrainer(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const openUser = () => {
  selectEmployee.value.open(formData.value.userId)
}

// 确认选择的用户信息
const employeeConfirm = (data: any) => {
  formData.value.name = data.nickname
  formData.value.userId = data.id
  formData.value.positions = data.postNames
  formData.value.company = data.companyName
  formData.value.department = data.deptName
  formData.value.badgeNumber = data.badgeNumber
  formData.value.email = data.email
  selectEmployee.value.show = false
}

const namesString = (data: any) => {
  const names = data.map((item: any) => item.postName).join(',')
  return names
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    userId: undefined,
    type: undefined
  }
  formRef.value?.resetFields()
}
</script>
