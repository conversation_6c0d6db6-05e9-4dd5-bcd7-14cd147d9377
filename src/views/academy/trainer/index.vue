<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="120x"
    >
      <el-form-item :label="t('academy.trainer.trainerType')" prop="type">
        <el-select
          v-model="queryParams.type"
          :placeholder="t('academy.trainer.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.trainer.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
          @node-click="handleCompanyClick"
        />
      </el-form-item>
      <el-form-item :label="t('sys.user.department')" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="departOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.departmentPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="Position" prop="positionId">-->
<!--        <el-select-->
<!--          v-model="queryParams.positionId"-->
<!--          :placeholder="t('sys.user.positionPH')"-->
<!--          clearable-->
<!--          filterable-->
<!--          collapse-tags-->
<!--          collapse-tags-tooltip-->
<!--          :max-collapse-tags="1"-->
<!--          class="!w-240px"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="item in postOptions"-->
<!--            :key="item.postId"-->
<!--            :label="item.postName"-->
<!--            :value="item.postId"-->
<!--          />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item :label="t('academy.trainer.time')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :start-placeholder="t('academy.trainer.startDate')"
          :end-placeholder="t('academy.trainer.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.trainer.trainerName')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="t('academy.trainer.pleaseInput')"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.trainer.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.trainer.reset') }}</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> {{ t('academy.trainer.add') }}
        </el-button>
<!--        暂时注释-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          :disabled="checkTrainer.length === 0"-->
<!--          @click="handleDelete(undefined,2)"-->
<!--        >-->
<!--          <Icon icon="ep:delete" class="mr-5px" /> Delete-->
<!--        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column :label="t('academy.trainer.trainerName')" align="center" min-width="180px" >
        <template #default="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.trainer.badgeNo')" align="center" prop="badgeNumber" min-width="180px" />
      <el-table-column :label="t('academy.trainer.trainerType')" align="center" prop="type" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRAINER_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.trainer.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('academy.trainer.department')" align="center" prop="deptName" min-width="180px" />
      <el-table-column :label="t('academy.trainer.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('academy.trainer.action')" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            {{ t('academy.trainer.edit') }}
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id,1)"
          >
            {{ t('academy.trainer.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TrainerForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {TrainerApi, TrainerRespVO} from '@/api/academy/trainer'
import TrainerForm from './TrainerForm.vue'
import { DeptRespVO, listDept } from "@/api/system/dept"
import { listCompany } from "@/api/system/company"
import { handlePhaseTree } from "@/utils/tree"
import { useUserStore } from "@/store/modules/user"

/** 教师信息 列表 */
defineOptions({ name: 'Trainer' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const userStore = useUserStore()
const list = ref<TrainerRespVO[]>([]) // 列表的数据
const companyList = ref([]) // 公司列表
const postOptions = ref([])
const departOptions = ref()
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  type: undefined,
  createTime: [],
  companyId: undefined,
  deptId: undefined,
})


const queryFormRef = ref() // 搜索的表单
const formRef = ref()
/**  已选择的讲师ID */
const checkTrainer = ref([])
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TrainerApi.getTrainerPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}


// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}
/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}

/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}


/** 选择条数  */
const handleSelectionChange = (selection: TrainerRespVO[]) => {
  checkTrainer.value = []
  selection.forEach(item => {
    checkTrainer.value.push(item.id)
  })
}

/** 删除按钮操作 1.单个 2.批量 */
const handleDelete = async (id: number,type?: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    let idStr
    if (type === 1) {
      idStr = id
    } else {
      idStr = checkTrainer.value.join(',')
    }
    // 发起删除
    await TrainerApi.deleteTrainer(idStr)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}


/** 初始化 **/
onMounted(() => {
  getList()
  getDepartmentTree(userStore.user.companyId)
  getCompanyTree()
})
</script>
