<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="150px"
    >
      <el-form-item :label="t('academy.external.country')" prop="receivingCountry">
        <el-select
          v-model="queryParams.receivingCountry"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in countryOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.external.costBearer')" prop="costBearer">
        <el-select
          v-model="queryParams.costBearer"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in bearerOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.external.travelDate')" prop="travelDate">
        <el-date-picker
          v-model="queryParams.travelDate"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          type="daterange"
          :start-placeholder="t('academy.external.startDate')"
          :end-placeholder="t('academy.external.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.external.returnDate')" prop="returnDate">
        <el-date-picker
          v-model="queryParams.returnDate"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          type="daterange"
          :start-placeholder="t('academy.external.startDate')"
          :end-placeholder="t('academy.external.endDate')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.external.courseTitleEnglish')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="t('common.inputText')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.external.courseTitleArabic')" prop="titleAr">
        <el-input
          v-model="queryParams.titleAr"
          :placeholder="t('common.inputText')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />{{ t('global.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />{{ t('common.reset') }}</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" />{{ t('action.add') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('academy.external.trainingCode')" align="center" prop="code" min-width="180px" />
      <el-table-column :label="t('academy.external.courseTitleEN')" align="center" prop="title" min-width="180px" />
      <el-table-column :label="t('academy.external.courseTitleAR')" align="center" prop="titleAr" min-width="180px" />
      <el-table-column :label="t('academy.external.receivingCountry')" align="center" prop="receivingCountry" min-width="180px" />
      <el-table-column :label="t('academy.external.travelDate')" align="center" prop="travelDate" min-width="180px">
        <template #default="scope">
          {{ formatDateArray(scope.row.travelDate )}}
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.external.returnDate')" align="center" prop="returnDate" min-width="180px">
        <template #default="scope">
          {{ formatDateArray(scope.row.returnDate )}}
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.external.adminNo')" align="center" prop="adminNo" min-width="180px" />
      <el-table-column :label="t('academy.external.costBearer')" align="center" prop="costBearer" min-width="180px" />
      <el-table-column :label="t('academy.external.studentNumber')" align="center" prop="userCount" min-width="180px">
        <template #default="scope">
          <el-link :underline="false" type="primary" @click="handleStudent(scope.row)">{{ scope.row.userCount }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openDetail('view', scope.row.id)"
          >
            {{ t('action.view') }}
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            {{ t('action.edit') }}
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            {{ t('action.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ExternalForm ref="formRef" @success="externalSuccess" />
  <ExternalDetail ref="externalDetailRef" />
  <StudentNumber ref="studentNumberRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { ExternalApi, ExternalRespVO } from '@/api/academy/outsourced/external'
import ExternalForm from './ExternalForm.vue'
import ExternalDetail from './Detail/index.vue'
import StudentNumber from './components/StudentNumber.vue'
import { formatDateArray } from "@/utils/formatDate"
/** 国外培训 列表 */
defineOptions({ name: 'External' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<ExternalRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  titleAr: undefined,
  receivingCountry: undefined,
  travelDate: [],
  returnDate: [],
  costBearer: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const studentNumberRef = ref()
const countryOptions = ref([])
const bearerOptions = ref([])


/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ExternalApi.getExternalPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const getAllList = async () => {
  const data = await ExternalApi.getExternalPage({pageNo: 1, pageSize: -1})
  countryOptions.value = Array.from(new Set(data.list?.map((item: ExternalRespVO) => item.receivingCountry)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
  bearerOptions.value = Array.from(new Set(data.list?.map((item: ExternalRespVO) => item.costBearer)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const externalDetailRef = ref()
const openDetail = (type: string, id?: number) => {
  externalDetailRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ExternalApi.deleteExternal(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

// 查看课程分配用户信息
const handleStudent = (item: ExternalRespVO) => {
  studentNumberRef.value.open(item)
}

const externalSuccess = () => {
  getList()
  getAllList()
}

/** 初始化 **/
onMounted(() => {
  getList()
  getAllList()
})
</script>
