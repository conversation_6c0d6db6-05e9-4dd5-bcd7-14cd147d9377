<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible" size="900">
    <div class="text-[#222222] text-xl mb-5"> {{ t('learningCenter.course.basicInfo') }} </div>
    <el-descriptions :column="1" border class="mt-5" v-loading="formLoading">
      <el-descriptions-item :label="t('academy.external.trainingCode')">
        {{ formData.code }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.external.courseTitleEN')">
        {{ formData.title }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.external.courseTitleAR')">
        {{ formData.titleAr }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.external.travelReturn')">
        {{ formData.travelDate }} - {{ formData.returnDate }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.external.receivingCountryLabel')">
        {{ formData.receivingCountry }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.external.adminNo')">
        {{ formData.adminNo }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.external.constBearer')">
        {{ formData.costBearer }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.external.comments')">
        {{ formData.remark }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="text-[#222222] text-xl mb-5 mt-5">{{ t('academy.external.studentInformation') }}</div>
    <div class="flex items-center">
      <span>{{ t('academy.external.multipleGroups') }}</span>
      <el-switch v-model="multipleGroup" size="large" :active-value="1" :inactive-value="0" class="ms-3 me-3" disabled />
      <span v-show="multipleGroup === 1">{{ t('academy.external.enableStudentsByMultipleGroups') }}</span>
    </div>
    <el-table border :data="formData.userList" :stripe="true" :show-overflow-tooltip="true" class="mt-3">
      <el-table-column :label="t('academy.external.groupNo')" align="center" prop="userGroupNo" min-width="180px" v-if="multipleGroup === 1" />
      <el-table-column :label="t('academy.external.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('academy.external.arabicName')" align="center" prop="userNameAr" min-width="180px" />
      <el-table-column :label="t('academy.external.bocBadgeNo')" align="center" prop="bocBadgeNo" min-width="180px" />
      <el-table-column :label="t('academy.external.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('academy.external.department')" align="center" prop="deptName" min-width="180px" />
    </el-table>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { ExternalApi } from '@/api/academy/outsourced/external'
import { formatDateArray } from "@/utils/formatDate"
/** 国外培训 详情 */
defineOptions({ name: 'ExternalDetail' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  title: undefined,
  titleAr: undefined,
  receivingCountry: undefined,
  travelDate: undefined,
  returnDate: undefined,
  adminNo: undefined,
  costBearer: undefined,
  attachments: undefined,
  remark: undefined,
  userList: [],
  createTime: [],
})
const formRef = ref() // 表单 Ref
const multipleGroup = ref(0)


// 获取国内培训人员分页信息
const getUserInfo = async (externalId: number) => {
  // 获取用户人员信息
  const queryForm = {
    externalId,
    pageNo: 1,
    pageSize: -1
  }
  const res = await ExternalApi.getExternalUserPage(queryForm)
  // 判断如果数组中userGroupNo 只要有值，开关展开
  multipleGroup.value = res.list?.some(item => item.userGroupNo) ? 1 : 0
  formData.value.userList = res.list?.map(item => {
    return {
      externalId: item.externalId,
      userId: item.userId,
      nickname: item.nickname,
      userNameAr: item.userNameAr,
      userGroupNo: item.userGroupNo,
      positionName: item.positionName,
      deptName: item.deptName
    }
  })
}


/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ExternalApi.getExternal(id)
      formData.value.travelDate = formatDateArray(formData.value.travelDate)
      formData.value.returnDate = formatDateArray(formData.value.returnDate)
      // 获取用户人员信息
      await getUserInfo(id as number)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗


/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    title: undefined,
    titleAr: undefined,
    receivingCountry: undefined,
    travelDate: undefined,
    returnDate: undefined,
    adminNo: undefined,
    costBearer: undefined,
    attachments: undefined,
    remark: undefined,
    userList: [],
    createTime: []
  }
  multipleGroup.value = 0
  formRef.value?.resetFields()
}

</script>
