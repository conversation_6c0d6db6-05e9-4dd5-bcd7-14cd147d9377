<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible" size="900">
    <div class="text-[#222222] text-xl mb-5"> {{ t('learningCenter.course.basicInfo') }} </div>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="170px"
      v-loading="formLoading"
    >
      <el-form-item :label="t('academy.external.trainingCode')" prop="code">
        <el-input v-model="formData.code" disabled />
      </el-form-item>
      <el-form-item :label="t('academy.external.courseTitleEN')" prop="title">
        <el-input v-model="formData.title" :placeholder="t('common.inputText')" />
      </el-form-item>
      <el-form-item :label="t('academy.external.courseTitleAR')" prop="titleAr">
        <el-input v-model="formData.titleAr" :placeholder="t('common.inputText')" />
      </el-form-item>
      <el-form-item :label="t('academy.external.travelReturn')" prop="createTime">
        <el-date-picker
          v-model="formData.createTime"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          type="daterange"
          :start-placeholder="t('academy.external.travel')"
          :end-placeholder="t('academy.external.return')"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.external.receivingCountryLabel')" prop="receivingCountry">
        <el-select
          v-model="formData.receivingCountry"
          :placeholder="t('common.selectText')"
          clearable
          allow-create
          filterable
          default-first-option
          :reserve-keyword="false"
          @change="(event) => {
            changeCondition(event,1)
          }"
          @blur="(event) => {
            blurSelectValue(event,1)
          }"
        >
          <el-option
            v-for="dict in countryOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
        <el-form-item :label="t('academy.external.adminNo')" prop="adminNo">
        <el-input v-model="formData.adminNo" :placeholder="t('common.inputText')" />
      </el-form-item>
      <el-form-item :label="t('academy.external.constBearer')" prop="costBearer">
        <el-select
          v-model="formData.costBearer"
          :placeholder="t('common.selectText')"
          clearable
          allow-create
          filterable
          default-first-option
          :reserve-keyword="false"
          @change="(event) => {
            changeCondition(event,2)
          }"
          @blur="(event) => {
            blurSelectValue(event,2)
          }"
        >
          <el-option
            v-for="dict in bearerOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.external.attachment')" prop="attachments">
        <CourseResource :is-from-repository="false" v-model="resourceList" ref="resourceRef" @confirm="confirmFile" class="w-full" :page-name-type="[...PDF]" :ooc="true" :edit="false" />
      </el-form-item>
      <el-form-item :label="t('academy.external.comments')" prop="remark">
        <el-input v-model="formData.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <div class="text-[#222222] text-xl mb-5">{{ t('academy.external.studentInformation') }}</div>
    <div class="flex items-center">
      <span>{{ t('academy.external.multipleGroups') }}</span>
      <el-switch v-model="multipleGroup" size="large" :active-value="1" :inactive-value="0" @change="changeGroup" class="ms-3 me-3" />
      <span v-show="multipleGroup === 1">{{ t('academy.external.enableStudentsByMultipleGroups') }}</span>
    </div>
    <el-button
      type="primary"
      class="mb-2"
      @click="selectUser"
    >
      <Icon icon="ep:plus" class="mr-5px" />{{ t('academy.external.addStudent') }}
    </el-button>
    <el-table border :data="formData.userList" :stripe="true" v-loading="formLoading" :show-overflow-tooltip="true" class="mt-3">
      <el-table-column :label="t('academy.external.groupNo')" align="center" prop="code" min-width="180px" v-if="multipleGroup === 1">
        <template #default="scope">
          <el-input v-model="scope.row.userGroupNo" :placeholder="t('common.inputText')" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.external.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('academy.external.arabicName')" align="center" min-width="180px">
        <template #default="scope">
          <el-input v-model="scope.row.userNameAr" :placeholder="t('common.inputText')" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.external.bocBadgeNo')" align="center" prop="bocBadgeNo" min-width="180px">
        <template #default="scope">
          <el-select v-model="scope.row.bocBadgeNo" :placeholder="t('common.selectText')">
            <el-option
              v-for="item in bocBadgeNoList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.external.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('academy.external.department')" align="center" prop="deptName" min-width="180px" />
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="90px">
        <template #default="scope">
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id,scope.row?.externalId,scope.row.userId)"
          >
            {{ t('action.remove') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('common.save') }}</el-button>
    </template>
  </el-drawer>

  <!-- 人员选择弹框 -->
  <EmployeeSelect
    ref="selectEmployeeRef"
    @confirm="employeeConfirm"
  />

</template>
<script setup lang="ts">
import { ExternalApi, ExternalRespVO, InternalSaveVO } from '@/api/academy/outsourced/external'
import { PDF, PPT, Video } from '@/components/LargeFileUpload/script/FileAllowTypes'
import CourseResource from '@/components/CourseResource/index.vue'
import EmployeeSelect from '@/components/EmployeeMultipleSelect/index.vue'
import { MediaType } from "@/enums/resource"
import { formatDateArray } from "@/utils/formatDate"
import {listUser} from "@/api/system/user";
interface SelectedResource {
  origin: undefined//
  format: string | undefined
  mediaType: MediaType
  lang: string[]
  duration: number
  fileId: number | undefined
  size: number | undefined
  url: string | undefined
  name: string | undefined
  resourceId: number | undefined
}
/** 国外培训 表单 */
defineOptions({ name: 'ExternalForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  title: undefined,
  titleAr: undefined,
  receivingCountry: undefined,
  travelDate: undefined,
  returnDate: undefined,
  adminNo: undefined,
  costBearer: undefined,
  attachments: undefined,
  remark: undefined,
  userList: [],
  createTime: [],
})
const formRules = reactive({
  title: [{ required: true, message: t('academy.external.courseTitleENRequired'), trigger: 'blur' }],
  titleAr: [{ required: true, message: t('academy.external.courseTitleARRequired'), trigger: 'blur' }],
  code: [{ required: true, message: t('academy.external.trainingCodeRequired'), trigger: 'blur' }],
  receivingCountry: [{ required: true, message: t('academy.external.receivingCountryRequired'), trigger: 'change' }],
  createTime: [{ required: true, message: t('academy.external.travelReturnRequired'), trigger: 'blur' }],
  adminNo: [{ required: true, message: t('academy.external.adminNoRequired'), trigger: 'blur' }],
  costBearer: [{ required: true, message: t('academy.external.costBearerRequired'), trigger: 'blur' }]
})
const resourceList = ref<SelectedResource[]>([])
const formRef = ref() // 表单 Ref
const resourceRef = ref()
const countryOptions = ref([])
const bearerOptions = ref([])
const multipleGroup = ref(0)
const selectEmployeeRef = ref()

// type  1.代表要添加的是Country 2.代表要添加的是bearer 公用一个方法,通过type区分
const changeCondition = (val: string, type: number) => {
  val = val.trim()
  if (!val) return

  const isCountry = type === 1
  const options = isCountry ? countryOptions.value : bearerOptions.value
  const exists = options.some(item => item.value === val)

  if (!exists) {
    options.splice(0, 0, { label: val, value: val })
  }
}

// type  1.代表要添加的是Country 2.代表要添加的是bearer 公用一个方法,通过type区分
const blurSelectValue = (val: Event, type: number) => {
  let value = ''
  if (type === 1) {
    // 去除字符串前后的所有空格
    const country = (val.target as HTMLInputElement).value.trim()
    value = country
  } else {
    // 去除字符串前后的所有空格
    const bearer = (val.target as HTMLInputElement).value.trim()
    value = bearer
  }

  if (!value) return

  if (type === 1) {
    const foundPlace = countryOptions.value.some(item => item.value === value)
    // 如果再次添加的值存在，则不进行添加
    if (!foundPlace) {
      countryOptions.value.splice(0, 0, { label: value, value })
      formData.value.receivingCountry = value
    }
  } else {
    const foundCompany = bearerOptions.value.some(item => item.value === value)
    // 如果再次添加的值存在，则不进行添加
    if (!foundCompany) {
      bearerOptions.value.splice(0, 0, { label: value, value })
      formData.value.costBearer = value;
    }
  }
}


const getAllList = async () => {
  const data = await ExternalApi.getExternalPage({pageNo: 1, pageSize: -1})
  countryOptions.value = Array.from(new Set(data.list?.map((item: ExternalRespVO) => item.receivingCountry)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
  bearerOptions.value = Array.from(new Set(data.list?.map((item: ExternalRespVO) => item.costBearer)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
}

// 获取国内培训人员分页信息
const getUserInfo = async (externalId: number) => {
  // 获取用户人员信息
  const queryForm = {
    externalId,
    pageNo: 1,
    pageSize: -1
  }
  const res = await ExternalApi.getExternalUserPage(queryForm)
  // 判断如果数组中userGroupNo 只要有值，开关展开
  multipleGroup.value = res.list?.some(item => item.userGroupNo) ? 1 : 0
  formData.value.userList = res.list?.map(item => {
    return {
      externalId: item.externalId,
      userId: item.userId,
      nickname: item.nickname,
      userNameAr: item.userNameAr,
      userGroupNo: item.userGroupNo,
      positionName: item.positionName,
      deptName: item.deptName,
      id: item.id,
      bocBadgeNo: item.bocBadgeNo
    }
  })
}

// 将对象数组转换为字符串数组格式
const objectToString = (obj: SelectedResource[]) => {
  return '{' + Object.entries(obj).map(([key, value]) => {
    const valStr = typeof value === 'string' ? `'${value}'` : String(value);
    return `${key}: ${valStr}`
  }).join(', ') + '}'
}

// 将字符串数组转换为对象数组格式
const parseCustomObjectArray = (rawAttachments) => {
  let attachments = []

  try {
    // 第一步：先解析最外层 JSON（即字符串数组）
    const stringArray = JSON.parse(rawAttachments)

    // 第二步：遍历每个字符串并转换为合法 JSON 字符串再解析
    attachments = stringArray.map(str => {
      // 替换键为合法格式："key"
      str = str.replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":')

      // 替换单引号值为双引号值
      str = str.replace(/:\s*'([^']*)'/g, ': "$1"')

      // 替换 undefined 为 null
      str = str.replace(/:\s*undefined/gi, ': null')

      // 删除最后可能出现的逗号（如 {a:1, b:2,} 这种非法结尾）
      str = str.replace(/,\s*}/g, '}').replace(/,\s*]/g, ']')

      return JSON.parse(str);
    });
  } catch (e) {
  }

  return attachments
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  await getAllList()
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ExternalApi.getExternal(id)
      const travelDate = formatDateArray(formData.value.travelDate)
      const returnDate = formatDateArray(formData.value.returnDate)
      if (formData.value.attachments === '[]') {
        formData.value.attachments = undefined
      }
      formData.value.attachments = parseCustomObjectArray(formData.value?.attachments)
      if (formData.value.attachments && formData.value.attachments.length > 0) {
        // 文件信息回显
        resourceList.value = formData.value.attachments
      }
      formData.value.createTime = [travelDate, returnDate]
      // 获取用户人员信息
      await getUserInfo(id as number)
    } finally {
      formLoading.value = false
    }
  }
  if (formType.value === 'create') {
    // 获取培训编号
    const data = await ExternalApi.getCode()
    formData.value.code = `MJN-EXT-${data}`
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 确定的文件信息
const confirmFile = (file: SelectedResource) => {
  formData.value.attachments = file
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InternalSaveVO
    // 转换为yyyy-MM-dd格式
    data.travelDate = formData.value.createTime[0].replace(/\//g, '-')
    data.returnDate = formData.value.createTime[1].replace(/\//g, '-')
    // 保存编辑时将数据统一转换为字符串数组格式
    data.attachments = formData.value?.attachments?.map(objectToString)
    if (formType.value === 'create') {
      await ExternalApi.createExternal(data)
      message.success(t('common.createSuccess'))
    } else {
      await ExternalApi.updateExternal(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    title: undefined,
    titleAr: undefined,
    receivingCountry: undefined,
    travelDate: undefined,
    returnDate: undefined,
    adminNo: undefined,
    costBearer: undefined,
    attachments: undefined,
    remark: undefined,
    userList: [],
    createTime: []
  }
  multipleGroup.value = 0
  resourceList.value = []
  formRef.value?.resetFields()
}

// 选择用户
const selectUser = () => {
  const ids = formData.value.userList?.map((item) => item.userId)
  selectEmployeeRef.value.open(ids)
}

// 确认用户
const employeeConfirm = (data: any) => {
  // 拿到数据先组装一下需要传递给后端的数据
  const userList = data.map((item) => {
    return {
      userId: item.id,
      nickname: item.nickname,
      positionName: item.positionName,
      deptName: item.deptName
    }
  })

  // 根据已绑定的用户人员信息根据userId去重(为的是存储住internalId 参数，如果直接把选择好的人员信息给到formData.value.internalUsers就不会有internalId这个参数)
  const map = new Map(formData.value.userList?.map((item) => [item.userId, item]))
  for (const user of userList) {
    if (!map.has(user.userId)) {
      map.set(user.userId, user)
    }
  }
  formData.value.userList = Array.from(map.values())
}

const changeGroup = (val: number | string) => {
  if (val === 0) {
    // 清空用户组
    formData.value.userList.forEach(item => item.userGroupNo = null)
  }
}

// 删除用户 externalId：培训id
const handleDelete = async (id: number, externalId: number,userId: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 证明当前数据是已绑定过课程会有一个唯一id值，externalId 可以直接调用删除接口
  if (externalId) {
    await ExternalApi.deleteExternalUser(id)
    await getUserInfo(externalId as number)
  } else {
    // 根据userId删除用户 (新选择的人不存在培训externalId)
    formData.value.userList = formData.value.userList.filter(item => item.userId !== userId)
  }
  message.success(t('common.delSuccess'))
}

const bocBadgeNoList = ref([])
// 获取用户信息
const getUserList = async () => {
  const data = await listUser({ pageNum: 1, pageSize: 999999,})
  bocBadgeNoList.value = data.list.map(item => {
    return {
      value: item.badgeNumber,
      label: item.badgeNumber,
    }
  })
}
onMounted(() => {
  getUserList()
})

</script>
