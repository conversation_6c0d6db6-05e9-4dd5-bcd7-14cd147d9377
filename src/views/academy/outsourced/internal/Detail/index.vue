<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible" size="900">
    <div class="text-[#222222] text-xl mb-5"> {{ t('learningCenter.course.basicInfo') }} </div>
    <el-descriptions :column="1" border class="mt-5" v-loading="formLoading">
      <el-descriptions-item :label="t('academy.internal.form.trainingCode')">
        {{ formData.code }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.internal.form.courseTitleEN')">
        {{ formData.title }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.internal.form.courseTitleAR')">
        {{ formData.titleAr }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.internal.form.trainingType')">
        <dict-tag :type="DICT_TYPE.INTERNAL_TRAINING_TYPE" :value="formData.type" />
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.internal.form.place')">
        {{ formData.place }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.internal.form.startEndTime')">
        {{ formData.startDate }} - {{ formData.endDate }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.internal.form.duration')">
        {{ formData?.duration ? formData?.duration : 0 }} {{ t('academy.internal.days') }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.internal.form.implementingCompany')">
        {{ formData.implementingCompany }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.internal.form.comments')">
        {{ formData.remark }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="text-[#222222] text-xl mb-5 mt-5">{{ t('academy.internal.form.studentInformation') }}</div>
    <div class="flex items-center">
      <span>{{ t('academy.internal.form.multipleGroups') }}</span>
      <el-switch v-model="multipleGroup" size="large" :active-value="1" :inactive-value="0" class="ms-3 me-3" disabled />
      <span v-show="multipleGroup === 1">{{ t('academy.internal.form.enableStudentsByMultipleGroups') }}</span>
    </div>
    <el-table border :data="formData.internalUsers" :stripe="true" :show-overflow-tooltip="true" class="mt-3">
      <el-table-column :label="t('academy.internal.form.groupNo')" align="center" prop="userGroupNo" min-width="180px" v-if="multipleGroup === 1" />
      <el-table-column :label="t('academy.internal.form.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('academy.internal.form.arabicName')" align="center" prop="userNameAr" min-width="180px" />
      <el-table-column :label="t('academy.internal.form.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('academy.internal.form.notes')" align="center" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INTERNAL_USER_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </el-drawer>

</template>
<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { InternalApi } from '@/api/academy/outsourced/internal'
import { formatDateArray } from "@/utils/formatDate"
/** 国内培训 详情 */
defineOptions({ name: 'InternalDetail' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  title: undefined,
  titleAr: undefined,
  type: undefined,
  place: undefined,
  startDate: undefined,
  endDate: undefined,
  duration: undefined,
  implementingCompany: undefined,
  attachments: [],
  remark: undefined,
  createTime: [],
  internalUsers: []
})
const formRef = ref() // 表单 Ref
const multipleGroup = ref(0)
const list = ref([]) // 用户信息


// 获取国内培训人员分页信息
const getUserInfo = async (internalId: number) => {
  // 获取用户人员信息
  const queryForm = {
    internalId,
    pageNo: 1,
    pageSize: -1
  }
  const data = await InternalApi.getInternalUserPage(queryForm)
  // 判断如果数组中userGroupNo 只要有值，开关展开
  multipleGroup.value = data.list?.some(item => item.userGroupNo) ? 1 : 0
  formData.value.internalUsers = data.list.map(item => {
    return {
      internalId: item.internalId,
      userId: item.userId,
      nickname: item.nickname,
      userNameAr: item.userNameAr,
      userGroupNo: item.userGroupNo,
      status: item.status,
      positionName: item.positionName
    }
  })
}


/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InternalApi.getInternal(id)
      formData.value.startDate = formatDateArray(formData.value.startDate)
      formData.value.endDate = formatDateArray(formData.value.endDate)
      // 获取用户人员信息
      await getUserInfo(id as number)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗


/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    title: undefined,
    titleAr: undefined,
    type: undefined,
    place: undefined,
    startDate: undefined,
    endDate: undefined,
    duration: undefined,
    implementingCompany: undefined,
    attachments: [],
    remark: undefined,
    createTime: [],
    internalUsers: []
  }
  multipleGroup.value = 0
  formRef.value?.resetFields()
}
</script>
