<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="150px"
    >
      <el-form-item :label="t('academy.internal.type')" prop="type">
        <el-select
          v-model="queryParams.type"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INTERNAL_TRAINING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.internal.place')" prop="place">
        <el-select
          v-model="queryParams.place"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in placeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.internal.company')" prop="implementingCompany">
        <el-select
          v-model="queryParams.implementingCompany"
          :placeholder="t('common.selectText')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in companyOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.internal.time')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          :start-placeholder="t('common.start')"
          :end-placeholder="t('common.end')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.internal.courseTitleEnglish')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="t('common.pleaseInput')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.internal.courseTitleArabic')" prop="titleAr">
        <el-input
          v-model="queryParams.titleAr"
          :placeholder="t('common.pleaseInput')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('global.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('global.reset') }}</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> {{ t('global.add') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('academy.internal.trainingCode')" align="center" prop="code" min-width="180px" />
      <el-table-column :label="t('academy.internal.courseTitleEN')" align="center" prop="title" min-width="180px" />
      <el-table-column :label="t('academy.internal.courseTitleAR')" align="center" prop="titleAr" min-width="180px" />
      <el-table-column :label="t('academy.internal.type')" align="center" prop="type" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INTERNAL_TRAINING_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.internal.place')" align="center" prop="place" min-width="180px" />
      <el-table-column
        :label="t('academy.internal.startEndDate')"
        align="center"
        :formatter="dateFormatter"
        width="200px">
        <template #default="scope">
          {{ scope.row.startDate }} - {{ scope.row.endDate }}
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.internal.company')" align="center" prop="implementingCompany" min-width="180px" />
      <el-table-column :label="t('academy.internal.courseDuration')" align="center" prop="duration" min-width="180px">
        <template #default="scope">
          {{ scope.row.duration }} {{ t('academy.internal.days') }}
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.internal.studentNumber')" align="center" prop="userCount" min-width="180px">
        <template #default="scope">
          <el-link :underline="false" type="primary" @click="handleStudent(scope.row)">{{ scope.row.userCount }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openDetail('view', scope.row.id)"
          >
            {{ t('action.view') }}
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            {{ t('global.edit') }}
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            {{ t('global.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InternalForm ref="formRef" @success="internalSuccess" />
  <!-- 详情弹窗 -->
  <InternalDetail ref="internalDetailRef" />
  <StudentNumber ref="studentNumberRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InternalApi, InternalConditionReqVO, InternalRespVO } from '@/api/academy/outsourced/internal'
import InternalForm from './InternalForm.vue'
import InternalDetail from './Detail/index.vue'
import StudentNumber from './components/StudentNumber.vue'
import { formatDateArray } from "@/utils/formatDate"
/** 国内培训 列表 */
defineOptions({ name: 'Internal' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<InternalRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  titleAr: undefined,
  type: undefined,
  startTime: [],
  endTime: [],
  implementingCompany: undefined,
  place: undefined,
})
const studentNumberRef = ref()
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const placeOptions = ref([])
const companyOptions = ref([])
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InternalApi.getInternalPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const getAllList = async () => {
  const data = await InternalApi.getInternalPage({pageNo: 1, pageSize: -1})
  placeOptions.value = Array.from(new Set(data.list?.map((item: InternalRespVO) => item.place)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
  companyOptions.value = Array.from(new Set(data.list?.map((item: InternalRespVO) => item.implementingCompany)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const internalDetailRef = ref()
const openDetail = (type: string, id?: number) => {
  internalDetailRef.value.open(type, id)
}


/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InternalApi.deleteInternal(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

// 查看课程分配用户信息
const handleStudent = (item: InternalRespVO) => {
  studentNumberRef.value.open(item)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InternalApi.exportInternal(queryParams)
    download.excel(data, 'Internal.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const internalSuccess = () => {
  getList()
  getAllList()
}

/** 初始化 **/
onMounted(() => {
  getList()
  getAllList()
})
</script>
