<template>
  <el-drawer :title="t('academy.internal.studentList')" v-model="dialogVisible" size="600">
    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column :label="t('academy.internal.form.name')" align="center" prop="nickname" min-width="180px" />
        <el-table-column :label="t('academy.internal.form.arabicName')" align="center" prop="userNameAr" min-width="180px" />
        <el-table-column :label="t('academy.internal.form.position')" align="center" prop="positionName" min-width="180px" />
        <el-table-column :label="t('academy.internal.notes')" align="center" prop="status" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.INTERNAL_USER_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="90px">
          <template #default="scope">
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row.id)"
            >
              {{ t('action.remove') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { InternalApi, InternalRespVO } from '@/api/academy/outsourced/internal'

/** 国内分配人员 列表 */
defineOptions({ name: 'InternalStudentNumber' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<InternalRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  internalId: undefined,
})
const dialogVisible = ref(false)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InternalApi.getInternalUserPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InternalApi.deleteInternalUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
    // 发送操作成功的事件
    emit('success')
  } catch {}
}

const resetForm = () => {
  queryParams.pageNo = 1
  getList()
}

const open = (data: InternalRespVO) => {
  queryParams.internalId = data.id
  resetForm()
  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
