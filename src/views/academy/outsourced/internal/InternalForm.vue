<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible" size="900">
    <div class="text-[#222222] text-xl mb-5"> {{ t('learningCenter.course.basicInfo') }} </div>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="190px"
      v-loading="formLoading"
    >
      <el-form-item :label="t('academy.internal.form.trainingCode')" prop="code">
        <el-input v-model="formData.code" disabled />
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.courseTitleEN')" prop="title">
        <el-input v-model="formData.title" :placeholder="t('common.pleaseInput')" />
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.courseTitleAR')" prop="titleAr">
        <el-input v-model="formData.titleAr" :placeholder="t('common.pleaseInput')" />
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.trainingType')" prop="type">
        <el-select v-model="formData.type" :placeholder="t('common.selectText')">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.INTERNAL_TRAINING_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.place')" prop="place">
        <el-select
          v-model="formData.place"
          :placeholder="t('common.selectText')"
          clearable
          allow-create
          filterable
          default-first-option
          :reserve-keyword="false"
          @change="(event) => {
            changeCondition(event,1)
          }"
          @blur="(event) => {
            blurSelectValue(event,1)
          }"
        >
          <el-option
            v-for="dict in placeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.startEndTime')" prop="createTime">
        <el-date-picker
          v-model="formData.createTime"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          type="daterange"
          :start-placeholder="t('academy.internal.form.start')"
          :end-placeholder="t('academy.internal.form.end')"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
          @change="handleTime"
        />
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.duration')" prop="duration">
        <el-text type="info">
          {{ formData?.duration ? formData?.duration : 0 }} {{ t('academy.internal.days') }}
        </el-text>
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.implementingCompany')" prop="implementingCompany">
        <el-select
          v-model="formData.implementingCompany"
          :placeholder="t('common.selectText')"
          allow-create
          filterable
          default-first-option
          :reserve-keyword="false"
          @change="(event) => {
            changeCondition(event,2)
          }"
          @blur="(event) => {
            blurSelectValue(event,2)
          }"
        >
          <el-option
            v-for="dict in companyOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.attachment')">
        <CourseResource :is-from-repository="false" v-model="resourceList" ref="resourceRef" @confirm="confirmFile" class="w-full" :page-name-type="[...PDF]" :ooc="true" :edit="false" />
      </el-form-item>
      <el-form-item :label="t('academy.internal.form.comments')">
        <el-input v-model="formData.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <div class="text-[#222222] text-xl mb-5"> {{ t('academy.internal.form.studentInformation') }}</div>
    <div class="flex" style="align-items: center">
      <span>{{ t('academy.internal.form.multipleGroups') }}</span>
      <el-switch v-model="multipleGroup" size="large" :active-value="1" :inactive-value="0" @change="changeGroup" class="ms-3 me-3" />
      <span v-show="multipleGroup === 1">{{ t('academy.internal.form.enableStudentsByMultipleGroups') }}</span>
    </div>
    <el-button
      type="primary"
      class="mb-2"
      @click="selectUser"
    >
      <Icon icon="ep:plus" class="mr-5px" /> {{ t('academy.internal.form.addStudent') }}
    </el-button>
    <el-table border :data="formData.internalUsers" :stripe="true" v-loading="formLoading" :show-overflow-tooltip="true" class="mt-3">
      <el-table-column :label="t('academy.internal.form.groupNo')" align="center" prop="code" min-width="180px" v-if="multipleGroup === 1">
        <template #default="scope">
          <el-input v-model="scope.row.userGroupNo" :placeholder="t('common.pleaseInput')" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.internal.form.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('academy.internal.form.arabicName')" align="center" min-width="180px">
        <template #default="scope">
          <el-input v-model="scope.row.userNameAr" :placeholder="t('common.pleaseInput')" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.internal.form.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('academy.internal.form.notes')" align="center" min-width="180px">
        <template #default="scope">
          <el-select v-model="scope.row.status" :placeholder="t('common.selectText')">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.INTERNAL_USER_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.internal.form.action')" align="center" fixed="right" min-width="90px">
        <template #default="scope">
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id,scope.row?.internalId,scope.row.userId)"
          >
            {{ t('academy.internal.form.remove') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="dialogVisible = false"> {{ t('common.cancel') }} </el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading"> {{ t('common.save') }} </el-button>
    </template>
  </el-drawer>

  <!-- 人员选择弹框 -->
  <EmployeeSelect
    ref="selectEmployeeRef"
    @confirm="employeeConfirm"
  />
</template>
<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { InternalApi, InternalRespVO, InternalSaveVO } from '@/api/academy/outsourced/internal'
import EmployeeSelect from '@/components/EmployeeMultipleSelect/index.vue'
import { PDF, PPT } from '@/components/LargeFileUpload/script/FileAllowTypes'
import CourseResource from '@/components/CourseResource/index.vue'
import { MediaType } from "@/enums/resource"
import { formatDateArray } from "@/utils/formatDate"
interface SelectedResource {
  origin: undefined//
  format: string | undefined
  mediaType: MediaType
  lang: string[]
  duration: number
  fileId: number | undefined
  size: number | undefined
  url: string | undefined
  name: string | undefined
  resourceId: number | undefined
}
/** 国内培训 表单 */
defineOptions({ name: 'InternalForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  title: undefined,
  titleAr: undefined,
  type: undefined,
  place: undefined,
  startDate: undefined,
  endDate: undefined,
  duration: undefined,
  implementingCompany: undefined,
  attachments: [],
  remark: undefined,
  createTime: [],
  internalUsers: []
})
const formRules = reactive({
  title: [{ required: true, message: t('academy.internal.form.courseTitleENRequired'), trigger: 'blur' }],
  titleAr: [{ required: true, message: t('academy.internal.form.courseTitleARRequired'), trigger: 'blur' }],
  code: [{ required: true, message: t('academy.internal.form.trainingCodeRequired'), trigger: 'blur' }],
  type: [{ required: true, message: t('academy.internal.form.trainingTypeRequired'), trigger: 'change' }],
  place: [{ required: true, message: t('academy.internal.form.placeRequired'), trigger: 'change' }],
  createTime: [{ required: true, message: t('academy.internal.form.startEndTimeRequired'), trigger: 'blur' }],
  duration: [{ required: true, message: t('academy.internal.form.durationRequired'), trigger: 'blur' }],
  implementingCompany: [{ required: true, message: t('academy.internal.form.implementingCompanyRequired'), trigger: 'change' }]
})
const resourceList = ref<SelectedResource[]>([])
const placeOptions = ref([])
const companyOptions = ref([])
const formRef = ref() // 表单 Ref
const resourceRef = ref()
const multipleGroup = ref(0)
const list = ref([]) // 用户信息
const selectEmployeeRef = ref()

// 获取国内培训人员分页信息
const getUserInfo = async (internalId: number) => {
  // 获取用户人员信息
  const queryForm = {
    internalId,
    pageNo: 1,
    pageSize: -1
  }
  const data = await InternalApi.getInternalUserPage(queryForm)
  // 判断如果数组中userGroupNo 只要有值，开关展开
  multipleGroup.value = data.list?.some(item => item.userGroupNo) ? 1 : 0
  formData.value.internalUsers = data.list.map(item => {
    return {
      internalId: item.internalId,
      userId: item.userId,
      nickname: item.nickname,
      userNameAr: item.userNameAr,
      userGroupNo: item.userGroupNo,
      status: item.status,
      positionName: item.positionName,
      id: item.id
    }
  })
}
// type  1.代表要添加的是place 2.代表要添加的是company 公用一个方法,通过type区分
const changeCondition = (val: string, type: number) => {
  val = val.trim()
  if (!val) return

  const isPlace = type === 1
  const options = isPlace ? placeOptions.value : companyOptions.value
  const exists = options.some(item => item.value === val)

  if (!exists) {
    options.splice(0, 0, { label: val, value: val })
  }
}

// type  1.代表要添加的是place 2.代表要添加的是company 公用一个方法,通过type区分
const blurSelectValue = (val: Event, type: number) => {
  let value = ''
  if (type === 1) {
    // 去除字符串前后的所有空格
    const place = (val.target as HTMLInputElement).value.trim()
    value = place
  } else {
    // 去除字符串前后的所有空格
    const company = (val.target as HTMLInputElement).value.trim()
    value = company
  }

  if (!value) return

  if (type === 1) {
    const foundPlace = placeOptions.value.some(item => item.value === value)
    // 如果再次添加的值存在，则不进行添加
    if (!foundPlace) {
      placeOptions.value.splice(0, 0, { label: value, value })
      formData.value.place = value
    }
  } else {
    const foundCompany = companyOptions.value.some(item => item.value === value)
    // 如果再次添加的值存在，则不进行添加
    if (!foundCompany) {
      companyOptions.value.splice(0, 0, { label: value, value })
      formData.value.implementingCompany = value;
    }
  }
}


const getAllList = async () => {
  const data = await InternalApi.getInternalPage({pageNo: 1, pageSize: -1})
  // 去重操作 调用列表分页,将所有的培训地点和公司取出来当组装成下拉条件
  placeOptions.value = Array.from(new Set(data.list?.map((item: InternalRespVO) => item.place)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
  companyOptions.value = Array.from(new Set(data.list?.map((item: InternalRespVO) => item.implementingCompany)))
    .filter(Boolean)
    .map(item => ({ label: item, value: item }))
}

// 将对象数组转换为字符串数组格式
const objectToString = (obj: SelectedResource[]) => {
  return '{' + Object.entries(obj).map(([key, value]) => {
    const valStr = typeof value === 'string' ? `'${value}'` : String(value);
    return `${key}: ${valStr}`
  }).join(', ') + '}'
}

// 将字符串数组转换为对象数组格式
const parseCustomObjectArray = (rawAttachments) => {
  let attachments = []

  try {
    // 第一步：先解析最外层 JSON（即字符串数组）
    const stringArray = JSON.parse(rawAttachments)

    // 第二步：遍历每个字符串并转换为合法 JSON 字符串再解析
    attachments = stringArray.map(str => {
      // 替换键为合法格式："key"
      str = str.replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":')

      // 替换单引号值为双引号值
      str = str.replace(/:\s*'([^']*)'/g, ': "$1"')

      // 替换 undefined 为 null
      str = str.replace(/:\s*undefined/gi, ': null')

      // 删除最后可能出现的逗号（如 {a:1, b:2,} 这种非法结尾）
      str = str.replace(/,\s*}/g, '}').replace(/,\s*]/g, ']')

      return JSON.parse(str);
    });
  } catch (e) {
  }

  return attachments
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  await getAllList()
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InternalApi.getInternal(id)
      if (formData.value.attachments === '[]') {
        formData.value.attachments = []
      }
      formData.value.attachments = parseCustomObjectArray(formData.value?.attachments)
      if (formData.value.attachments && formData.value.attachments.length > 0) {
        // 文件信息回显
        resourceList.value = formData.value.attachments
      }
      const startDate = formatDateArray(formData.value.startDate)
      const endDate = formatDateArray(formData.value.endDate)
      formData.value.createTime = [startDate, endDate]
      // 获取用户人员信息
      await getUserInfo(id as number)
    } finally {
      formLoading.value = false
    }
  }
  if (formType.value === 'create') {
    // 获取培训编号
    const data = await InternalApi.getCode()
    formData.value.code = `MJN-INT-${data}`
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 确定的文件信息
const confirmFile = (file: SelectedResource) => {
  formData.value.attachments = file
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InternalSaveVO
    data.startDate = formData.value.createTime[0].replace(/\//g, '-')
    data.endDate = formData.value.createTime[1].replace(/\//g, '-')
    // 保存编辑时将数据统一转换为字符串数组格式
    data.attachments = formData.value?.attachments?.map(objectToString)
    if (formType.value === 'create') {
      await InternalApi.createInternal(data)
      message.success(t('common.createSuccess'))
    } else {
      await InternalApi.updateInternal(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const getDateString = (date) => {
  return date.toISOString().split('T')[0];
}
const handleTime = (value) => {
  // 计算相差几天
  const startTime = new Date(value[0])
  const endTime = new Date(value[1])

  // 清除时间部分，只保留日期
  startTime.setHours(0, 0, 0, 0)
  endTime.setHours(0, 0, 0, 0)

  // 计算时间差（毫秒）
  const diffTime = Math.abs(endTime - startTime)

  // 转换为天数差
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  formData.value.duration =  diffDays + 1
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    title: undefined,
    titleAr: undefined,
    type: undefined,
    place: undefined,
    startDate: undefined,
    endDate: undefined,
    duration: undefined,
    implementingCompany: undefined,
    attachments: [],
    remark: undefined,
    createTime: [],
    internalUsers: []
  }
  multipleGroup.value = 0
  resourceList.value = []
  formRef.value?.resetFields()
}

// 选择用户
const selectUser = () => {
  const ids = formData.value.internalUsers?.map((item) => item.userId)
  selectEmployeeRef.value.open(ids)
}

// 确认用户
const employeeConfirm = (data: any) => {
  // 拿到数据先组装一下需要传递给后端的数据
  const userList = data.map((item) => {
    return {
      userId: item.id,
      nickname: item.nickname,
      positionName: item.positionName,
      // 默认赋值
      status: 1,
    }
  })
  // 根据已绑定的用户人员信息根据userId去重(为的是存储住internalId 参数，如果直接把选择好的人员信息给到formData.value.internalUsers就不会有internalId这个参数)
  const map = new Map(formData.value.internalUsers?.map((item) => [item.userId, item]))
  for (const user of userList) {
    if (!map.has(user.userId)) {
      map.set(user.userId, user)
    }
  }
  formData.value.internalUsers = Array.from(map.values())
}


const changeGroup = (val: number | string) => {
  if (val === 0) {
    // 清空用户组
    formData.value.internalUsers.forEach(item => item.userGroupNo = null)
  }
}
// 删除用户 internalId：培训id
const handleDelete = async (id: number, internalId: number,userId: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 证明当前数据是已绑定过课程会有一个唯一id值，externalId 可以直接调用删除接口
  if (internalId) {
    await InternalApi.deleteInternalUser(id)
    await getUserInfo(internalId as number)
  } else {
    // 根据userId删除用户 (新选择的人不存在培训externalId)
    formData.value.internalUsers = formData.value.internalUsers.filter(item => item.userId !== userId)
  }
  message.success(t('common.delSuccess'))
}
</script>
