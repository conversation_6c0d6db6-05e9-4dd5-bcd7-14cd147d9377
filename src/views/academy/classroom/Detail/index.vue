<template>
  <el-drawer :title="dialogTitle" v-model="dialogVisible">
    <el-descriptions :column="1" border class="mt-5" v-loading="formLoading">
      <el-descriptions-item :label="t('academy.classroom.name')">
        {{ formData.name }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.classroom.location')">
        <dict-tag :type="DICT_TYPE.CLASSROOM_LOCATION" :value="formData.location" />
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.classroom.roomNumber')">
        {{ formData.roomNumber }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.classroom.totalSeats')">
        {{ formData.totalSeats }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('academy.classroom.description')">
        {{ formData.description }}
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-button @click="dialogVisible = false">Cancel</el-button>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassRoomApi } from '@/api/academy/classroom'
/** 教室信息 详情 */
defineOptions({ name: 'ClassRoomFormDetail' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改 view - 查看
const formData = ref({
  id: undefined,
  name: undefined,
  roomNumber: undefined,
  location: undefined,
  picture: undefined,
  totalSeats: undefined,
  description: undefined
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ClassRoomApi.getClassRoom(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    roomNumber: undefined,
    location: undefined,
    picture: undefined,
    totalSeats: undefined,
    description: undefined
  }
  formRef.value?.resetFields()
}
</script>
