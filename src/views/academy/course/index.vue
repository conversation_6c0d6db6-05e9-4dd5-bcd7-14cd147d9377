<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item :label="t('academy.course.category')" prop="categoryId">
        <el-tree-select
          v-model="queryParams.categoryId"
          :data="categoryList"
          :props="defaultProps"
          filterable
          clearable
          check-strictly
          default-expand-all
          value-key="id"
          :placeholder="t('academy.course.categoryPlaceholder')"
          class="!w-240px"
          @focus="changeCategory"
        />
      </el-form-item>
      <el-form-item :label="t('academy.course.language')" prop="languages">
        <el-select
          v-model="queryParams.languages"
          :placeholder="t('academy.course.languagePlaceholder')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.course.trainerType')" prop="trainerType">
        <el-select
          v-model="queryParams.trainerType"
          :placeholder="t('academy.course.trainerTypePlaceholder')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.TRAINER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.course.courseTitle')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="t('academy.course.courseTitlePlaceholder')"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />{{ t('academy.course.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />{{ t('academy.course.reset') }}</el-button>
        <el-button
          type="primary"
          plain
          v-hasPermi="['academy:course:add']"
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" />{{ t('academy.course.add') }}
        </el-button>
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          :disabled="checkCourse.length === 0"-->
<!--          @click="handleDelete(undefined,2)"-->
<!--        >-->
<!--          <Icon icon="ep:delete" class="mr-5px" />{{ t('academy.course.delete') }}-->
<!--        </el-button>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="50" fixed="left" />-->
      <el-table-column :label="t('academy.course.courseTitle')" align="center" prop="title" min-width="180px" />
      <el-table-column :label="t('academy.course.courseCode')" align="center" prop="code" min-width="180px">
        <template #default="scope">
          MJN-{{ getCode(scope.row.categoryFullPath) }}-{{ scope.row.code }}
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.course.category')" align="center" prop="categoryFullPath" min-width="180px" />
      <el-table-column :label="t('academy.course.language')" align="center" prop="language" min-width="180px">
        <template #default="scope">
          <el-tag v-for="item in scope.row.languageStr" :key="item" type="primary" class="me-1">
            {{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.course.trainerType')" align="center" prop="trainerType" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.TRAINER_TYPE" :value="scope.row.trainerType" min-width="180px" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.course.approvals')" align="center" min-width="180px">
        <template #default="scope">
          {{ scope.row.isApprovals ? t('academy.course.yes') : t('academy.course.no') }}
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.course.validity')" align="center" prop="validity" min-width="180px">
        <template #default="scope">
          {{ scope.row.validity }} {{ t('academy.course.months') }}
        </template>
      </el-table-column>
<!--        应测试人员需求，需要加入按钮权限，假如本地启动项目调用的是开发环境，界面不展示按钮证明开发环境没有加按钮的权限标识，去菜单模块那去加一下即可-->
      <el-table-column :label="t('academy.course.action')" align="center" fixed="right" min-width="240px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            v-hasPermi="['academy:course:assign']"
            @click="openStudent(scope.row.id)"
          >
            {{ t('academy.course.assign') }}
          </el-button>
          <el-button
            link
            type="primary"
            v-hasPermi="['academy:course:edit']"
            @click="openForm('update', scope.row.id)"
          >
            {{ t('academy.course.edit') }}
          </el-button>
          <el-button
            link
            type="primary"
            v-hasPermi="['academy:course:copy']"
            @click="handleCopy(scope.row.id)"
          >
            {{ t('academy.course.copy') }}
          </el-button>
          <el-button
            link
            type="danger"
            v-hasPermi="['academy:course:delete']"
            @click="handleDelete(scope.row.id,1)"
          >
            {{ t('academy.course.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CourseForm ref="formRef" @success="getList" />

  <Dialog v-model="showCourse" :title="t('academy.course.newCourseTitle')">
    <el-form
      ref="formCourseRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item :label="t('academy.course.courseTitle')" prop="title">
        <el-input v-model="formData.title" :placeholder="t('academy.course.courseTitlePlaceholder')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showCourse = false">{{ t('academy.course.cancel') }}</el-button>
      <el-button type="primary" :loading="formLoading" @click="submitForm">{{ t('academy.course.confirm') }}</el-button>
    </template>
  </Dialog>

  <!--  选择人员-->
  <el-drawer v-model="showStudent" :size="800" :title="t('academy.course.selectStudents')">
<!--    <el-row :gutter="20">-->
<!--      <el-col :span="24">-->
<!--        <el-radio-group id="exam" v-model="formStudentData.scope" name="exam" @change="changeScope">-->
<!--          <el-radio name="examTrue" :value="1">-->
<!--            All Company Employees-->
<!--          </el-radio>-->
<!--          <el-radio name="examFalse" :value="2">-->
<!--            Specific Employees-->
<!--          </el-radio>-->
<!--        </el-radio-group>-->
<!--      </el-col>-->
<!--      <el-col :span="24" v-show="formStudentData.scope === 2">-->
<!--        <el-input placeholder="Please select the scope" disabled>-->
<!--          <template #suffix>-->
<!--            <Icon icon="ep:more" class="mr-5px cursor-pointer" @click="openScope" />-->
<!--          </template>-->
<!--        </el-input>-->
<!--      </el-col>-->
<!--    </el-row>-->
    <ScopeSelect
      ref="scopeRef"
      v-model="scopeList"
      v-loading="loading"
      :employee-table-props="{
          height: 420,
        }"
      :show-type="false"
      :confirm-loading="confirmLoading"
      @confirm="handleConfirm"
      @delete="handleStudentDelete"
    />
  </el-drawer>

</template>

<script setup lang="ts">
import { CourseApi, CourseRespVO, CourseSaveVO } from '@/api/academy/course'
import CourseForm from './CourseForm.vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listTopic } from "@/api/category/training"
import { handlePhaseTree, handleTree, defaultProps } from "@/utils/tree"
import { NodeType } from "@/components/ScopeSelect/enums/NodeType"
import { deptTreeSelect, listUser} from "@/api/system/user"
import { listCompany } from "@/api/system/company"
import { ScopeConfirm, ScopeData } from "@/components/ScopeSelect/typings"
import { CourseScopeVO } from "@/api/topicMgt/elearning"
import ScopeSelect from '@/components/ScopeSelect/index.vue'
import type { CourseScope } from '@/typings/views/topicMgt/elearning'
/** 学院课程信息 列表 */
defineOptions({ name: 'TrainingCourse' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const scopeRef = ref<InstanceType<typeof ScopeSelect>>()
const loading = ref(false) // 列表的加载中
const confirmLoading = ref(false)
const list = ref<CourseRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  categoryId: undefined,
  languages: undefined,
  trainerType: undefined
})
const formData = ref({
  title: undefined,
})
const formRules = reactive({
  title: [{ required: true, message: t('academy.course.courseNameRequired'), trigger: 'blur' }],
})
const queryFormRef = ref() // 搜索的表单
const formCourseRef = ref()
const formLoading = ref(false)
const showCourse = ref(false)
const showStudent = ref(false)
/** 添加/修改操作 */
const formRef = ref()
const exportLoading = ref(false) // 导出的加载中
const courseInfo = ref({}) // 存储课程详情信息
const categoryList = ref([]) // 课程分类信息
/**  已选择的课程ID */
const checkCourse = ref([])
// 分配人form表单
const formStudentData = ref({
  scope: 1,
  courseAssignVO: {
    scope: undefined,
    scopeDetails: [],
  },
})

const companyList = ref([]) // 部门信息
const userList = ref([]) // 用户信息
const scopeList = ref<ScopeData[]>([])
const dialogLoading = ref(false)
const trainingCourseId = ref()
const formatScopeList = (list: CourseScope[]) => {
  return list.map(item => ({
    relevanceId: item.relevanceId,
    relevanceName: item.relevanceName,
    scope: item.scope,
    id: item.id,
  }))
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await CourseApi.getCoursePage(queryParams)
    list.value = res.list
    list.value = res.list.map((course: CourseRespVO) => {
      return {
        ...course,
        isApprovals: course.ifmsApprovalKey || course.contractorApprovalKey ? 1 : 0,
      }
    })
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 复制课程操作 */
const handleCopy = async (id: number) => {
  try {
    formData.value.title = ''
    const data = await CourseApi.getCourse(id)
    // 删除详情信息中的id值
    delete data.id
    courseInfo.value = data
    showCourse.value = true
  } catch {}
}

const submitForm = async () => {
  // 校验表单
  await formCourseRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    courseInfo.value.title = formData.value.title
    // 调用新增接口
    await CourseApi.createCourse(courseInfo.value)
    message.success(t('common.copySuccess'))
    await getList()
    showCourse.value = false
  } finally {
    formLoading.value = false
  }
}
// 触发select获取焦点实时刷新分类接口(课程界面存在路由缓存)
const changeCategory = () => {
  getCategory()
}

// 分类列表
const getCategory = async () => {
  const data = await listTopic()
  categoryList.value = handleTree(data)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查询公司列表(暂时不需要 勿删！！！)  */
const getCompanyList = async () => {
  const data = await listCompany({ pageNum: 1, pageSize: 99999 })
  const companyIds = data.list.map((v) => v.companyId)
  const userList = await listUser({ companyIds, pageNum: 1, pageSize: 99999})
  userList.value = userList
}
/** 选择All Company Employees 调用公司接口取出所有id 暂时先不需要*/
const changeScope = async (val: number) => {
  if (val === 1) {
    const modelArray = userList.value.map((v: any) => ({
      relevanceId: v.userId,
      relevanceName: v.nickName,
      scope: NodeType.Employee,
    }))
    formStudentData.value.courseAssignVO.scopeDetails = modelArray
  } else {
    formStudentData.value.courseAssignVO = undefined
  }
}
// 查询学院课程已分配列表
const getAssignList = async (courseId: number) => {
  const data = await CourseApi.getCourseAssignPage(courseId)
  scopeList.value = formatScopeList(data)
}

// 打开分配人员弹框
const openStudent = (courseId: number) => {
  trainingCourseId.value = courseId
  getAssignList(courseId)
  showStudent.value = true
}

const handleConfirm = async (data: ScopeConfirm) => {
  const dataParams = data.scopes.map(item => ({
    id: trainingCourseId.value,
    relevanceId: item.relevanceId,
    relevanceName: item.relevanceName,
    scope: item.scope,
    type: 0,
  }))
  dialogLoading.value = true
  confirmLoading.value = true
  try {
    await CourseApi.AssignFun(trainingCourseId.value,data.scope, 0, dataParams)
    message.success(t('common.updateSuccess'))
    getAssignList(trainingCourseId.value)
    scopeRef.value?.closeDialog()
  } finally {
    dialogLoading.value = false
    confirmLoading.value = false
  }
}

const handleStudentDelete = async (list: CourseScopeVO[] | CourseScopeVO) => {
  let ids: any
  try {
    // 删除的二次确认
    await message.delConfirm(t('common.delOption'))
    ids = Array.isArray(list) ? list.map(item => item.id).join(',') : list.id
    await CourseApi.delAssign(ids)
    message.success(t('common.delSuccess'))
    getAssignList(trainingCourseId.value)
  } catch {}
}


// 截取code值(根据分类名称第一个 - 进行截取拼接)
const getCode = (val: string) => {
  const parts = val.split('-')
  return parts.length > 0 ? parts[0] : val
}

/** 选择条数  */
const handleSelectionChange = (selection: CourseRespVO[]) => {
  checkCourse.value = []
  selection.forEach(item => {
    checkCourse.value.push(item.id)
  })
}

/** 删除按钮操作 */
const handleDelete = async (id: number,type?: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    let idStr
    if (type === 1) {
      idStr = id
    } else {
      idStr = checkCourse.value.join(',')
    }
    // 发起删除
    await CourseApi.deleteCourse(idStr)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}


/** 初始化 **/
onMounted(() => {
  // 暂时隐藏
  // getCompanyList()
  getList()
  getCategory()
})
</script>
