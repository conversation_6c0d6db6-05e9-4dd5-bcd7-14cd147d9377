<template>
  <Dialog v-model="showExam" align-center :width="1050" :title="t('academy.course.selectOnlineExam')">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="120x"
      >
        <el-form-item :label="t('academy.course.name')" prop="name">
          <el-input
            v-model="queryParams.name"
            :placeholder="t('academy.course.pleaseInputName')"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
<!--        <el-form-item label="Status" prop="status">-->
<!--          <el-select-->
<!--            v-model="queryParams.status"-->
<!--            placeholder="Please select"-->
<!--            clearable-->
<!--            class="!w-240px"-->
<!--          >-->
<!--            <el-option :label="t('examMgt.exam.notStarted')" value="0" />-->
<!--            <el-option :label="t('examMgt.exam.inProcess')" value="1" />-->
<!--            <el-option :label="t('examMgt.exam.expired')" value="2" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item :label="t('academy.course.subject')" prop="classifyId">
          <SubjectSelect v-model="queryParams.classifyId" :has-no-subject="true" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />{{ t('academy.course.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />{{ t('academy.course.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <!-- 列表 -->
    <ContentWrap>
      <el-table
        v-loading="loading"
        :data="list"
        :show-overflow-tooltip="true"
        @current-change="handleCurrentChange"
        ref="RefSingleTable"
      >
        <el-table-column label="" width="34">
          <template #default="{ row }">
            <el-radio-group v-model="templateSelection[0]">
              <el-radio :label="row.id" size="small">
                {{}}
              </el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.examName')" align="center" prop="name" min-width="180px" />
        <el-table-column :label="t('academy.course.subject')" align="center" prop="classifyName" min-width="180px">
          <template #default="{ row }">
            {{ row.classifyId === 0 || !row.classifyName ? t('common.noSubject') : row.classifyName }}
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.type')" align="center" prop="type" min-width="180px">
          <template #default="{ row }">
            {{ row.type === 1 ? t('examMgt.paper.autoPaper') : t('learningCenter.exam.customizedPaper') }}
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.examTime')" align="center" prop="answerTime" min-width="180px">
          <template #default="{ row }">
            <p>
              {{ row.beginTime }}
            </p>
            <p>
              {{ row.endTime }}
            </p>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.status')" align="center" prop="totalSeats" min-width="180px">
          <template #default="{ row }">
            <div>
              {{ statusList[row.status] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.assignedNumber')" align="center" prop="totalNum" min-width="180px">
          <template #default="{ row }">
            <div v-if="row.totalNum > 0">
              <el-link type="primary" :underline="false">
                {{ row.totalNum }}
              </el-link>
            </div>
            <div v-else class="text-primary">
              {{ row.totalNum }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.creator')" align="center" prop="createBy" min-width="180px" />
        <el-table-column :label="t('academy.course.creationTime')" align="center" prop="createTime" min-width="180px" />
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showExam = false">{{ t('academy.course.cancel') }}</el-button>
        <el-button type="primary" @click="confirmExam">{{ t('academy.course.confirm') }}</el-button>
      </div>
    </template>

  </Dialog>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listExam, ExamRespVO } from "@/api/topicMgt/exam"

/** 考试信息 列表 */

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<ExamRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  status: 0,
  classifyId: undefined
})
const queryFormRef = ref() // 搜索的表单
const formRef = ref()
const statusList = ref([t('examMgt.exam.notStarted'), t('examMgt.exam.inProcess'), t('examMgt.exam.expired')])
const showExam = ref(false)
const emits = defineEmits(['confirm'])
const templateSelection = ref<string[]>([])
const multipleSelection = ref([])
const RefSingleTable = ref()
const selectedId = ref()
/** 查询列表 */
const getList = async () => {
  // 取消table选中行
  if (templateSelection.value && templateSelection.value.length !== 0) {
    RefSingleTable.value.setCurrentRow()
  }
  loading.value = true
  try {
    const data = await listExam(queryParams.value)
    list.value = data.list
    list.value.forEach((element) => {
      if (element.id === selectedId.value) {
        RefSingleTable.value?.setCurrentRow(element)
      }
    })
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  templateSelection.value = []
  multipleSelection.value = []
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    status: 0,
    classifyId: undefined
  }
  handleQuery()
}

/** 单选考试 */
const handleCurrentChange = (currentRow, oldCurrentRow) => {
  if (currentRow) {
    multipleSelection.value = currentRow
    templateSelection.value = [currentRow.id]
  } else {
    templateSelection.value = []
  }
  RefSingleTable.value?.setCurrentRow(currentRow)
}
const confirmExam = () => {
  showExam.value = false
  emits('confirm', multipleSelection.value)
}

/** 打开弹窗 */
const open = async (examId: number) => {
  selectedId.value = examId
  showExam.value = true
  resetQuery()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
