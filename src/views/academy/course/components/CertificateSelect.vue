<template>
  <Dialog v-model="showCertificate" align-center :width="1050" :title="t('academy.course.selectCertificate')">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="120x"
      >
        <el-form-item :label="t('academy.course.name')" prop="name">
          <el-input
            v-model="queryParams.name"
            :placeholder="t('academy.course.pleaseInputName')"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
<!--        <el-form-item label="Status" prop="status">-->
<!--          <el-select-->
<!--            v-model="queryParams.status"-->
<!--            placeholder="Please select"-->
<!--            clearable-->
<!--            class="!w-240px"-->
<!--          >-->
<!--            <el-option-->
<!--              v-for="dict in getIntDictOptions(DICT_TYPE.CERTIFICATE_STATUS)"-->
<!--              :key="dict.value"-->
<!--              :label="dict.label"-->
<!--              :value="dict.value"-->
<!--            />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item :label="t('academy.course.time')" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            :start-placeholder="t('academy.course.startDate')"
            :end-placeholder="t('academy.course.endDate')"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" />{{ t('academy.course.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" />{{ t('academy.course.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <!-- 列表 -->
    <ContentWrap>
      <el-table
        v-loading="loading"
        :data="list"
        :show-overflow-tooltip="true"
        @current-change="handleCurrentChange"
        ref="RefSingleTable"
        >
        <el-table-column label="" width="34">
          <template #default="{ row }">
            <el-radio-group v-model="templateSelection[0]">
              <el-radio :label="row.id" size="small">
                {{}}
              </el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.name')" align="center" prop="name" min-width="180px" />
        <el-table-column :label="t('academy.course.status')" align="center" prop="status" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CERTIFICATE_STATUS" :value="scope.row.status" min-width="180px" />
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.course.creationTime')" align="center" prop="createTime" min-width="180px" createTime :formatter="dateFormatter" />
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showCertificate = false">{{ t('academy.course.cancel') }}</el-button>
        <el-button type="primary" @click="confirmCertificate">{{ t('academy.course.confirm') }}</el-button>
      </div>
    </template>

  </Dialog>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {CertificateApi, CertificateRespVO, CertificateStatusEnum} from '@/api/system/certification/certificate'
import { dateFormatter } from '@/utils/formatTime'
/** 证书信息 列表 */

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref<CertificateRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: -1,
  name: '',
  status: CertificateStatusEnum.Enable,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const formRef = ref()
const showCertificate = ref(false)
const multipleSelection = ref([])
const templateSelection = ref<string[]>([])
const RefSingleTable = ref()
const selectedId = ref()
const emits = defineEmits(['confirm'])
/** 查询列表 */
const getList = async () => {
  // 取消table选中行
  if (templateSelection.value && templateSelection.value.length !== 0) {
    RefSingleTable.value.setCurrentRow()
  }
  loading.value = true
  try {
    const res = await CertificateApi.getCertificatePage(queryParams)
    list.value = res.list
    list.value.forEach((element) => {
      if (element.id === selectedId.value) {
        RefSingleTable.value?.setCurrentRow(element)
      }
    })
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  templateSelection.value = []
  multipleSelection.value = []
  queryParams.pageSize = 10
  queryParams.name = undefined
  queryParams.status = CertificateStatusEnum.Enable
  queryParams.createTime = []
  handleQuery()
}

/** 单选用户 */
const handleCurrentChange = (currentRow, oldCurrentRow) => {
  if (currentRow) {
    multipleSelection.value = currentRow
    templateSelection.value = [currentRow.id]
  } else {
    multipleSelection.value = {}
    templateSelection.value = []
  }
  RefSingleTable.value?.setCurrentRow(currentRow)
}
const confirmCertificate = () => {
  showCertificate.value = false
  emits('confirm', multipleSelection.value)
}
/** 打开弹窗 */
const open = async (certificateId: number) => {
  selectedId.value = certificateId
  showCertificate.value = true
  resetQuery()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
