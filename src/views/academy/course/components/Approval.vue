<template>
  <div v-for="(item,index) in getContent(props.modelId)" :key="index">
    <div class="flex flex-col h-[65px] ps-[5px] rounded-[5px]" style="border: 1px solid #BBBBBB">
      <span>{{ item.title }}</span>
      <span class="text-[#BBBBBB]">{{item.description }}</span>
    </div>
    <div style="border: 1px solid green" class=" h-[30px] w-[0px] ml-[50%]"></div>
  </div>
  <div class="h-[65px] text-center leading-[65px] bg-[#038F00] rounded-[5px] text-white" v-if="props.modelId">{{ t('academy.course.end') }}</div>
</template>

<script setup lang="ts">

const { t } = useI18n() // 国际化
const props = defineProps<{
  modelId: string | number
}>()
// 1
const trainingAdminItems =  ref([
  { title: t('academy.course.student'), description: t('academy.course.noReviewNeeded') },
  { title: t('academy.course.review'), description: t('academy.course.trainingAdmin') },
])
// 2
const lineManagerItems =  ref([
  { title: t('academy.course.student'), description: t('academy.course.submitBookingRequest') },
  { title: t('academy.course.review'), description: t('academy.course.lineManager') },
  { title: t('academy.course.review'), description: t('academy.course.trainingAdmin') },
])
// 3
const EptwAdminItems =  ref([
  { title: t('academy.course.student'), description: t('academy.course.submitBookingRequest') },
  { title: t('academy.course.review'), description: t('academy.course.lineManager') },
  { title: t('academy.course.review'), description: t('academy.course.trainingAdmin') },
  { title: t('academy.course.review'), description: t('academy.course.eptwAdmin') },
])
// 4
const ContractorItems =  ref([
  { title: t('academy.course.student'), description: t('academy.course.submitBookingRequest') },
  // { title: t('academy.course.review'), description: t('academy.course.lineManager') },
  { title: t('academy.course.review'), description: t('academy.course.contractorHolder') },
  { title: t('academy.course.review'), description: t('academy.course.trainingAdmin') },
])
// 5
const ContracotrEptwAdminItems =  ref([
  { title: t('academy.course.student'), description: t('academy.course.submitBookingRequest') },
  // { title: t('academy.course.review'), description: t('academy.course.lineManager') },
  { title: t('academy.course.review'), description: t('academy.course.contractorHolder') },
  { title: t('academy.course.review'), description: t('academy.course.trainingAdmin') },
  { title: t('academy.course.review'), description: t('academy.course.eptwAdmin') },
])

// 根据key进行展示 key是唯一标识
const getContent = (key: string) => {
  switch (key) {
    case 'ifms_training_admin':
    case 'contractor_training_admin':
      // IFMS管理员审批 或者 contractor管理员审批
      return trainingAdminItems.value
    case 'ifms_line_manager':
      // IFMS直属领导
      return lineManagerItems.value
    case 'ifms_eptw_admin':
      // IFMS EPTW管理员审批
      return EptwAdminItems.value
    case 'contractor_holder':
      // Contractor 负责人审批
      return ContractorItems.value
    case 'contractor_eptw_admin':
      // Contractor EPTW管理员审批
      return ContracotrEptwAdminItems.value
    default:
      break;
  }
}
</script>
