<template>
  <ContentWrap>
    <div class="flex justify-between items-center">
      <div class="flex">
        <div class="flex items-center w-[200px] truncate">
          {{ classInfo?.courseName }}
        </div>
        <div class="ms-[30px]">
          <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="classInfo?.language"  class="me-5" />
          <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="bookingInfo?.type" />
        </div>
      </div>
      <dict-tag :type="DICT_TYPE.BOOKING_STATUS" :value="classInfo?.status" />
    </div>
    <div class="flex flex-col">
      <span>{{ t('academy.class.trainingDescription') }}</span>
      <span class="text-[#BBBBBB] mt-2">
        {{ courseInfo?.remarks }}
      </span>
    </div>
    <div class="flex flex-col mt-3">
      <span>{{ t('academy.class.startEndTime') }}</span>
      <span class="text-[#BBBBBB] mt-2">
        {{ classInfo?.startTime && classInfo?.endTime	? `${classInfo?.startDate} ${classInfo?.startTime} ~ ${classInfo?.startDate} ${classInfo?.endTime}` : ''}}
      </span>
    </div>
    <div class="mt-10">
      <el-row :gutter="10">
        <el-col :span="12">
          <span>{{ t('academy.class.bookingInformation') }}</span>
          <el-form
            ref="formRef"
            label-width="180px"
          >
            <el-form-item :label="t('academy.class.allowedBookingTime') + ':'">
              <el-text class="mx-1">{{ formatDate(classInfo?.bookingStartTime) }} - {{ formatDate(classInfo?.bookingEndTime) }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.bookingRate') + ':'">
              <el-text class="mx-1">{{ classInfo?.assignNum ? classInfo?.assignNum : 0  }}/ {{ classInfo?.maxNum }} </el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.bookingTime') + ':'">
              <el-text class="mx-1">{{ formatDate(bookingInfo?.createTime) }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.prerequisite') + ':'">
              <el-row :gutter="20">
                <el-col :span="24">
                  <div>
                    {{ t('academy.course.course') }}
                  </div>
                </el-col>
                <el-col :span="24" v-show="bookingInfo?.courseJson">
                  <div style="overflow-wrap: break-word" class="min-h-[100px] overflow-y-auto">
                    <el-tag
                      class="tag mt-1 me-2"
                      type="primary"
                      :disable-transitions="false"
                      v-for="(item,index) in bookingInfo?.courseJson"
                      :key="index"
                    >
                      <div class="flex justify-center align-center">
                        {{ item.courseName }}
                        <Icon v-show="item.existStudy" icon="ep:check" class="ms-2" />
                        <Icon v-show="!item.existStudy" icon="ep:close" />
                      </div>
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div>
                    {{ t('academy.class.attachment') }}
                  </div>
                </el-col>
                <el-col :span="24" v-show="attachmentInfo">
                  <div style="overflow-wrap: break-word" class="min-h-[100px] overflow-y-auto">
                    <el-tag
                      class="me-2 mb-2"
                      type="primary"
                      :disable-transitions="false"
                      v-for="(item,index) in attachmentInfo"
                      :key="index"
                    >
                      {{ item?.name }}
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <span>{{ t('academy.class.studentInformation') }}</span>
          <el-form
            ref="formRef"
            label-width="180px"
          >
            <el-form-item :label="t('academy.classroom.name') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.nickname }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.badgeNo') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.badgeNo }}</el-text>
            </el-form-item>
            <el-form-item :label="t('sys.user.company') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.companyName }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.position') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.positionName }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.fullRefresher') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.firstStudy ? t('academy.class.full') : t('academy.class.refresher') }}</el-text>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

    </div>
  </ContentWrap>
</template>
<script setup lang="ts">
import { Position, User, DocumentChecked } from '@element-plus/icons-vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi, ClassInfoRespVO } from '@/api/academy/class'
import * as TaskApi from "@/api/bpm/task"
import { formatDate } from "@/utils/formatTime"
import * as ProcessInstanceApi from "@/api/bpm/processInstance"
import { CourseApi } from "@/api/academy/course"
import { getProcessInstance } from "@/api/bpm/processInstance"

/** 审核界面  详情*/
defineOptions({ name: 'ProcessDetail' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const route = useRoute()

const dialogVisible = ref(false) // 抽屉的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formRef = ref() // 表单 Ref
const bookingInfo = ref()
const courseInfo = ref()
const attachmentInfo = ref([])
const classInfo = ref() // 课堂详情
const taskId = ref()  // 任务id(最终审批和拒绝使用这个id)

// 获取预定的信息
const getInfo = async (id: number) => {
  bookingInfo.value =  await ClassInfoApi.getBookingDetail(id)
  if (bookingInfo.value?.courseJson !== '') {
    bookingInfo.value.courseJson = JSON.parse(bookingInfo.value?.courseJson)
  }
  if (bookingInfo.value?.attachmentJson !== '') {
    // 将字符串转换为数组形式
    attachmentInfo.value = JSON.parse(bookingInfo.value?.attachmentJson)
  }
  // 在预定课堂详情接口中没有课程id,需要拿课堂id调用课堂详情获取课程id
  await getClassInfo(bookingInfo.value?.classId)

}
// 获取课堂详情信息
const getClassInfo = async (id: number) => {
  classInfo.value = await ClassInfoApi.getClassInfo(id)
  await getCourseInfo(classInfo.value.courseId)
}

// 获取课程信息详情
const getCourseInfo = async (courseId: number) => {
  courseInfo.value = await CourseApi.getCourse(courseId)
}


const getProcessInstanceInfo = async () => {
  if (route.query.id) {
    const data = await getProcessInstance(route.query.id as unknown as string)
    if (!data) {
      message.error(t('common.cantQueryProcessInfo'))
      return
    }
  // businessKey是课堂id
    await getInfo(data.businessKey)
  }
}

onMounted(() => {
  getProcessInstanceInfo()
})
</script>
<style scoped lang='scss'>
:deep(.steps) {
.el-step__icon.is-text {
  color: transparent;
  border: 1px solid #007943;
}
}
</style>
