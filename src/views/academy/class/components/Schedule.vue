<template>
<!--  搜索条件-->
  <div class="flex justify-between items-center">
    <div>
      <!-- 课程类别下拉菜单 -->
      <el-tree-select
        v-model="queryParams.categoryId"
        :data="categoryList"
        :props="defaultProps"
        filterable
        clearable
        check-strictly
        default-expand-all
        value-key="id"
        :placeholder="t('academy.course.courseCategory')"
        class="!w-200px"
        @change="changeCategory"
      />

      <!-- 月份选择 -->
      <el-date-picker
        style="width: 100px;"
        class="w-[100px] ms-3"
        v-model="selectedDate"
        @change="changeDate"
        type="month"
      />

      <!--    年份选择-->
      <el-date-picker
        style="width: 100px;"
        class="w-[100px] ms-3"
        v-model="selectedYear"
        @change="changeYear"
        type="year"
      />

    </div>
    <div class="flex">
      <!-- 类型和语言标签 -->
      <div class="tags">
        <span class="tag offline">{{ t('academy.class.offlineClass') }}</span>
        <span class="tag virtual">{{ t('academy.class.virtualClass') }}</span>
        <span class="tag arabic">AR</span>
        <span class="tag english">EN</span>
        <span class="tag chinese">CN</span>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
<!--        <el-button link type="primary" @click="save">Save</el-button>-->
        <el-button link type="primary" @click="publishClass" :disabled="selectedCourseIds.length === 0">{{ t('academy.class.publish') }}</el-button>
      </div>
    </div>


  </div>

  <el-table :data="scheduleData" :show-overflow-tooltip="true" border v-loading="loading">
    <!-- 显示课程标题 -->
    <el-table-column prop="courseTitle" :label="t('academy.class.courseTitle')" width="180">
      <template #header>
        <div class="table-header">{{ t('academy.class.courseTitle') }}</div>
      </template>
      <template #default="scope">
<!--        class="cursor-pointer" @click="handleCourse(scope.row)" 不能对课程有任何操作 -->
        <div >{{ scope.row.courseTitle }}</div>
      </template>
    </el-table-column>

    <!-- 动态生成每天的列 -->
    <el-table-column v-for="(day, index) in daysInMonth" :key="index" :label="day.toString()" min-width="200px">
      <template #header>
        <div class="text-center">
          {{ day }}
          <br />
          {{ getDayOfWeek(currentYear, currentMonth, day) }}
        </div>
      </template>
      <template #default="scope">
        <div v-if="scope.row.schedule && scope.row.schedule[day]">
<!--          Todo 先默认写上已发布是1-->
          <div
            v-for="course in scope.row.schedule[day]"
            :key="course.id"
            class="text-center cursor-pointer relative truncate h-[60px]"
            :class="{'highlighted': isCourseSelected(course.classId),'publishStatus': course.publishStatus == 1,'disabled-course': course.publishStatus === 1 }"
            @click="toggleCourseSelection(course)"
            :style="{ backgroundColor: isCourseSelected(course.classId) ? '#ffffcc' : '' }"
          >
            {{ course.instructor }}
            <br />
            {{ course.time }}
            <el-tag type="primary" class="absolute top-0 right-0" v-show="course.language === 1">AR</el-tag>
            <el-tag type="warning" class="absolute top-0 right-0" v-show="course.language === 2">EN</el-tag>
            <el-tag type="success" class="absolute top-0 right-0" v-show="course.language === 3">CN</el-tag>
            <el-button link type="primary" class="absolute bottom-0 right-0" :disabled="course.publishStatus == 1" @click="editClass(course.classId)">
              <Icon icon="ep:edit" />
            </el-button>

          </div>
        </div>
<!--        Todo 最后跟后端接口判断是否参数名称是courseId-->
        <div class="text-center cursor-pointer" v-else @click="addClass(scope.row.courseId,scope.row.categoryId)">
          +
        </div>
      </template>
    </el-table-column>
  </el-table>

  <Dialog v-model="showCourse" :title="t('academy.course.course')">
    <div class="text-center">
      {{ courseInfo.courseName }}
    </div>
    <template #footer>
      <el-button type="primary" @click="handleManage">{{ t('academy.course.manage') }}</el-button>
      <el-button type="primary" @click="hanldeRemove">{{ t('academy.course.remove') }}</el-button>
    </template>
  </Dialog>

  <ManageClass ref="manageRef" />

  <!-- 表单弹窗：添加/修改 -->
  <ClassForm ref="classFormRef" @success-class="handleSubmit" />

  <Dialog v-model="showPublish" :title="t('academy.class.publishClass')" width="500">
    <div>{{ t('academy.class.confirmPublishSchedule') }}</div>
    <div class="flex justify-between items-center">
      <span class="text-black">{{ t('academy.class.messageNotification') }}</span>
      <el-switch v-model="formData.notification" size="large" :active-value="1" :inactive-value="0" />
    </div>
    <div>{{ t('academy.class.notificationDescription') }}</div>
    <template #footer>
      <el-button @click="showPublish = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :loading="formLoading">{{ t('dialog.confirm') }}</el-button>
    </template>
  </Dialog>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi, ClassInfoRespVO } from '@/api/academy/class'
import { CourseApi, CourseRespVO, CourseSaveVO } from '@/api/academy/course'
import { listTopic } from '@/api/category/training'
import ManageClass from './ManageClass.vue'
import ClassForm from '../../class/ClassForm.vue'
import { handleTree,defaultProps } from "@/utils/tree"
import { getMonthStartAndEndByMonth, getYearStartAndEndByYear } from "@/utils/formatDate"
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(false)
const formLoading = ref(false) // 列表的加载中
const showCourse = ref(false)
const showPublish = ref(false)
const formRef = ref()
const classFormRef = ref()
const manageRef = ref()
const categoryList = ref([]) // 分类列表
const selectedDate = ref()
const selectedYear = ref()
// 课程搜索form
const queryParams = reactive({
  pageNo: 1,
  pageSize: -1,
  categoryId: undefined,
  createTime: [],
})
const selectedCourseIds = ref([])

// 发布使用
const formData = ref({
  notification: 1,
  ids: [],
  publishStatus: 1,
})

// 点击课程标题使用
const courseInfo = ref({
  courseName: '',
  courseId: '',
})

const courseList = ref([]) // 课程信息
const classList = ref([])  // 课堂信息
const scheduleData = ref([])

const changeCategory = (id: number) => {
  queryParams.categoryId = id
  getCourseList()
  mergeCourse()
}
const changeDate = (date: Date) => {
  if (date instanceof Date && !isNaN(date)) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 月份从 0 开始，所以要 +1
    queryParams.createTime = getMonthStartAndEndByMonth(year,month)
  } else {
    queryParams.createTime = []
  }
  getCourseList()

  mergeCourse()
}

const changeYear = (date: Date) => {
  if (date) {
    // 获取当前年的开始时间和结束时间
    queryParams.createTime = getYearStartAndEndByYear(new Date(date).getFullYear())
  } else {
    queryParams.createTime = []
  }
  getCourseList()
  mergeCourse()
}

const toggleCourseSelection = (course) => {
  if (course.publishStatus === 1) {
    // 如果状态为 1，阻止点击
    message.warning(t('academy.class.classPublishedCannotSelect'))
    return
  }
  const index = selectedCourseIds.value.indexOf(course.classId)
  if (index > -1) {
    // 已存在，移除
    selectedCourseIds.value.splice(index, 1);
  } else {
    // 不存在，添加
    selectedCourseIds.value.push(course.classId)
    if (selectedCourseIds.value) {
      formData.value.ids = selectedCourseIds.value
    }
  }
}
const isCourseSelected = (classId: number) => {
  return selectedCourseIds.value.includes(classId)
}


const currentYear = new Date().getFullYear();
const currentMonth = new Date().getMonth(); // 注意：getMonth() 返回 0-11 的值，表示 1-12 月

// 计算某个月有多少天
const daysInMonth = computed(() => {
  return new Array(new Date(currentYear, currentMonth + 1, 0).getDate()).fill(null).map((_, index) => index + 1);
})

// 获取指定日期是星期几（返回 "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"）
const getDayOfWeek = (year, month, day) => {
  const date = new Date(year, month, day);
  const options = { weekday: 'short' };
  return new Intl.DateTimeFormat('en-US', options).format(date);
}


/** 查询课程列表 */
const getCourseList = async () => {
  const data = await CourseApi.getCoursePage(queryParams)
  courseList.value = data.list
}

// /** 查询课堂列表 */
const getClassList = async () => {
  const data = await ClassInfoApi.getClassInfoPage({pageNo: 1, pageSize: -1})
  classList.value = data.list
}

// 模拟数据 最后删除 Todo
// const courseList = ref([
//   {title: 'HSE & ERW Induction/H2S', id: 1,categoryId: 8},
//   {title: 'hello', id: 3,categoryId: 8},
// ])
// const classList = ref([
//   {courseId:1,className:'hello',instructor:'hello课堂内容',id: 5,startDate:'2025-05-01',language: 1,publishStatus: 1},
//   {courseId:3,className:'hi',instructor:'hi课堂内容',id: 6,startDate:'2025-05-02',language: 2,publishStatus: 1},
//   {courseId:3,className:'hi',instructor:'hi课堂内容',id: 8,startDate:'2025-05-03',language: 3,publishStatus: 2},
//   {courseId:1,className:'hi',instructor:'hi课堂内容',id: 9,startDate:'2025-05-05',language: 3,publishStatus: 2},
//   {courseId:3,className:'hi',instructor:'hi课堂内容',id: 10,startDate:'2025-05-07',language: 3,publishStatus: 2},
//   {courseId:1,className:'hi',instructor:'hi课堂内容',id: 11,startDate:'2025-05-08',language: 3,publishStatus: 2},
// ])



const mergeCourse = () => {
  loading.value = true
  try {
    scheduleData.value =  courseList.value.map(course => {
      // 获取与当前课程匹配的班级
      const relatedClasses = classList.value.filter(cls => cls.courseId === course.id)

      // 组装课程的 schedule 数据
      const schedule = {}

      relatedClasses.forEach(cls => {
        // 解析 startDate 得到日期（例如: '2025-05-08' 变成 8）
        const date = new Date(cls.startDate).getDate()

        // 为每个日期创建一个 schedule 条目
        if (!schedule[date]) {
          schedule[date] = []
        }

        schedule[date].push({
          classId: cls.id,
          instructor: cls.name,
          time: cls.startTime && cls.endTime ? `${cls.startTime} - ${cls.endTime}` : '',
          color: 'blue',         // 假设颜色是固定的
          language: cls.language,
          publishStatus: cls.publishStatus,
        })
      })

      // 返回最终的课表数据格式
      return {
        courseTitle: course.title,
        courseId: course.id,
        categoryId: course.categoryId,
        schedule: schedule,
      }
    })
  } finally {
    loading.value = false
  }
}

// 课程分类信息
const getCategoryList = async () => {
  const data = await listTopic()
  categoryList.value = handleTree(data)
}


const resetForm = () => {
  formRef.value?.resetFields()
  courseInfo.value = {
    courseName: '',
    courseId: '',
  }
}

// 在每个课程日期上新增课堂
const addClass = (courseId: number, categoryId: number) => {
  classFormRef.value.open('create', undefined, courseId,  categoryId)
}
// 编辑课堂
const editClass = (id: number) => {
  classFormRef.value.open('update', id)

}
// 操作课程的信息
const handleCourse = (item: any) => {
  resetForm()
  courseInfo.value.courseName = item.courseTitle
  courseInfo.value.courseId = item.courseId
  showCourse.value = true
}
const handleManage = () => {
  manageRef.value.open(courseInfo.value.courseId)
}
// Todo 删除课程
const hanldeRemove = async () => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ClassInfoApi.deleteCalendar(courseInfo.value.courseId)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await handleSubmit()
    showCourse.value = false
  } catch {}
}

// 发布课堂弹框提醒
const publishClass = () => {
  // 默认发送提醒
  formData.value.notification = 1
  showPublish.value = true
}

// 最终发布操作
const submitForm = async () => {
  try {
    formLoading.value = true
    await ClassInfoApi.publishClass(formData.value)
    message.success(t('academy.class.publishedSuccessfully'))
    showPublish.value = false
    // 清空数据并清除高亮显示
    selectedCourseIds.value = []
    formData.value.ids = []
    handleSubmit()
  } finally {
    formLoading.value = false
  }
}


// 把所有方法放一起
const handleSubmit = () => {
  getCourseList()
  getClassList()
  mergeCourse()
}

defineExpose({ handleSubmit })

onMounted(() => {
  handleSubmit()
  getCategoryList()

})
</script>
<style scoped>
.highlighted {
  background-color: #FFFF00 !important; /* 黄色高亮 */
}
.publishStatus {
  background-color: #FFFF00;
}
.disabled-course {
  /*pointer-events: none; !* 禁止任何交互 *!*/
  opacity: 0.8; /* 视觉上变灰 */
  cursor: not-allowed;
}
.tags .tag {
  margin-left: 10px;
  padding: 5px 10px;
  border-radius: 5px;
  position: relative; /* 确保内部绝对定位元素相对于此标签定位 */
}

/* 添加小圆点 */
.tags .tag::before {
  content: '';
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: absolute;
  left: -10px; /* 调整这个值以控制小圆点与文本之间的距离 */
  top: 50%;
  transform: translateY(-50%);
}

.tags .offline::before { background-color: orange; }
.tags .virtual::before { background-color: purple; }
.tags .arabic::before { background-color: #0070C0; }
.tags .english::before { background-color: #FDF6EC; }
.tags .chinese::before { background-color: #00B050; }
</style>
