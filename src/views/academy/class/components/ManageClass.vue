<template>
<!--  该组件文件暂时弃用-->
  <el-drawer title="Manage Class" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="Course Title">
        <el-input v-model="formData.name"  disabled />
      </el-form-item>
<!--      Todo-->
      <el-form-item label="Category">
        <el-input v-model="formData.categoryName"  disabled />
      </el-form-item>
      <el-form-item label="Class Type" prop="type">
        <el-radio-group v-model="formData.type" @change="changeType">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="Classroom" :prop="classRoomIdProp" v-show="formData.type === 1 || formData.type === 3">
        <el-input v-model="checkClassroomInfo.name" placeholder="Please select the Classroom" disabled>
          <template #suffix>
            <Icon icon="ep:more" class="mr-5px cursor-pointer" @click="openClassroom" />
          </template>
        </el-input>
      </el-form-item>
<!--      Todo-->
      <el-form-item label="Venue" :prop="venueProp" v-show="formData.type === 1 || formData.type === 3">
        <el-select
          v-model="formData.venue"
          placeholder="Please select"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="item in courseList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Maximum Attendance Num" :prop="maxNumProp" v-show="formData.type === 1 || formData.type === 3">
        <el-input-number v-model="formData.maxNum" :min="1" />
      </el-form-item>
      <el-form-item label="Minimum Attendance Num" :prop="minNumProp" v-show="formData.type === 1 || formData.type === 3">
        <el-input-number v-model="formData.minNum" :min="1" />
      </el-form-item>
      <el-form-item label="Live Link" v-show="formData.type === 2 || formData.type === 3">
        <el-input v-model="formData.liveLink" disabled />
      </el-form-item>
      <el-form-item label="Training Description" prop="description">
        <el-input v-model="formData.description" type="textarea"  />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">Cancel</el-button>
      <el-button @click="submitForm" type="primary" :loading="formLoading">Confirm</el-button>
    </template>
  </el-drawer>


<!--选择教室-->
  <ClassroomSelect
    ref="classroomRef"
    @confirm="classroomConfirm"
  />
</template>
<script setup lang="ts">
import { ClassInfoApi, ClassInfoRespVO } from '@/api/academy/class'
import { CourseApi } from '@/api/academy/course'
import { TrainerApi } from '@/api/academy/trainer'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import ClassroomSelect from './ClassroomSelect.vue'
import { calculateTrainingDays } from '@/utils/formatDate'


const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const router = useRouter()
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: undefined,
  categoryId: undefined,
  name: undefined,
  type: 1,
  classRoomId: undefined,
  maxNum: undefined,
  minNum: 3,
  liveLink: undefined,
  description: undefined
})
const formRules = reactive({
  type: [{ required: true, message: 'Course type cannot be empty', trigger: 'change' }],
  classRoomId: [{ required: true, message: 'Classroom cannot be empty', trigger: 'change' }],
  maxNum: [{ required: true, message: 'Maximum number of participants cannot be empty', trigger: 'blur' }],
  minNum: [{ required: true, message: 'Minimum number of participants cannot be empty', trigger: 'blur' }],
  description: [{ required: true, message: 'Training Description cannot be empty', trigger: 'blur' }],
  venue: [{ required: true, message: 'Venue cannot be empty', trigger: 'change' }],

})
const formRef = ref() // 表单 Ref
const courseList = ref([]) // 课程信息
const classroomRef = ref()
const checkClassroomInfo = ref({
  name:'',
  id: ''
}) // 选择的教室信息

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      // Todo
      getCourseInfo(id as number)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ClassInfoRespVO
    // Todo 补充接口
    await ClassInfoApi.createClassInfo(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

const classRoomIdProp = computed(() => {
  return formData.type === 1 || formData.type === 3 ? 'classRoomId' : ''
})
const maxNumProp = computed(() => {
  return formData.type === 1 || formData.type === 3 ? 'maxNum' : ''
})
const minNumProp = computed(() => {
  return formData.type === 1 || formData.type === 3 ? 'minNum' : ''
})
const venueProp = computed(() => {
  return formData.type === 1 || formData.type === 3 ? 'venue' : ''
})

/** 获取课程信息 Todo */
const getCourseInfo = async (id: number) => {
  formData.value = await CourseApi.getCourse(id)
}


// 根据开始时间和结束时间计算出训练天数 Todo
const changeDuation = (val) => {
  if (formData.value.startTime && formData.value.endTime) {
    formData.value.trainingDays = calculateTrainingDays(formData.value.startTime, formData.value.endTime)
  }
}
/** 打开教室弹框 */
const openClassroom = () => {
  classroomRef.value.open()
}

/** 获取教室信息 */
const classroomConfirm = (data: any) => {
  formData.value.classRoomId = data.id
  checkClassroomInfo.value = data
}
// 切换type Todo 需要跟后端确定其他数据是否需要传递
const changeType = (val: number) => {
  if (val === 2) {
    formData.value.liveLink = ''
    formData.value.classRoomId = undefined
    formData.value.venue = ''
    formData.value.maxNum = undefined
    formData.value.minNum = undefined
  } else {
    formData.value.maxNum = undefined
    formData.value.minNum = 3
  }
}


/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    categoryId: undefined,
    name: undefined,
    type: 1,
    classRoomId: undefined,
    status: undefined,
    maxNum: undefined,
    minNum: 3,
    liveLink: undefined,
    description: undefined
  }
  formRef.value?.resetFields()
  checkClassroomInfo.value = {
    name:'',
    id: ''
  }
}
</script>
<style scoped lang="scss">
</style>
