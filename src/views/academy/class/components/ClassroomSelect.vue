<template>
  <Dialog v-model="showClassroom" align-center :width="1050" :title="t('dialog.selectClassroom')">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item :label="t('academy.classroom.location')" prop="location">
          <el-select
            v-model="queryParams.location"
            :placeholder="t('academy.classroom.pleaseSelect')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.CLASSROOM_LOCATION)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('academy.classroom.status')" prop="status">
          <el-select
            v-model="queryParams.status"
            :placeholder="t('academy.classroom.pleaseSelect')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.CLASSROOM_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('academy.classroom.time')" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            :start-placeholder="t('academy.classroom.startDate')"
            :end-placeholder="t('academy.classroom.endDate')"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item :label="t('academy.classroom.name')" prop="name">
          <el-input
            v-model="queryParams.name"
            :placeholder="t('academy.classroom.pleaseInput')"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.classroom.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.classroom.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table
        ref="RefSingleTable"
        v-loading="loading"
        :data="list"
        :stripe="true"
        :show-overflow-tooltip="true"
        @current-change="handleCurrentChange"
      >
        <el-table-column label="" width="34">
          <template #default="{ row }">
            <el-radio-group v-model="templateSelection[0]">
              <el-radio :label="row.id" size="small">
                {{}}
              </el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.classroom.name')" align="center" prop="name" />
        <el-table-column :label="t('dialog.venue')" align="center" prop="location">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASSROOM_LOCATION" :value="scope.row.location" />
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.classroom.classroomNumber')" align="center" prop="roomNumber" />
        <el-table-column :label="t('academy.classroom.status')" align="center" prop="status">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASSROOM_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.classroom.totalSeats')" align="center" prop="totalSeats" />
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showClassroom = false">{{ t('academy.classroom.cancel') }}</el-button>
        <el-button type="primary" @click="confirmClassroom"> {{ t('dialog.confirm') }}</el-button>
      </div>
    </template>

  </Dialog>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassRoomApi } from "@/api/academy/classroom"

/** 教室信息 列表 */

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  status: undefined,
  location: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const formRef = ref()
/**  已选择的教室信息 */
const checkClassroomInfo = ref()
const showClassroom = ref(false)
const emits = defineEmits(['confirm'])
const selectedId = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  // 取消table选中行
  // if (templateSelection.value && templateSelection.value.length !== 0) {
  //   RefSingleTable.value.setCurrentRow()
  // }
  try {
    const res = await ClassRoomApi.getClassRoomPage(queryParams.value)
    list.value = res.list
    list.value.forEach((element) => {
      if (element.id === selectedId.value) {
        RefSingleTable.value?.setCurrentRow(element)
      }
    })
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  templateSelection.value = []
  multipleSelection.value = []
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    location: undefined,
    createTime: []
  }
  handleQuery()
}
const templateSelection = ref<string[]>([])
const multipleSelection = ref([])
const RefSingleTable = ref()

/** 单选用户 */
const handleCurrentChange = (currentRow, oldCurrentRow) => {
  if (currentRow) {
    multipleSelection.value = currentRow
    templateSelection.value = [currentRow.id]
  } else {
    multipleSelection.value = []
    templateSelection.value = []
  }
  RefSingleTable.value?.setCurrentRow(currentRow)
}

const confirmClassroom = () => {
  showClassroom.value = false
  emits('confirm', multipleSelection.value)
}

/** 打开弹窗 */
const open = async (classroomId: number) => {
  selectedId.value = classroomId
  showClassroom.value = true
  resetQuery()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
