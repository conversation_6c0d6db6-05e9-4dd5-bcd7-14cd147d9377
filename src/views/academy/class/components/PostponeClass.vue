<template>
  <Dialog v-model="showClass" align-center :width="1050" :title="t('academy.class.postponeClass')">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item :label="t('academy.class.type')" prop="type">
          <el-select
            v-model="queryParams.type"
            :placeholder="t('academy.classroom.pleaseSelect')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('academy.class.title')" prop="name">
          <el-input
            v-model="queryParams.name"
            :placeholder="t('academy.classroom.pleaseInput')"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}</el-button>
        </el-form-item>
      </el-form>

    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table
        v-loading="loading"
        :data="list"
        :show-overflow-tooltip="true"
        @current-change="handleCurrentChange"
      >
        <el-table-column label="" width="34">
          <template #default="{ row }">
            <el-radio-group v-model="templateSelection[0]">
              <el-radio :label="row.id" size="small">
                {{}}
              </el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.class.courseTitle')" align="center" prop="courseName" min-width="180px" />
        <el-table-column :label="t('academy.class.classTitle')" align="center" prop="name" min-width="180px" />
        <el-table-column :label="t('academy.class.classType')" align="center" prop="type" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.class.trainer')" align="center" prop="trainerName" min-width="180px" />
        <el-table-column :label="t('academy.class.language')" align="center" prop="language" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="scope.row.language" />
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.class.classroom')" align="center" prop="classRoomName" min-width="180px" />
        <el-table-column :label="t('academy.class.date')" align="center" prop="startDate" min-width="180px" />
        <el-table-column :label="t('academy.class.duration')" align="center" prop="duration" min-width="180px">
          <template #default="scope">
            {{ scope.row.startTime }} - {{ scope.row.endTime }}
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.class.bookingNumber')" align="center" min-width="180px">
          <!--       报名的人数。前面的数字为当前已经报名的人数，后面数字为该课堂最大的可容纳的学生人数。-->
          <template #default="scope">
            <div>
              {{ scope.row.type === ClassTypeEnum.VIRTUAL_CLASS ? scope.row.assignNum : `${ scope.row.assignNum } / ${ scope.row.maxNum }` }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.class.publishStatus')" align="center" prop="publishStatus" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASS_PUBLISH_STATUS" :value="scope.row.publishStatus" />
          </template>
        </el-table-column>
        <el-table-column :label="t('academy.class.status')" align="center" prop="status" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASS_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <el-button @click="showClass = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitForm" :disabled="!checkClassInfo?.id" type="primary">{{ t('dialog.confirm') }}</el-button>
    </template>
  </Dialog>

  <Dialog v-model="showPostponeClass" :title="t('academy.class.confirmPostponeClass')" width="500">
    <div>{{ t('academy.class.confirmPostponeAndMerge', { name: checkClassInfo.name }) }}</div>
    <div class="flex justify-between items-center">
      <span class="text-black">{{ t('academy.class.messageNotification') }}</span>
      <el-switch v-model="formData.notification" size="large" :active-value="1" :inactive-value="0" />
    </div>
    <div>{{ t('academy.class.notificationDescription') }}</div>
    <template #footer>
      <el-button @click="showPostponeClass = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitPostpone" type="primary" :loading="formLoading">{{ t('dialog.confirm') }}</el-button>
    </template>
  </Dialog>

</template>

<script setup lang="ts">
import download from '@/utils/download'
import { CourseApi } from '@/api/academy/course'
import {ClassInfoApi, ClassInfoRespVO, ClassStatusEnum,ClassTypeEnum} from '@/api/academy/class'
import ClassForm from './ClassForm.vue'
import PostponeClass from './components/postponeClass.vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {
  getMonthStartAndEnd,
  getNextMonthStartAndEnd,
  getQuarterStartAndEnd,
  getWeekStartAndEnd,
  getYearStartAndEnd
} from '@/utils/formatDate'

/** postpone合并class信息 列表 */
defineOptions({ name: 'PostponeClass' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(false) // 列表的加载中
const formLoading = ref(false) //
const list = ref<ClassInfoRespVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  courseId: undefined,
  name: undefined,
  type: undefined,
  languages: undefined,
  status: ClassStatusEnum.DRAFT,
  startDate: []
})
const formData = ref({
  classId: '',
  targetClassId: '',
  notification: 1
})
const queryFormRef = ref() // 搜索的表单
const timeType = ref() // 默认当前月
const checkClassInfo = ref({}) // 已经选择的class信息
const showClass = ref(false)
const showPostponeClass = ref(false)
const classId = ref() // 通过首页传递过来的class Id
const templateSelection = ref<string[]>([])
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await ClassInfoApi.getClassInfoPage(queryParams)
    // 根据点击Postpone按钮将外层的课堂id带过来,如果是一样的课堂按id去除
    list.value = res.list?.filter((clazz: ClassInfoRespVO) => clazz.id !== classId.value)
    total.value = list.value?.length
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  timeType.value = 2
  // 课程id和语言id不允许全部清空
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.name = ''
  queryParams.type = undefined
  queryParams.status = 0
  handleQuery()
}



/** 单选class */
const handleCurrentChange = (currentRow: ClassInfoRespVO, oldCurrentRow) => {
  if (currentRow) {
    checkClassInfo.value = currentRow
    templateSelection.value = [currentRow.id]
  } else {
    templateSelection.value = []
  }
}

const addHoursToDate = (date, hours) => {
  const result = new Date(date)
  result.setHours(result.getHours() + hours)
  return result.toISOString().split('T')[0]
}

/** 打开弹窗 */
const open = async (item: ClassInfoRespVO) => {
  templateSelection.value =  []
  showClass.value = true
  classId.value = item.id
  checkClassInfo.value = {}
  // 根据外层选择的课堂的开始时间筛选出推迟24小时后的课堂
  queryParams.startDate = [`${addHoursToDate(item.startDate, 24)}`, '2099-01-01']
  // 该页面中的Course和要延期的Class的Course  需求
  queryParams.courseId = item.courseId
  queryParams.languages = item.language
  resetQuery()

}
const submitForm = () => {
  if (checkClassInfo.value) {
    formData.value.classId = classId.value
    formData.value.targetClassId = checkClassInfo.value.id
  }
  showPostponeClass.value = true
  // 默认开启发送消息
  formData.value.notification = 1
}
// 最终合并
const submitPostpone = async () => {
  try {
    formLoading.value = true
    await ClassInfoApi.postponeClass(formData.value)
    await message.success(t('academy.class.mergeSuccessful'))
    showClass.value = false
    showPostponeClass.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

</script>
