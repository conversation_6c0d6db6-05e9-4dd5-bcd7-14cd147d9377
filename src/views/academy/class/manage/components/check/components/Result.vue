<template>
  <ContentWrap>
    <div class="my-3.5 flex justify-between items-center">
      <div class="flex">
        <span class="me-5 text-[#BBBBBB]"> {{ t('academy.class.checkInStudentNumber') }} </span>
        <div class="me-5 flex">
          <span class="text-yellow">{{ checkInLength }}</span>
          <span class="text-[#BBBBBB]">/</span>
          <span>{{ total }}</span>
        </div>
        <el-progress :percentage="checkInLength ? parseFloat(((checkInLength / total) * 100).toFixed(2)) : 0" class="w-[400px]" />
      </div>
      <div class="flex">
        <span class="me-5 text-[#BBBBBB]"> {{ t('academy.class.checkOutStudentNumber') }} </span>
        <div class="me-5 flex">
          <span class="text-yellow">{{ checkOutLength }}</span>
          <span class="text-[#BBBBBB]">/</span>
          <span>{{ total }}</span>
        </div>
        <el-progress :percentage="checkOutLength ? parseFloat(((checkOutLength / total) * 100).toFixed(2)) : 0" class="w-[400px]" />
      </div>

    </div>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px mt-5"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="140px"
    >
      <el-form-item :label="t('sys.user.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
          @node-click="handleCompanyClick"
        />
      </el-form-item>
      <el-form-item :label="t('sys.user.department')" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="departOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.departmentPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.attendanceStatus')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ATTENDANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.classroom.name')" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          :placeholder="t('academy.classroom.pleaseInput')"
          clearable
          @input="getUserList"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="checkList" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('academy.classroom.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('sys.user.department')" align="center" prop="deptName" min-width="180px" />
      <el-table-column :label="t('academy.class.badgeNo')" align="center" prop="badgeNo" min-width="180px" />
      <el-table-column :label="t('sys.user.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('academy.class.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('academy.class.checkIn')" align="center" min-width="180px">
        <template #default="scope">
          <el-checkbox-group v-model="scope.row.isCheckIn" disabled>
            <el-checkbox :label="t('academy.class.checkIn')" />
          </el-checkbox-group>
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.checkOut')" align="center" min-width="180px">
        <template #default="scope">
          <el-checkbox-group v-model="scope.row.isCheckOut" disabled>
            <el-checkbox :label="t('academy.class.checkOut')" />
          </el-checkbox-group>
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.attendanceStatus')" align="center" prop="status" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ATTENDANCE_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.checkInTime')" align="center" prop="checkInTime" :formatter="dateFormatter" min-width="180px" />
      <el-table-column :label="t('academy.class.checkOutTime')" align="center" prop="checkOutTime" :formatter="dateFormatter" min-width="180px" />

    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listCompany } from "@/api/system/company"
import { DeptRespVO, listDept } from "@/api/system/dept"
import { handlePhaseTree } from "@/utils/tree"
import { listUser, UserRespVO } from "@/api/system/user"
import { ClassInfoApi, ClassInfoRespVO } from '@/api/academy/class'
import { dateFormatter } from '@/utils/formatTime'
import { useUserStore } from "@/store/modules/user"

defineOptions({ name: 'Result' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const checkInLength = ref(0)
const checkOutLength = ref(0)
const total = ref(0)
const userStore = useUserStore()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  nickname: undefined,
  status: undefined,
  companyId: undefined,
  deptId: undefined,
  userIds:[],
  classIds: route.query.id
})
const queryFormRef = ref() // 搜索的表单
const companyList = ref([])
const departOptions = ref([])
const queryUserParams = ref({
  pageNo: 1,
  pageSize: 99999,
  nickname: undefined
})
const userList = ref([])
const loading = ref(false)
const checkList = ref([])

const getList = async () => {
  loading.value = true
  try {
    const res = await ClassInfoApi.getResultList(queryParams)
    if (res) {
      // 如果数据中的开始时间和结束时间同时不为空,加个参数 为true
      checkList.value = res.list.map((item) => {
        return {
          ...item,
          isCheckIn: item.checkInTime ? true : false,
          isCheckOut: item.checkOutTime ? true : false,
        }
      })
      checkInLength.value = res.list.filter(item => item.isCheckIn).length
      checkOutLength.value = res.list.filter(item => item.isCheckOut).length
    }
    total.value = res.total

  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryUserParams.value = {
    nickname: undefined
  }
  queryFormRef.value.resetFields()
  queryParams.userIds = []
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}

/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}


/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}

/** 查询用户列表 */
const getUserList = async () => {
  queryUserParams.value.nickname = queryParams.nickname
  // 每次调用之前清除一遍用户数据
  queryParams.userIds  = []
  userList.value = await listUser(queryUserParams.value)
  if (userList.value) {
    // 取出数据中的id
    queryParams.userIds = userList.value.list.map((item) => {
      return item.userId
    })
  }
}

defineExpose({ getList })
onMounted(() => {
  getList()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>
