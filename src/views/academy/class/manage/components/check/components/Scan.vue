<template>
  <ContentWrap>
    <div class="mt-[20px]">
      <canvas id="qrcode"></canvas>
<!--      <el-button @click="jumpScan">确认</el-button>-->
    </div>
  </ContentWrap>

</template>

<script setup lang="ts">
import * as ConfigApi from "@/api/infra/config"
const route = useRoute()
const { push } = useRouter()

defineOptions({ name: 'Scan' })
import QRCode from 'qrcode'
const scanUrl = ref('')

const query = {
  id: route.query.id
}

// 跳转单独扫描界面
const jumpScan = () => {
  push({
    name: 'ScanQrCode',
    query: {
      id: route.query.id
    }
  })
}

onMounted(async () => {
  try {
    const url = await ConfigApi.getConfigKey('academy.class.scan.url')

    // 创建一个 URL 对象
    const baseUrl = new URL(url)

    // 添加查询参数
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        baseUrl.searchParams.append(key, value)
      }
    })
    scanUrl.value = baseUrl.toString()
    const element = document.getElementById('qrcode')
    QRCode.toCanvas(element, scanUrl.value,{
      width: 200,
      height: 200,
    });
  } catch (e) {}
})
</script>
<style scoped lang="scss">
</style>

