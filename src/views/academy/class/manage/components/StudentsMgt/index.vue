<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item :label="t('sys.user.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
          @node-click="handleCompanyClick"
        />
      </el-form-item>
      <el-form-item :label="t('sys.user.department')" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="departOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.departmentPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.classroom.name')" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          :placeholder="t('academy.classroom.pleaseInput')"
          clearable
          @input="getUserList"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}</el-button>
<!--        当课堂状态为草稿和未开始的情况下允许添加人员-->
        <el-button
          type="primary"
          plain
          @click="openUser"
          v-show="props.basicInformation.status === ClassStatusEnum.DRAFT || props.basicInformation.status === ClassStatusEnum.NOT_STARTED"
        >
          <Icon icon="ep:plus" class="mr-5px" /> {{ t('academy.class.add') }}
        </el-button>
<!--        展示注释，后续要批量删除解开即可-->
<!--        <el-select-->
<!--          v-model="actionType"-->
<!--          placeholder="Batch Action"-->
<!--          clearable-->
<!--          class="!w-200px ms-3"-->
<!--          @change="changeBatchAction"-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="dict in actionList"-->
<!--            :key="dict.value"-->
<!--            :label="dict.label"-->
<!--            :value="dict.value"-->
<!--            :disabled="checkStudent.length === 0"-->
<!--          />-->
<!--        </el-select>-->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="50" fixed="left" />-->
      <el-table-column :label="t('academy.classroom.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('academy.class.badgeNo')" align="center" prop="badgeNo" min-width="180px" />
      <el-table-column :label="t('sys.user.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('common.projectAsset')" align="center" prop="roomNumber" min-width="180px" />
      <el-table-column :label="t('common.workType')" align="center" prop="workType" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TYPE" :value="scope.row.workType" />
        </template>
      </el-table-column>
      <el-table-column :label="t('common.workTerm')" align="center" prop="workTerms" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TERMS" :value="scope.row.workTerms" />
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('common.pocEmail')" align="center" prop="email" min-width="180px" />
      <el-table-column :label="t('common.pocPhoneNumber')" align="center" prop="roomNumber" min-width="180px" />
      <el-table-column :label="t('common.contractHolder')" align="center" prop="roomNumber" min-width="180px" />
      <el-table-column :label="t('sys.user.department')" align="center" prop="roomNumber" min-width="180px" />
      <el-table-column :label="t('common.ePassportNo')" align="center" prop="totalSeats" min-width="180px" />
      <el-table-column :label="t('common.bookingMode')" align="center" prop="roomNumber" min-width="180px" />
      <el-table-column :label="t('academy.class.bookingTime')" align="center" prop="createTime" min-width="180px" :formatter="dateFormatter" />
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="changeRemove(scope.row.id,1)"
          >
            {{ t('common.remove') }}
          </el-button>
        </template>
      </el-table-column>

    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 人员选择弹框 -->
  <EmployeeSelect
    ref="selectEmployeeRef"
    @confirm="employeeConfirm"
  />

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listCompany } from "@/api/system/company"
import { DeptRespVO, listDept } from "@/api/system/dept"
import { handlePhaseTree } from "@/utils/tree"
import { listUser, UserRespVO } from "@/api/system/user"
import { ClassInfoApi, ClassInfoRespVO,ClassStatusEnum } from '@/api/academy/class'
import { dateFormatter } from '@/utils/formatTime'
import { useUserStore } from "@/store/modules/user"
import EmployeeSelect from '@/components/EmployeeMultipleSelect/index.vue'
defineOptions({ name: 'StudentsManagement' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const userStore = useUserStore()
const checkInLength = ref(0)
const checkOutLength = ref(0)
const total = ref(0)
const props = defineProps<{
  classId: string | number
  basicInformation: any
}>()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  nickname: undefined,
  companyId: undefined,
  deptId: undefined,
  userIds:[],
  classIds: route.query.id
})
const queryFormRef = ref() // 搜索的表单
const companyList = ref([])
const departOptions = ref([])
const selectEmployeeRef = ref()
const queryUserParams = ref({
  pageNo: 1,
  pageSize: 99999,
  nickname: undefined
})
const userList = ref([])
const loading = ref(false)
const list = ref([])
const actionType = ref()
const actionList = computed(() => [
  { label: t('common.batchRemove'), value: 1 },
])
const checkStudent = ref([])


const formData = ref({
  classId: route.query.id,
  type: 1,
  userReqVOList: []
})

const employeeConfirm = async (data: any) => {
  formData.value.userReqVOList = data?.map((item) => {
    // 产品需求更改:后续有需求说要在用户模块新增字段信息,就不需要按照在线学院的FRS中的需求:(Contract Holder：当学员Work Type是contract时，需要填写Contractor Holder。),用户模块新增完字段将数据带过来即可
    return {
      userId: item.id,
    }
  })
  try {
    await ClassInfoApi.getClassAssign(formData.value)
    message.success(t('common.createSuccess'))
    getList()
  } finally {
  }
}
const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.getStudentList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  actionType.value = undefined
  queryFormRef.value.resetFields()
  queryParams.userIds = []
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

const openUser = () => {
  const ids = list.value?.map((item) => item.userId)
  selectEmployeeRef.value.open(ids)
}


// 多选
const handleSelectionChange = (selection: any[]) => {
  checkStudent.value = []
  selection.forEach(item => {
    checkStudent.value.push(item.id)
  })
}

// 移除学员 1.单个 2.批量
const changeRemove = async (id: number | string[],type: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    let idStr
    if (type === 1) {
      idStr = id
    } else {
      idStr = checkStudent.value.join(',')
    }
    await ClassInfoApi.removeStudent(idStr)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
    actionType.value = undefined
  } catch {}
}

const changeBatchAction = (val: number) => {
  switch (val) {
    case 1:
      changeRemove(checkStudent.value,2)
      break
    default:
      break
  }
}

// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}

/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}


/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'deptId')
  }
}

/** 查询用户列表 */
const getUserList = async () => {
  queryUserParams.value.nickname = queryParams.nickname
  // 每次调用之前清除一遍用户数据
  queryParams.userIds = []
  userList.value = await listUser(queryUserParams.value)
  if (userList.value.list) {
    // 取出数据中的id
    queryParams.userIds = userList.value.list.map((user) => {
      return user.userId
    })
  }
}

defineExpose({ getList })
onMounted(() => {
  getList()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>
