<template>
<!--  该组件暂时不用 但是勿删除！！！-->
  <el-drawer title="Test Reminder" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="160px"
      v-loading="formLoading"
    >
<!--      Todo-->
      <el-form-item label="Reminder Mode" prop="mode">
        <el-checkbox-group v-model="formData.mode">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.REMINDER_MODE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="Notification Title" prop="title">
        <el-input v-model="formData.title" placeholder="Please input notification title" />
      </el-form-item>
      <el-form-item label="Content" prop="content">
        <el-input v-model="formData.content" type="textarea" />
      </el-form-item>
      <el-form-item label="Notification Scope" prop="notificationScope">
        <el-radio-group v-model="formData.notificationScope">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.NOTIFICATION_SCOPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-radio-group>
      </el-form-item>
      <el-form-item label="Notification Time" prop="NotificationTime">
        <el-radio-group v-model="formData.NotificationTime">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.NOTIFICATION_TIME)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">Cancel</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">Save</el-button>
    </template>
  </el-drawer>

</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi } from '@/api/academy/class'
/** 延期申请 表单 */
defineOptions({ name: 'TestReminder' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  moed: [],
  title: '',
  content: '',
  notificationScope: undefined,
  NotificationTime: undefined
})
const formRules = reactive({
  mode: [{ required: true, message: '提醒模式不能为空', trigger: 'change' }],
  title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '内容不能为空', trigger: 'blur' }],
  notificationScope: [{ required: true, message: '通知范围不能为空', trigger: 'change' }],
  NotificationTime: [{ required: true, message: '通知时间不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    await ClassInfoApi.testReminder(formData.value)
    message.success('Successful operation')
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    mode: [],
    title: '',
    content: '',
    notificationScope: undefined,
    NotificationTime: undefined
  }
  formRef.value?.resetFields()
}
</script>
