<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="140px"
    >
      <el-form-item :label="t('sys.user.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
          @node-click="handleCompanyClick"
        />
      </el-form-item>
      <el-form-item :label="t('sys.user.department')" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="departOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.departmentPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.attendanceStatus')" prop="attendanceStatus">
        <el-select
          v-model="queryParams.attendanceStatus"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ATTENDANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.class.status')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_ROSTER_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('academy.classroom.name')" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          :placeholder="t('academy.classroom.pleaseInput')"
          clearable
          @input="getUserList"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}</el-button>
<!--        暂时隐藏-->
<!--        <el-button type="success" plain @click="changeReminder">-->
<!--          <Icon icon="ep:refresh" class="mr-5px" />-->
<!--          Test Reminder-->
<!--        </el-button>-->
        <el-select
          v-model="actionType"
          :placeholder="t('common.batchAction')"
          clearable
          class="!w-200px ms-3"
          @change="changeBatchAction"
        >
          <el-option
            v-for="dict in actionList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
            :disabled="checkStudent.length === 0"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" fixed="left" />
      <el-table-column v-if="status" :label="t('common.ddtPermitNo')" align="center" prop="ddtPermitNo" min-width="180px" />
      <el-table-column :label="t('academy.classroom.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('academy.class.badgeNo')" align="center" prop="badgeNo" min-width="180px" />
      <el-table-column v-if="status" :label="t('common.dateOfBirth')" align="center" prop="dateOfBirth" min-width="180px" />
      <el-table-column :label="t('sys.user.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('common.projectAsset')" align="center" prop="roomNumber" min-width="180px" />
      <el-table-column :label="t('common.workType')" align="center" prop="workType" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TYPE" :value="scope.row.workType" />
        </template>
      </el-table-column>
      <el-table-column :label="t('common.workTerm')" align="center" prop="workTerms" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TERMS" :value="scope.row.workTerms" />
        </template>
      </el-table-column>
      <el-table-column v-if="status" :label="t('common.drivingLicenceNumber')" align="center" prop="drivingLicenceNumber" min-width="180px" />
      <el-table-column v-if="status" :label="t('common.issuingDate')" align="center" prop="issuingDate" min-width="180px" :formatter="dateFormatter" />
      <el-table-column v-if="status" :label="t('common.expiryDate')" align="center" prop="expiryDate" min-width="180px" :formatter="dateFormatter" />
      <el-table-column v-if="status" :label="t('common.vehicle')" align="center" prop="vehicle" min-width="180px" />
      <el-table-column :label="t('academy.class.position')" align="center" prop="positionName" min-width="180px" />
<!--      <el-table-column label="Full/Refresher" align="center" prop="totalSeats" min-width="180px" />-->
      <el-table-column v-if="status" :label="t('common.eyeTest')" align="center" prop="eyeTest" min-width="180px" />
      <el-table-column :label="t('academy.class.attendanceStatus')" align="center" prop="attendanceStatus" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ATTENDANCE_STATUS" :value="scope.row.attendanceStatus" />
        </template>
      </el-table-column>
      <el-table-column :label="t('common.testResult')" align="center" prop="status" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CLASS_ROSTER_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
<!--      当时ddt课程的时候是没有操作功能的-->
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="280px" v-if="!status">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleStatus(scope.row.id,1,1)"
          >
            {{ t('common.reject') }}
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleStatus(scope.row.id,1,2)"
          >
            {{ t('common.pass') }}
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleStatus(scope.row.id,1,3)"
          >
            {{ t('common.fail') }}
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleStatus(scope.row.id,1,4)"
          >
            {{ t('common.postpone') }}
          </el-button>
        </template>
      </el-table-column>

    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <TestReminder
    ref="testReminderRef"
  />

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listCompany } from "@/api/system/company"
import { DeptRespVO, listDept } from "@/api/system/dept"
import { handlePhaseTree } from "@/utils/tree"
import { listUser, UserRespVO } from "@/api/system/user"
import { ClassInfoApi } from '@/api/academy/class'
import TestReminder from './TestReminder.vue'
import { dateFormatter } from '@/utils/formatTime'
import { useUserStore } from "@/store/modules/user"
import {getTopic, TopicDdtEnum} from "@/api/category/training"

defineOptions({ name: 'ClassRosterStudent' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const userStore = useUserStore()
const total = ref(0)
const status = ref() // 记录是否ddt 状态(0.否 1.是)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  attendanceStatus: undefined,
  status: undefined,
  companyId: undefined,
  deptId: undefined,
  userIds:[],
  classIds: route.query.id,
  nickname: undefined
})
const queryFormRef = ref() // 搜索的表单
const testReminderRef = ref()
const companyList = ref([])
const departOptions = ref([])
const queryUserParams = ref({
  pageNo: 1,
  pageSize: 99999,
  nickname: undefined
})
const userList = ref([])
const loading = ref(false)
const checkStudent = ref([])
const actionType = ref()
const actionList = computed(() => [
  { label: t('common.batchReject'), value: 1 },
  { label: t('common.batchPass'), value: 2 },
  { label: t('common.batchFail'), value: 3 },
  { label: t('common.batchPostpone'), value: 4},
])

const queryData = ref({
  ids: [],
  status: 0,
  classId: route.query.id
})

const list = ref([])

const getList = async () => {
  loading.value = true
  try {
    const res = await ClassInfoApi.getRosterStudentList(queryParams)
    list.value = res.list
    total.value = res.total
    actionType.value = undefined
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  actionType.value = undefined
  queryFormRef.value.resetFields()
  queryParams.userIds = []
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

/** 选择条数  */
const handleSelectionChange = (selection: any[]) => {
  checkStudent.value = []
  selection.forEach(item => {
    checkStudent.value.push(item.id)
  })
}
const handleStatus = async (id: number, type: number,status: number) => {
  queryData.value.status = status
  try {
    // 1.单个 2.批量
    if (type === 1) {
      queryData.value.ids = [id]
    } else {
      queryData.value.ids = checkStudent.value
    }
    await ClassInfoApi.rejectStudent(queryData.value)
    message.success(t('common.successfulOperation'))
    getList()
  } catch {}
}

const changeBatchAction = (val: number) => {
  // 检查 val 是否在预期范围内
  if ([1, 2, 3, 4].includes(val)) {
    handleStatus(undefined, 2, val);
  }
}

// 提醒延期的学生参加补考
const changeReminder = () => {
  testReminderRef.value.open()
}

// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}

/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}


/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}

/** 查询用户列表  */
const getUserList = async () => {
  queryUserParams.value.nickname = queryParams.nickname
  // 每次调用之前清除一遍用户数据
  queryParams.userIds  = []
  userList.value = []
  userList.value = await listUser(queryUserParams.value)
  if (userList.value.list) {
    // 取出数据中的id
    queryParams.userIds = userList.value.list.map((item) => {
      return item.userId
    })
  }
}

const getInfo = async (id: number) => {
  const data = await ClassInfoApi.getClassInfo(id)
  // 获取课程的分类id
  const categoryId = data?.courseDO?.categoryId
  // 获取当前分类信息是否是HSE还是DDT，调用分类详情接口获取状态
  const topicInfo = await getTopic(categoryId)
  status.value = topicInfo.ddt === TopicDdtEnum.YES ? true : false
}

defineExpose({ getList })
onMounted(() => {
  getInfo(route.query.id as unknown as number)
  getList()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>
