<template>
  <!-- 顶部返回 -->
  <div class="w-full h-[140px] bg-[#E4F4EE] rounded-[10px]">
    <el-row :gutter="20">
      <el-col :span="5">
        <div class="flex flex-col text-[#BBBBBB] ms-8 text-m">
          <span class="mt-2">{{ t('academy.class.planned') }}</span>
          <span>{{ t('academy.class.attendance') }}: {{ total }}</span>
          <span class="mt-2">{{ t('academy.class.actualAttendance') }}: {{ attendanceLength }}</span>
          <span class="mt-4">{{ t('academy.class.noShow') }}: {{ noShowLength }}</span>
        </div>
      </el-col>
      <el-col :span="19">
        <div class="mt-3">
          <el-progress type="dashboard" :percentage="attendanceLength ? parseFloat(((attendanceLength / total) * 100).toFixed(2)) : 0">
            <template #default="{ percentage }">
              <span class="percentage-value">{{ percentage }}%</span>
              <span class="percentage-label">{{ t('academy.class.attendanceRate') }}</span>
            </template>
          </el-progress>
        </div>
      </el-col>
    </el-row>
  </div>
  <el-tabs v-model="activeName">
    <el-tab-pane :label="t('academy.class.student')" name="1">
      <Student />
    </el-tab-pane>
    <el-tab-pane :label="t('sys.user.company')" name="2">
      <Company />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
defineOptions({ name: 'ClassRoster' })
import Student from './components/Student.vue'
import Company from './components/Company.vue'
import {ClassAttendanceStatusEnum, ClassInfoApi} from '@/api/academy/class'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const activeName = ref('1') // 默认选择第一个卡片
const attendanceLength = ref(0)
const total = ref(0)
const noShowLength = ref(0)
const getList = async () => {
  const res = await ClassInfoApi.getStudentList({classIds: route.query.id,pageNo:1, pageSize: -1})
  total.value = res.total
  res.list.map((item) => {
    return {
      ...item,
      isCheckIn: item.checkInTime && item.checkOutTime ? true : false,
    }
  })
  attendanceLength.value = res.list?.filter(item => item.attendanceStatus === ClassAttendanceStatusEnum.SIGNED_IN).length
  noShowLength.value = res.list?.filter(item => item.attendanceStatus === ClassAttendanceStatusEnum.NOT_SIGNED_IN).length
}
onMounted(() => {
  getList()
})
</script>
<style scoped lang="scss">
:deep(.el-divider--vertical) {
  height: 200px;
  margin-left: 40px;
}
.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 30px;
}
.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}
.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}
.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>
