<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="60px"
    >
      <el-form-item :label="t('academy.classroom.name')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="t('academy.classroom.pleaseInput')"
          clearable
          @input="getResource"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}</el-button>
        <el-button type="success" plain @click="handleUpload"><Icon icon="ep:upload" class="mr-5px" /> {{ t('common.uploadFile') }}</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('common.materialName')" align="center" prop="title" min-width="180px">
        <template #default="scope">
          <el-link :underline="false" @click="handleReview(scope.row.resourceId,scope.row.mediaType)">{{ scope.row.title }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="ID" align="center" prop="identity" min-width="180px" />
      <el-table-column :label="t('common.type')" align="center" prop="mediaType" min-width="180px">
        <template #default="scope">
          <el-tag v-if="scope.row.mediaType === 1">{{ t('common.video') }}</el-tag>
          <el-tag v-if="scope.row.mediaType === 3">{{ t('common.pdf') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.creationTime')" align="center" prop="time" min-width="180px" :formatter="dateFormatter" />
      <el-table-column :label="t('table.action')" align="center" fixed="right" min-width="180px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row.resourceNewId)"
          >
            {{ t('common.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <UploadMaterials
  ref="uploadMaterials"
  @success="getList"
  />

  <ResourcePreview v-model="previewShow" :data="previewData" :media-type="activeName" />

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi } from '@/api/academy/class'
import UploadMaterials from './components/UploadMaterials.vue'
import { dateFormatter } from '@/utils/formatTime'
import { getResource, listResource } from "@/api/resource/list"
import ResourcePreview from '@/components/ResourcePreview/index.vue'
import { MediaType } from '@/enums/resource'
defineOptions({ name: 'MaterialsManagement' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  classId: route.query.id,
  resourceId: undefined,
  title: undefined
})

const queryResourceForm = ref({
  pageNum: 1,
  pageSize: 99999,
  title: undefined
})

const queryFormRef = ref() // 搜索的表单
const uploadMaterials = ref()
const loading = ref(false)
const list = ref([])
const previewShow = ref(false)
const previewData = ref()
const activeName = ref()

const getResource = async () => {
  queryResourceForm.value.title = queryParams.title
  // 每次调用之前清除一遍数据
  queryParams.resourceId = undefined
  const data = await listResource(queryResourceForm.value)
  if (data.list) {
    queryParams.resourceId = data.list[0].id
  }

}
const getList = async () => {
  loading.value = true
  try {
    const res = await ClassInfoApi.getMaterialPage(queryParams)
    // 获取到所有的资源id，调用资源列表接口获取所有信息，通过resourceId参数去查询，
    // 拿到所有信息后,通过data.list中的resourceId去resourceData.list中的id匹配，
    // 成功后在将data.list中的id以一个新的参数插入到指定的数组对象中，这个id用来外层删除课堂附件使用
    const resourceId = res.list.map((item) => item.resourceId)
    const resourceData = await listResource({ resourceIds: resourceId.join(',')})
    resourceData.list.forEach((item) => {
      const matchingItem = res.list.find((resource) => resource.resourceId === item.id)
      if (matchingItem) {
        item.resourceNewId = matchingItem.id
      }
    })
    list.value = resourceData.list
    total.value = resourceData.list.length
  } finally {
    loading.value = false
  }
}


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const handleUpload = () => {
  uploadMaterials.value.open()
}

// 移除课件
const handleDelete = async (id: number) => {
  await message.delConfirm()
  await ClassInfoApi.deleteMaterial(id)
  message.success(t('common.delSuccess'))
  await getList()
}

// 预览课件
const handleReview = async (id: number,mediaType: number) => {
  // 1.视频 3. pdf 通过id获取资源信息进行预览
  const data =  await getResource(id)
  previewData.value = data
  previewShow.value = true
  activeName.value = mediaType
}


defineExpose({ getList })
onMounted(() => {
  getList()
})
</script>
