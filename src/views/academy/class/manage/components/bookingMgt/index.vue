<template>
  <ContentWrap>
    <div class="rounded-x py-5 px-7">
      <span class="text-[#222222] text-xl"> {{ t('academy.class.bookingManagement') }} </span>
      <div class="my-3.5 flex">
        <span class="me-5 text-[#BBBBBB]"> {{ t('academy.class.bookingOverallProgress') }}</span>
        <div class="me-5 flex">
          <span class="text-yellow">{{ approvedLength }}</span>
          <span class="text-[#BBBBBB]">/</span>
          <span>{{ total }}</span>
        </div>
        <el-progress :percentage="approvedLength ? parseFloat(((approvedLength / total) * 100).toFixed(2)) : 0" class="w-[400px]" />
      </div>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px mt-5"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="80px"
      >
        <el-form-item :label="t('sys.user.company')" prop="companyId">
          <el-tree-select
            v-model="queryParams.companyId"
            :data="companyList"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            :placeholder="t('sys.user.companyPH')"
            check-strictly
            clearable
            filterable
            class="!w-240px"
            @node-click="handleCompanyClick"
          />
        </el-form-item>
        <el-form-item :label="t('sys.user.department')" prop="deptId">
          <el-tree-select
            v-model="queryParams.deptId"
            :data="departOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            :placeholder="t('sys.user.departmentPH')"
            check-strictly
            clearable
            filterable
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item :label="t('academy.class.status')" prop="status">
          <el-select
            v-model="queryParams.status"
            :placeholder="t('academy.classroom.pleaseSelect')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.BOOKING_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('academy.classroom.name')" prop="nickname">
          <el-input
            v-model="queryParams.nickname"
            :placeholder="t('academy.classroom.pleaseInput')"
            clearable
            @input="getUserList"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('academy.classroom.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('academy.class.badgeNo')" align="center" prop="badgeNo" min-width="180px" />
      <el-table-column :label="t('sys.user.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('sys.user.department')" align="center" prop="deptName" min-width="180px" />
      <el-table-column :label="t('academy.class.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('academy.class.bookingTime')" align="center" prop="createTime" min-width="180px" :formatter="dateFormatter" />
      <el-table-column :label="t('academy.class.bookingStatus')" align="center" prop="status" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BOOKING_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="t('table.action')" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleReview(scope.row.id)"
            :disabled="scope.row.status === 2 || scope.row.status === ClassBookingStatusEnum.APPROVED"
          >
            {{ t('academy.class.review') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

<!--  审核界面-->
  <Review
    ref="reviewRef"
    @success="getList"
  />

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import Review from './components/Review.vue'
import { ClassInfoApi, ClassInfoRespVO,ClassBookingStatusEnum } from '@/api/academy/class'
import { listCompany } from "@/api/system/company"
import { DeptRespVO, listDept } from "@/api/system/dept"
import { handlePhaseTree } from '@/utils/tree'
import { listUser, UserRespVO } from "@/api/system/user"
import { dateFormatter } from '@/utils/formatTime'
import { useUserStore } from "@/store/modules/user"
defineOptions({ name: 'BookingManagement' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const userStore = useUserStore()
const loading = ref(false) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const approvedLength = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  nickname: undefined,
  status: undefined,
  companyId: undefined,
  deptId: undefined,
  userIds:[],
  classId: route.query.id
})
const props = defineProps<{
  classId: string | number
  basicInformation: any
}>()
const queryFormRef = ref() // 搜索的表单
const formRef = ref()
const reviewRef = ref()
const companyList = ref([]) // 公司列表
const departOptions = ref()
const queryUserParams = ref({
  pageNo: 1,
  pageSize: 99999,
  nickname: undefined
})
const userList = ref([])

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.getBookingList(queryParams)
    list.value = data.list
    // 筛选出来approved状态的人数
    if (data.list) {
      approvedLength.value = data.list?.filter((item) => item.status === ClassBookingStatusEnum.APPROVED).length
    }
    total.value = data.total
  } finally {
    loading.value = false
  }
}


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryUserParams.value = {
    nickname: undefined
  }
  queryFormRef.value.resetFields()
  queryParams.userIds = []
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}
// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}
/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}

/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}


/** 查询用户列表 */
const getUserList = async () => {
  queryUserParams.value.nickname = queryParams.nickname
  // 每次调用之前清除一遍用户数据
  queryParams.userIds  = []
  userList.value = await listUser(queryUserParams.value)
  if (userList.value.list) {
    // 取出数据中的id
    queryParams.userIds = userList.value.list.map((item) => {
      return item.userId
    })
  }
}

/** 审核 */
const handleReview = (id: number) => {
  reviewRef.value.open(id, props.basicInformation)
}

/** 初始化 **/
onMounted(() => {
  getList()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>
