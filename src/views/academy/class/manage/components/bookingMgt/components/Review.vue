<template>
  <el-drawer v-model="dialogVisible" :size="1100">
    <template #header>
      <div  class="flex justify-between items-center">
        <div class="flex">
          <div class="flex items-center w-[200px] truncate">
            {{ classInfo?.courseName }}
          </div>
          <div class="ms-[30px]">
            <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="classInfo?.language"  class="me-5" />
            <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="bookingInfo?.type" />
          </div>
        </div>
        <dict-tag :type="DICT_TYPE.BOOKING_STATUS" :value="classInfo?.status" />
      </div>
    </template>
    <div class="flex flex-col">
      <span>{{ t('academy.class.trainingDescription') }}</span>
      <span class="text-[#BBBBBB] mt-2">
        {{ courseInfo?.remarks }}
      </span>
    </div>
    <div class="flex flex-col mt-3">
      <span>{{ t('academy.class.startEndTime') }}</span>
      <span class="text-[#BBBBBB] mt-2">
        {{ classInfo?.startTime && classInfo?.endTime	? `${classInfo?.startDate} ${classInfo?.startTime} ~ ${classInfo?.startDate} ${classInfo?.endTime}` : ''}}
      </span>
    </div>
    <div class="mt-10">
      <el-row :gutter="10">
        <el-col :span="12">
          <span>{{ t('academy.class.bookingInformation') }}</span>
          <el-form
            ref="formRef"
            label-width="180px"
          >
            <el-form-item :label="t('academy.class.allowedBookingTime') + ':'">
              <el-text class="mx-1">{{ formatDate(classInfo?.bookingStartTime) }} - {{ formatDate(classInfo?.bookingEndTime) }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.bookingRate') + ':'">
              <el-text class="mx-1">{{ classInfo?.assignNum ? classInfo?.assignNum : 0  }}/ {{ classInfo?.maxNum }} </el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.bookingTime') + ':'">
              <el-text class="mx-1">{{ formatDate(bookingInfo?.createTime) }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.prerequisite') + ':'">
              <el-row :gutter="20">
                <el-col :span="24">
                  <div>
                    {{ t('academy.course.course') }}
                  </div>
                </el-col>
                <el-col :span="24" v-show="bookingInfo?.courseJson">
                  <div style="overflow-wrap: break-word" class="min-h-[100px] overflow-y-auto">
                    <el-tag
                      class="tag mt-1 me-2"
                      type="primary"
                      :disable-transitions="false"
                      v-for="(item,index) in bookingInfo?.courseJson"
                      :key="index"
                    >
                      <div class="flex justify-center align-center">
                        {{ item.courseName }}
                        <Icon v-show="item.existStudy" icon="ep:check" class="ms-2" />
                        <Icon v-show="!item.existStudy" icon="ep:close" />
                      </div>
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="24">
                  <div>
                    {{ t('academy.class.attachment') }}
                  </div>
                </el-col>
                <el-col :span="24" v-show="attachmentInfo">
                  <div style="overflow-wrap: break-word" class="min-h-[100px] overflow-y-auto">
                    <el-tag
                      class="me-2 mb-2 cursor-pointer"
                      type="primary"
                      :disable-transitions="false"
                      v-for="(item,index) in attachmentInfo"
                      :key="index"
                      @click="handleReview(item.url,3)"
                    >
                      {{ item?.name }}
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="t('academy.class.reviewProcess') + ':'">
              <!-- 审批记录时间线 -->
              <ProcessInstanceTimeline :activity-nodes="activityNodes" />
            </el-form-item>
            <el-form-item v-if="isApprove">
              <div class="ms-28">
                <el-button type="primary" :disabled="formLoading" @click="handleApprove">
                  {{ t('academy.class.approve') }}
                </el-button>
                <el-button type="info" :disabled="formLoading" @click="handleReject">
                  {{ t('academy.class.reject') }}
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <span>{{ t('academy.class.studentInformation') }}</span>
          <el-form
            ref="formRef"
            label-width="180px"
          >
            <el-form-item :label="t('academy.classroom.name') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.nickname }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.badgeNo') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.badgeNo }}</el-text>
            </el-form-item>
            <el-form-item :label="t('system.user.company') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.companyName }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.position') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.positionName }}</el-text>
            </el-form-item>
            <el-form-item :label="t('academy.class.fullRefresher') + ':'">
              <el-text class="mx-1 overflow-hidden">{{ bookingInfo?.firstStudy ? t('academy.class.full') : t('academy.class.refresher') }}</el-text>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>

  <ResourcePreview v-model="previewShow" :data="previewData" />

</template>
<script setup lang="ts">
import { Position, User, DocumentChecked } from '@element-plus/icons-vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi, ClassInfoRespVO } from '@/api/academy/class'
import * as TaskApi from "@/api/bpm/task"
import { formatDate } from "@/utils/formatTime"
import * as ProcessInstanceApi from "@/api/bpm/processInstance"
import ProcessInstanceTimeline from './ProcessInstanceTimeline.vue'
import { CourseApi } from "@/api/academy/course"
import { getTodoTaskDetail } from "@/api/bpm/task"
import ResourcePreview from '@/components/ResourcePreview/index.vue'
/** 审核界面  */
defineOptions({ name: 'Review' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 抽屉的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formRef = ref() // 表单 Ref
const bookingInfo = ref()
const bookingId = ref()
const courseInfo = ref()
const attachmentInfo = ref([])
// 审批节点信息
const activityNodes = ref<ProcessInstanceApi.ApprovalNodeInfo[]>([])
const classInfo = ref()
const isApprove = ref(false)
const taskId = ref()
const previewShow = ref(false)
const previewData = ref()

// 获取任务id
const getTaskInfo = async (processInstanceId: number) => {
  const data = await TaskApi.getTodoTaskDetail(processInstanceId)
  // 如果有值证明该当前账号属于他审批的节点,展示审批通过和拒绝按钮;如果未返回值则证明不属于他审批的节点,则不展示按钮
  if (data) {
    isApprove.value = true
    taskId.value = data
  } else {
    isApprove.value = false
  }
}
/** 打开弹窗 */
const open = async (id: number,classDetail) => {
  taskId.value = undefined
  classInfo.value = classDetail
  dialogVisible.value = true
  bookingId.value = id
  previewData.value = {
    url: '',
    mediaType: undefined
  }
  await getInfo(id)
  await getCourseInfo(classDetail?.courseId)
}

// 获取预定的信息
const getInfo = async (id: number) => {
  bookingInfo.value =  await ClassInfoApi.getBookingDetail(id)
  if (bookingInfo.value?.courseJson !== '') {
    bookingInfo.value.courseJson = JSON.parse(bookingInfo.value?.courseJson)
  }
  if (bookingInfo.value?.attachmentJson !== '') {
    // 将字符串转换为数组形式
    attachmentInfo.value = JSON.parse(bookingInfo.value?.attachmentJson)
  }
  // 获得审批详情(审批图)
  await getApprovalDetail(bookingInfo.value?.processInstanceId)
  // 使用流程实例id获取任务id
  await getTaskInfo(bookingInfo.value?.processInstanceId)

}

// 获取课程信息详情
const getCourseInfo = async (courseId: number) => {
  courseInfo.value = await CourseApi.getCourse(courseId)
}

/** 获取审批详情 */
const getApprovalDetail = async (processInstanceId: number) => {
  try {
    const param = {
      processInstanceId: processInstanceId,
      taskId: processInstanceId
    }

    const data = await ProcessInstanceApi.getApprovalDetail(param)
    // 获取审批节点，显示 Timeline 的数据
    activityNodes.value = data.activityNodes
  } catch {}
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const handleApprove = async () => {
  formLoading.value = true
  try {
    await TaskApi.approveTask({ id: taskId.value })
    message.success(t('academy.class.approvalPassedSuccessfully'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}


const handleReject = async () => {
  formLoading.value = true
  try {
    await TaskApi.rejectTask({ id: taskId.value })
    message.success(t('academy.class.approvalFailed'))
    dialogVisible.value = false
    // 发送操作成功事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
// 预览附件 文件类型默认为文件 type: 3
const handleReview = async (url: number,mediaType: number) => {
  const fileInfo = {
    address: url,
    mediaType: mediaType
  }
  previewData.value = fileInfo
  previewShow.value = true
}
</script>
<style scoped lang='scss'>
:deep(.steps) {
.el-step__icon.is-text {
  color: transparent;
  border: 1px solid #007943;
}
}
</style>
