<template>
  <ContentWrap>
    <el-row :gutter="20">
      <el-col :span="5">
        <div class="mt-[20px]">
          <canvas id="qrcode"></canvas>
        </div>
      </el-col>
      <el-col :span="5">
        <div class="mt-[50px]">
          <el-button @click="fullscreenPreview">
            {{ t('common.projectQRCode') }}
          </el-button>
        </div>
        <div class="mt-[50px]">
          <el-button @click="downloadQrCode">
            {{ t('common.downloadQRCode') }}
          </el-button>
        </div>
      </el-col>
    </el-row>
  </ContentWrap>

  <!-- 全屏预览Dialog -->
  <el-dialog
    v-model="dialogVisible"
    :title="t('common.projectQRCode')"
    width="600px"
    center
    :show-close="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <div class="flex justify-center items-center">
      <canvas id="qrcode-fullscreen" class="max-w-full max-h-full"></canvas>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('dialog.close') }}</el-button>
      <el-button type="primary" @click="downloadFullscreenQrCode">{{ t('common.downloadQRCode') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import * as ConfigApi from "@/api/infra/config"

defineOptions({ name: 'Launch' })
import { ClassInfoApi } from '@/api/academy/class'
import QRCode from 'qrcode'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const dialogVisible = ref(false)
const feedBackUrl = ref('')
// 全屏预览二维码
const fullscreenPreview = () => {
  dialogVisible.value = true
  // 等待Dialog渲染完成后生成二维码
  nextTick(() => {
    const element = document.getElementById('qrcode-fullscreen')
    if (element) {
      QRCode.toCanvas(element, feedBackUrl.value, {
        width: 400,
        height: 400,
      })
    }
  })
}
// 下载二维码
const downloadQrCode = () => {
  const canvas = document.getElementById('qrcode') as HTMLCanvasElement
  const image = canvas.toDataURL('image/png')
  const link = document.createElement('a')
  link.href = image
  link.download = 'QR Code.png'
  link.click()
}

// 下载全屏二维码
const downloadFullscreenQrCode = () => {
  const canvas = document.getElementById('qrcode-fullscreen') as HTMLCanvasElement
  if (canvas) {
    const image = canvas.toDataURL('image/png')
    const link = document.createElement('a')
    link.href = image
    link.download = 'QR Code Fullscreen.png'
    link.click()
  }
}


onMounted(async () => {
  try {
    // 通过配置模块所配置的url进行跳转用户端填写反馈
    feedBackUrl.value = await ConfigApi.getConfigKey('academy.class.feedback.url')
    const element = document.getElementById('qrcode');
    QRCode.toCanvas(element, feedBackUrl.value,{
      width: 200,
      height: 200,
    });
  } catch (e) {}
})
</script>
<style scoped lang="scss">
</style>
