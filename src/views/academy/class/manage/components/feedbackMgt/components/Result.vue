<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item :label="t('sys.user.company')" prop="companyId">
        <el-tree-select
          v-model="queryParams.companyId"
          :data="companyList"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.companyPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
          @node-click="handleCompanyClick"
        />
      </el-form-item>
      <el-form-item :label="t('sys.user.department')" prop="deptId">
        <el-tree-select
          v-model="queryParams.deptId"
          :data="departOptions"
          :props="{ value: 'id', label: 'name', children: 'children' }"
          value-key="id"
          :placeholder="t('sys.user.departmentPH')"
          check-strictly
          clearable
          filterable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item :label="t('academy.class.comments')" prop="comments">
        <el-select
          v-model="queryParams.comments"
          :placeholder="t('academy.classroom.pleaseSelect')"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.FEEDBACK_COMMENTS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('academy.class.search') }}</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('academy.class.reset') }}</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column :label="t('academy.classroom.name')" align="center" prop="nickname" min-width="180px" />
      <el-table-column :label="t('academy.class.badgeNo')" align="center" prop="badgeNo" min-width="180px" />
      <el-table-column :label="t('sys.user.company')" align="center" prop="companyName" min-width="180px" />
      <el-table-column :label="t('sys.user.department')" align="center" prop="deptName" min-width="180px" />
      <el-table-column :label="t('academy.class.position')" align="center" prop="positionName" min-width="180px" />
      <el-table-column :label="t('academy.class.evaluationTime')" align="center" prop="createTime" min-width="180px" :formatter="dateFormatter" />
      <el-table-column :label="t('academy.class.courseEvaluation')" prop="courseEvaluation" min-width="160px">
        <template #default="{ row }">
          <div class="flex justify-between items-center">
            <span>{{ evaluationEnum[row.courseEvaluation] }}</span>
            <el-image :src="row.courseEvaluation === ClassEvaluationEnum.POSITIVE ? positive : row.courseEvaluation === ClassEvaluationEnum.NEUTRAL ? netural : row.courseEvaluation === ClassEvaluationEnum.NEGATIVE ? negative : ''" class="w-[20px] h-[20px]" />
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.trainerEvaluation')" prop="trainerEvaluation" min-width="160px">
        <template #default="{ row }">
          <div class="flex justify-between items-center">
            <span>{{ evaluationEnum[row.trainerEvaluation] }}</span>
            <el-image :src="row.trainerEvaluation === ClassEvaluationEnum.POSITIVE ? positive : row.trainerEvaluation === ClassEvaluationEnum.NEUTRAL ? netural : row.trainerEvaluation === ClassEvaluationEnum.NEGATIVE ? negative : ''" class="w-[20px] h-[20px]" />
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.facilityEvaluation')" prop="facilityEvaluation" min-width="160px">
        <template #default="{ row }">
          <div class="flex justify-between items-center">
            <span>{{ evaluationEnum[row.facilityEvaluation] }}</span>
            <el-image :src="row.facilityEvaluation === ClassEvaluationEnum.POSITIVE ? positive : row.facilityEvaluation === ClassEvaluationEnum.NEUTRAL ? netural : row.facilityEvaluation === ClassEvaluationEnum.NEGATIVE ? negative : ''" class="w-[20px] h-[20px]" />
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="t('academy.class.comments')" align="center" prop="comments" min-width="180px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.FEEDBACK_COMMENTS" :value="scope.row.comments" />
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { listCompany } from "@/api/system/company"
import { DeptRespVO, listDept } from "@/api/system/dept"
import { handlePhaseTree } from "@/utils/tree"
import { ClassInfoApi,ClassEvaluationEnum } from '@/api/academy/class'
import positive from '@/assets/images/class/positive.svg'
import netural from '@/assets/images/class/netural.svg'
import negative from '@/assets/images/class/negative.svg'
import { evaluationEnum } from '@/utils/class'
import { dateFormatter } from '@/utils/formatTime'
import { useUserStore } from "@/store/modules/user"

defineOptions({ name: 'FeedbackResult' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const userStore = useUserStore()
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  comments: undefined,
  companyId: undefined,
  deptId: undefined,
  classId: route.query.id
})
const queryFormRef = ref() // 搜索的表单
const companyList = ref([])
const departOptions = ref([])
const loading = ref(false)
const list = ref([])

const getList = async () => {
  loading.value = true
  try {
    const res = await ClassInfoApi.getFeedbackPage(queryParams)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  getDepartmentTree(userStore.user.companyId)
  handleQuery()
}

// 获取公司数据
const getCompanyTree = async () => {
  companyList.value = await listCompany()
}

/** 点击公司树查询下面的人员 */
const handleCompanyClick = (node: DeptRespVO) => {
  departOptions.value = []
  getDepartmentTree(node.id)
}

/** 查询部门下拉树结构 */
const getDepartmentTree = async (companyId: number) => {
  const data = await listDept({ companyId })
  if (data) {
    departOptions.value = handlePhaseTree(data, 'id')
  }
}



defineExpose({ getList })
onMounted(() => {
  getList()
  getCompanyTree()
  getDepartmentTree(userStore.user.companyId)
})
</script>
