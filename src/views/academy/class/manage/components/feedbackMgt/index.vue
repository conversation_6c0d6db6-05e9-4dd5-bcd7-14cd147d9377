<template>
  <el-tabs v-model="activeName" @tab-change="selectActive">
    <el-tab-pane :label="t('common.launch')" name="1">
      <Launch />
    </el-tab-pane>
    <el-tab-pane :label="t('common.result')" name="2">
      <Result ref="resultRef" />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
defineOptions({ name: 'FeedBack' })
import Launch from './components/Launch.vue'
import Result from './components/Result.vue'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const activeName = ref('1') // 默认选择第一个卡片
const resultRef = ref()
const selectActive = (val: string | number) => {
  // 每次切换result实时更新数据
  if (Number(val) === 2) {
    resultRef.value.getList()
  }
}
</script>
