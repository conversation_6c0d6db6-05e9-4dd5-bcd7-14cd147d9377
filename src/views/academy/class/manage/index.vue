<template>
  <div v-loading="loading">
    <!-- 顶部返回 -->
    <div class="w-full h-[140px] bg-[#E4F4EE] flex items-center rounded-[10px] px-4">
      <div class="flex gap-5 mr-16">
        <el-image :src="courseDefaultImage" class="w-[154px] h-[90px] row-span-2 rounded-lg" />
        <div class="pt-2.5">
          <span class="text-[#23293A] text-xl break-all">{{ basicInformation?.name}} </span>
          <div class="flex gap-1.5 mt-3 text-[#BBBBBB]">
            <Icon icon="ep:user" />
            <el-text class="text-sm" type="info">{{ basicInformation?.assignNum ? `${t('academy.class.owner')}(${basicInformation.assignNum})` : `${t('academy.class.owner')}(0)` }} </el-text>
            <Icon icon="ep:pieChart" />
            <el-text class="text-sm" type="info">{{ t('academy.class.startEndTime') }}:</el-text>
            <el-text class="text-sm" type="info">
              {{ basicInformation?.startTime && basicInformation?.endTime	 ? `${date} ${basicInformation.startTime} ~ ${date} ${basicInformation.endTime}` : ''}}
            </el-text>
          </div>
        </div>
      </div>
      <div class="ms-auto flex-shrink-0">
        <div class="flex flex-col cursor-pointer">
          <el-button @click="handleWithdraw" type="primary" :disabled="basicInformation?.status === ClassStatusEnum.ONGOING || basicInformation?.status === ClassStatusEnum.ENDED || basicInformation?.status === ClassStatusEnum.POSTPONED || basicInformation?.status === ClassStatusEnum.CANCELLED">
            <Icon :icon="basicInformation?.publishStatus === ClassPublishStatusEnum.PUBLISHED ? 'ep:refresh-left' : 'ep:position'" class="mr-5px" />
            {{ basicInformation?.publishStatus === ClassPublishStatusEnum.PUBLISHED ? t('academy.class.withdraw') : t('academy.class.publish') }}
          </el-button>
        </div>
      </div>
    </div>
    <!-- 下方课程设置区域 -->
    <div class="flex mt-6 gap-5">
      <!-- 左侧菜单 -->
      <ContentWrap>
        <div class="w-[220px] shrink-0 rounded-x p-2.5 border border-[#CDDBF1]">
          <el-menu
            v-model="menuActive"
            :default-active="menuActive"
            :style="{
            '--el-menu-item-height': '34px',
          }"
            @select="handleSelect"
          >
            <el-menu-item v-for="menu in menuItems" :key="menu.id" :index="`${menu.id}`" :disabled="menu.disabled" v-hasPermi="menu.perms">
              {{ menu.text }}
            </el-menu-item>
          </el-menu>
        </div>
      </ContentWrap>
      <!-- 右侧组件内容 -->
      <div class="flex-1 overflow-hidden">
        <component
          ref="dynamicComponentRef"
          :is="components[menuActive]"
          v-bind="{
            classId,
            basicInformation,
          }"
        />
      </div>
    </div>
  </div>
<!--撤回操作-->
  <Dialog v-model="showWithdraw" :title="t('academy.class.withdrawClass')" width="500">
    <div>{{ t('academy.class.confirmWithdrawClass') }}</div>
    <div class="flex justify-between items-center">
      <span class="text-black">{{ t('academy.class.messageNotification') }}</span>
      <el-switch v-model="formData.notification" size="large" :active-value="1" :inactive-value="0" />
    </div>
    <div>{{ t('academy.class.notificationDescription') }}</div>
    <template #footer>
      <el-button @click="showWithdraw = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitWithdrawForm" type="primary" :loading="formLoading">{{ t('dialog.confirm') }}</el-button>
    </template>
  </Dialog>

<!--  发布操作-->
  <Dialog v-model="showPublish" :title="t('academy.class.publishClass')" width="500">
    <div>{{ t('academy.class.confirmPublishClass') }}</div>
    <div class="flex justify-between items-center">
      <span class="text-black">{{ t('academy.class.messageNotification') }}</span>
      <el-switch v-model="formCancelData.notification" size="large" :active-value="1" :inactive-value="0" />
    </div>
    <div>{{ t('academy.class.notificationDescription') }}</div>
    <template #footer>
      <el-button @click="showPublish = false">{{ t('academy.class.cancel') }}</el-button>
      <el-button @click="submitPublishForm" type="primary" :loading="formLoading">{{ t('dialog.confirm') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts" name="Manage">
import { formatDate } from '@/utils/formatTime'
import { ClassInfoApi,ClassPublishStatusEnum,ClassStatusEnum } from '@/api/academy/class'
import BasicInformation from './components/Basicinformation/index.vue'
import BookingManagement from './components/bookingMgt/index.vue'
import Check from './components/check/index.vue'
import StudentsManagement from './components/StudentsMgt/index.vue'
import FeedbackManagement from './components/feedbackMgt/index.vue'
import MaterialsManagement from './components/materialsMgt/index.vue'
import ClassRoster from './components/classRoster/index.vue'
import { formatImgUrl } from '@/utils'
import { formatTime } from '@/utils/formatDate'
import courseDefaultImage from "@/assets/images/academyCourse/courseDefaultImage.png"

import { useTagsViewStore } from "@/store/modules/tagsView"
const { delView } = useTagsViewStore() // 视图操作
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const route = useRoute()
const { router, push, currentRoute } = useRouter() // 路由
const classId = route.query.id
const showWithdraw = ref(false)
const showPublish = ref(false)
const dynamicComponentRef = ref()
// 发布课堂
const formData = ref({
  ids: [route.query.id],
  notification: 1,
  publishStatus: ClassPublishStatusEnum.PUBLISHED  // 状态(0.未发布 1.发布)
})
// 取消课堂
const formCancelData = ref({
  ids: [route.query.id],
  notification: 1,
  publishStatus: ClassPublishStatusEnum.UNPUBLISHED
})
const formLoading = ref(false)
const date = ref()
const menuItems = computed(() => [
  {
    id: 1,
    text: t('academy.class.basicInformation'),
    perms: ['academy:class:manage-basic-info-tab'],
  },
  {
    id: 2,
    text: t('academy.class.bookingManagement'),
    perms: ['academy:class:manage-booking-tab'],
  },
  {
    id: 3,
    text: t('academy.class.checkInOut'),
    perms: ['academy:class:manage-check-tab'],
  },
  {
    id: 4,
    text: t('academy.class.studentsManagement'),
    perms: ['academy:class:manage-students-tab'],
  },
  {
    id: 5,
    text: t('academy.class.feedbackManagement'),
    perms: ['academy:class:manage-feedback-tab'],
  },
  {
    id: 6,
    text: t('academy.class.materialsManagement'),
    perms: ['academy:class:manage-materials-tab'],
  },
  {
    id: 7,
    text: t('academy.class.classRoster'),
    perms: ['academy:class:manage-roster-tab'],
  }
])

const menuActive = ref((route.query.active as string) || '1')
const components: Record<any, Component> = {
  1: BasicInformation,
  2: BookingManagement,
  3: Check,
  4: StudentsManagement,
  5: FeedbackManagement,
  6: MaterialsManagement,
  7: ClassRoster
}
const loading = ref(false)
const basicInformation = ref()
const handleSelect = (key: string) => {
  menuActive.value = key
}

const handleWithdraw = () => {
  if (basicInformation.value.publishStatus === 1) {
    showWithdraw.value = true
  } else {
    showPublish.value = true
  }
  // 默认发送消息提醒
  formData.value.notification = 1
  formCancelData.value.notification = 1
}

// 撤回操作
const submitWithdrawForm = async () => {
  formLoading.value = true
  try {
    await ClassInfoApi.publishClass(formCancelData.value)
    await message.success(t('academy.class.withdrawSuccessfully'))
    await getInfo(route.query.id as unknown as number)
    showWithdraw.value = false
    // 更新子组件的详情内容,控制子组件的编辑按钮是否可以操作
    dynamicComponentRef.value.getInfo(route.query.id as number)
  } finally {
    formLoading.value = false
  }
}
// 发布操作
const submitPublishForm = async () => {
  formLoading.value = true
  try {
    await ClassInfoApi.publishClass(formData.value)
    await message.success(t('academy.class.publishSuccessfully'))
    await getInfo(route.query.id as unknown as number)
    showPublish.value = false
    // 更新子组件的详情内容,控制子组件的编辑按钮是否可以操作
    dynamicComponentRef.value.getInfo(route.query.id as number)
  } finally {
    formLoading.value = false
  }
}


const handleBack = () => {
  delView(unref(currentRoute))
  push('/academy/class')
}
/** 获取课堂基本信息 */
const getInfo = async (id: number) => {
  basicInformation.value = await ClassInfoApi.getClassInfo(id)
  date.value = formatDate(basicInformation.value?.createTime).split(' ')[0]
}
onMounted(() => {
  if (route.query.id) {
    getInfo(route.query.id as unknown as number)
  }
})
</script>


<style scoped lang="scss">
:deep(.el-menu) {
  border-right: 0 !important;
  .el-menu-item {
    &.is-active {
      background-color: #E4F4EE;
      border-radius: 4px;
    }
  }
}
</style>
