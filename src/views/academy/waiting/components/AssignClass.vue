<template>
  <el-drawer :title="t('common.assign')" v-model="dialogVisible" :size="1500">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="120px"
      >
        <el-form-item :label="t('common.type')" prop="type">
          <el-select
            v-model="queryParams.type"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('common.language')" prop="language">
          <el-select
            v-model="queryParams.language"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.TRAINING_COURSE_LANGUAGE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('common.publishStatus')" prop="publishStatus">
          <el-select
            v-model="queryParams.publishStatus"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_PUBLISH_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('common.status')" prop="status">
          <el-select
            v-model="queryParams.status"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.CLASS_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('academy.class.time')">
          <el-select
            v-model="timeType"
            :placeholder="t('common.selectText')"
            clearable
            class="!w-240px"
            @change="changeTime"
          >
            <el-option
              v-for="dict in dateDictOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('common.date')" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            :start-placeholder="t('common.start')"
            :end-placeholder="t('common.end')"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item :label="t('academy.class.title')" prop="name">
          <el-input
            v-model="queryParams.name"
            :placeholder="t('common.pleaseInput')"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> {{ t('global.search') }}</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> {{ t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @current-change="handleSelectionChange">
        <el-table-column label="" width="34">
          <template #default="{ row }">
            <el-radio-group v-model="templateSelection[0]">
              <el-radio :label="row.id" size="small">
                {{}}
              </el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column :label="t('common.courseTitle')" align="center" prop="courseName" min-width="180px" />
        <el-table-column :label="t('common.classTitle')" align="center" prop="name" min-width="180px" />
        <el-table-column :label="t('common.classType')" align="center" prop="type" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASS_TYPE" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column :label="t('common.trainer')" align="center" prop="trainerName" min-width="180px" />
        <el-table-column :label="t('academy.course.language')" align="center" prop="language" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.TRAINING_COURSE_LANGUAGE" :value="scope.row.language" />
          </template>
        </el-table-column>
        <el-table-column :label="t('common.classroom')" align="center" prop="classRoomName" min-width="180px" />
        <el-table-column :label="t('common.date')" align="center" prop="startDate" min-width="180px" />
        <el-table-column :label="t('academy.class.duration')" align="center" min-width="180px">
          <template #default="scope">
            {{ scope.row.startTime }} - {{ scope.row.endTime }}
          </template>
        </el-table-column>
        <el-table-column :label="t('common.bookingNumber')" align="center" min-width="180px">
          <template #default="scope">
            <div>
              {{ scope.row.type === ClassTypeEnum.VIRTUAL_CLASS ? scope.row.assignNum : `${ scope.row.assignNum } / ${ scope.row.maxNum }` }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('common.publishStatus')" align="center" prop="publishStatus" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASS_PUBLISH_STATUS" :value="scope.row.publishStatus" />
          </template>
        </el-table-column>
        <el-table-column :label="t('common.status')" align="center" prop="status" min-width="180px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.CLASS_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
      <el-button @click="submitForm" type="primary" :disabled="!formData.classId" :loading="formLoading">{{ t('common.save') }}</el-button>
    </template>
  </el-drawer>


</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ClassInfoApi, ClassInfoRespVO,ClassTypeEnum } from '@/api/academy/class'
import {
  getMonthStartAndEnd,
  getNextMonthStartAndEnd,
  getWeekStartAndEnd,
  getYearStartAndEnd
} from "@/utils/formatDate"

defineOptions({ name: 'AssignClass' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const route = useRoute()
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  courseId: undefined,
  name: undefined,
  type: undefined,
  language: undefined,
  publishStatus: undefined,
  status: undefined,
  createTime: []
})
const userIdList = ref([])

const dateDictOptions = ref([
  { label: t('common.thisWeek'), value: 1 },
  { label: t('common.thisMonth'), value: 2 },
  { label: t('common.nextWeek'), value: 3 },
  { label: t('common.thisYear'), value: 4 },
])
const templateSelection = ref<string[]>([])

const formData = ref({
  classId: undefined,
  type: 2,
  userReqVOList: [],
  waitingCourseId: undefined
})

const queryFormRef = ref() // 搜索的表单
const loading = ref(false)
const dialogVisible = ref(false)
const formLoading = ref(false)
const list = ref([])
const timeType = ref()

const getList = async () => {
  loading.value = true
  try {
    const data = await ClassInfoApi.getClassInfoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  templateSelection.value = []
  formData.value.classId = undefined
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  queryParams.name = undefined
  queryParams.type = undefined
  queryParams.language = undefined
  queryParams.publishStatus = undefined
  queryParams.status = undefined
  queryParams.createTime = []
  handleQuery()
}
/** 选择条数  */
const handleSelectionChange = (selection: ClassInfoRespVO) => {
  if (selection) {
    formData.value.classId = selection.id
    templateSelection.value = [selection.id]
    getAssignList(selection.id)
  }

}

// 根据课堂id查看已分配列表
const getAssignList = async (classIds: number) => {
  const data = await ClassInfoApi.getStudentList({pageNo: 1,pageSize: -1, classIds})
  const ids = data.list?.map((item) => item.userId)
  // 先合并用户id
  userIdList.value = userIdList.value?.concat(ids)
}

const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调


const submitForm = async () => {
// 提交请求
  // 去重操作
  const ids = [...new Set(userIdList.value)]
  // 组装数据
  formData.value.userReqVOList = ids?.map(id =>{
    return {
      userId: id
    }
  })
  formLoading.value = true
  try {
    await ClassInfoApi.getClassAssign(formData.value)
    message.success(t('common.allocationSuccessful'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}


// 选择时间
const changeTime = (val: number) => {
  switch (val) {
    case 1:
      // 获取当前天的星期一到星期天的开始时间和结束时间
      queryParams.createTime = getWeekStartAndEnd();
      break;
    case 2:
      // 获取当前天的月的开始时间和结束时间
      queryParams.createTime = getMonthStartAndEnd();
      break;
    case 3:
      // 获取当前天的下个月开始时间和结束时间
      queryParams.createTime = getNextMonthStartAndEnd();
      break;
    case 4:
      // 获取当前年的开始时间和结束时间
      queryParams.createTime = getYearStartAndEnd();
      break;
    default:
      break;
  }
}

/** 打开弹窗 */
const open = async (userIds: number | number[],courseId: number) => {
  formData.value.userReqVOList = []
  userIdList.value = [userIds]
  dialogVisible.value = true
  // 用来查询必须课程内容保持一致的的课程id
  queryParams.courseId = courseId
  formData.value.waitingCourseId  = courseId
  resetQuery()
}

defineExpose({ open })
</script>
