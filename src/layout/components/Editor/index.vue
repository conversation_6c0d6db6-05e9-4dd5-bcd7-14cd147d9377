<script setup lang="ts">
import Editor from '@tinymce/tinymce-vue'

const props = defineProps({
  // route object
  modelValue: {
    type: String,
    required: true
  }
})
const emits = defineEmits(['update:modelValue'])
const value = computed({
  get: () => props.modelValue,
  set: val => emits('update:modelValue', val)
})

const init = ref({
  toolbar_mode: 'sliding',
  // plugins:
  //   'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount checklist mediaembed casechange export formatpainter pageembed linkchecker a11ychecker tinymcespellchecker permanentpen powerpaste advtable advcode editimage advtemplate ai mentions tinycomments tableofcontents footnotes mergetags autocorrect typography inlinecss markdown',
  plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table',
  toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat',
  toolbar_mode: 'wrap',
  tinycomments_mode: 'embedded',
  tinycomments_author: 'Author name',
  mergetags_list: [
    { value: 'First.Name', title: 'First Name' },
    { value: 'Email', title: 'Email' }
  ],
  ai_request: (request, respondWith) => respondWith.string(() => Promise.reject('See docs to implement AI Assistant'))
})
</script>

<template>
  <div>
    <Editor v-model="value" api-key="y09i2v3vbc3yrsig0nghvraycmct4eth8xn6etlsxaosid5m" :init="init" />
  </div>
</template>

<style lang="scss" scoped></style>
