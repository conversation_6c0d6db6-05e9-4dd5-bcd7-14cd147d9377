import type { RouteMeta } from 'vue-router'
import { Icon } from '@/components/Icon'
import { useI18n } from '@/hooks/web/useI18n'
import { getLangMenu } from '@/utils/dict'
export const useRenderMenuTitle = () => {
    // 当是home菜单单独处理进行去国际化包中匹配,反之取后端返回的国际化值
  const renderMenuTitle = (meta: RouteMeta) => {
    const { t } = useI18n()
    const { title = 'Please set title', icon } = meta

    return icon ? (
      <>
        <Icon icon={meta.icon}></Icon>
        <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
          { title === 'router.home' ? t('router.home') : getLangMenu(meta as AppCustomRouteRecordRaw)}
        </span>
      </>
    ) : (
      <span class="v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap">
        { title === 'router.home' ? t('router.home') : getLangMenu(meta as AppCustomRouteRecordRaw)}
      </span>
    )
  }

  return {
    renderMenuTitle
  }
}
