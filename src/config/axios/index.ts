import { service } from './service'

import { config } from './config'
import { CACHE_KEY, useCache } from "@/hooks/web/useCache"
const { wsCache } = useCache()
const { default_headers, phaseI_base_url } = config

const request = (option: any) => {
  const { headersType, headers, baseURL, ...otherOption } = option
  return service({
    ...otherOption,
    headers: {
      'Content-Type': headersType || default_headers,
      'Lang': wsCache.get(CACHE_KEY.LANG),
      // 'tag': 'f75759dd1937',
      // 'tag': 'constantine',
      ...headers
    },
    baseURL
  })
}
export default {
  get: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', ...option })
    return res.data as unknown as T
  },
  post: async <T = any>(option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res.data as unknown as T
  },
  postOriginal: async (option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res
  },
  delete: async <T = any>(option: any) => {
    const res = await request({ method: 'DELETE', ...option })
    return res.data as unknown as T
  },
  put: async <T = any>(option: any) => {
    const res = await request({ method: 'PUT', ...option })
    return res.data as unknown as T
  },
  download: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  upload: async <T = any>(option: any) => {
    option.headersType = 'multipart/form-data'
    const res = await request({ method: 'POST', ...option })
    return res as unknown as Promise<T>
  },
  phaseIPut: async <T = any>(option: any) => {
    const res = await request({ method: 'PUT', ...option, baseURL: phaseI_base_url })
    return res.data as unknown as T
  },
}
