<script setup lang='ts'>
import type { ComponentInternalInstance } from 'vue'
import { differenceWith, merge, toUpper, unionBy } from 'lodash-es'
import type { FormRules, UploadStatus } from 'element-plus'
import type { CustomUploadUserFile } from '../LargeFileUpload/index.vue'
import CustomProgress from '../LargeFileUpload/components/CustomProgress.vue'
import ResourceSelector from '@/components/ResourceSelector/index.vue'
import type { Resource } from '@/typings/views/Resource/list'
import { formatBytes, formatSecond } from '@/utils/ruoyi'
import { formatFileType } from '@/views/resource/list/components/BatchUpload/script'
import LargeFileUpload from '@/components/LargeFileUpload/index.vue'
import { CourseOrigin } from '@/enums/CourseOrigin'
import { MediaType } from '@/enums/resource'
import DurationInput from '@/views/resource/list/components/DurationInput/index.vue'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
type UploadResponse = NonNullable<CustomUploadUserFile['response']>

interface SelectedResource {
  origin: CourseOrigin
  format: string | undefined
  mediaType: MediaType
  lang: string[]
  duration: number | undefined
  fileId: number | undefined
  size: number | undefined
  url: string | undefined
  name: string | undefined
  resourceId: number | undefined
  title: string | undefined // 仅Course使用
}
const props = withDefaults(defineProps<{ limit?: number, edit: boolean, ooc: boolean, course: boolean, appedixs: boolean, pageNameType: string, isFromRepository: boolean, }>(), {
  limit: 99,
  edit: false,
  ooc: false,
  course: false,
  appedixs: false,
  pageNameType: '',
  isFromRepository: true, // 在线学院上传附件不支持从资源库选择,公用上传组件功能
})
const { t } = useI18n()
const uploadRef = ref<InstanceType<typeof LargeFileUpload>>()
const show = ref(false)
const ids = ref<number[]>([])
const selectedResources = defineModel<SelectedResource[]>()
const rules = ref<FormRules<{ lang: string, name: string, duration: number }>>({
  lang: [{ required: true, message: t('learningCenter.course.languageRule'), trigger: 'change' }],
  name: [{ required: true, message: t('sys.user.userNicknameRule'), trigger: 'blur' }],
  title: [{ required: false, message: t('learningCenter.course.titleRule'), trigger: 'blur' }],
  duration: [{ required: true, validator: (rule, value, callback) => {
    if (!value || value === 0) {
      callback(new Error(t('learningCenter.course.durationRule')))
      return
    }
    callback()
  } }],
})
/** 从资源选择器选择的文件信息 */
const fromResource = ref<Resource[]>([])
/** 从大文件上传组件来的文件信息 */
const fromUpload = ref<UploadResponse[]>([])
const fileList = ref<CustomUploadUserFile[]>()
/** 创建uid对应selectedResource中的index下标，创建index下标对应的uid */
const uploadIndex: Record<number, number> = {}
/** 创建fileid对应的index,index对应的fileId */
const uploadFileIdIndex: Record<number, number> = {}
const uploadResult = computed(() => {
  return fileList.value?.reduce((prev, cur) => {
    prev[cur.uid!] = {
      viewStatus: cur.viewStatus,
      percentage: cur.percentage,
      index: cur.index,
    }
    return prev
  }, {} as Record<number, { viewStatus: UploadStatus | undefined, percentage: number | undefined, index: number | undefined }>)
})

/** 表单间距值 */
const courseFormItemWidth = ref(12)
const generateData = (mediaType: MediaType, origin: CourseOrigin, response?: Partial< UploadResponse>, resource?: Resource) => {
  return {
    origin,
    format: response?.format || resource?.format,
    mediaType,
    lang: resource?.lang ? resource.lang.split(',') : ['1'],
    duration: response?.duration ?? resource?.duration ?? 0,
    fileId: response?.id ?? resource?.fileId,
    size: response?.origin?.size ?? resource?.size,
    url: response?.url || resource?.address,
    name: response?.name || resource?.title,
    resourceId: resource?.id,
    title: response?.name || resource?.title,
  }
}
/** 资源库选择资源完毕事件 */
const handleConfirm = (ids: number[] | undefined, resources: Resource[] | undefined) => {
  const mapData = (data: Resource) => {
    return generateData(data.mediaType, CourseOrigin.Repository, undefined, data)
  }
  show.value = false
  // if (!resources?.length)
  //   return
  if (!selectedResources.value?.length || props.limit === 1) {
    selectedResources.value = resources?.map(item => mapData(item))
    return
  }
  // 过滤掉当前已经存在的资源数据
  const formatResource = resources?.filter((item) => {
    return !(selectedResources.value?.find(res => res.resourceId === item.id))
  }).map(item => mapData(item))
  selectedResources.value = [...selectedResources.value, ...formatResource || []]
}
const emit = defineEmits(['confirm'])
/**
 * 设置文件uid对应的index,和index对应的uid
 * @param fileUid 文件的uid
 * @param index 对应的下标
 */
const setUploadIndex = (fileUid?: number, index?: number) => {
  if (fileUid !== undefined && index !== undefined) {
    uploadIndex[fileUid] = index
    uploadIndex[index] = fileUid
  }
}
/**
 * 设置文件id对应的index,和index对应的id
 * @param fileId 文件的fileId
 * @param index 对应的下标
 */
const setUploadFileIdIndex = (fileId: number, index: number) => {
  if (uploadFileIdIndex[fileId] !== undefined) {
    return
  }
  uploadFileIdIndex[fileId] = index
  uploadFileIdIndex[index] = fileId
}
/**
 * 文件初始化完毕
 * @param file 当前初始化完毕的文件
 * @param files 当前初始化完毕的文件数组
 */
const handleInit = (file: CustomUploadUserFile, files: CustomUploadUserFile[]) => {


  // 组织一部分数据初始化
  const response: Partial<UploadResponse> = {
    name: file.name,
    duration: file.duration,
    mediaType: file.mediaType!,
    origin: {
      size: file.size!,
      name: file.name!,
      fullName: file.name!,
      type: file.instance?.fileInfo?.type || '',
    },
  }
  // 初始化选择的文件信息
  const genData = generateData(file.mediaType!, CourseOrigin.Local, response, undefined)
  if (!selectedResources.value?.length || props.limit === 1) {
    selectedResources.value = [genData]
    setUploadIndex(file.uid, 0)
    return
  }
  selectedResources.value.push(genData)
  setUploadIndex(file.uid, selectedResources.value.length - 1)

}
/**
 * 上传成功后的事件
 * @param response 接口结果
 * @param file 文件信息
 * @param files 整体上传的文件数组
 */
const handleUploadSuccess = (response: UploadResponse, file: CustomUploadUserFile, files: CustomUploadUserFile[]) => {
  if (!file.duration) {
    if (file.mediaType === MediaType.Scorm) {
      file.duration = 600
    }
    else if (file.mediaType === MediaType.File) {
      file.duration = 300
    }
  }
  response.duration = file.duration
  const resourceInfo = response?.resourceInfo
  // 获取当前上传成功的文件，对应selectedResources的下标
  const index = uploadIndex[file.uid!]

  const genData = generateData(response!.mediaType, resourceInfo ? CourseOrigin.Repository : CourseOrigin.Local, response, resourceInfo)
  merge(selectedResources.value![index], genData)
  // 查找当前列表是否已经存在从资源库选择，但是文件id与当前上传的文件id一致的，如果一致则证明当前上传的文件已经出现过了，更新文件fileid对应的index为资源库选择出来的文件对应的index，否则正常设置
  const selectIndex = selectedResources.value!.findIndex(item => item.fileId === response?.id && item.origin === CourseOrigin.Repository)

  if (selectIndex > -1) {
    setUploadFileIdIndex(response!.id, selectIndex)
  }
  else {
    setUploadFileIdIndex(response!.id, index)
  }
  emit('confirm', selectedResources.value)
}
/**
 * 大文件上传组件删除功能
 * @param file 已经删除的文件信息
 * @param files 删除过后的文件数组
 */
const handleUploadDelete = (file: CustomUploadUserFile, files: CustomUploadUserFile[]) => {
  if (file.response?.resourceInfo) {
    fromResource.value.splice()
  }
  fromUpload.value = files
}
/**
 * 选择资源点击事件
 */
const handleSelectResource = () => {
  show.value = true
  const filterResources = selectedResources.value?.filter(item => item.resourceId)
  console.log('filter', filterResources)

  ids.value = filterResources ? filterResources.map(item => item.resourceId!) : []
}
const handleDelete = (resource: SelectedResource, index: number) => {
  selectedResources.value?.splice(index, 1)
  // 如果来源是文件上传，调用文件上传的删除
}
/**
 * 判断当前文件是否存在
 * @param resource 当前文件信息
 */
const checkExist = (resource: SelectedResource, index: number) => {
  // 查找当前fileId第一次出现的下标
  const firstIndex = uploadFileIdIndex[resource.fileId!]
  if (firstIndex === undefined)
    return false
  if (index === firstIndex)
    return false
  return true
}
// 清除列表展示文件信息
const clearFile = () => {
  selectedResources.value = []
}
defineExpose({ clearFile }) // 提供 open 方法，用于打开弹窗
</script>

<template>
  <div>
    <div class="flex w-full gap-2.5">
      <LargeFileUpload ref="uploadRef" v-model:file-list="fileList" class="custom-course-upload" multiple :limit="props.limit" :show-list="false" :allow-types="props.pageNameType" @success="handleUploadSuccess" @delete="handleUploadDelete" @init="handleInit">
        <template #tip>
          <span v-if="props.course" class="text-[#abacae]">{{ t('common.uploadFormatMessage') }}</span>
          <span v-else-if="props.ooc" class="text-[#abacae]">{{ t('common.uploadFormatMessage2') }}</span>
          <span v-else-if="props.appedixs && !props.course" class="text-[#abacae] cursor-pointer" :title="t('common.uploadFormatMessage3')">{{ t('common.uploadFormatMessage3') }}</span>
        </template>
      </LargeFileUpload>
      <div v-show="isFromRepository" class="flex flex-col justify-center gap-2.5 items-center basis-72 border border-dashed border-[#D6DEE3] cursor-pointer active:border-primary duration-300 transition-all mb-8" @click="handleSelectResource">
        <svg-icon icon-class="SelectResource" class="text-[22px]" />
        <span class="text-sm text-primary">{{ t('common.fromRepository') }}</span>
      </div>
    </div>
    <div>
      <!-- 第二行操作行 -->
      <el-form :model="selectedResources" inline class="mt-5">
        <el-row
          v-for="(item, index) in selectedResources"
          :key="item.fileId"
          class="border border-dashed p-3.5 mt-3.5"
          :class="uploadResult?.[item.fileId!]?.viewStatus === 'fail' ? 'border-[--el-color-danger]' : 'border-[#D6DEE3]'"
          :gutter="20"
        >
          <template v-if="checkExist(item, index)">
            <div class="mt-5 text-[#E2A03B]">
              {{ t('common.noExist') }}
            </div>
          </template>
          <template v-else>
            <!-- 第一行 文件名称与删除按钮 -->
            <!-- couse中title同name，允许修改title -->
            <el-col v-if="props.course" :span="24" class="!flex items-center justify-between mb-2.5">
              <el-form-item label="Title" :prop="`[${index}].title`" class="!w-full" :rules="rules.title">
                <el-input v-if="props.course" v-model="item.title" clearable class="!w-3/5" />
                <span v-else class="text-sm text-[#233A35] line-clamp-1 mr-10">{{ item.title }}</span>
                <el-link
                  type="danger"
                  @click="handleDelete(item, index)"
                  :underline="false"
                >
                  <Icon icon="ep:delete" class="mr-1" />
                </el-link>
              </el-form-item>
            </el-col>
            <el-col v-else :span="24" class="!flex items-center justify-between mb-2.5">
              <el-form-item label="Name" :prop="`[${index}].name`" class="!w-full" :rules="rules.name">
                <span class="text-sm text-[#233A35] line-clamp-1 mr-10">{{ item.name }}</span>
                <el-link
                  type="danger"
                  @click="handleDelete(item, index)"
                  :underline="false"
                >
                  <Icon icon="ep:delete" class="mr-1" />
                </el-link>
              </el-form-item>
            </el-col>
            <!-- 语言 -->
            <el-col v-if="!props.appedixs" :span="(item.origin === CourseOrigin.Repository || !props.edit) ? 6 : 12">
              <el-form-item :prop="`[${index}].lang`" :rules="rules.lang">
                <template #label>
                  <div class="h-full w-full flex items-center">
                    <svg-icon icon-class="Lang" class="text-base" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.language') }}</span>
                  </div>
                </template>
                <dict-tag v-if="item.origin === CourseOrigin.Repository || !props.edit" :type="DICT_TYPE.RESOURCE_LIST_LANG" :value="item.lang" />
                <!-- 非资源库选择，设置为可选择 -->
                <!-- 非资源库选择， 编辑时不可选择 -->
                <el-select v-else v-model="item.lang" clearable multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="1" style="width: 240px;">
                  <el-option v-for="lang in getStrDictOptions(DICT_TYPE.RESOURCE_LIST_LANG)" :key="lang.value" :label="lang.label" :value="lang.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 时长 -->
            <el-col v-if="!props.appedixs " :span="(item.origin === CourseOrigin.Repository || [MediaType.Audio, MediaType.Video].includes(item.mediaType) || !props.edit) ? 6 : 12">
              <el-form-item :prop="`[${index}].duration`" :rules="rules.duration">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="time" class="text-base text-[#606266]" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.duration') }}</span>
                  </div>
                </template>
                <span v-if="item.origin === CourseOrigin.Repository || [MediaType.Audio, MediaType.Video].includes(item.mediaType) || !props.edit" class="font-normal ms-1.5 text-[#606266]">{{ formatSecond(item.duration || 0) }}</span>
                <DurationInput v-else v-model="item.duration" />
              </el-form-item>
            </el-col>
            <!-- 文件大小 -->
            <el-col v-if="!props.appedixs" :span="6">
              <el-form-item prop="size">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="cube" class="text-base text-[#606266]" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.size') }}</span>
                  </div>
                  <span class="font-normal ms-1.5 whitespace-nowrap">{{ formatBytes(item.size || 0) }}</span>
                </template>
              </el-form-item>
            </el-col>
            <!-- 文件类别，后缀名 -->
            <el-col v-if="!props.appedixs" :span="6">
              <el-form-item prop="format">
                <template #label>
                  <div class="h-full w-full flex items-center gap-1">
                    <svg-icon icon-class="cube" class="text-base text-[#606266]" />
                    <span class="text-sm text-[#606266] font-normal">{{ t('common.format') }}</span>
                  </div>

                  <span class="font-normal ms-1.5 whitespace-nowrap">{{ toUpper(item.format) }}</span>
                </template>
              </el-form-item>
            </el-col>
            <!-- 是否需要展示进度条,如果是 -->
            <el-col v-if="item.origin === CourseOrigin.Local && uploadResult?.[uploadIndex[index]]" :span="24">
              <CustomProgress :percentage="uploadResult?.[uploadIndex[index]].percentage" />
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
    <ResourceSelector v-show="isFromRepository"  v-model:ids="ids" v-model:show="show" :type="props.limit === 1 ? 'single' : 'multiple'" :ooc="props.ooc" :course="props.course" :appendix-value="props.appedixs" @confirm="handleConfirm" />
  </div>
</template>

<style scoped lang='scss'>
:deep(.custom-course-upload){
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
 .el-upload-dragger{
  @apply border-dashed border-[#D6DEE3] rounded-none #{!important};
 }
}

:deep(.el-form-item){
  @apply w-full;
}
:deep .el-tag + .el-tag{
  margin-left: 0;
  margin-top:2px
  }

   /*
* select 多选时tag超长时去掉回车占位
*/
:deep .el-select__selection{
  flex-wrap: nowrap !important;
}
</style>
