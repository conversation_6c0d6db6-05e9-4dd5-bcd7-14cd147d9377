import { Audio, Video } from '../script/FileAllowTypes'

export function readFileDuration(file: any): Promise<number | undefined> {
  return new Promise((resolve) => {
    // 如果是非视频或者音频类文件直接返回undefined
    if (!([...Video, ...Audio].includes(file.type))) {
      resolve(undefined)
      return
    }
    // 获取时长
    const url = URL.createObjectURL(file)
    const videoElement = document.createElement('video')
    videoElement.style.display = 'none'
    videoElement.src = url
    videoElement.onloadeddata = function () {
      resolve(Math.round(videoElement.duration))
      videoElement.remove()
    }
    videoElement.onerror = () => {
      resolve(undefined)
      videoElement.remove()
    }
  })
}
