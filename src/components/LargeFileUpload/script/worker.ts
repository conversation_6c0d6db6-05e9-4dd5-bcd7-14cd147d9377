// // import Sparkmd5 from 'spark-md5'

// // addEventListener('message', async (args) => {
// //   console.log(args)
// // })
// interface EventData {
//   i: number
//   chunks: Blob[]
//   spark: any
// }
// onmessage = function (event) {
//   console.log(event)

//   const { i, chunks, spark } = event.data as EventData
//   _read(0, chunks, spark, i)
// }
// function _read(i: number, chunks: Blob[], spark: any, workerIndex: number, bytesArr: ArrayBuffer[] = []) {
//   if (i >= chunks.length) {
//     // this.fileHash = spark.end()
//     // resolve(true)
//     postMessage({ workerIndex, bytesArr })
//     return
//   }
//   const blob = chunks[i]

//   const reader = new FileReader()
//   reader.onload = (e) => {
//     const bytes = e.target?.result
//     bytesArr.push(bytes as <PERSON><PERSON><PERSON><PERSON>uffer)
//     // spark.append(bytes as ArrayBuffer)
//     _read(i + 1, chunks, spark, workerIndex, bytesArr)
//   }
//   reader.readAsArrayBuffer(blob)
// }
