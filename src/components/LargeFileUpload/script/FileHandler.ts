// import Sparkmd5 from 'spark-md5'
import { type HashChksParam, type HashChksRes, Strategy, getFileHashChunks } from 'hash-worker'
// import Worker from './worker?worker'
// import md5 from 'md5-webworker'

interface FileInfo {
  name: string
  fullName: string
  type: string
  size: number
}
export default class FileHandler {
  file: File
  chunSizeMb: number
  chunkSize: number
  chunks: Blob[]
  /** 分片数量 */
  chunkNum: number
  fileHash: string
  fileInfo: FileInfo | undefined
  isCancel: boolean
  constructor(file: File, chunSize = 5) {
    this.file = file
    this.chunSizeMb = chunSize
    this.chunkSize = 1024 * 1024 * chunSize
    this.chunks = []
    this.chunkNum = 0
    this.fileHash = ''
    this.fileInfo = undefined
    this.isCancel = false
    this.setFileInfo()
  }

  /**
   * 设置文件信息
   */
  setFileInfo() {
    const name = this.file.name.substring(0, this.file.name.lastIndexOf('.'))
    const type = this.file.type
    const size = this.file.size
    this.fileInfo = {
      name,
      fullName: this.file.name,
      type,
      size,
    }
  }

  /**
   * 获取文件hash
   * @returns 文件hash值
   */
  generateHash() {
    return new Promise((resolve, reject) => {
      try {
        // const spark = new Sparkmd5.ArrayBuffer()
        // work最大创建数量
        const maxWorkerCount = navigator.hardwareConcurrency || 4
        // 计算出要开启线程的数量
        const workerCount = maxWorkerCount >= this.chunks.length ? this.chunks.length : maxWorkerCount

        const param: HashChksParam = {
          file: this.file,
          config: {
            chunkSize: this.chunSizeMb,
            workerCount,
            strategy: Strategy.md5,
          },
        }

        getFileHashChunks(param).then((data: HashChksRes) => {
          this.fileHash = data.merkleHash
          resolve(data.merkleHash)
        }).catch((err) => {
          window.console.log('Error computing Hash', err)
        })
        // md5(this.file).then((res) => {
        //   console.log('MD5计算完毕', res)
        //   this.fileHash = res
        //   resolve(res)
        // }).catch((error) => {
        //   console.log('MD5计算有错误', error)
        // })
      }
      catch (error) {
        window.console.log('Error fileHandler generateHash', error)

        reject(error)
      }
    })
  }

  /**
   *文件切片方法
   * @param i 当前切片下标（当前要处理第几个切片任务）
   * @returns 切片结果
   */
  splitChunk(i: number) {
    const start = i * this.chunkSize
    const end = (i + 1) * this.chunkSize
    const blob = this.file.slice(start, end)
    return blob
  }

  /**
   * 创建文件分片，同时设置分片数量与分片数组
   */
  createChunks() {
    const count = Math.ceil(this.file.size / this.chunkSize)
    const result: Blob[] = []
    for (let i = 0; i < count; i++) {
      result.push(this.splitChunk(i))
    }
    this.chunks = result
    this.chunkNum = result.length
  }
}
