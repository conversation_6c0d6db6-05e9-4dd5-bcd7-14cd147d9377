import request from '@/config/axios'
import { FileTaskInitReqVO } from "@/api/infra/fileTask"
const code = import.meta.env.VITE_STORAGE_CODE

// 校验文件是否存在
export const checkFileExist = (params: any) => {
  return request.get({ url: '/adapter/v1/file/check', params })
}
// 文件分片上传初始化
export const uploadChunksInit = (taskReqVO: FileTaskInitReqVO) => {
  taskReqVO.code = code
  return request.post({ url: '/infra/file/v2/task/init', data: taskReqVO })
}

// 分片上传
export const uploadChunk = (url: string, blob: any, type: string | number) => {
  return request.phaseIPut({ url, headers: { isToken: false, repeatSubmit: false, }, timeout: 120000, data: blob })
}

// 合并分片接口
export const mergeChunks = (params: any) => {
  return request.post({ url: '/adapter/v1/file/merge',headers: { repeatSubmit: false }, params })
}
