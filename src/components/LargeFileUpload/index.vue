<script setup lang='ts'>
import { type UploadInstance, type UploadProps, type UploadRawFile, type UploadRequestOptions, type UploadUserFile, genFileId } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import FileHandler from './script/FileHandler'
import { checkFileExist, mergeChunks, uploadChunk, uploadChunksInit } from './api/index'
import { readFileDuration } from './script/fileDuration'
import ResourcePreview, { type PreviewData } from '@/components/ResourcePreview/index.vue'
import { getCheckResource } from '@/api/resource/list'
import { formatFileType } from '@/views/resource/list/components/BatchUpload/script'
import { MediaType } from '@/enums/resource'
import type { Resource } from '@/typings/views/Resource/list'
import * as Md5Util from "@/utils/md5"
import {Part, TaskInfoDTO} from "@/api/infra/fileTask"
import * as FileTaskApi from "@/api/infra/fileTask"
import axios from "axios"
import PQueue from 'p-queue'
import {StatusEnum} from "@/components/SuperUpload/src/config";

const props = withDefaults(
  defineProps<{
    /** 文件上传数量限制，默认是1 */
    limit?: number
    /** 是否允许多选，默认不允许 */
    multiple?: boolean
    /** 需要限制的文件类型 */
    allowTypes?: string[]
    /** 是否显示正在上传的文件列表,默认为true */
    showList?: boolean
  }>(),
  {
    limit: 1,
    multiple: false,
    showList: true,
  },
)
const emits = defineEmits<{
  (event: 'success', response: CustomUploadUserFile['response'], file: CustomUploadUserFile, files: CustomUploadUserFile[]): void
  (event: 'delete', file: CustomUploadUserFile, files: CustomUploadUserFile[]): void
  (event: 'init', file: CustomUploadUserFile, files: CustomUploadUserFile[]): void
}>()
defineSlots<{
  /** 同element plus upload组件的tip插槽 */
  tip: () => any
}>()
interface MergeData {
  createBy: string
  createTime: null
  updateBy: null
  updateTime: null
  remark: null
  pageNum: number
  pageSize: number
  id: number
  metaId: number
  url: string
  originalFileName: string
  fileType: null
  fileSize: number
  fileName: string
  bucket: string
  status: number
  uploadStatus: number
  delete: null
}
interface FormatInfo {
  name: string
  type: string
  url: string
  id: number
  origin: FileHandler['fileInfo']
  duration?: number | undefined
  hash: string
}
export interface ModelValue {
  name: string
  type: string
  url: string
  id: number
  origin: {
    name: string
    fullName: string
    type: string
    size: number
  }
}
export interface CustomUploadUserFile extends UploadUserFile {
  index?: number
  lastIndex?: number
  duration?: number | undefined
  mediaType?: MediaType
  instance?: FileHandler
  raw?: UploadUserFile['raw'] & { index: number, hash: string }
  viewStatus?: UploadUserFile['status']
  response?: UploadUserFile['response'] & FormatInfo & { format: string, hash: string, resourceInfo: Resource, mediaType: MediaType }
}

interface CustomUploadRequestOptions extends UploadRequestOptions {
  index?: number
  file: UploadRequestOptions['file'] & { index: number, hash: string }
}
// type CustomUploadUserFile = UploadUserFile & { index: number, duration: number | undefined }
const uploadRef = ref<UploadInstance>()
const modelValue = defineModel<ModelValue[]>({ default: [] })
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const fileList = ref<CustomUploadUserFile[]>([])
// const fileList = defineModel<UploadUserFile[]>('fileList', { default: [] })
const uploadList = defineModel<CustomUploadUserFile[]>('fileList', { default: [] })

const loading = ref(false)
const fileResult = ref<any[]>([])
const isCancel = ref(false)
// 当前正在上传的文件Index
let fileIndex = -1
// 当前正在读取文件的index
let uplaodIndex = -1
const preview = reactive<{
  previewShow: boolean
  previewData: PreviewData | undefined
}>({
  previewShow: false,
  previewData: undefined,
})
const { previewShow, previewData } = toRefs(preview)
/**
 * 一个由Promise组成的数组，功能为每一个文件需要等待上一个文件解析完毕再开始解析
 */
const hashPromiseList: any = []
/**
 * 分片上传方法
 * @param fileInstance 文件实例
 * @param uploadUrlList 文件上传列表
 * @param uploadIndex 正在上传的文件编号
 * @param index chunk下标
 */
const upload = async (fileInstance: FileHandler, uploadUrlList: string[], uploadIndex: number, index = 0) => {
  try {
    // 是否需要提前结束递归,全部结束，或者单个文件结束
    if (isCancel.value || fileInstance.isCancel) {
      // isCancel.value = false
      // 清空当前instance
      // fileInstance = undefined
      throw new Error('Cancel')
    }
    if (index >= fileInstance.chunks.length) {
      return
    }
    await uploadChunk(uploadUrlList[index], fileInstance.chunks[index], fileInstance.fileInfo?.type)
    if (fileList.value[uploadIndex]) {
      fileList.value[uploadIndex].percentage = Math.floor(Math.min(Math.min(((index + 1) / fileInstance.chunkNum) * 100, 100)))
    }

    await upload(fileInstance, uploadUrlList, uploadIndex, index + 1)
  }
  catch (error) {
    throw new Error(error as string)
  }
}
/**
 * 返回格式化后的文件数据
 * @param file 文件相关内容
 */
const formatFileInfo = (file: any, fileInstance: FileHandler) => {
  const fileObj = {
    name: file.fileName,
    type: file.contentType,
    url: file.url,
    id: file.id,
    origin: fileInstance.fileInfo,
    duration: file.duration,
    format: file.fileType,
  }
  return fileObj
  // 判断当前result中是否有保存的数据，如果有重新返回
  // if (fileResult.value) {
  //   return [
  //     ...fileResult.value,
  //     fileObj,
  //   ]
  // }
  // return [fileObj]
}
/**
 * 重新设置当前文件数组的下标
 * @param deleteIndex 删除项目的Index
 */
const setFileIndex = (deleteIndex: number) => {
  for (let index = deleteIndex; index < fileList.value.length; index++) {
    fileList.value[index].lastIndex = fileList.value[index].index
    fileList.value[index].index = index
    fileList.value[index]!.raw!.index = index
  }
}
/**
 * 设置文件上传为失败状态
 * @param index 文件数组下标
 */
const setFileFail = (index: number) => {
  setTimeout(() => {
    fileList.value[index].status = 'ready'

    fileList.value[index].viewStatus = 'fail'
  }, 100)
}
/**
 * 文件上传之前的钩子，用于确定是否可以继续上传行为
 * @param rawFile 上传的文件信息
 */
const handleBeforeUpload: UploadProps['beforeUpload'] = async (rawFile) => {
  /**
   * 初始化文件信息，duration与md5值
   */
  // 设置默认需要解析的文件Index，这么写是因为可能出现先上传了一批文件，接着上传另一批文件
  // uplaodIndex = fileIndex > -1 ? fileIndex : uplaodIndex
   const fileInit = async () => {
    return (async () => {
      try {
        uplaodIndex += 1
        const index = uplaodIndex
        // loading.value = true
        // 读取写入文件中的下标
        const duration = await readFileDuration(rawFile)
        fileList.value[index].duration = duration
        fileList.value[index].mediaType = formatFileType(rawFile)
        const fileInstance = new FileHandler(rawFile)
        fileInstance.createChunks()
        // hashPromiseList[index] = (async () => {
        //   if (index === 0) {
        //     return fileInstance.generateHash()
        //   }
        //   await hashPromiseList[index - 1]
        //   return fileInstance.generateHash()
        // })()

        // // 所有文件遍历完毕，index归零，给httpRequest使用
        // if (index + 1 === fileList.value.length) {
        //   uplaodIndex = -1
        // }

        // await hashPromiseList[index]
        fileList.value[index].instance = fileInstance
        emits('init', fileList.value[index], fileList.value)
        // httprequest当中会偶发性获取不到文件的instance，暂时设置一个延迟解决问题
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(true)
          }, 100)
        })
      }
      catch (error: any) {
        throw new Error (error)
      }
    })()
  }
  if (props.allowTypes) {
    if (props.allowTypes.includes(rawFile.type)) {
      await fileInit()
      return
    }
    message.error(t('error.fileTypeError'))
    return false
  }
  else {
    await fileInit()
  }
}

let identifier = ''
// 队列相关 上传队列
const partQueue = new PQueue({ concurrency: 3, autoStart: false })
/**响应参数*/
const status = ref(0)
const uploadIndex = ref()
let file: File | null = null
/**
 * 文件上传方法
 * @param index 要上传的文件下标
 * @param fileInstance 文件实例
 */
const handleUpload = async (index: number, fileInstance: FileHandler) => {
  uploadIndex.value = index
  file = fileInstance!.file

  try {
    let uploadUrlList = []
    // // 需要上传的分片下标
    let chunkStartIndex = 0
    // // 检查文件是否存在,不存在时data为空
    // const fileData = await checkFileExist({ md5: fileInstance.fileHash })
    // // 如果存在数据，判断已经上传的文件序号
    // /** 已经上传的分片数量 */
    // if (fileData) {
    //   const chunkUploadedLength = fileData.chunkUploadedList?.length
    //   // 是否存在切片上传路径
    //   const uploadUrlListLength = fileData.uploadUrlList
    //
    //   if (!uploadUrlListLength) {
    //   // 证明文件已经全部上传完毕，检查当前资源是否存在于资源库中
    //     const  resourceInfo = await getCheckResource(fileData.id)
    //
    //     fileList.value[index].percentage = 100
    //
    //     fileResult.value[index] = formatFileInfo(fileData, fileInstance)
    //
    //     modelValue.value = fileResult.value
    //     fileList.value[index].viewStatus = 'success'
    //     return {
    //       ...fileResult.value[index],
    //       resourceInfo,
    //     }
    //   }
    //   // 上传过一部分分片
    //   else {
    //     chunkStartIndex = chunkUploadedLength
    //     // 这里是直接使用文件初始化接口里面的数据，进行断点续传
    //     uploadUrlList = fileData.uploadUrlList
    //   }
    // }
    // else {
    // // 还未进行过上传，进行初始化操作
    // // 1、获取分片上传接口地址链接
    // // fileType: fileInstance.fileInfo?.type,
    //   const chunkData  = await uploadChunksInit({
    //     chunkNum: fileInstance.chunkNum,
    //     // chunkSize: fileInstance.chunkSize,
    //     contentType: fileInstance.fileInfo?.type,
    //     fileMd5: fileInstance.fileHash,
    //     fileName: fileInstance.fileInfo?.fullName,
    //   // totalSize: fileInstance.fileInfo?.size,
    //   // originalFileName: fileInstance.fileInfo?.fullName,
    //   })
    //   uploadUrlList = chunkData.uploadUrlList
    // }

    // 还未进行过上传，进行初始化操作
    // 1、获取分片上传接口地址链接
    // fileType: fileInstance.fileInfo?.type,
    const chunkData  = await uploadChunksInit({
      identifier: await Md5Util.getMd5(fileInstance!.file),
      fileName: fileInstance.fileInfo?.fullName,
      totalSize: fileInstance.fileInfo?.size,
      type: fileInstance.fileInfo?.type,
      chunkSize: fileInstance.chunkSize,
      currentFolderId: 0,
      relativePath: ''
    })

    modelValue.value = fileInstance

    if (!chunkData || !chunkData.taskRecord) {
      const errorMsg = '文件上传初始化失败，无法获取上传任务信息'
      useMessage().error(errorMsg)
      emitError(errorMsg)
      throw new Error(errorMsg)
    }
    // uploadUrlList = chunkData.uploadUrlList
    const { taskRecord } = chunkData
    // 添加分片上传任务
    identifier = taskRecord.identifier
    let uploadedParts = 0
    const totalParts = taskRecord.chunkNum

    // 重置进度映射
    percentageMap.clear()

    for (let partNumber = 1; partNumber <= taskRecord.chunkNum; partNumber++) {
      const existPart = (taskRecord.exitPartList || []).find(
        (_exitPart: Part) => _exitPart.partNumber == partNumber
      )
      if (existPart) {
        // 分片已上传完成，累计到上传完成的总额中,同时记录一下上次断点上传的大小，用于计算上传速度
        update(partNumber, existPart.size, existPart.size)
        uploadedParts++
      } else {
        partQueue.add(async () => {
          const startSize = taskRecord.chunkSize * (partNumber - 1)
          const endSize = startSize + taskRecord.chunkSize
          const blob = fileInstance.file!.slice(startSize, endSize)

          // 添加重试机制
          const maxRetries = 3
          let retryCount = 0

          while (retryCount < maxRetries) {
            try {
              const preSignUrl = await FileTaskApi.getFileTaskPreSignUrl(identifier, partNumber)

              await axios.request({
                url: preSignUrl,
                method: 'PUT',
                data: blob,
                onUploadProgress: (event) => update(partNumber, event.total!, event.loaded),
                headers: { 'Content-Type': 'application/octet-stream' },
                timeout: 30000 // 30秒超时
              })

              uploadedParts++
              console.log(`分片 ${partNumber} 上传成功，进度: ${uploadedParts}/${totalParts}`)
              break // 成功后跳出重试循环

            } catch (error) {
              retryCount++
              console.error(`分片 ${partNumber} 上传失败，重试次数: ${retryCount}/${maxRetries}`, error)

              if (retryCount >= maxRetries) {
                // 达到最大重试次数，抛出错误
                throw new Error(`分片 ${partNumber} 上传失败，已重试 ${maxRetries} 次`)
              }

              // 等待一段时间后重试
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
            }
          }
        })
      }
    }

    // 启动队列并等待所有分片上传完成
    try {
      await startUpload()

      // 所有分片上传完成后，设置为100%，等待合并
      fileList.value[index].percentage = 100
      fileList.value[index].viewStatus = 'uploading'

      // 重要：返回当前文件的 response 数据，这会被 el-upload 设置到 file.response 中
      return fileList.value[index].response || fileResult.value[index]
    } catch (error) {
      console.error('分片上传失败:', error)
      fileList.value[index].viewStatus = 'fail'
      throw error
    }
    // 开始分片上传的逻辑
    // 2、开始递归上传分片
    // await upload(fileInstance, uploadUrlList, fileIndex, chunkStartIndex)
    // 3、调用文件合并接口合并文件，完成上传
    // const mergeData = await mergeChunks({ fileMd5: fileInstance.fileHash })
    // fileResult.value[index] = formatFileInfo({ ...mergeData, contentType: fileInstance.file.type }, fileInstance)
    // modelValue.value = fileResult.value
    // fileList.value[index].percentage = 100
    // fileList.value[index].viewStatus = 'success'
    // return fileResult.value[index]
    // return fileList.value[index]
  }
  catch (error) {
  }
}

const emitError = (error: string) => {
  console.error('Upload error:', error)
  useMessage().error(error)
  // emit('error', error)
}

let percentageMap: Map<number, { totalSize: number; loadedSize: number }> = new Map()

const update = (partNumber: number, totalSize: number, loadedSize: number) => {
  percentageMap.set(partNumber, { totalSize, loadedSize })
  let total = 0
  let loaded = 0

  // 遍历percentageMap，取值
  percentageMap.forEach((value: { totalSize: number; loadedSize: number }) => {
    total += value.totalSize
    loaded += value.loadedSize
  })

  // 防止除零错误
  if (!file || file.size === 0) {
    return
  }

  // 当前时间
  const nowTime = new Date().getTime()
  // 时间间隔（s）= 当前时间 - 开始时间
  const intervalTime = (nowTime - startTime) / 1000

  // 防止时间间隔为0
  if (intervalTime <= 0) {
    return
  }

  /** 获取从开始上传到现在的平均速度（byte/s）- start */
  // 平均速度 = 已上传总大小 / 时间间隔
  const uploadSpeed = loaded / intervalTime
  // 剩余大小 = 文件总大小 - 已上传大小
  const totalRemaining = file.size - loaded

  // 计算上传进度，未合并前最多99%
  const currentProgress = Math.min((loaded / file.size) * 99, 99)

  // 更新当前上传文件的进度
  if (uploadIndex.value !== undefined && fileList.value[uploadIndex.value]) {
    fileList.value[uploadIndex.value].percentage = Math.floor(currentProgress)
  }

  percentage.value = Math.floor(currentProgress)
  remainingSize.value = `${(totalRemaining / 1024 / 1024).toFixed(1)}MB`

  // 剩余时间计算
  if (uploadSpeed > 0 && totalRemaining > 0) {
    const remainingSeconds = Math.ceil(totalRemaining / uploadSpeed)
    if (remainingSeconds < 60) {
      remainingTime.value = `${remainingSeconds}秒`
    } else if (remainingSeconds < 3600) {
      remainingTime.value = `${Math.ceil(remainingSeconds / 60)}分钟`
    } else {
      remainingTime.value = `${Math.ceil(remainingSeconds / 3600)}小时`
    }
  } else {
    remainingTime.value = '计算中...'
  }

  speed.value = `${(uploadSpeed / 1024 / 1024).toFixed(2)}MB/s`
}

/**
 * 自定义的el-upload组件上传方法
 * @param option elementplus提供的参数
 */
const handleRequest = async (option: CustomUploadRequestOptions) => {

  fileIndex += 1
  // 读取最后一个文件的
  const index = fileIndex
  try {
    // loading.value = false
    // 初始化文件状态
    fileList.value[index].index = index
    fileList.value[index].viewStatus = 'ready'

    const fileInstance = fileList.value[index].instance!
    option.file.hash = fileInstance.fileHash
    option.file.index = index
    // 这里的函数，返回的值会填充到filelist里面的response里
    return await handleUpload(index, fileInstance)
  }
  catch (error) {
    window.console.log(error)
    // 出错了
    window.console.log('Request Error', index)
    window.console.log('Request Error', fileList.value[index])
    setFileFail(index)
  }
}
/**
 * 自定义el-upload组件上传成功方法
 * @param response 接口返回的数据
 * @param uploadFile 上传的文件信息
 * @param uploadFiles 这次上传的文件列表
 */
function handleSuccess(response: any, uploadFile: any, uploadFiles: any[]) {
  emits('success', response as CustomUploadUserFile['response'], uploadFile as CustomUploadUserFile, uploadFiles as CustomUploadUserFile[])
}
/**
 * 自定义el-upload组件超出限制时的方法
 * @param files 超出的文件列表
 */
const handleExceed: UploadProps['onExceed'] = (files) => {
  if (props.allowTypes) {
    const isAllow = files.every(f => props.allowTypes?.includes(f.type))
    if (!isAllow) {
      message.error(t('error.fileTypeError'))
      return
    }
  }
  // 如果这一次上传的文件数量等于最大的允许数量，则允许上传，清空当前文件，重新上传
  let remain
  if (props.limit === 1) {
    remain = 1
  }
  else {
    remain = props.limit - fileList.value.length
  }
  if (files.length > remain) {
    message.error(`${t('error.uploadTo')} ${props.limit} ${t('error.resourcesAtATime')}`)
    return
  }
  if (files.length === props.limit) {
    fileIndex = -1
    uplaodIndex = -1
    modelValue.value = []
    uploadRef.value!.clearFiles()
    fileList.value = []
    fileResult.value = []
    for (const file of files as UploadRawFile[]) {
      file.uid = genFileId()
      uploadRef.value!.handleStart(file)
    }
    // const file = files[0] as UploadRawFile
    // file.uid = genFileId()
    // uploadRef.value!.handleStart(file)
    uploadRef.value?.submit()
  }
  else {
    message.error(`${t('error.uploadTo')} ${props.limit} ${t('error.resourcesAtATime')}`)
  }
}
/**
 * 删除当前文件列表中的某一项
 * @param index 要移除的文件下标
 */
function handleRemoveFile(index: number) {
  message.confirm(`${`${t('global.deleteTip')}?`}`)
  const deleteFile = fileList.value[index]

  fileList.value.splice(index, 1)
  fileResult.value.splice(index, 1)
  fileIndex = fileList.value.length - 1
  uplaodIndex = fileList.value.length - 1
  // 修改fileInstance结束上传,需要具备的条件是存在文件实例，并且上传进度小于100（还没有上传完毕）
  if (fileList.value[index].instance && (fileList.value[index].percentage && fileList.value[index].percentage < 100)) {
    fileList.value[index].instance.isCancel = true
  }

  nextTick(() => {
    setFileIndex(index)
    emits('delete', deleteFile, fileList.value)
  })
  return fileList.value
}
/**
 * 重新上传某一个文件
 * @param file 当前要重新上传的文件信息
 */
const handleReUpload = async (file: CustomUploadUserFile) => {
  const index = file.index!
  try {
    // 由于是自定义重新上传逻辑，需要自行补全upload组件逻辑，避免处理fileIndex
    fileList.value[index].response = await handleUpload(index, file.instance!)
    fileList.value[index].status = 'success'
    emits('success', fileList.value[index].response, file, fileList.value)
  }
  catch (error) {
    window.console.log(error)

    setFileFail(index)
  }
}
/** 文件预览 */
function handlePreview(row: any) {
  const fileType = formatFileType(row.raw)
  // Scorm类文件因为需要解析，这里不做预览功能
  if (fileType === MediaType.Scorm) {
    message.warning(t('warning.noPreview'))
    return
  }
  previewData.value = {
    title: row.response?.name,
    mediaType: fileType,
    address: row.response?.url,
  }
  previewShow.value = true
}
/** 终止所有文件上传 */
function cancel() {
  isCancel.value = true
}
/** 初始化文件预览 */
watch(() => modelValue.value, (newValue) => {
  // && fileList.value.length === 0
  if (newValue && newValue.length > 0) {
    fileResult.value = newValue
    fileIndex = fileList.value.length - 1
// 启动上传
    handleUpload().then(() => startUpload().then())
    // nextTick(() => {
    //   modelValueWatcher()
    // })
  }
  // else {
  //   fileList.value = []
  //   fileIndex = -1
  //   fileResult.value = []
  // }
}, {
  immediate: true,
})

const percentage = ref(0)
const remainingSize = ref('0mb')
const remainingTime = ref('0s')
const speed = ref('0mbps')
let startTime = new Date().getTime()

const startUpload = async () => {
  console.log('startUpload', status.value)

  return new Promise<string>((resolve, reject) => {
    startTime = new Date().getTime()
    partQueue
      .start()
      .on('error', (reason) => {
        console.error('队列执行出错:', reason)
        partQueue.clear()
        reject(reason)
      })
      .onIdle()
      .then(async () => {
        console.log('所有分片上传完成，开始合并文件')

        // 合并分片
        try {
          const result = await FileTaskApi.mergeFileTask({
            identifier,
            fileType: file?.type || 'application/octet-stream',
            digitFlag: false,
            currentFolderId: 0,
            relativePath: file?.webkitRelativePath ?
              file.webkitRelativePath.substring(0, file.webkitRelativePath.lastIndexOf('/')) : ''
          })

          // 合并成功后更新文件信息
          if (uploadIndex.value !== undefined && fileList.value[uploadIndex.value]) {
            const currentFile = fileList.value[uploadIndex.value]

            // 更新文件列表状态
            currentFile.id = result.id
            currentFile.percentage = 100
            currentFile.viewStatus = 'success'

            // 关键修复：更新 response 数据，这是父组件获取数据的关键
            currentFile.response = {
              ...result,
              name: file?.name || '',
              type: file?.type || '',
              url: result.url || '',
              id: result.id,
              format: result.fileType || file?.type || '',
              hash: currentFile.raw?.hash || '',
              resourceInfo: result.resourceInfo || null,
              mediaType: currentFile.mediaType || formatFileType(currentFile)
            }

            // 更新文件结果数组
            fileResult.value[uploadIndex.value] = {
              ...result,
              name: file?.name || '',
              type: file?.type || '',
              url: result.url || '',
              id: result.id,
              origin: {
                name: file?.name || '',
                fullName: file?.name || '',
                type: file?.type || '',
                size: file?.size || 0
              }
            }

            // 更新 modelValue 供父组件使用
            modelValue.value = fileResult.value

            // 触发成功事件，确保父组件能收到通知
            emits('success', currentFile.response, currentFile, fileList.value)

            // 调试日志
            console.log('文件上传完成，数据已更新:', {
              fileIndex: uploadIndex.value,
              response: currentFile.response,
              fileResult: fileResult.value[uploadIndex.value],
              modelValue: modelValue.value
            })
          }

          console.log('文件合并成功:', result)
          message.success(t('common.uploadSuccess'))
          resolve(result.url || '')

        } catch (error) {
          console.error('文件合并失败:', error)

          // 合并失败时更新状态
          if (uploadIndex.value !== undefined && fileList.value[uploadIndex.value]) {
            fileList.value[uploadIndex.value].viewStatus = 'fail'
          }

          const errorMessage = error instanceof Error ? error.message : '文件合并失败'
          useMessage().error(errorMessage)
          emitError(errorMessage)
          reject(error)
        }
      })
      .catch((error) => {
        console.error('队列执行异常:', error)
        reject(error)
      })
  })
}

watchEffect(() => {
  uploadList.value = fileList.value
})
defineExpose({
  cancel,
  handleRemoveFile,
})
const fileInfoList = ref([])
</script>

<template>
  <div v-loading="loading" class="min-h-28" :element-loading-text="t('resource.fileBeingAnalyzed')">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      drag
      :limit="limit"
      :http-request="(handleRequest as any)"
      :on-exceed="handleExceed"
      :show-file-list="false"
      :multiple="props.multiple"
      :before-upload="handleBeforeUpload"
      :on-success="handleSuccess"
    >
      <template #tip>
        <!-- 提示语 -->
        <slot name="tip"></slot>
      </template>
      <!-- <el-icon>
        <upload-filled />
      </el-icon> -->
      <div class="my-3.5">
        <svg-icon icon-class="uploadfill" class="text-2xl" />
        <div class="el-upload__text">
          {{ t('error.dropFileHereOr') }} <em>{{ t('action.upload') }}</em>
        </div>
      </div>
    </el-upload>
    <div v-if="props.showList" class="flex flex-col gap-2.5 mt-2.5">
      <!-- 循环渲染上传文件列表 -->
      <div
        v-for="(item, index) in fileList" :key="item.index"
        :class="item.viewStatus === 'fail' ? 'bg-[#FFF2F2]' : ['ready', 'uploading'].includes(item.status as any) ? 'bg-[#F4F4F4]' : 'bg-[#EAF5F1]'"
        class="w-full flex flex-col gap-4 rounded p-2.5"
      >
        <!-- 文件名称行 -->
        <div class="flex justify-between gap-7">
          <el-link class="custom-upload-file-link" type="primary" :underline="false" :title="item.name" @click="handlePreview(item)">
            {{ item.name }}
          </el-link>
          <!-- <span class="text-sm text-[#222222] line-clamp-1" @click="handlePreview(item)">{{ item.name }}
          </span> -->
          <!-- 上传失败时，需要一个重新上传按钮 -->
          <div class="flex items-center">
            <svg-icon v-if="item.viewStatus === 'fail'" icon-class="ReUpload" class="me-2.5 text-base cursor-pointer" @click="handleReUpload(item)" />
            <el-icon class="cursor-pointer" :size="16" @click="handleRemoveFile(index)">
              <Delete />
            </el-icon>
          </div>
        </div>

        <!-- 如果正在解析hash值 -->
        <div v-if="item.percentage !== 100 && !item.raw?.hash" class="flex items-center gap-1">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span class="text-sm">{{ t('resource.fileParsing') }}</span>
        </div>
        <!-- 进度条行  如果上传成功 -->
        <!-- <el-progress v-else-if="item.viewStatus !== 'fail'" :percentage="item.percentage" :color="customColor" class="flex-1" /> -->
        <CustomProgress v-else-if="item.viewStatus !== 'fail'" :percentage="item.percentage" />
        <!-- 上传失败 -->
        <div v-else class="flex items-center gap-1.5">
          <el-icon size="16" color="#DB3D3D">
            <CircleClose />
          </el-icon>
          <span class="text-sm text-[#DB3D3D]">{{ t('resource.uploadFailed') }}</span>
        </div>
      </div>
    </div>
    <ResourcePreview v-model="previewShow" :data="previewData" />
  </div>
</template>

<style scoped lang='scss'>
:deep .el-upload-dragger{
  padding: 0 !important;
  border: 1px solid var(--el-border-color);
}
:deep(.custom-upload-file-link){
  .el-link__inner{
    @apply break-all line-clamp-1;
  }
}
</style>
