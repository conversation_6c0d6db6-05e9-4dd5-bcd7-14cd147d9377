<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import type { UploadProps } from 'element-plus'
import { formatImgUrl } from '@/utils'
import { getAccessToken } from '@/utils/auth'
import { useI18n } from "vue-i18n"
import { useDraggable } from '@vueuse/core'

const { t } = useI18n()
const message = useMessage() // 消息弹窗
const props = withDefaults(defineProps<{
  modelValue: string | object | Array<any>
  /** 图片数量限制 */
  limit: number
  /** 图片大小限制(MB/KB) */
  fileSize: number
  /** 文件类型 */
  fileType?: string[]
  /** 图片比例 */
  fileRatio?: [number, number]
  /** 是否显示提示 */
  isShowTip?: boolean
  /** 提示文字 */
  tipText?: string
  /** 上传模块  1.course subject模块上传使用(因为使用的上传文件大小限制不一样) */
  uploadModule?: number
  coordinateDetails: any
}>(), {
  limit: 5,
  fileSize: 5,
  fileType: () => ['png', 'jpg', 'jpeg', 'GIF'],
  isShowTip: true,
  tipText: '750*442 pixels or 16:9,PNG、JPG、GIF format,under 5M',
  uploadModule: 0,
})

const emit = defineEmits(['update:modelValue','confirmName','confirmLogo','confirmNumber','confirmSeal','confirmUser','confirmTime'])

const number = ref(0)
const uploadList = ref<any[]>([])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const baseUrl = import.meta.env.VITE_APP_BASE_API
// const uploadImgUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/common/upload`) // 上传的图片服务器地址
const uploadImgUrl = ref(`${import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL}/infra/file/direct/upload`) // 上传的图片服务器地址
const uploadData = ref({
  code: import.meta.env.VITE_STORAGE_CODE,
  uuid: 1
})
const headers = ref({ Authorization: `Bearer ${getAccessToken()}` })
const fileList = ref<any[]>([])

// 定义五个可拖动元素
// 1. 名称 2.  编号 3. 图标 4.公章 5.用户 6.时间
const draggableItems = ref([
  { id: 1, label: 'Name', x: 20, y: 20, elementRef: null },
  { id: 2, label: 'NumberPrefix', x: 20, y: 100, elementRef: null },
  { id: 3, label: 'Logo', x: 20, y: 180, elementRef: null },
  { id: 4, label: 'OfficialSeal', x: 20, y: 260, elementRef: null },
  { id: 5, label: 'User', x: 20, y: 340, elementRef: null },
  { id: 6, label: 'Time', x: 20, y: 400, elementRef: null }
])


const activeItemId = ref(null)
let startPos = { x: 0, y: 0 }


interface Position {
  x: number
  y: number
}

interface DraggableItem {
  id: string
  label: string
  x: number
  y: number
  elementRef: HTMLElement | null
}

interface CoordinateDetails {
  nameDetails: Position
  numberDetails: Position
  logoDetails: Position
  officialSealDetails: Position
  userDetails: Position
  timeDetails: Position
}

const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize),
)
const previewList = computed(() => {
  return fileList.value.map((fileInfo: { name: string, url: string }) => {
    fileInfo = {
      name: fileInfo.name,
      url: formatImgUrl(fileInfo.url.replace(baseUrl, '')),
    }
    return fileInfo
    // url = url.replace(baseUrl, '')
    // return formatImgUrl(url)
  })
})
watch(() => props.modelValue, (val) => {
  if (val) {
    // 首先将值转为数组
    const list: any[] = Array.isArray(val) ? val : (props.modelValue as any).split(',')
    // 然后将数组转为对象数组
    fileList.value = list.map((item) => {
      if (typeof item === 'string') {
        if (!item.includes(baseUrl))
          item = { name: baseUrl + item, url: item }
        else
          item = { name: item, url: item }
      }
      return item
    })
  }
  else {
    fileList.value = []
    return []
  }
}, { deep: true, immediate: true })

const container = ref(null);


//
// 上传前loading加载
async function handleBeforeUpload(file: any) {
  let isImg = false
  if (props.fileType.length) {
    let fileExtension = ''
    if (file.name.includes('.'))
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)

    isImg = props.fileType.some((type) => {
      if (file.type.includes(type))
        return true
      if (fileExtension && fileExtension.includes(type))
        return true
      return false
    })
  }
  else {
    isImg = file.type.includes('image')
  }
  if (!isImg) {
    message.error(`${t('error.fileFormatError')} ${props.fileType.join('/')} ${t('error.imageFormatFile')}`)
    return false
  }
  if (props.fileSize) {
    // 1代表是course subject模块使用上传
    const isLt = props.uploadModule === 1 ? file.size / 1024 < props.fileSize : file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      message.error(`${t('error.imageSizeError')} ${props.fileType.join('/')} ${props.fileSize} ${props.uploadModule === 1 ? 'KB' : 'MB'}!`)

      return false
    }
  }
  if (props.fileRatio) {
    await new Promise((resolve, reject) => {
      let url = ''
      try {
        url = window.URL ? window.URL.createObjectURL(file) : window.webkitURL.createObjectURL(file)
      }
      catch (e) {
        if (window.console) {
        // window.console.error(e);
          window.console.dir(e)
        }
        const binaryData = [].concat(file)
        const blobFile = new Blob(binaryData)
        url = window.URL ? window.URL.createObjectURL(blobFile) : window.webkitURL.createObjectURL(blobFile)
      }
      const image = new Image()
      image.src = url
      image.onload = () => {
        const width = image.width
        const height = image.height

        if (width !== undefined && height !== undefined && width > 0 && height > 0) {
          if (width / height === props.fileRatio[0] / props.fileRatio[1]) {
            resolve(true)
          // return true
          }
          else {

            message.error(t('error.imageSizeErrorLength'))
            // eslint-disable-next-line prefer-promise-reject-errors
            reject(false)

          // return false
          }
        }
        else {
          message.error(t('error.imageLoading'))

          // eslint-disable-next-line prefer-promise-reject-errors
          reject(false)
        // return false
        }
      }
    })
  }
  message.loading(t('loading.upLoading'))
  number.value++
}

// 文件个数超出
function handleExceed() {
  message.error(`${t('error.fileLengthError')} ${props.limit} !`)
}
const imageUpload = ref()
// 上传成功回调
function handleUploadSuccess(res: any, file: any) {
  if (res.code === 0) {
    uploadList.value.push({ name: file.name, url: res.data.url })
    uploadedSuccessfully()
  }
  else {
    number.value--
    message.closeLoading()
    message.error(res.msg)
    imageUpload.value.handleRemove(file)
    uploadedSuccessfully()
  }
}
// 删除图片
function handleDelete(file: File): UploadProps['beforeRemove'] {
  const findex = fileList.value.map(f => f.name).indexOf(file.name)
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1)
    emit('update:modelValue', listToString(fileList.value))
    return () => false
    // return false
  }
  return () => true
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value)
    uploadList.value = []
    number.value = 0
    emit('update:modelValue', listToString(fileList.value))
    message.closeLoading()
  }
}

// 上传失败
function handleUploadError() {
  message.error(t('error.uploadErrorMessage'))
  message.closeLoading()
}

// 预览
function handlePictureCardPreview(file: any) {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}

// 对象转成指定字符串分隔
function listToString(list: any[], separator?: string) {
  let strs = ''
  separator = separator || ','
  for (const i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf('blob:') !== 0)
      strs += list[i].url.replace(baseUrl, '') + separator
  }
  return strs !== '' ? strs.substr(0, strs.length - 1) : ''
}


// 设置元素引用
const setItemRef = (el) => {
  if (!el) return
  const item = draggableItems.value.find(i => i.id === el.dataset.id)
  if (item) item.elementRef = el
}
// 获取元素样式
const getItemStyle = (item) => ({
  left: `${item.x}px`,
  top: `${item.y}px`,
  backgroundColor: activeItemId.value === item.id ? '#ff7f50' : getColorById(item.id),
  zIndex: activeItemId.value === item.id ? 100 : 10
})
// 根据ID获取不同颜色
const getColorById = (id) => {
  const colors = {
    1: '#42b983',
    2: '#3498db',
    3: '#9b59b6',
    4: '#e74c3c',
    5: '#f39c12'
  }
  return colors[id] || '#42b983'
}
const startDrag = (id, e) => {
  activeItemId.value = id
  const item = draggableItems.value.find(i => i.id === id)
  if (!item) return

  startPos = {
    x: e.clientX - item.x,
    y: e.clientY - item.y
  }

  // 添加事件监听
  window.addEventListener('mousemove', handleMove)
  window.addEventListener('touchmove', handleMove)
  window.addEventListener('mouseup', stopDrag)
  window.addEventListener('touchend', stopDrag)

  e.preventDefault()
}
// 处理移动
const handleMove = (e) => {
  if (!activeItemId.value) return

  const clientX = e.touches ? e.touches[0].clientX : e.clientX
  const clientY = e.touches ? e.touches[0].clientY : e.clientY

  const item = draggableItems.value.find(i => i.id === activeItemId.value)
  if (!item) return

  // 计算新位置并取整
  let newX = Math.floor(clientX - startPos.x)
  let newY = Math.floor(clientY - startPos.y)

  // 限制在容器内
  const containerRect = container.value.getBoundingClientRect()
  const itemWidth = 120
  const itemHeight = 80

  item.x = Math.max(0, Math.min(newX, containerRect.width - itemWidth))
  item.y = Math.max(0, Math.min(newY, containerRect.height - itemHeight))
  switch (item.id)
  {
    case 1:
      emit('confirmName', item.x,item.y)
      break
    case 2:
      emit('confirmNumber', item.x,item.y)
      break
    case 3:
      emit('confirmLogo', item.x,item.y)
      break
    case 4:
      emit('confirmSeal', item.x,item.y)
      break
    case 5:
      emit('confirmUser', item.x,item.y)
      break
    case 6:
      emit('confirmTime', item.x,item.y)
      break
    default:
      break
  }

}
// 停止拖动
const stopDrag = () => {
  activeItemId.value = null
  // 移除事件监听
  window.removeEventListener('mousemove', handleMove)
  window.removeEventListener('touchmove', handleMove)
  window.removeEventListener('mouseup', stopDrag)
  window.removeEventListener('touchend', stopDrag)
}
// 创建 ID 到 props 键名的映射
const idToDetailKeyMap: Record<string, keyof CoordinateDetails> = {
  '1': 'nameDetails',
  '2': 'numberDetails',
  '3': 'logoDetails',
  '4': 'officialSealDetails',
  '5': 'userDetails',
  '6': 'timeDetails'
}
watch(
  () => props.coordinateDetails,
  (newVal) => {
    draggableItems.value.forEach(item => {
      const detailKey = idToDetailKeyMap[item.id]
      if (detailKey) {
        item.x = newVal[detailKey].x
        item.y = newVal[detailKey].y
      }
    })
  },
  { immediate: true, deep: true }
)

// 清空原有的封面文件进行重新上传封面
const clearFile = () => {
  fileList.value = []
  emit('update:modelValue', listToString(fileList.value))
}


</script>

<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload" multiple :action="uploadImgUrl" list-type="picture-card"
      :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload" :limit="limit" :on-error="handleUploadError"
      :on-exceed="handleExceed" :before-remove="handleDelete" :show-file-list="true" :headers="headers"
      :file-list="previewList" :on-preview="handlePictureCardPreview" :class="{ hide: fileList.length >= limit }"
      :data="uploadData"
    >
      <Icon icon="ep:plus" />

      <template #file="{ file }">
        <div class="drag-system">
          <div >
            <img :src="file.url" alt=""  ref="container" fit="cover" class="drag-container h-full w-full" />
            <div
              v-for="item in draggableItems"
              :key="item.id"
              :ref="setItemRef"
              class="draggable-item"
              :style="getItemStyle(item)"
              @mousedown="startDrag(item.id, $event)"
              @touchstart="startDrag(item.id, $event.touches[0])"
            >
              <div class="item-header">{{ item.label }}</div>
            </div>
          </div>
        </div>

      </template>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      {{ props.tipText }}
    </div>
    <el-button plain type="primary" @click="clearFile" v-show="fileList.length > 0">
      {{ t('common.uploadFile') }}
    </el-button>

    <Dialog v-model="dialogVisible" :title="t('action.preview')" width="800px" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
    </Dialog>
  </div>

</template>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
:deep(.el-upload-list__item) {
  display: block;
  width: 500px;
  height: 500px;
}

.drag-container {
  position: relative;
  width: 500px;
  height: 500px;
  //border: 2px solid #ccc;
  margin-bottom: 20px;
  overflow: hidden;
}

.draggable-item {
  position: absolute;
  width: 120px;
  height: 80px;
  border-radius: 8px;
  cursor: move;
  user-select: none;
  touch-action: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s;
  text-align: center;
  line-height: 80px;
}
</style>
