<template>
  <div class="stat-card" :class="`stat-card--${color}`">
    <div class="stat-card__header">
      <div class="stat-card__icon">
        <span class="stat-icon">{{ icon }}</span>
      </div>
      <div class="stat-card__title">{{ title }}</div>
    </div>

    <div class="stat-card__content">
      <div class="stat-card__value">{{ displayValue }}</div>
      <div v-if="subtitle" class="stat-card__subtitle">{{ subtitle }}</div>

      <!-- 进度条 -->
      <div v-if="progress !== undefined" class="stat-card__progress">
        <el-progress
          :percentage="Math.min(Math.max(progress, 0), 100)"
          :show-text="false"
          :stroke-width="6"
          :color="progressColor"
        />
      </div>

      <!-- 趋势指示器 -->
      <div v-if="trend !== undefined" class="stat-card__trend">
        <div class="trend-item">
          <Icon
            :icon="trend >= 0 ? 'ep:arrow-up' : 'ep:arrow-down'"
            :class="['trend-icon', trend >= 0 ? 'trend-up' : 'trend-down']"
          />
          <span class="trend-value">{{ Math.abs(trend) }}%</span>
          <span v-if="trendLabel" class="trend-label">{{ trendLabel }}</span>
        </div>
      </div>

      <!-- 对比值 -->
      <div v-if="comparison !== undefined" class="stat-card__comparison">
        <span :class="['comparison-value', comparison >= 0 ? 'comparison-positive' : 'comparison-negative']">
          {{ comparison >= 0 ? '+' : '' }}{{ comparison }}
        </span>
        <span v-if="comparisonLabel" class="comparison-label">{{ comparisonLabel }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@/components/Icon'

interface Props {
  title: string
  icon: string
  value: string | number
  subtitle?: string
  progress?: number
  trend?: number
  trendLabel?: string
  comparison?: number
  comparisonLabel?: string
  color?: 'blue' | 'green' | 'orange' | 'purple' | 'red'
}

const props = withDefaults(defineProps<Props>(), {
  color: 'blue'
})

const displayValue = computed(() => {
  if (typeof props.value === 'number') {
    // 格式化数字显示
    if (props.value >= 1000000) {
      return (props.value / 1000000).toFixed(1) + 'M'
    } else if (props.value >= 1000) {
      return (props.value / 1000).toFixed(1) + 'K'
    }
    return props.value.toString()
  }
  return props.value
})

const progressColor = computed(() => {
  const colors = {
    blue: '#1890ff',
    green: '#52c41a',
    orange: '#faad14',
    purple: '#722ed1',
    red: '#ff4d4f'
  }
  return colors[props.color]
})
</script>

<style lang="scss" scoped>
.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &--blue {
    --primary-color: #1890ff;
    --bg-color: rgba(24, 144, 255, 0.1);
  }

  &--green {
    --primary-color: #52c41a;
    --bg-color: rgba(82, 196, 26, 0.1);
  }

  &--orange {
    --primary-color: #faad14;
    --bg-color: rgba(250, 173, 20, 0.1);
  }

  &--purple {
    --primary-color: #722ed1;
    --bg-color: rgba(114, 46, 209, 0.1);
  }

  &--red {
    --primary-color: #ff4d4f;
    --bg-color: rgba(255, 77, 79, 0.1);
  }

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  &__icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    .stat-icon {
      font-size: 24px;
    }
  }

  &__title {
    font-size: 16px;
    font-weight: 500;
    color: #666;
  }

  &__content {
    .stat-card__value {
      font-size: 2.5rem;
      font-weight: bold;
      color: #1f2937;
      margin: 8px 0;
      line-height: 1;
    }

    .stat-card__subtitle {
      color: #6b7280;
      font-size: 14px;
      margin-bottom: 12px;
    }

    .stat-card__progress {
      margin: 12px 0;
    }

    .stat-card__trend {
      margin-top: 12px;

      .trend-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;

        .trend-icon {
          font-size: 16px;

          &.trend-up {
            color: #52c41a;
          }

          &.trend-down {
            color: #ff4d4f;
          }
        }

        .trend-value {
          font-weight: 600;
          color: #1f2937;
        }

        .trend-label {
          color: #6b7280;
        }
      }
    }

    .stat-card__comparison {
      margin-top: 12px;
      font-size: 14px;

      .comparison-value {
        font-weight: 600;

        &.comparison-positive {
          color: #52c41a;
        }

        &.comparison-negative {
          color: #ff4d4f;
        }
      }

      .comparison-label {
        color: #6b7280;
        margin-left: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stat-card {
    padding: 16px;

    &__content .stat-card__value {
      font-size: 2rem;
    }

    &__icon {
      width: 40px;
      height: 40px;

      .stat-icon {
        font-size: 20px;
      }
    }
  }
}
</style>
