<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { listTopic } from '@/api/category/topic'
import { handlePhaseTree } from '@/utils/tree'
const props = defineProps<{ hasNoSubject?: boolean; modelValue: string; selecteTreeStyle?: string }>()
const emit = defineEmits(['update:subjectId', 'update:modelValue'])
const subjectId = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const { t } = useI18n()
const subjectList = ref()
const subjectSelectRef = ref()

const getList = async () => {
  const data = await listTopic({})
  data.forEach(element => {
    element.value = element.id
    element.label = element.name
  })

    subjectList.value = handlePhaseTree(data, 'id')
    if (props.hasNoSubject) {
      subjectList.value.unshift({
        id: 0,
        value: 0,
        name: 'No Subject',
        label: 'No Subject'
      })
    }
}
onMounted(() => {
  getList()
})
</script>

<template>
  <div ref="subjectSelectRef" :style="selecteTreeStyle ? selecteTreeStyle : `width: 180px`">
    <el-tree-select v-model="subjectId" :data="subjectList" check-strictly :render-after-expand="false" clearable :style="selecteTreeStyle ? selecteTreeStyle : `width: 180px`" :placeholder="t('common.chooseText')" />
  </div>
</template>
