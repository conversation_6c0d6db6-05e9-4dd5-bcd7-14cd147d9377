<script setup lang="ts" name="AssignPosition">
import type { ElTable, ElTree } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import { uniqBy } from 'lodash-es'
import type { ScopeTableProps } from '../typings'
import { OrgType } from '@/enums/OrgType'
import { listPosition } from '@/api/system/post'
import { sectTreeSelect } from '@/api/system/section'

const props = defineProps<{
  positionIds: any[],
/** 表格props */
  tableProps?: ScopeTableProps
  positionInfo: any[]
  confirmLoading?: { type: Boolean, default: false}
}>()
const { t } = useI18n()
const emits = defineEmits(['confirm'])
const deptName = ref('')
const deptTreeRef = ref<InstanceType<typeof ElTree>>()
const tableRef = ref<InstanceType<typeof ElTable>>()
const deptOptions = ref<any[]>([])
const defaultExpand = ref()
const selDept = ref()
const queryRef = ref()
const positionTotal = ref(0)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)
const showSearch = ref(true)
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    sectCode: undefined,
    name: undefined,
    status: '0',
    parentId: undefined,
    deptId: undefined,
    deptName: undefined,
    serviceCompanyId: undefined,
  },
})

const { queryParams } = toRefs(data)
const positionTableList = ref([])
// const positionList = defineModel('positionList', {
//   default: [] as any[],
// })
const positionList = ref([])
const positionInfoList = ref([])
const loading = ref(true)
const show = defineModel({ default: false })
const dialogLoading = defineModel('dialogLoading', {
  default: false,
})
/** 左侧-公司部门下拉树结构 */
const getDeptTree = async () => {
  /**
   * status-状态 (0：正常，1：停用)
   * type-类型 (0: 本部，1:contractor)
   */
  const data = await sectTreeSelect({ status: '0', type: 0 })
  if (data.length) {
    deptOptions.value = data
    selDept.value = deptOptions.value[0]
    queryParams.value.compId = deptOptions.value[0].id
    // 展开默认选中节点
    // const firstOption = deptOptions.value[0]
    // defaultExpand.value = [deptOptions.value[0].virtualId, firstOption.level === OrgType.Company && firstOption.children ? deptOptions.value[0].children[0].virtualId : undefined]
    nextTick(() => {
      deptTreeRef.value?.setCurrentNode(deptOptions.value[0])
    })
    getList()
  }
}
/** 左侧-通过条件过滤节点  */
function filterNode(value: any, data: any) {
  if (!value)
    return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 左侧-根据名称筛选部门树 */
watch(deptName, (val) => {
  deptTreeRef.value!.filter(val)
})
/** 左侧-节点触发事件 */
function handleNodeClick(node: any) {
  selDept.value = node
  if (node.level === OrgType.Company) {
    queryParams.value.compId = node.id
    queryParams.value.deptId = null
    queryParams.value.sectId = null
  }
  if (node.level === OrgType.Department) {
    queryParams.value.deptId = node.id
    queryParams.value.compId = null
    queryParams.value.sectId = null
  }
  if (node.level === OrgType.Section) {
    queryParams.value.sectId = node.id
    queryParams.value.compId = null
    queryParams.value.deptId = null
  }
  getList()
}
/** 右侧-重置按钮操作 */
function resetQuery() {
  queryRef.value?.resetFields()
  queryParams.value = {
    sectId: null,
    deptId: null,
    compId: null,
    name: undefined
  }
  positionTotal.value = 0
  handleQuery()
  getDeptTree()
}
/** 右侧-搜索按钮操作 */
function handleQuery() {
  getList()
}
/** 根据employeeList设置选中状态 */
function setTableSelect() {
  positionTableList.value.forEach((item, index) => {
    props.positionIds.forEach(sel => {
      if (sel === item.id) {
        sel = item
        tableRef.value.toggleRowSelection(item, true)
      }
    })
  })
}
/** 右侧-查询position列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await listPosition(queryParams.value)
    positionTableList.value = data // proxy!.handleTree(response.data, 'sectId')
    positionTotal.value = data.length
    // 回显选中状态
    if (props.positionIds) {
      setTableSelect()
    }
  } finally {
    loading.value = false
  }
}
/**
 * 拼接人员的名称全路径
 * @param args 要拼接的字符串数据
 */
function formatParamsName(...args: string[]) {
  return args.filter(part => part).join(' / ')
}
function handleConfirm() {
  // 最终组装一遍数据
  positionList.value = positionList.value.map(user => {
    return {
      relevanceId: user.relevanceId,
      relevanceName: user.relevanceName,
    }
  })
  emits('confirm', positionList.value)
  show.value = false
}
function handleClose() {
  show.value = false
}
/** 选择条数  */
function handleSelectionChange(selection: any[]) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
  const list = selection.map(position => {
    return {
      relevanceId: position.id,
      relevanceName: position.name,
    }
  })
  positionList.value = positionInfoList.value
  // 在这里先合并数组然后进行去重,因为界面一加载会先调用一遍这个方法
  positionList.value = positionList.value.concat(list)
  const uniqueList = Object.values(positionList.value.reduce((acc, item) => {
    // 如果当前item的relevanceId还没有被添加到结果集中，则添加
    if (!acc[item.relevanceId]) {
      acc[item.relevanceId] = item;
    }
    return acc;
  }, {}));
  positionList.value = uniqueList
}
/** 表格选中状态 */
function handleSelect(selectionList: any, row: any) {
  if (!selectionList.includes(row)) {
    // 取消了当前行的勾选，现在寻找原始数据是否存在
    const modelListIndex = positionList.value.findIndex(item => `${item.relevanceId}` === `${row.id}`)
    if (modelListIndex >= 0) {
      positionList.value.splice(modelListIndex, 1)
      // 同时也要把备用的数据也取消
      positionInfoList.value.splice(modelListIndex, 1)
    }
  }
}
function handleClearSelection() {
  tableRef.value?.clearSelection()
}
onMounted(() => {
  getDeptTree()
  getList()
})
watch(() => show.value, (newValue) => {
  if (newValue) {
    positionList.value = props.positionInfo
    positionInfoList.value = props.positionInfo
    resetQuery()
    handleClearSelection()
  }
})
defineExpose({
  show,
  positionList
})
</script>

<template>
  <Dialog v-model="show" :width="930" :title="t('sys.user.positionPH')">
    <div v-loading="dialogLoading" class="employee-dialog">
      <Splitpanes class="default-theme">
        <Pane :size="26" class="!bg-white">
          <div class="h-[--left-panel-height] flex flex-col">
            <div class="head-container">
              <el-input
                v-model="deptName"
                :placeholder="t('sys.user.deptNamePH')"
                clearable
                style="margin-bottom: 20px;width:95%"
              >
                <template #prefix>
                  <Icon class="mr-5px" icon="ep:search" />
                </template>
              </el-input>
            </div>
            <el-scrollbar class="h-full flex-1">
              <el-tree
                ref="deptTreeRef"
                :data="deptOptions"
                :props="{ label: 'label', children: 'children' }"
                :expand-on-click-node="false"
                :default-expanded-keys="defaultExpand"
                :filter-node-method="filterNode"
                node-key="virtualId"
                highlight-current
                @node-click="handleNodeClick"
              >
                <template #default="{ node }">
                  <div class="flex justify-between">
                    <div class="me-2.5">
                      <el-tag
                        v-if="node.data.level === OrgType.Company " :title="node.data.shortName"
                        :style="{
                          '--el-tag-text-color': '#630EB8',
                          '--el-tag-bg-color': '#F3F1FF',
                          '--el-tag-border-color': '#D3CEF0',
                        }"
                      >
                        {{ node.data.shortName ? node.data.shortName : 'Company' }}
                      </el-tag>
                      <el-tag v-else-if="node.data.level === OrgType.Department" :title="node.data.shortName">
                        {{ node.data.shortName ? node.data.shortName : 'Dept' }}
                      </el-tag>
                      <el-tag v-else-if="node.data.level === OrgType.Section" type="info">
                        {{ 'Section' }}
                      </el-tag>
                    </div>
                    <span
                      :title="node.data.label" class="whitespace-normal line-clamp-1 break-all"
                    > {{ node.data.label }}</span>
                  </div>
                </template>
              </el-tree>
            </el-scrollbar>
          </div>
        </Pane>
        <Pane class=" !bg-white">
          <div>
            <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
              <el-form-item :label="t('sys.post.positionName')" prop="name">
                <el-input
                  v-model="queryParams.name"
                  :placeholder="t('sys.post.positionPH') "
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleQuery">
                  <Icon icon="ep:search" class="mr-5px" />
                  {{ t('action.search') }}
                </el-button>
                <el-button @click="resetQuery">
                  <Icon icon="ep:refresh" class="mr-5px" />
                  {{ t('action.reset') }}
                </el-button>
              </el-form-item>
            </el-form>

            <div class="pt-3 flex items-center">
              <div>
                <el-tag
                  v-if="selDept?.level === OrgType.Company" :style="{
                    '--el-tag-text-color': '#630EB8',
                    '--el-tag-bg-color': '#F3F1FF',
                    '--el-tag-border-color': '#D3CEF0',
                  }"
                >
                  {{ t("global.company") }}
                </el-tag>
                <el-tag v-else>
                  {{ t('sys.user.department') }}
                </el-tag>
                <span class="ms-2.5">{{ selDept?.label }}</span>
              </div>
            </div>
            <OrgTotalBar :number="positionTotal" :text="t('sys.post.totalTip')" class="mt-3" />
            <el-table
              ref="tableRef"
              v-loading="loading" :data="positionTableList"
              row-key="id"
              v-bind="props.tableProps"
              border
              @select="handleSelect"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="50" align="center" reserve-selection fixed="left" />
              <el-table-column :label="t('sys.post.positionName')" prop="name" min-width="360" />
              <el-table-column :label="t('sys.company.uniqueCode')" prop="postCode" min-width="180" />
              <el-table-column prop="dataSource" align="center" :label="t('sys.company.dataSource')" width="110" fixed="right" />
            </el-table>
          </div>
        </Pane>
      </Splitpanes>
    </div>
    <!-- </el-scrollbar> -->
    <template #footer>
      <el-button type="primary" :loading="props.confirmLoading" @click="handleConfirm">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="handleClose">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">
// :deep(.custom-page){
//   @apply relative p-[unset] #{!important};
//   .el-pagination{
//     @apply relative #{!important};
//   }
// }
</style>

<style>
/* .employee-dialog{
  --left-panel-height:600px;
} */
</style>
