<script setup lang='ts'>
import type { ComponentInternalInstance } from 'vue'
import type { ElTree } from 'element-plus'
import { handleTree } from '../utils/index'
import { NodeType } from '../enums/NodeType'
import type { ScopeData, ScopeTableProps } from '../typings'
// import { Scope } from '../typings/index'
import fake from './fake.json'
// import { listDept } from '@/api/system/dept'
import { deepClone } from '@/utils'
import { deptTreeSelect } from '@/api/system/user'
import { OrgType } from '@/enums/OrgType'
import EmployeeSelect from './EmployeeSelect.vue'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { handlePhaseTree } from '@/utils/tree'
interface DialogData {
  show: boolean
  leftSearch: string
  rightSearch: string
  leftTreeOptions: any[] | undefined
  rightData: any[]
  leftLoading: boolean
}
const props = defineProps<{
  tableProps?: ScopeTableProps
  confirmLoading?: { type: Boolean, default: false}
}>()
const emits = defineEmits(['confirm'])
const { t } = useI18n()
const model = defineModel<ScopeData[]>({ default: [] })
// const loading = defineModel('loading', ({
//   default: false,
// }))
const loading = ref(false)
const segmentedOptions = computed(() => {
  return getIntDictOptions(DICT_TYPE.SCOPE_TYPE).map((dict: any) => ({
    label: dict.label,
    value: +dict.value,
  }))
})
const segmentedValue = ref<any>(1)
let rightDataCache: any[] = []
const dialogData = reactive<DialogData>({
  show: false,
  leftSearch: '',
  rightSearch: '',
  leftTreeOptions: handleTree(fake, 'id'),
  rightData: [],
  leftLoading: false,
})
const { show, leftSearch, rightSearch, leftTreeOptions, rightData, leftLoading } = toRefs(dialogData)
const treeRef = ref<InstanceType<typeof ElTree>>()
const selectEmployeeShow = ref(false)
const employeeList = ref()
const employeeIds = ref()
const checkStrictly = ref(false)
/** Section-Start */
const selectSectionShow = ref(false)
const sectionSelect = ref()
const sectionList = ref()
const sectionIds = ref()
/** Section-End */
/** Position-Start */
const selectPositionShow = ref(false)
const sectionPosition = ref()
const positionList = ref()
const positionIds = ref()
/** Position-End */
// const selectEmployee = ref()
/** 获取节点的层级关系 */
function getPathName(node: any) {
  const nodeArr = treeRef.value?.getNodePath(node)
  const shortArr = ref([])
  nodeArr?.map((item) => {
    shortArr.value.push(item.shortName)
  })
  node.hieRelation = shortArr.value.join(' / ')
}
function filterNode(value: string, data: any) {
  if (!value)
    return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
function setRightData(arr: any[]) {
  rightData.value = deepClone(arr)
  rightDataCache = deepClone(arr)
}
function handleLeftClick() {
  rightData.value = []
  treeRef.value?.setCheckedKeys([], false)
}

function handleRightClick() {
  const filterNodes = (treeRef.value?.getCheckedNodes(false, false) as any[]).filter(item => item.level === segmentedValue.value)
  setRightData(filterNodes)
}
/** 保留选择左侧联动右侧的功能 */
// function handleCheckChange(data: any, checked: boolean, p3: any) {
//   if (!checked) {
//     // 检查当前节点是否存在于右侧显示中，如果不存在为筛选状态，需要删除右侧节点数据缓存

//     const findIndex = rightData.value.findIndex(f => f.deptId === data.deptId)
//     if (findIndex >= 0) {
//       rightData.value.splice(findIndex, 1)
//     }
//     else {
//       const cacheIndex = rightDataCache.findIndex(f => f.deptId === data.deptId)
//       if (cacheIndex >= 0) {
//         rightDataCache.splice(cacheIndex, 1)
//       }
//     }
//   }
// }
function handleRemoveRight(item: any, index: number) {
  const findIndex = rightDataCache.findIndex(f => f.deptId === item.deptId ? item.deptId : item.id)
  rightData.value.splice(index, 1)
  rightDataCache.splice(findIndex, 1)
  treeRef.value?.setChecked(item.deptId ? item.deptId : item.id, false, false)
}
/**
 * 选择公司部门等信息结束后的事件
 */
function handleConfirm() {
  const modelArray = rightData.value.map(v => ({
    relevanceId: v.deptId ? v.deptId : v.id,
    relevanceName: v.hieRelation ? v.hieRelation : v.relevanceName,
    scope: segmentedValue.value,
  })) as any[]
  emits('confirm', modelArray, segmentedValue.value)
  // show.value = false
  // model.value = uniqBy([...model.value, ...modelArray], 'relevanceId')
}
function handleClose() {
  show.value = false
}
/** 重置dialog内的元素状态 */
function resetDialog() {
  rightData.value = []
  rightDataCache = []
  leftSearch.value = ''
  checkStrictly.value = false
  treeRef.value?.setCheckedKeys([], false)
}
/** 获取（公司、部门列表） */
const getListTree = async () => {
  show.value = true
  leftLoading.value = true
  try {
    if (segmentedValue.value === 1) {
      const data = await deptTreeSelect({ status: '0' })
      leftTreeOptions.value = handlePhaseTree(data, 'id', 'label')
    }
    if (segmentedValue.value === 2) {
      leftTreeOptions.value = await deptTreeSelect({ status: '0', type: 0 })
    }
  } finally {
    leftLoading.value = false
  }
}
/** 顶部选择按钮点击事件 */
async function handleSearchClick() {
  /** Company/Department */
  if (segmentedValue.value === NodeType.Company || segmentedValue.value === NodeType.Department) {
    resetDialog()
    // 回显数据的勾选状态
    const arr = model.value.filter(v => v.scope === segmentedValue.value)
    setRightData(arr.map(item => ({
      deptId: item.relevanceId,
      relevanceName: item.relevanceName,
    })))
    await getListTree()
    treeRef.value?.setCheckedKeys(arr.map(item => item.relevanceId))
    rightData.value = setNodesValue()
  }
  else if (segmentedValue.value === NodeType.Section) {
    selectSectionShow.value = true
    const dataList: any[] = []
    model.value.forEach((item) => {
      if (item.scope === NodeType.Section) {
        dataList.push(item)
      }
    })
    sectionList.value = dataList
    sectionIds.value = sectionList.value?.map(item => item.relevanceId)
  }
  else if (segmentedValue.value === NodeType.Position) {
    // sectionPosition.value.show = true
    selectPositionShow.value = true
    const dataList: any[] = []
    model.value.forEach((item) => {
      if (item.scope === NodeType.Position) {
        dataList.push(item)
      }
    })
    positionList.value = dataList
    positionIds.value = positionList.value?.map(item => item.relevanceId)
  }
  else if (segmentedValue.value === NodeType.Employee) {
    selectEmployeeShow.value = true
    const dataList: any[] = []
    model.value.forEach((item) => {
      if (item.scope === NodeType.Employee) {
        dataList.push(item)
      }
    })
    employeeList.value = dataList
    employeeIds.value = employeeList.value?.map(item => item.relevanceId)
  }
}

function setNodesValue() {
  const arr = ref()
  arr.value = model.value.filter(v => v.scope === segmentedValue.value)
  const arrayA = ref()
  arrayA.value = treeRef?.value?.getCheckedNodes()
  const mergeArrays = () => {
    arrayA.value.forEach((itemA: any) => {
      const matchingItemB = arr.value.find((itemB: any) => itemB.relevanceId === itemA.id)
      if (matchingItemB) {
        // 将B的hieRelation属性添加到A中
        itemA.hieRelation = matchingItemB.hieRelation ? matchingItemB.hieRelation : itemA.hieRelation
      }
      getPathName(itemA)
    })
  }
  mergeArrays(arrayA, 'arrayA')
  return arrayA.value
}
function closeDialog() {
  // if (segmentedValue.value !== NodeType.Employee) {
  //   show.value = false
  // }
  if (segmentedValue.value === NodeType.Company || segmentedValue.value === NodeType.Department) {
    show.value = false
  }
  else if (segmentedValue.value === NodeType.Section) {
    selectSectionShow.value = false
  }
  else if (segmentedValue.value === NodeType.Position) {
    selectPositionShow.value = false
  }
  else {
    selectEmployeeShow.value = false
  }
}
/** section弹框选择section */
function sectionConfirm(list) {
  sectionList.value = list
  const modelArray = sectionList.value.map((v: any) => ({
    relevanceId: v.relevanceId,
    relevanceName: v.relevanceName,
    scope: NodeType.Section,
  }))
  emits('confirm', modelArray, NodeType.Section)
}
/** position弹框选择position */
function positionConfirm(list) {
  positionList.value = list
  const modelArray = positionList.value.map((v: any) => ({
    relevanceId: v.id || v.relevanceId,
    relevanceName: v.relevanceName,
    scope: NodeType.Position,
  }))
  emits('confirm', modelArray, NodeType.Position)
}
/** 员工弹框选择完毕员工 */
function employeeConfirm(list) {
  employeeList.value = list
  const modelArray = employeeList.value?.map((v: any) => ({
    relevanceId: v.relevanceId,
    relevanceName: v.relevanceName,
    scope: NodeType.Employee,
  }))

  emits('confirm', modelArray, NodeType.Employee)
}
watch(leftSearch, (newValue) => {
  treeRef.value?.filter(newValue)
})
watch(rightSearch, (newValue) => {
  if (!newValue) {
    rightData.value = rightDataCache
  }

  rightData.value = rightDataCache.filter((item) => {
    if (item.deptName) {
      return item.deptName.toLocaleLowerCase().includes(newValue.toLocaleLowerCase())
    }
    else if (item.relevanceName) {
      return item.relevanceName.toLocaleLowerCase().includes(newValue.toLocaleLowerCase())
    }
  })
})
defineExpose({
  closeDialog,
})
</script>

<template>
  <div class="flex items-center">
    <div class="custom-style border border-solid border-[#007943]">
      <el-segmented v-model="segmentedValue" :options="segmentedOptions" />
    </div>
    <el-button
      class="w-8 h-8 ms-3"
      icon="Search"
      type="primary"
      @click="handleSearchClick"
    >
      <Icon class="mr-1" icon="ep:search" />
    </el-button>

    <Dialog v-model="show" :width="930" :title="segmentedValue === NodeType.Company ? t('dialog.selectCompany') : segmentedValue === NodeType.Department ? t('dialog.selectDepartment') : t('dialog.select')" draggable append-to-body>
      <div v-loading="loading" class="flex w-full gap-2.5 overflow-hidden">
        <!-- 左侧树选择 -->
        <div v-loading="leftLoading" class="flex-1 h-full flex-shrink-0" style="border: 1px solid #D6E7EB">
          <!-- <div class="w-full p-1.5 flex items-center justify-end">
            <el-switch v-model="checkStrictly" />
            <span class="ms-1.5">Enable cascading selection</span>
          </div> -->

          <el-input v-model="leftSearch" placeholder="Please input keyword" suffix-icon="Search" clearable />
          <!-- 父子联动，保留父子联动的功能 -->
          <!-- @check-change="handleCheckChange" -->
          <el-scrollbar height="500">
            <el-tree
              ref="treeRef" :data="leftTreeOptions" :props="{ label: 'label', children: 'children' }"
              :expand-on-click-node="false" :filter-node-method="filterNode"
              node-key="id"
              highlight-current
              default-expand-all show-checkbox
              check-strictly
              :check-on-click-node="true"
              @node-click="getPathName"
              @check-change="getPathName"
            >
              <template #default="{ node }">
                <div class="flex justify-between">
                  <div class="me-2.5">
                    <el-tag
                      v-if="node.data.level === OrgType.Company " :title="node.data.shortName"
                      :style="{
                        '--el-tag-text-color': '#630EB8',
                        '--el-tag-bg-color': '#F3F1FF',
                        '--el-tag-border-color': '#D3CEF0',
                      }"
                    >
                      {{ node.data.shortName ? node.data.shortName : 'Company' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Department" :title="node.data.shortName">
                      {{ node.data.shortName ? node.data.shortName : 'Dept' }}
                    </el-tag>
                    <el-tag v-else-if="node.data.level === OrgType.Section" type="info">
                      {{ 'Section' }}
                    </el-tag>
                  </div>
                  <span
                    :title="node.label" class="whitespace-normal line-clamp-1 break-all"
                  > {{ node.label }}</span>
                </div>
              </template>
            </el-tree>
          </el-scrollbar>
        </div>
        <!-- 中间按钮区域 -->
        <div class="flex flex-col justify-center w-10">
          <el-button type="primary" @click="handleRightClick">
            <Icon icon="ep:arrow-right" />
          </el-button>
          <el-button type="primary" class="!ms-0 mt-2" @click="handleLeftClick">
            <Icon icon="ep:arrow-left" />
          </el-button>
        </div>
        <!-- 右侧选择结果区域 -->
        <div class="w-[400px]" style="border: 1px solid #D6E7EB">
          <el-input v-model="rightSearch" placeholder="Please input keyword" suffix-icon="Search" clearable />
          <el-scrollbar height="500">
            <ul>
              <li v-for="(item, index) in rightData" :key="item.id" class="text-[#233A35] text-sm px-4 py-2.5 flex items-center justify-between " style="border: 1px solid #D6E7EB">
                <span class="line-clamp-1 me-8" :title="item.fullParent">
                  {{ item.hieRelation ? item.hieRelation : item.relevanceName }}
                </span>
                <el-button type="primary" link @click="handleRemoveRight(item, index)">
                  {{ t('action.remove') }}
                </el-button>
              </li>
            </ul>
          </el-scrollbar>
        </div>
      </div>
      <template #footer>
        <el-button type="primary" :loading="props.confirmLoading" @click="handleConfirm">
          {{ t('global.confirm') }}
        </el-button>
        <el-button @click="handleClose">
          {{ t('global.cancel') }}
        </el-button>
      </template>
    </Dialog>
    <!-- Section选择框 -->
    <SectionSelect
      v-model="selectSectionShow"
      :section-info="sectionList"
      v-model:dialog-loading="loading"
      :confirm-loading="props.confirmLoading"
      :section-ids="sectionIds"
      :table-props="props.tableProps"
      @confirm="sectionConfirm"
    />
    <!-- Position选择框 -->
    <PositionSelect
      v-model="selectPositionShow"
      :position-info="positionList"
      v-model:dialog-loading="loading"
      :confirm-loading="props.confirmLoading"
      :position-ids="positionIds"
      :table-props="props.tableProps"
      @confirm="positionConfirm"
    />
    <!-- 人员选择弹框 -->
    <EmployeeSelect
      v-model="selectEmployeeShow"
      :employee-info="employeeList"
      v-model:dialog-loading="loading"
      :confirm-loading="props.confirmLoading"
      :employee-ids="employeeIds"
      :table-props="props.tableProps"
      @confirm="employeeConfirm"
    />
  </div>
</template>

<style scoped lang="scss">
.custom-style {
  @apply w-fit border border-primary p-[1px] rounded-[4px];

  :deep(.el-segmented) {
    --el-segmented-item-selected-color: #ffffff;
    // --el-segmented-item-selected-bg-color: ;
    --el-border-radius-base: 6px;
    --el-segmented-bg-color: #ffffff;

    .el-segmented__group {
      label {
        @apply font-normal #{!important};
      }
    }

  }

}

.custom-div {
  @apply w-[200px] h-[200px];
  border: 1px solid transparent;
  background-origin: border-box;
  background-clip: content-box, border-box;
  background-image: linear-gradient(to right, #4F7EF7, #ED35F9), linear-gradient(to right, #4F7EF7, #ED35F9);
}
</style>
