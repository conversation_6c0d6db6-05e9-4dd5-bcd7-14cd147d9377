<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import type { ElTable, FormInstance } from 'element-plus'
import type { ScopeData, ScopeTableProps } from '../typings'
import { deepClone } from '@/utils'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

const props = defineProps<{
  tableProps?: ScopeTableProps
  // 在线学院课程分配列表不需要展示type
  showType?: Boolean
}>()
const emits = defineEmits(['delete'])
const model = defineModel<ScopeData[]>()
const { t } = useI18n()
const tableRef = ref<InstanceType<typeof ElTable>>()

const cacheData = ref<ScopeData[] | undefined>()
const queryData = reactive({
  queryParams: {
    type: undefined,
    name: '',
  },
})
const queryRef = ref<FormInstance>()
const { queryParams } = toRefs(queryData)
const ids = ref<string[]>([])
const selections = ref<ScopeData[]>()
const multiple = ref(true)

function handleDelete(row?: ScopeData | MouseEvent, index?: number) {
  if (row && index !== undefined) {
    emits('delete', deepClone(row))
  }
  else {
    emits('delete', deepClone(selections.value))
  }
  // if (row && index !== undefined) {
  //   // 单独删除
  //   const findIndex = model.value!.findIndex(f => f.relevanceId === (row as ScopeData).relevanceId)
  //   cacheData.value!.splice(index, 1)
  //   model.value?.splice(findIndex, 1)
  // }
  // else {
  //   for (let index = 0; index < ids.value.length; index++) {
  //     const findIndex = model.value!.findIndex(f => f.relevanceId === ids.value[index])
  //     cacheData.value?.splice(findIndex, 1)
  //     model.value?.splice(findIndex, 1)
  //   }
  // }

  // handleReset()
}

function handleSearch() {
  cacheData.value = model.value?.filter(item => item.relevanceName.toLocaleLowerCase().includes(queryParams.value.name.toLocaleLowerCase()))
  if (queryParams.value.type !== undefined) {
    cacheData.value = cacheData.value?.filter(item => item.scope === queryParams.value.type)
  }
}
function handleReset() {
  queryRef.value?.resetFields()
  if (model.value)
    cacheData.value = deepClone(model.value)
}
function handleSelectionChange(selection: ScopeData[]) {
  selections.value = selection
  ids.value = selection.map(item => item.relevanceId)
  multiple.value = !selection.length
}
function handleRowClick(row: ScopeData) {
  let flag = true
  if (ids.value && ids.value.find(item => item === row.relevanceId)) {
    flag = false
  }
  tableRef.value?.toggleRowSelection(row, flag)
}
watch(model, () => {
  cacheData.value = deepClone(model.value)
})
defineExpose({
  resetSearch: handleReset,
})
</script>

<template>
  <div>
    <div>
      <ContentWrap>
        <el-form ref="queryRef" :model="queryParams" inline class="-mb-15px">
          <el-form-item prop="type" :label="t('learningCenter.course.type')">
            <el-select v-model="queryParams.type" class="!w-44" clearable>
              <el-option v-for="(dict, index) in getIntDictOptions(DICT_TYPE.SCOPE_TYPE)" :key="index" :label="dict.label" :value="+dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="name" :label="t('sys.user.userName')">
            <el-input v-model="queryParams.name" />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleSearch">
              <Icon class="mr-5px" icon="ep:search" />
              {{ t('action.search') }}
            </el-button>
            <el-button @click="handleReset">
              <Icon class="mr-5px" icon="ep:refresh" />
              {{ t('action.reset') }}
            </el-button>
            <el-button plain type="primary" :disabled="multiple" @click="handleDelete">
              <Icon class="mr-5px" icon="ep:delete" />
              {{ t('action.batchDelete') }}
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
    </div>
    <div>
      <ContentWrap>
        <el-table ref="tableRef" :data="cacheData" v-bind="props.tableProps" @selection-change="handleSelectionChange" @row-click="handleRowClick">
          <el-table-column type="selection" fixed="left" />
          <el-table-column prop="scope" :label="t('learningCenter.course.type')" fixed="left" v-if="props.showType">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.SCOPE_TYPE" :value="row.scope" />
            </template>
          </el-table-column>
          <el-table-column prop="relevanceName" :label="t('sys.user.userName')" min-width="300" />
          <el-table-column :label="t('global.action')" fixed="right">
            <template #default="{ row, $index }">
              <el-button link type="primary" @click.stop="handleDelete(row, $index)">
                <Icon icon="ep:delete" />
                {{ t('action.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </ContentWrap>
    </div>
  </div>
</template>

<style></style>
