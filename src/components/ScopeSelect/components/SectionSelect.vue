<script setup lang="ts" name="AssignSection">
import type { ElTable, ElTree } from 'element-plus'
import type { ComponentInternalInstance } from 'vue'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import { uniqBy } from 'lodash-es'
import type { ScopeTableProps } from '../typings'
import { OrgType } from '@/enums/OrgType'
import { listSection } from '@/api/system/section'
import { deptTreeSelect } from '@/api/system/user'
import { parseTime } from '@/utils/ruoyi'
const props = defineProps<{
  sectionIds: any[],
/** 表格props */
  tableProps?: ScopeTableProps
  sectionInfo: any[]
  confirmLoading?: { type: Boolean, default: false}
}>()
const { t } = useI18n()
const emits = defineEmits(['confirm'])
const deptName = ref('')
const deptTreeRef = ref<InstanceType<typeof ElTree>>()
const tableRef = ref<InstanceType<typeof ElTable>>()
const deptOptions = ref<any[]>([])
const defaultExpand = ref()
const selDept = ref()
const queryRef = ref()
const sectionTotal = ref(0)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)
const showSearch = ref(true)
const data = reactive<{
  queryParams: any
}>({
  queryParams: {
    sectCode: undefined,
    sectName: undefined,
    status: '0',
    parentId: undefined,
    deptId: undefined,
    deptName: undefined,
    serviceCompanyId: undefined,
  },
})

const { queryParams } = toRefs(data)
const sectionTableList = ref([])
// const sectionList = defineModel('sectionList', {
//   default: [] as any[],
// })
const sectionList = ref([])
const sectionInfoList = ref([])
const loading = ref(true)
const show = defineModel({ default: false })
const dialogLoading = defineModel('dialogLoading', {
  default: false,
})
/** 左侧-公司部门下拉树结构 */
const getDeptTree = async () => {
  const data = await deptTreeSelect({ status: '0' })
  if (data.length) {
    deptOptions.value = data
    selDept.value = deptOptions.value[0]
    queryParams.value.compId = deptOptions.value[0].id
    nextTick(() => {
      deptTreeRef.value?.setCurrentNode(deptOptions.value[0])
    })
    getList()
  }
}
/** 左侧-通过条件过滤节点  */
function filterNode(value: any, data: any) {
  if (!value)
    return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 左侧-根据名称筛选部门树 */
watch(deptName, (val) => {
  deptTreeRef.value!.filter(val)
})

/** 左侧-节点触发事件 */
function handleNodeClick(node: any) {
  selDept.value = node
  if (node.level === OrgType.Company) {
    queryParams.value.compId = node.id
    queryParams.value.deptId = null
    queryParams.value.deptName = null
  }
  if (node.level === OrgType.Department) {
    queryParams.value.deptId = node.id
    queryParams.value.compId = null
  }
  getList()
}
/** 右侧-重置按钮操作 */
function resetQuery() {
  queryRef.value?.resetFields()
  queryParams.value.deptId = null
  queryParams.value.deptName = null
  queryParams.value.sectName = undefined
  deptName.value = ''
  handleQuery()
  getDeptTree()
}
/** 右侧-搜索按钮操作 */
function handleQuery() {
  getList()
}
/** 根据employeeList设置选中状态 */
function setTableSelect() {
  sectionTableList.value.forEach((item, index) => {
    props.sectionIds.forEach(sel => {
      if (sel === item.sectId) {
        sel = item
        tableRef.value.toggleRowSelection(item, true)
      }
    })
  })
}
/** 右侧-查询Section列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await listSection(queryParams.value)
    sectionTableList.value = data
    sectionTotal.value = data.length
    // 回显选中状态
    if (props.sectionIds) {
      setTableSelect()
    }
  } finally {
    loading.value = false
  }
}
/**
 * 拼接人员的名称全路径
 * @param args 要拼接的字符串数据
 */
function formatParamsName(...args: string[]) {
  return args.filter(part => part).join(' / ')
}
function handleConfirm() {
  // 最终组装一遍数据
  sectionList.value = sectionList.value.map(user => {
    return {
      relevanceId: user.relevanceId,
      relevanceName: user.relevanceName,
    }
  })

  emits('confirm', sectionList.value)
  show.value = false
}
function handleClose() {
  show.value = false
}
/** 选择条数  */
function handleSelectionChange(selection: any[]) {
  ids.value = selection.map(item => item.sectId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
  const list = selection.map(sect => {
    return {
      relevanceId: sect.sectId,
      relevanceName: sect.sectName,
    }
  })
  sectionList.value = sectionInfoList.value
  // 在这里先合并数组然后进行去重,因为界面一加载会先调用一遍这个方法
  sectionList.value = sectionList.value.concat(list)
  const uniqueList = Object.values(sectionList.value.reduce((acc, item) => {
    // 如果当前item的relevanceId还没有被添加到结果集中，则添加
    if (!acc[item.relevanceId]) {
      acc[item.relevanceId] = item;
    }
    return acc;
  }, {}));
  sectionList.value = uniqueList
}
/** 表格选中状态 */
function handleSelect(selectionList: any, row: any) {
  if (!selectionList.includes(row)) {
    // 取消了当前行的勾选，现在寻找原始数据是否存在
    const modelListIndex = sectionList.value.findIndex(item => `${item.relevanceId}` === `${row.sectId}`)
    if (modelListIndex >= 0) {
      sectionList.value.splice(modelListIndex, 1)
      // 同时也要把备用的数据也取消
      sectionInfoList.value.splice(modelListIndex, 1)
    }
  }
}
function handleClearSelection() {
  tableRef.value?.clearSelection()
}
onMounted(() => {
  getDeptTree()
  getList()
})
watch(() => show.value, (newValue) => {
  if (newValue) {
    sectionList.value = props.sectionInfo
    sectionInfoList.value = props.sectionInfo
    resetQuery()
    handleClearSelection()
  }
})
defineExpose({
  show,
  sectionList
})
</script>

<template>
  <Dialog v-model="show" :width="930" :title="t('sys.user.sectionPH')">
    <div v-loading="dialogLoading" class="employee-dialog">
      <Splitpanes class="default-theme">
        <Pane :size="26" class="!bg-white">
          <!-- <ElScrollbar class=" p-2.5"> -->
          <div class="h-[--left-panel-height] flex flex-col">
            <div class="head-container">
              <el-input
                v-model="deptName"
                :placeholder="t('sys.user.deptNamePH')"
                clearable
                style="margin-bottom: 20px;width:95%"
              >
                <template #prefix>
                  <Icon class="mr-5px" icon="ep:search" />
                </template>
              </el-input>
            </div>
            <el-scrollbar class="h-full flex-1">
              <el-tree
                ref="deptTreeRef"
                :data="deptOptions"
                :props="{ label: 'label', children: 'children' }"
                :expand-on-click-node="false"
                :default-expanded-keys="defaultExpand"
                :filter-node-method="filterNode"
                node-key="id"
                highlight-current
                @node-click="handleNodeClick"
              >
                <template #default="{ node }">
                  <div class="flex justify-between">
                    <div class="me-2.5">
                      <el-tag
                        v-if="node.data.level === OrgType.Company " :title="node.data.shortName"
                        :style="{
                          '--el-tag-text-color': '#630EB8',
                          '--el-tag-bg-color': '#F3F1FF',
                          '--el-tag-border-color': '#D3CEF0',
                        }"
                      >
                        {{ node.data.shortName ? node.data.shortName : 'Company' }}
                      </el-tag>
                      <el-tag
                        v-else
                        :title="node.data.shortName"
                      >
                        {{ node.data.shortName }}
                      </el-tag>
                    </div>
                    <span
                      :title="node.data.label" class="whitespace-normal line-clamp-1 break-all"
                    > {{ node.data.label }}</span>
                  </div>
                </template>
              </el-tree>
            </el-scrollbar>
          </div>
          <!-- </ElScrollbar> -->
        </Pane>
        <Pane class="!bg-white">
          <div>
            <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" class="custom-search-form">
              <el-form-item :label="t('sys.section.sectionName')" prop="sectName">
                <el-input
                  v-model="queryParams.sectName"
                  :placeholder="t('sys.section.sectionNamePH')"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleQuery">
                  <Icon class="mr-5px" icon="ep:search" />
                  {{ t('global.search') }}
                </el-button>
                <el-button @click="resetQuery">
                  <Icon class="mr-5px" icon="ep:refresh" />
                  {{ t('global.reset') }}
                </el-button>
              </el-form-item>
            </el-form>

            <div class="pt-3 flex items-center">
              <div>
                <el-tag
                  v-if="selDept?.level === OrgType.Company" :style="{
                    '--el-tag-text-color': '#630EB8',
                    '--el-tag-bg-color': '#F3F1FF',
                    '--el-tag-border-color': '#D3CEF0',
                  }"
                >
                  {{ t("global.company") }}
                </el-tag>
                <el-tag v-else>
                  {{ t('sys.user.department') }}
                </el-tag>
                <span class="ms-2.5">{{ selDept?.label }}</span>
              </div>
            </div>
            <OrgTotalBar :number="sectionTotal" :text="t('hr.section.totalTip')" class="mt-3" />
            <el-table
              ref="tableRef"
              v-loading="loading" :data="sectionTableList"
              v-bind="props.tableProps"
              row-key="sectId"
              border
              @select="handleSelect"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="50" align="center" reserve-selection fixed="left" />
              <el-table-column :label="t('sys.post.section')" prop="sectName" min-width="120" fixed="left" />
              <el-table-column :label="t('sys.company.abbreviation')" prop="shortName" width="140" />
              <el-table-column :label="t('sys.company.uniqueCode')" prop="sectCode" width="200" />
              <el-table-column :label="t('sys.company.createTime')" align="center" prop="createTime" width="180">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="dataSource" align="center" :label="t('sys.company.dataSource')" width="110" fixed="right" />
            </el-table>
          </div>
        </Pane>
      </Splitpanes>
    </div>
    <template #footer>
      <el-button type="primary" :loading="props.confirmLoading" @click="handleConfirm">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="handleClose">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>
