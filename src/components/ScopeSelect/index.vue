<script setup lang='ts'>
import Segmented from './components/Segmented.vue'
import ScopeTable from './components/ScopeTable.vue'
import type { ScopeConfirm, ScopeData, ScopeTableProps } from './typings'

const props = defineProps<{
  tableProps?: ScopeTableProps
  employeeTableProps?: ScopeTableProps
  showType: { type: Boolean, default: true}
  confirmLoading?: { type: Boolean, default: false}
}>()
const emits = defineEmits(['confirm', 'delete'])
const segmentedRef = ref()
const scopeTableRef = ref()
const scopeSelect = ref()
const selectedItems = defineModel<ScopeData[]>({
  required: true,
})
const loading = defineModel<boolean>('loading', {
  default: false,
})

function handleConfirm(scopes: ScopeData[], scope: number) {
  emits('confirm', { scope, scopes } as ScopeConfirm)
}
function handleDelete(list: ScopeData[]) {
  emits('delete', list)
}
function closeDialog() {
  segmentedRef.value.closeDialog()
}
function resetSearch() {
  scopeTableRef.value.resetSearch()
}

defineExpose({
  closeDialog,
  resetSearch,
})
</script>

<template>
  <div ref="scopeSelect">
    <Segmented
      ref="segmentedRef"
      v-model="selectedItems"
      v-model:loading="loading"
      :confirm-loading="props.confirmLoading"
      :table-props="props.employeeTableProps"
      @confirm="handleConfirm"
    />
    <div class="h-[1px] w-full bg-[#D6DEE3] my-5"></div>
    <ScopeTable ref="scopeTableRef" v-model="selectedItems" :show-type="props.showType" :table-props="props.tableProps" @delete="handleDelete" />
  </div>
</template>
