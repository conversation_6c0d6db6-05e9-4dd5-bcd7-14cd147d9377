<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import type { UploadProps } from 'element-plus'
import { formatImgUrl } from '@/utils'
import { getAccessToken } from '@/utils/auth'
import { useI18n } from "vue-i18n"
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const props = withDefaults(defineProps<{
  modelValue: string | object | Array<any>
  /** 图片数量限制 */
  limit: number
  /** 图片大小限制(MB/KB) */
  fileSize: number
  /** 文件类型 */
  fileType?: string[]
  /** 图片比例 */
  fileRatio?: [number, number]
  /** 是否显示提示 */
  isShowTip?: boolean
  /** 提示文字 */
  tipText?: string
  /** 上传模块  1.course subject模块上传使用(因为使用的上传文件大小限制不一样) */
  uploadModule?: number
}>(), {
  limit: 5,
  fileSize: 5,
  fileType: () => ['png', 'jpg', 'jpeg', 'GIF'],
  isShowTip: true,
  tipText: '750*442 pixels or 16:9,PNG、JPG、GIF format,under 5M',
  uploadModule: 0,
})

// const props = defineProps({
//   modelValue: [String, Object, Array],
//   // 图片数量限制
//   limit: {
//     type: Number,
//     default: 5,
//   },
//   // 大小限制(MB)
//   fileSize: {
//     type: Number,
//     default: 5,
//   },
//   // 文件类型, 例如['png', 'jpg', 'jpeg']
//   fileType: {
//     type: Array as () => Array<any>,
//     default: () => ['png', 'jpg', 'jpeg', 'GIF'],
//   },
//   fileRatio: {
//     type: Number,
//     default: undefined,
//   },
//   // 是否显示提示
//   isShowTip: {
//     type: Boolean,
//     default: true,
//   },
//   tipText: {
//     type: String,
//     default: '750*442 pixels or 16:9，PNG、JPG、GIF format，under 5M',
//   },
// })

const emit = defineEmits(['update:modelValue'])

const number = ref(0)
const uploadList = ref<any[]>([])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const baseUrl = import.meta.env.VITE_API_URL
// const uploadImgUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/common/upload`) // 上传的图片服务器地址
const uploadImgUrl = ref(`${import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL}/infra/file/direct/upload`) // 上传的图片服务器地址
const uploadData = ref({
  code: import.meta.env.VITE_STORAGE_CODE,
  uuid: 1
})
const headers = ref({ Authorization: `Bearer ${getAccessToken()}` })
const fileList = ref<any[]>([])
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize),
)
const previewList = computed(() => {
  return fileList.value.map((fileInfo: { name: string, url: string }) => {
    fileInfo = {
      name: fileInfo.name,
      url: formatImgUrl(fileInfo.url.replace(baseUrl, '')),
    }
    return fileInfo
    // url = url.replace(baseUrl, '')
    // return formatImgUrl(url)
  })
})
watch(() => props.modelValue, (val) => {
  if (val) {
    // 首先将值转为数组
    const list: any[] = Array.isArray(val) ? val : (props.modelValue as any).split(',')
    // 然后将数组转为对象数组
    fileList.value = list.map((item) => {
      if (typeof item === 'string') {
        if (!item.includes(baseUrl))
          item = { name: baseUrl + item, url: item }
        else
          item = { name: item, url: item }
      }
      return item
    })
  }
  else {
    fileList.value = []
    return []
  }
}, { deep: true, immediate: true })

//
// 上传前loading加载
async function handleBeforeUpload(file: any) {
  let isImg = false
  if (props.fileType.length) {
    let fileExtension = ''
    if (file.name.includes('.'))
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)

    isImg = props.fileType.some((type) => {
      if (file.type.includes(type))
        return true
      if (fileExtension && fileExtension.includes(type))
        return true
      return false
    })
  }
  else {
    isImg = file.type.includes('image')
  }
  if (!isImg) {
    message.error(`${t('error.fileFormatError')} ${props.fileType.join('/')} ${t('error.imageFormatFile')}`)
    return false
  }
  if (props.fileSize) {
    // 1代表是course subject模块使用上传
    const isLt = props.uploadModule === 1 ? file.size / 1024 < props.fileSize : file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      message.error(`${t('error.imageSizeError')} ${props.fileType.join('/')} ${props.fileSize} ${props.uploadModule === 1 ? 'KB' : 'MB'}!`)

      return false
    }
  }
  if (props.fileRatio) {
    await new Promise((resolve, reject) => {
      let url = ''
      try {
        url = window.URL ? window.URL.createObjectURL(file) : window.webkitURL.createObjectURL(file)
      }
      catch (e) {
        if (window.console) {
        // window.console.error(e);
          window.console.dir(e)
        }
        const binaryData = [].concat(file)
        const blobFile = new Blob(binaryData)
        url = window.URL ? window.URL.createObjectURL(blobFile) : window.webkitURL.createObjectURL(blobFile)
      }
      const image = new Image()
      image.src = url
      image.onload = () => {
        const width = image.width
        const height = image.height

        if (width !== undefined && height !== undefined && width > 0 && height > 0) {
          if (width / height === props.fileRatio[0] / props.fileRatio[1]) {
            resolve(true)
          // return true
          }
          else {

            message.error(t('error.imageSizeErrorLength'))
            // eslint-disable-next-line prefer-promise-reject-errors
            reject(false)

          // return false
          }
        }
        else {
          message.error(t('error.imageLoading'))

          // eslint-disable-next-line prefer-promise-reject-errors
          reject(false)
        // return false
        }
      }
    })
  }
  message.loading(t('loading.upLoading'))
  number.value++
}

// 文件个数超出
function handleExceed() {
  message.error(`${t('error.fileLengthError')} ${props.limit} !`)
}
const imageUpload = ref()
// 上传成功回调
function handleUploadSuccess(res: any, file: any) {
  if (res.code === 0) {
    uploadList.value.push({ name: file.name, url: res.data.url })
    uploadedSuccessfully()
  }
  else {
    number.value--
    message.closeLoading()
    message.error(res.msg)
    imageUpload.value.handleRemove(file)
    uploadedSuccessfully()
  }
}
// 删除图片
function handleDelete(file: File): UploadProps['beforeRemove'] {
  const findex = fileList.value.map(f => f.name).indexOf(file.name)
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1)
    emit('update:modelValue', listToString(fileList.value))
    return () => false
    // return false
  }
  return () => true
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value)
    uploadList.value = []
    number.value = 0
    emit('update:modelValue', listToString(fileList.value))
    message.closeLoading()
  }
}

// 上传失败
function handleUploadError() {
  message.error(t('error.uploadErrorMessage'))
  message.closeLoading()
}

// 预览
function handlePictureCardPreview(file: any) {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}

// 对象转成指定字符串分隔
function listToString(list: any[], separator?: string) {
  let strs = ''
  separator = separator || ','
  for (const i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf('blob:') !== 0)
      strs += list[i].url.replace(baseUrl, '') + separator
  }
  return strs !== '' ? strs.substr(0, strs.length - 1) : ''
}
</script>

<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload" multiple :action="uploadImgUrl" list-type="picture-card"
      :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload" :limit="limit" :on-error="handleUploadError"
      :on-exceed="handleExceed" :before-remove="handleDelete" :show-file-list="true" :headers="headers"
      :file-list="previewList" :on-preview="handlePictureCardPreview" :class="{ hide: fileList.length >= limit }"
      :data="uploadData"
    >
      <Icon icon="ep:plus" />
<!--      <el-icon class="avatar-uploader-icon">-->
<!--        <plus />-->
<!--      </el-icon>-->
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      {{ props.tipText }}
      <!-- 请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
<template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
的文件 -->
    </div>

    <Dialog v-model="dialogVisible" :title="t('action.preview')" width="800px" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
    </Dialog>
  </div>
</template>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
</style>
