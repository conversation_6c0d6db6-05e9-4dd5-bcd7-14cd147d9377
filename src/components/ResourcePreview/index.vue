<script setup lang='ts'>
import type { Resource } from '@/typings/views/Resource/list'
import ScormPreview from '@/components/ScormPreview/index.vue'
import AudioPreview from '@/components/AudioPreview/index.vue'
import VideoPreview from '@/components/VideoPreview/index.vue'
import FilePreview from '@/components/FilePreview/index.vue'
import { MediaType } from '@/enums/resource'
import CustomDialog from '@/components/CustomDialog/index.vue'

interface PreviewData {
  mediaType: MediaType | undefined
  title: string
  scormRunPath?: string
  scormVersion?: string
  address: string
}
const props = defineProps<{
  data: Resource | undefined | PreviewData
  mediaType: number
}>()
const show = ref(false)
const coms: any = {
  [MediaType.Audio]: AudioPreview,
  [MediaType.Video]: VideoPreview,
  [MediaType.File]: FilePreview,
}
const isFull = ref(false)

const handleClose = () => {
  show.value = false
  setTimeout(() => {
    isFull.value = false
  }, 500)
}
</script>

<template>
  <Dialog
    v-model="show"
    v-model:is-full-screen="isFull"
    :need-max="[MediaType.Scorm, MediaType.File].includes(props.data?.mediaType || MediaType.Scorm)"
    :auto-height="[MediaType.Scorm, MediaType.File].includes(props.data?.mediaType || MediaType.Scorm)"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    @close="handleClose"
    :title="props.data?.title"
  >
<!--    <template #header>-->
<!--      <span>{{ props.data?.title }}</span>-->
<!--    </template>-->
    <template #default>
      <div class="w-full h-full flex items-center justify-center">
        <ScormPreview v-if="props.data?.mediaType === MediaType.Scorm || props.mediaType === MediaType.Aicc" :scorm-url="props.mediaType === MediaType.Aicc ? props.data : props.data.scormRunPath!" :scorm-version="props.mediaType === MediaType.Aicc ? '' : props.data.scormVersion!" />
        <component :is="coms[props.data!.mediaType]" :url="props.data!.address" />
      </div>
    </template>
  </Dialog>
</template>
