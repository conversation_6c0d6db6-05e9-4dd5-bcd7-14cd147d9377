<script setup lang="ts" name="AssignEmployee">
import type { ComponentInternalInstance } from 'vue'
import { ElTable } from 'element-plus'
import { find, includes, matches, uniqBy } from 'lodash-es'
import type { ScopeTableProps } from './typings'
import { deptTreeSelect, employeeData } from '@/api/system/user'
import 'splitpanes/dist/splitpanes.css'
import { Pane, Splitpanes } from 'splitpanes'
import { OrgType } from '@/enums/OrgType'
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

const props = defineProps<{
  /** 用于数据回显的ID数组 */
  // employeeIds: any[]
  /** 表格props */
  tableProps?: ScopeTableProps
}>()
const emits = defineEmits(['confirm'])
const show = ref(false)
const employeeList = ref([])
const dialogLoading = ref(false)
const userList = ref([])
const loading = ref(true)
const showSearch = ref(false)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const dateRange = ref<any>([])
const deptName = ref('')
const deptOptions = ref(undefined)
const queryRef = ref()
const { t } = useI18n()
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  nickName: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined,
})
const tableRef = ref<InstanceType<typeof ElTable>>()
const deptTreeRef = ref()
const highCurrentStatus = ref(false)
const clickedNodeId = ref<number | null>(null) // 存储当前点击的节点ID
const templateSelection = ref<string[]>([])
const multipleSelection = ref([])
const RefSingleTable = ref()
const selectedIds = ref([])
/** 通过条件过滤节点  */
function filterNode(value: any, data: any) {
  if (!value)
    return true
  return data.label.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  deptTreeRef.value!.filter(val)
})
/** 根据employeeList设置选中状态 */
const setTableSelect = (listData: any[]) => {
  listData.forEach((element) => {
    if (employeeList.value.find(item => `${item.relevanceId}` === `${element.id}`)) {
      RefSingleTable.value?.toggleRowSelection(element, true)
    }
  })
}
/** 查询部门下拉树结构 */
const getDeptTree = async () => {
  deptOptions.value = await deptTreeSelect({ status: '0' })
}
/** 查询用户列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await employeeData(queryParams.value)
    userList.value = data.list
    if (selectedIds.value) {
      await setTableSelected()
    }
    total.value = data.total
  } finally {
    loading.value = false
  }
}
/** 节点单击事件 */
function handleNodeClick(data: any) {
  highCurrentStatus.value = true
  clickedNodeId.value = data.virtualId // 点击节点时，更新当前点击的节点ID
  if (data.level === OrgType.Company) {
    queryParams.value.companyId = data.id
    queryParams.value.deptId = null
  }
  if (data.level === OrgType.Department) {
    queryParams.value.deptId = data.id
    queryParams.value.companyId = null
  }
  handleQuery()
}
/** 左侧树点击取消高亮 */
function handleCancelHighlight() {
  clickedNodeId.value = null
  highCurrentStatus.value = false
  queryParams.value.deptId = null
  queryParams.value.companyId = null
  handleQuery()
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1
  getList()
}
/** 重置按钮操作 */
function resetQuery() {
  multipleSelection.value = []
  templateSelection.value = []
  dateRange.value = []
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    nickname: undefined,
    mobile: undefined,
    status: undefined,
    deptId: undefined,
  }
  queryParams.value.deptId = undefined
  // deptTreeRef.value.setCurrentKey(null)
  handleQuery()
}
/** 选择条数  */
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}
/**
 * 拼接人员的名称全路径
 * @param args 要拼接的字符串数据
 */
function formatParamsName(...args: string[]) {
  return args.filter(part => part).join(' / ')
}
function handleConfirm() {
  emits('confirm', multipleSelection.value)
  show.value = false
}
const handleClose = () => {
  show.value = false
}

function handleClearSelection() {
  RefSingleTable.value?.clearSelection()
}
/** 展开/折叠操作 */
function toggleExpandAll() {
  showSearch.value = !showSearch.value
}
// onMounted(() => {
//   getDeptTree()
//   getList()
// })
watch(() => show.value, (newValue) => {
  if (newValue) {
    resetQuery()
    handleClearSelection()
  }
})
const open = async (employeeIds: number | number[]) => {
  await getDeptTree()
  await getList()
  selectedIds.value = employeeIds
  show.value = true
  await resetQuery()
}
// 设置表格数据回显
const setTableSelected = () => {
  setTimeout(() => {
    userList.value.forEach((item, index) => {
      selectedIds.value.forEach(sel => {
        if (sel === item.id) {
          RefSingleTable.value.toggleRowSelection(item, true)
        }
      })
    })
  }, 0)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

</script>

<template>
  <Dialog v-model="show" align-center :width="1050" :title="t('dialog.selectEmployees')">
    <div v-loading="dialogLoading" class="employee-dialog">
      <Splitpanes class="default-theme">
        <!-- 部门数据 -->
        <Pane :size="26" class="!bg-white">
          <div class="h-[--left-panel-height] flex flex-col">
            <div class="head-container">
              <el-input
                v-model="deptName"
                :placeholder="t('sys.user.deptNamePH')"
                clearable
                style="margin-bottom: 10px;width:95%"
              >
                <template #prefix>
                  <Icon class="mr-5px" icon="ep:search" />
                </template>
              </el-input>
            </div>
            <el-scrollbar class="h-full flex-1">
              <el-tree
                ref="deptTreeRef"
                :data="deptOptions"
                :props="{ label: 'label', children: 'children' }"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                node-key="id"
                :highlight-current="highCurrentStatus"
                @node-click="handleNodeClick"
              >
                <template #default="{ node }">
                  <div class="flex justify-between">
                    <div class="me-2.5">
                      <el-tag
                        v-if="node.data.level === OrgType.Company " :title="node.data.shortName"
                        :style="{
                          '--el-tag-text-color': '#630EB8',
                          '--el-tag-bg-color': '#F3F1FF',
                          '--el-tag-border-color': '#D3CEF0',
                        }"
                      >
                        {{ node.data.shortName ? node.data.shortName : 'Company' }}
                      </el-tag>
                      <el-tag
                        v-else
                        :title="node.data.shortName"
                      >
                        {{ node.data.shortName }}
                      </el-tag>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex gap-1.5 me-5">
                        <span
                          :title="node.label" class="whitespace-normal line-clamp-1 break-all"
                        > {{ node.label }}</span>
                      </div>
                      <div class="flex justify-end ms-auto" @click.stop="handleCancelHighlight">
                        <el-icon v-if="clickedNodeId === node.data.virtualId">
                          <CircleClose />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </template>
              </el-tree>
            </el-scrollbar>
          </div>
        </Pane>
        <!-- 用户数据 -->
        <Pane class=" !bg-white ml-2">
          <div>
            <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="100px" label-position="left">
              <el-row :gutter="24">
                <el-col :span="12" :xs="12">
                  <el-form-item :label="t('sys.user.userName')" prop="nickname">
                    <el-input
                      v-model="queryParams.nickname"
                      :placeholder="t('sys.user.userNamePH')"
                      clearable
                      class="!w-48"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('sys.user.badgeNo')" prop="badgeNumber">
                    <el-input
                      v-model="queryParams.badgeNumber"
                      :placeholder="t('sys.user.badgePH')"
                      clearable
                      class="!w-48"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-show="showSearch" :gutter="24">
                <el-col :span="12" :xs="12">
                  <el-form-item :label="t('sys.user.email')" prop="email">
                    <el-input
                      v-model="queryParams.email"
                      :placeholder="t('sys.user.emailPH')"
                      clearable
                      class="!w-48"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('sys.user.workType')" prop="workType">
                    <el-select v-model="queryParams.workType" :placeholder="t('sys.user.workTypePH')" clearable filterable class="!w-48">
                      <el-option
                        v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_WORK_TYPE)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-show="showSearch" :gutter="24">
                <el-col :span="12" :xs="12">
                  <el-form-item :label="t('sys.user.userStatus')" prop="userStatus">
                    <el-select
                      v-model="queryParams.userStatus" :placeholder="t('sys.user.userStatusPH')" clearable filterable
                      class="!w-48"
                    >
                      <el-option
                        v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_USER_STATUS)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('sys.user.nationality')" prop="nationalityCode">
                    <el-select
                      v-model="queryParams.nationalityCode" :placeholder="t('sys.user.nationalityPH')"
                      clearable filterable
                      class="!w-48"
                    >
                      <el-option
                        v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_NATIONALITY)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-show="showSearch" :gutter="24">
<!--                <el-col :span="12" :xs="12">-->
<!--                  <el-form-item :label="t('sys.user.onboardingDate')" prop="onboardingDate">-->
<!--                    <el-date-picker v-model="queryParams.onboardingDate" type="date" :placeholder="t('sys.user.onboardingDatePH')" clearable value-format="YYYY-MM-DD" class="!w-48" />-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
                <el-col :span="12" :xs="12">
                  <el-form-item :label="t('sys.user.workTerms')" prop="workTerms">
                    <el-select
                      v-model="queryParams.workTerms" :placeholder="t('sys.user.workTermPH')"
                      clearable filterable
                      class="!w-48"
                    >
                      <el-option
                        v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_WORK_TERMS)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item>
                <el-button type="primary" @click="handleQuery">
                  <Icon class="mr-5px" icon="ep:search" />
                  {{ t('global.search') }}
                </el-button>
                <el-button @click="resetQuery">
                  <Icon class="mr-5px" icon="ep:refresh" />
                  {{ t('global.reset') }}
                </el-button>
                <el-button @click="handleClearSelection">
                  <Icon class="mr-5px" icon="ep:refresh" />
                  {{ t('global.clearSelection') }}
                </el-button>
                <el-button
                  plain
                  @click="toggleExpandAll"
                >
                  <Icon class="mr-5px" icon="ep:sort" />
                  {{ t('sys.dept.expand') }}
                </el-button>
              </el-form-item>
            </el-form>
            <ElTable
              ref="RefSingleTable"
              v-loading="loading" :data="userList" row-key="id"
              border
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" :reserve-selection="true" width="55" align="center" />
              <el-table-column key="nickName" :label="t('sys.user.nickName')" align="center" prop="nickname" :show-overflow-tooltip="true" width="150" />
              <el-table-column key="email" :label="t('sys.user.email')" align="center" prop="email" :show-overflow-tooltip="true" width="240" />
              <el-table-column key="badgeNumber" :label="t('sys.user.badgeNo')" align="center" prop="badgeNumber" :show-overflow-tooltip="true" />
              <el-table-column key="mobile" :label="t('sys.user.phoneNumber')" align="center" prop="mobile" width="140" show-overflow-tooltip />
              <el-table-column key="workType" :label="t('sys.user.workType')" align="center" prop="workType" width="120">
                <template #default="scope">
                  <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TYPE" :value="scope.row.workType" />
                </template>
              </el-table-column>
              <el-table-column key="userStatus" :label="t('sys.user.userStatus')" align="center" prop="userStatus" width="120">
                <template #default="scope">
                  <dict-tag :type="DICT_TYPE.SYSTEM_USER_USER_STATUS" :value="scope.row.userStatus" />
                </template>
              </el-table-column>
              <el-table-column key="nationalityCode" :label="t('sys.user.nationality')" align="center" prop="nationalityCode" width="170" show-overflow-tooltip>
                <template #default="{ row }">
                  <el-select
                    v-model="row.nationalityCode"
                    class="custom-elselect w-80" placeholder="" disabled
                  >
                    <el-option
                      v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_NATIONALITY)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column key="workTerms" :label="t('sys.user.workTerms')" align="center" prop="workTerms" width="120">
                <template #default="scope">
                  <dict-tag :type="DICT_TYPE.SYSTEM_USER_WORK_TERMS" :value="scope.row.workTerms" />
                </template>
              </el-table-column>
              <el-table-column key="onboardingDate" :label="t('sys.user.onboardingDate')" align="center" prop="onboardingDate" width="120" show-overflow-tooltip fixed="right" />
            </ElTable>
            <pagination
              v-show="total > 0"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              :total="total"
              :page-sizes="[10, 15, 20, 30, 40, 50]"
              layout="total, sizes, prev, pager, next"
              @pagination="getList"
            />
          </div>
        </Pane>
      </Splitpanes>
    </div>
    <template #footer>
      <el-button type="primary" @click="handleConfirm">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="handleClose">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">
:deep(.custom-page){
  @apply relative p-[unset] #{!important};
  .el-pagination{
    @apply relative #{!important};
  }
}
/**国籍列CSS */
:deep .custom-elselect .el-select__wrapper{
  border: none;
  margin:0;
  padding:0;
}
:deep .custom-elselect .is-disabled{
  cursor: text;
  background-color: #fff
}
:deep .custom-elselect .is-disabled:hover{
  cursor: text;
  background-color: #f5f7fa;
  box-shadow: 0 0 0 1px #f5f7fa inset
}
:deep .custom-elselect .el-select__wrapper .el-select__suffix .el-select__icon {
  cursor: text;
  visibility: hidden;
}
:deep .custom-elselect .el-select__placeholder {
  // width: 0;
  text-align: center
}
:deep .el-select {
  --el-select-disabled-border: #fff;
  --el-select-disabled-color: #606266;
}
</style>
