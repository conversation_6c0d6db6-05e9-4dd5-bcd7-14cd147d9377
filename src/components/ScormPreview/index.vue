<script setup lang='ts'>
import { Scorm12API, Scorm2004API } from 'scorm-again'
import { ScormVersion } from '@/enums/scorm'

const props = defineProps<{ scormVersion: string, scormUrl: string }>()
const scormLoading = ref(false)
const { t } = useI18n()
const init = () => {

  // 获取传入的scorm版本
  const version = props.scormVersion
  if (version === ScormVersion.Scorm12) {
    window.API = new Scorm12API({
      logLevel: 5,
      autocommit: false,
      autocommitSeconds: 5,
      asyncCommit: true,
      dataCommitFormat: 'json',
      mastery_override: true,
      selfReportSessionTime: true,
      alwaysSendTotalTime: true,
    })
  }
  else {
    // 初始化2004API
    window.API_1484_11 = new Scorm2004API({
      logLevel: 5,
      autocommit: false,
      autocommitSeconds: 5,
      asyncCommit: true,
      dataCommitFormat: 'json',
      mastery_override: true,
      selfReportSessionTime: true,
      alwaysSendTotalTime: true,
    })
  }
}
const iframeOnload = () => {
  scormLoading.value = false
}
onMounted(() => {
  init()
})

watch(() => props.scormUrl, () => {
  scormLoading.value = true
  init()
})
</script>

<template>
  <!-- <div class="w-full h-full p-10"> -->
  <iframe v-if="props.scormUrl" :key="props.scormUrl" class="w-full h-full" :src="props.scormUrl" :onload="iframeOnload"></iframe>
  <div v-else class="w-full h-full flex flex-col items-center justify-center gap-5 bg-[#2D2F2E]">
    <img src="@/assets/images/resource/load-error.png" width="220" />
    <span class="text-base text-white">{{ t('common.uploadError') }}</span>
    <span class="text-base text-white">{{ t('common.noScormLink') }}</span>
    <!-- <el-button plain type="primary" class="mt-1" @click="handleRetry">
      Click here to retry
    </el-button> -->
  </div>
  <!-- </div> -->
</template>

<style scoped lang='scss'>

</style>
