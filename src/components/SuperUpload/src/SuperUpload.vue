<template>
  <slot
    :uid="fileInfo!.uid"
    :progress="{ status, remainingSize, remainingTime, percentage, speed }"
    :result="uploadResult"
    :emitPrepared="emitPrepared"
    :emitComplete="emitComplete"
    :emitError="emitError"
  ></slot>
</template>
<script lang="ts" setup>
import * as Md5Util from '@/utils/md5'
import * as FileTaskApi from '@/api/infra/fileTask'
import { FileTaskInitReqVO, Part, TaskInfoDTO } from '@/api/infra/fileTask'
import axios from 'axios'
import { ref } from 'vue'
import PQueue from 'p-queue'
import { FileInfo, StatusEnum, UploadResult } from '@/components/SuperUpload/src/config'

defineOptions({ name: 'SuperUpload' })

// const { t } = useI18n() // 国际化
// const { push } = useRouter()

// 队列相关 上传队列
const partQueue = new PQueue({ concurrency: 3, autoStart: false })
// 是否单并发

/**非响应参数*/
let file: File | null = null
let startTime = new Date().getTime()
let percentageMap: Map<number, { totalSize: number; loadedSize: number }> = new Map()

let identifier = ''

/**响应参数*/
const status = ref(0)
const remainingSize = ref('0mb')
const remainingTime = ref('0s')
const speed = ref('0mbps')
const percentage = ref(0)

const uploadResult = ref<UploadResult>({
  url: '', // 文件s3存储链接
  path: '', // 文件存储路径，如：archive/6064/eb91bb25-1a87-48a9-8406-422db580a122.png
  id: 0, // 文件id
  folderId: 0, // 文件夹id
  fullPath: '', // 全路径
  fullPathIds: '', // 全路径文件夹id字符串})
  fileSize: 0,
  fileType: '',
  fileName: ''
})

const props = withDefaults(
  defineProps<{
    fileInfo: FileInfo | null // 文件信息
    concurrency: number // 并发数
    chunkSize: number // 分片大小
  }>(),
  {
    fileInfo: null,
    concurrency: 3, // 默认3并发
    chunkSize: 5 * 1024 * 1024 // 5M/片
  }
)

// 定义 emit
const emit = defineEmits<{
  (e: 'prepared', v: UploadResult)
  (e: 'complete', v: UploadResult)
  (e: 'error', v: string)
}>()

// 触发上传完成事件
const emitPrepared = () => {
  emit('prepared', uploadResult.value)
}

const emitComplete = () => {
  emit('complete', uploadResult.value)
}
const emitError = (error: string) => {
  emit('error', error)
}
/**
 * 更新上传进度
 * @param partNumber
 * @param totalSize
 * @param loadedSize
 */
const update = (partNumber: number, totalSize: number, loadedSize: number) => {
  percentageMap.set(partNumber, { totalSize, loadedSize })
  let total = 0
  let loaded = 0

  // 遍历percentageMap，取值

  percentageMap.forEach((value: { totalSize: number; loadedSize: number }) => {
    total += value.totalSize
    loaded += value.loadedSize
  })

  // 当前时间
  const nowTime = new Date().getTime()
  // 时间间隔（s）= 当前时间 - 开始时间
  const intervalTime = (nowTime - startTime) / 1000
  /** 获取从开始上传到现在的平均速度（byte/s）- start */
  // 平均速度 = 已上传总大小 / 时间间隔
  const uploadSpeed = loaded / intervalTime
  // 剩余大小 = 文件总大小 - 已上传大小
  const totalRemaining = file!.size - loaded
  // 未合并前以99%计算
  percentage.value = Number(((loaded / file!.size) * 0.99).toFixed(2)) * 100
  remainingSize.value = `${(totalRemaining / 1024 / 1024).toFixed(0)}mb`
  // 剩余时间 = 剩余大小 / 平均速度
  remainingTime.value = uploadSpeed != 0 ? Math.ceil(totalRemaining / uploadSpeed) + 's' : '未知'
  speed.value = `${(uploadSpeed / 1024 / 1024).toFixed(2)}mbps`
}

/**
 * 预上传，初始化数据
 */
const preUpload = async () => {
  console.log('prepareUpload')
  // 开始上传
  // selectedUploadFile.value.status = StatusEnum.UPLOADING

  return new Promise(async (resolve, reject) => {
    // 初始化任务

    try {
      const fileTaskReqVO: FileTaskInitReqVO = {
        identifier: await Md5Util.getMd5(file!),
        fileName: file!.name,
        totalSize: file!.size,
        type: props.fileInfo!.type,
        chunkSize: props.chunkSize,
        currentFolderId: 0,
        relativePath: ''
      }
      const taskInfoDTO: TaskInfoDTO = await FileTaskApi.initFileTask(fileTaskReqVO)
      if (!taskInfoDTO) {
        useMessage().error(`File upload error, failed to retrieve upload task.`)
        emitError('File upload error, failed to retrieve upload task.')
        console.log('失败1')
        status.value = StatusEnum.ERROR
        reject(null)
      }

      const { taskRecord } = taskInfoDTO

      // 添加分片上传任务
      identifier = taskRecord.identifier
      for (let partNumber = 1; partNumber <= taskRecord.chunkNum; partNumber++) {
        const existPart = (taskRecord.exitPartList || []).find(
          (_exitPart: Part) => _exitPart.partNumber == partNumber
        )
        if (existPart) {
          // 分片已上传完成，累计到上传完成的总额中,同时记录一下上次断点上传的大小，用于计算上传速度
          update(partNumber, existPart.size, existPart.size)
        } else {
          partQueue.add(async () => {
            const startSize = taskRecord.chunkSize * (partNumber - 1)
            const endSize = startSize + taskRecord.chunkSize
            const blob = file!.slice(startSize, endSize)
            try {
              const preSignUrl = await FileTaskApi.getFileTaskPreSignUrl(identifier, partNumber)
              await axios.request({
                url: preSignUrl,
                method: 'PUT',
                data: blob,
                onUploadProgress: (event) => update(partNumber, event.total!, event.loaded),
                headers: { 'Content-Type': 'application/octet-stream' }
              })
            } catch {
              console.log('失败2')
              emitError('File upload error, failed to retrieve upload task.')
            }
            console.log('分片传完了')
          })
        }
      }

      // 就绪
      status.value = StatusEnum.WAITING
      emitPrepared()
      resolve(void 0)
    } catch (e) {
      status.value = StatusEnum.ERROR
      reject(e)
    }
  })
}

const startUpload = async () => {
  console.log('startUpload', status.value)
  // 只有waiting状态，允许开始分片上传，否则是成功状态不处理
  if (status.value !== StatusEnum.WAITING) return
  return new Promise<string>((resolve, reject) => {
    startTime = new Date().getTime()
    partQueue
      .start()
      .on('error', (reason) => {
        partQueue.clear()
        reject(reason)
      })
      .onIdle()
      .then(async () => {
        // 合并分片
        // 后端可能返回： { url, path, id, folderId, fullPath, fullPathIds }
        try {
          uploadResult.value = await FileTaskApi.mergeFileTask({
            identifier,
            fileType: props.fileInfo!.type,
            digitFlag: false,
            currentFolderId: props.fileInfo!.folderId,
            relativePath: props.fileInfo!.file.webkitRelativePath.substring(
              0,
              file!.webkitRelativePath.lastIndexOf('/')
            )
          })
          uploadResult.value.fileType = props.fileInfo!.type
          uploadResult.value.fileSize = props.fileInfo!.file.size
          uploadResult.value.fileName = props.fileInfo!.file.name
          // 如需合并，合并后，才算100%
          percentage.value = 100
          // useMessage().success(`上传成功.`)

          status.value = StatusEnum.SUCCESS
          // successNum.value++
          // useMessage().success('Upload successfully')
          emitComplete()
          resolve('')
        } catch {
          emitError('File upload error, failed to retrieve upload task.')
        }
      })
  })
}

// 监听输入属性
watch(
  () => props.fileInfo,
  (fileInfo: FileInfo) => {
    file = fileInfo.file
    status.value = StatusEnum.NOTSTART
    // 启动上传
    preUpload().then(() => startUpload().then())
  },
  { immediate: true }
)

/** 初始化 **/
onMounted(() => {})
</script>
<style lang="scss" scoped></style>
