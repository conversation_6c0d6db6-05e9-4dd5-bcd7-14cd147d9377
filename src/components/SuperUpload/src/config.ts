export type FileInfo = {
  uid: string
  file: File
  type: string
  folderId: 0 // 例如目录id，默认根目录
  relativePath: '' // 例如相对路径，默认无相对路径
}
export type UploadResult = {
  url: string // 文件s3存储链接
  path: string // 文件存储路径，如：archive/6064/eb91bb25-1a87-48a9-8406-422db580a122.png
  id: number // 文件id
  folderId: number // 文件夹id
  fullPath: string // 全路径
  fullPathIds: string // 全路径文件夹id字符串
  fileType: string
  fileSize: number
  fileName: string
  isCurrent?: boolean
}

const enum StatusEnum {
  NOTSTART = 0,
  WAITING = 1,
  UPLOADING = 2,
  PAUSE = 3,
  SUCCESS = 4,
  ERROR = 5,
  CANCEL = 6
}

export { StatusEnum }
