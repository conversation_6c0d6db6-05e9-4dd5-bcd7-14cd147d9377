<script setup lang='ts'>
import { Base64 } from 'js-base64'
import { formatImgUrl } from '@/utils'

const props = defineProps<{
  url: any
}>()
const loading = ref(true)
const url = computed(() => {
  if (props.url) {
    let u = `${location.origin}${formatImgUrl(props.url)}&fullfilename=${props.url?.split('/').pop()}`
    u = encodeURIComponent(Base64.encode(u))
    return `${import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL}/preview/onlinePreview?url=${u}`
  }

  return ''
})
const handleLoad = () => {
  loading.value = false
}
const handleError = () => {
  console.log('cuole')
}
</script>

<template>
  <div v-loading="loading" class="w-full h-full">
    <iframe :src="url" class="w-full h-full" @load="handleLoad" @error="handleError"></iframe>
  </div>
</template>

<style scoped lang='scss'>

</style>
