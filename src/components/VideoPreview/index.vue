<script setup lang='ts'>
import { formatImgUrl } from '@/utils/index'

const props = defineProps<{
  url: string
}>()
const { t } = useI18n()
const isError = ref(false)
const videoRef = ref<HTMLVideoElement>()
const handleError = () => {
  isError.value = true
}
const handleRetry = () => {
  videoRef.value?.load()
  isError.value = false
}
</script>

<template>
  <div class="w-full m-10 mt-0">
    <video v-show="!isError" ref="videoRef" controls :src="formatImgUrl(props.url)" class="w-full aspect-video" @error="handleError"></video>
    <div v-show="isError" class="w-full aspect-video flex flex-col items-center justify-center gap-5 bg-[#2D2F2E] rounded-md">
      <img src="@/assets/images/resource/load-error.png" width="220" />
      <span class="text-base text-white">{{ t('common.uploadError') }}</span>
      <el-button plain type="primary" class="mt-1" @click="handleRetry">
        {{ t('common.noScormLink') }}
      </el-button>
    </div>
  </div>
</template>

<style scoped lang='scss'>

</style>
