<script setup lang='ts'>
import CustomDialog from '@/components/CustomDialog/index.vue'
import type { Resource } from '@/typings/views/Resource/list'
import ResourceTable from '@/views/resource/list/components/ResourceTable/index.vue'
// const resources = defineModel<Resource[]>('resources')
const props = defineProps<{ type: 'multiple' | 'none' | 'single', ooc: boolean, course: boolean, appendixValue: boolean }>()
const emits = defineEmits<{
  (event: 'confirm', ids: number[] | undefined, resources: Resource[]): void
}>()
const modelIds = defineModel<number[] | undefined>('ids')
const show = defineModel<boolean>('show', { default: false })
const { t } = useI18n()
const isMini = ref(false)
const isFull = ref(false)
const selections = ref()
const resourceTable = ref<InstanceType<typeof ResourceTable>>()
// const ids = ref([6168])
const handleSelectionChange = (res: Resource[]) => {
  selections.value = res
}

const handleConfirm = () => {
  show.value = true
  emits('confirm', modelIds.value, selections.value)
}
const handleCancel = () => {
  show.value = false
  setTimeout(() => {
    isFull.value = false
    isMini.value = false
    modelIds.value = []
    resourceTable.value?.clearSelection()
  }, 500)
}
// watchEffect(() => {
//   if (show.value) {
//     console.log('执行')

//     resourceTable.value?.setChecked()
//   }
// })
watch(show, () => {
  if (show.value) {
    nextTick(() => {
      resourceTable.value?.clearSelection()
      resourceTable.value?.setChecked()
    })
  }
})
</script>

<template>
  <Dialog v-model="show" :title="t('dialog.resourceSelection')" @close="handleCancel" width="1000">
    <ResourceTable ref="resourceTable" v-model:ids="modelIds" class="p-2" :type="props.type" :ooc-value="props.ooc" :course-value="props.course" :appendixs-value="props.appendixValue" @selection-change="handleSelectionChange" />
    <template #footer>
      <el-button type="primary" @click="handleConfirm">
        {{ t('global.confirm') }}
      </el-button>
      <el-button @click="handleCancel">
        {{ t('global.cancel') }}
      </el-button>
    </template>
  </Dialog>
</template>

<style scoped lang='scss'>

</style>
