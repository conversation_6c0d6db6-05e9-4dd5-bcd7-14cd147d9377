<script setup lang='ts'>
const props = defineProps<{
  number: number
  text: string
}>()
</script>

<template>
  <!-- 展示部门总数 -->
  <div class="rounded-md border text-[#007943] border-[#7fbca1] bg-[#e5f1ec] h-12 flex items-center justify-center mb-3.5">
    <svg-icon icon-class="OrgTotal" class="text-[25px] " />
    <span class="text-lg text-primary font-bold ms-3.5 me-2.5">
      {{ props.number }}
    </span>
    <span class="text-xs text-primary">
      {{ props.text }}
    </span>
<!--    <slot />-->
  </div>
</template>

<style scoped lang="scss">
.custom-bar{
  // background: linear-gradient( 90deg, #EFFFFC 0%, #D3F4FD 26%, #A4DFF8 41%, #D2D4FB 56%, #F3D4FE 69%, #C4D4FB 77%, #A1DAF8 87%, #FFFFFF 100%);
  // box-shadow: inset 0px 4px 4px 0px rgba(98,74,117,0.14);
  @apply rounded-md border border-[#7fbca1] bg-[#e5f1ec];
}
</style>
