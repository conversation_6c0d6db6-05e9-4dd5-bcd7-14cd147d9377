<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { listTopic } from '@/api/category/topic'
import { handlePhaseTree } from '@/utils/tree'
const props = defineProps<{ hasNoSubject?: boolean, modelValue: string, selecteTreeStyle?: string }>()
const subjectId = defineModel<any>('modelValue')
const subjectList = ref()

const getList = async () => {
  const data = await listTopic({})
  data.forEach((element) => {
    element.value = element.id
    element.label = element.name
  })
  subjectList.value = handlePhaseTree(data, 'id')
  if (props.hasNoSubject) {
    subjectList.value.unshift({
      id: 0,
      value: 0,
      name: 'No Subject',
      label: 'No Subject',
    })
  }
}
onMounted(() => {
  getList()
})
</script>

<template>
  <el-cascader
    v-model="subjectId" :options="subjectList" :props="{ checkStrictly: true, multiple: true, value: 'id', label: 'name', children: 'children', expandTrigger: 'hover' }" :clearable="true"
    :collapse-tags="true" :collapse-tags-tooltip="true" :emit-path="false" separator=" / "
  />
</template>
