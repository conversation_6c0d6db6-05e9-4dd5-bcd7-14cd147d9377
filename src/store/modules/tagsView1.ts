import type { RouteLocationNormalizedLoaded, RouteRecordName } from 'vue-router'

const useTagsViewStore = defineStore(
  'tags-view',
  {
    state: (): {
      visitedViews: RouteLocationNormalizedLoaded[]
      cachedViews: RouteRecordName[]
      iframeViews: RouteLocationNormalizedLoaded[]
      manualCloseCachedViews: RouteRecordName[]
    } => ({
      visitedViews: [],
      cachedViews: [],
      iframeViews: [],
      manualCloseCachedViews: [],
    }),
    actions: {
      // 检测是否需要手动关闭
      isManualClose(item: RouteLocationNormalizedLoaded) {
        return item.meta.manualClose
      },
      addView(view: RouteLocationNormalizedLoaded) {
        this.addVisitedView(view)
        this.addCachedView(view)
      },
      addIframeView(view: RouteLocationNormalizedLoaded) {
        if (this.iframeViews.some(v => v.path === view.path))
          return
        this.iframeViews.push(
          Object.assign({}, view, {
            title: view.meta.title || 'no-name',
          }),
        )
      },
      addVisitedView(view: RouteLocationNormalizedLoaded) {
        if (this.visitedViews.some(v => v.path === view.path))
          return
        this.visitedViews.push(
          Object.assign({}, view, {
            title: view.meta.title || 'no-name',
          }),
        )
      },
      addCachedView(view: RouteLocationNormalizedLoaded) {
        if (this.cachedViews.includes(view.name ?? ''))
          return
        if (!view.meta.noCache && view.name)
          this.cachedViews.push(view.name)
      },
      addManualCloseCachedViews(view: RouteLocationNormalizedLoaded) {
        if (this.manualCloseCachedViews.includes(view.name ?? ''))
          return
        if (this.isManualClose(view) && view.name) {
          this.manualCloseCachedViews.push(view.name)
        }
      },
      delView(view: RouteLocationNormalizedLoaded): Promise<any> {
        return new Promise((resolve) => {
          this.delVisitedView(view)
          this.delCachedView(view)
          resolve({
            visitedViews: [...this.visitedViews],
            cachedViews: [...this.cachedViews],
          })
        })
      },
      delVisitedView(view: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          for (const [i, v] of this.visitedViews.entries()) {
            if (v.path === view.path) {
              this.visitedViews.splice(i, 1)
              break
            }
          }
          this.iframeViews = this.iframeViews.filter(item => item.path !== view.path)
          resolve([...this.visitedViews])
        })
      },
      delIframeView(view: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          this.iframeViews = this.iframeViews.filter(item => item.path !== view.path)
          resolve([...this.iframeViews])
        })
      },
      delCachedView(view: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          const index = this.cachedViews.indexOf(view.name ?? '')
          index > -1 && this.cachedViews.splice(index, 1)
          resolve([...this.cachedViews])
        })
      },
      delManualCloseCachedView(view: RouteLocationNormalizedLoaded) {
        const index = this.manualCloseCachedViews.findIndex(name => name === view.name)
        this.manualCloseCachedViews.splice(index, 1)
        this.delCachedView(view)
      },
      delOthersViews(view: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          this.delOthersVisitedViews(view)
          this.delOthersCachedViews(view)
          resolve({
            visitedViews: [...this.visitedViews],
            cachedViews: [...this.cachedViews],
          })
        })
      },
      delOthersVisitedViews(view: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          this.visitedViews = this.visitedViews.filter((v) => {
            if (this.isManualClose(v))
              return true
            return v.meta.affix || v.path === view.path
          })
          this.iframeViews = this.iframeViews.filter(item => item.path === view.path)
          resolve([...this.visitedViews])
        })
      },
      delOthersCachedViews(view: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          const index = this.cachedViews.indexOf(view.name ?? '')
          // 过滤cachedViews，避免将需要手动关闭的路由缓存清理掉
          const filterCachedViews = this.cachedViews.filter((name, filterIndex) => {
            return filterIndex === index || this.manualCloseCachedViews.find(m => m === name)
          })

          if (index > -1)
            // this.cachedViews = filterCachedViews.slice(index, index + 1)
            this.cachedViews = filterCachedViews
          else
            this.cachedViews = []

          resolve([...this.cachedViews])
        })
      },
      delAllViews(view?: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          this.delAllVisitedViews(view)
          this.delAllCachedViews(view)
          resolve({
            visitedViews: [...this.visitedViews],
            cachedViews: [...this.cachedViews],
          })
        })
      },
      delAllVisitedViews(_view?: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          const affixTags = this.visitedViews.filter((tag) => {
            if (tag.meta.affix) {
              return true
            }
            if (this.isManualClose(tag)) {
              return true
            }
          })
          this.visitedViews = affixTags
          this.iframeViews = []
          resolve([...this.visitedViews])
        })
      },
      delAllCachedViews(_view?: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          // 过滤需要手动关闭的路由
          // 过滤cachedViews，避免将需要手动关闭的路由缓存清理掉
          const filterCachedViews = this.cachedViews.filter((name) => {
            return this.manualCloseCachedViews.find(m => m === name)
          })
          this.cachedViews = filterCachedViews
          resolve([...this.cachedViews])
        })
      },
      updateVisitedView(view: RouteLocationNormalizedLoaded) {
        for (let v of this.visitedViews) {
          if (v.path === view.path) {
            v = Object.assign(v, view)
            break
          }
        }
      },
      delRightTags(view: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          const index = this.visitedViews.findIndex(v => v.path === view.path)
          if (index === -1)
            return

          this.visitedViews = this.visitedViews.filter((item, idx) => {
            if (idx <= index || (item.meta && item.meta.affix))
              return true

            const i = this.cachedViews.indexOf(item.name ?? '')
            // 过滤cachedViews，避免将需要手动关闭的路由缓存清理掉
            const filterCachedViews = this.cachedViews.filter((name, filterIndex) => {
              return filterIndex === i || this.manualCloseCachedViews.find(m => m === name)
            })
            if (i > -1)
              this.cachedViews = filterCachedViews

            if (item.meta.link) {
              const fi = this.iframeViews.findIndex(v => v.path === item.path)
              this.iframeViews.splice(fi, 1)
            }
            if (this.isManualClose(item)) { return true }
            return false
          })
          resolve([...this.visitedViews])
        })
      },
      delLeftTags(view: RouteLocationNormalizedLoaded) {
        return new Promise((resolve) => {
          const index = this.visitedViews.findIndex(v => v.path === view.path)
          if (index === -1)
            return

          this.visitedViews = this.visitedViews.filter((item, idx) => {
            if (idx >= index || (item.meta && item.meta.affix))
              return true

            const i = this.cachedViews.indexOf(item.name ?? '')
            // 过滤cachedViews，避免将需要手动关闭的路由缓存清理掉
            const filterCachedViews = this.cachedViews.filter((name, filterIndex) => {
              return filterIndex === i || this.manualCloseCachedViews.find(m => m === name)
            })
            if (i > -1)
              this.cachedViews = filterCachedViews

            if (item.meta.link) {
              const fi = this.iframeViews.findIndex(v => v.path === item.path)
              this.iframeViews.splice(fi, 1)
            }
            // 如果存在手动关闭控制，跳过
            if (this.isManualClose(item)) { return true }
            return false
          })
          resolve([...this.visitedViews])
        })
      },
    },
  },
)

export default useTagsViewStore
