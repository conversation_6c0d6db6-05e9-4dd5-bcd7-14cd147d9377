/**=================================== 定义固定常量 开始 ===================================*/
// 项目git地址
def gitUrl = 'http://***********:9000/olp/phase2/olp-admin.git'
// 测试服务器
def testServer = 'AA-FAT-OLP'
// 生产服务器
def prodServer = 'Web2'
def serverName = ''
def project = "olp-admin"
def volumePath = "/root/.jenkins/workspace/OLP管理端"

// 发布制品常量
def artifactsServerPath = '/home/<USER>/olp'
def projectZip = "${project}.zip"
/**=================================== 定义固定常量 结束 ===================================*/

/**=================================== 定义公共方法 开始 ===================================*/

def publishSSH(configName, execCommand, remoteDirectory, removePrefix, sourceFiles) {
    sshPublisher(publishers: [sshPublisherDesc(configName: "${configName}", transfers: [sshTransfer(cleanRemote: false, excludes: '',
            execCommand: "${execCommand}",
            execTimeout: 120000, flatten: false, makeEmptyDirs: false, noDefaultExcludes: false, patternSeparator: '[, ]+',
            remoteDirectory: "${remoteDirectory}", remoteDirectorySDF: false, removePrefix: "${removePrefix}", sourceFiles: "${sourceFiles}")],
            usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])
}

/**=================================== 定义公共方法 开始 ===================================*/


node {
    /**
     * 获取参数变量
     */
    /**=================================== 来自gitlab 开始 ===================================*/
    def type = "${type}"
    def branch = "${branch}" // 发布分支，如果存在，则来自于gitlab
//     def branch = env.BRANCH_NAME // jenkins自带的触发分支环境变量
    /**=================================== 来自gitlab 结束 ===================================*/

    /**=================================== 判断是来及gitlab还是jenkins 开始 ===================================*/

    // 如果是来自gitlab，则取所有的服务和节点进行部署，否则来自jenkins，则取已选服务和节点进行部署
    def env = ""
    /**=================================== 判断是来及gitlab还是jenkins 结束 ===================================*/


    /**=================================== 根据分支计算tag值 开始 ===================================*/

    def tag = "latest"
    def branchArr = branch.split('/')
    // 判断branchArr的长度，如果大于0， 则取最后一个元素，否则取第一个元素
    if (branchArr.length > 0) {
        tag = branchArr[branchArr.length - 1]
    } else {
        tag = branchArr[0]
    }

    if (branch) {
        if (branch.contains("main")) {
            env = "prod"
        } else {
            if (branch.contains("develop")) {
                env = 'dev'
            } else {
                if (branch.contains("release")) {
                    env = 'test'
                } else {
                    env = 'dev'
                }
            }
        }
    }

    // 根据环境变量指定路径
    artifactsServerPath += "/${env}"

    // 发布的服务器
    if (env == "dev" || env == 'test') {
        serverName = testServer
    } else {
        serverName = prodServer
    }

    /**=================================== 根据分支计算tag值 结束 ===================================*/


    stage('构建参数') {
        echo "=================================== 构建参数 开始==================================="
        echo "事件来源：${type}"
        echo "发布的环境：${env}"
        echo "发布的分支：${branch}"
        echo "发布的版本：${tag}"
        echo "发布的服务器：${serverName}"
        echo "=================================== 构建参数 结束==================================="
    }

    stage('环境验证') {
        echo "=================================== 环境验证 开始==================================="
        sh 'docker -v'
        sh 'git version'
        sh "docker exec -w ${volumePath} my-node pnpm -v"
        echo "=================================== 环境验证 结束==================================="
    }

    stage('拉取代码') {
        echo "=================================== 拉取代码 开始==================================="
        checkout scmGit(branches: [[name: "${branch}"]], extensions: [], userRemoteConfigs: [[credentialsId: '56276da0-330b-443b-b950-4fe59e6bfacf', url: gitUrl]])
        echo "=================================== 拉取代码 结束==================================="
    }

    // 编译打包微服务，制作镜像
    stage('执行构建') {
        echo "=================================== 执行构建 开始 ==================================="
        // 指定子项目构建，同时构建依赖的服务
        sh "docker exec -w ${volumePath} my-node pnpm install --no-frozen-lockfile"
        sh "docker exec -w ${volumePath} my-node pnpm build:${env}"
        echo "=================================== 执行构建 结束 ==================================="
    }

    stage('推送编译包并重启服务') {
        echo "=================================== 推送编译包并重启服务 开始 ==================================="

        echo "--------------压缩'${projectZip}'开始--------------"
        sh "cd dist && zip -rq ${projectZip} ${project}"
        echo "--------------压缩结束--------------"

        echo "--------------推送'${projectZip}'到服务器${serverName}的${artifactsServerPath}目录下，解压、重启容器 开始--------------"
        echo "cd ${artifactsServerPath} && rm -rf ${project} && unzip ${projectZip} && rm -rf ${projectZip} && docker restart ${project}_${env}"
        publishSSH("${serverName}",
                "cd ${artifactsServerPath} && rm -rf ${project} && unzip ${projectZip} && rm -rf ${projectZip} && docker restart ${project}_${env}",
                "${artifactsServerPath}",
                "dist/",
                "dist/${projectZip}")
        echo "--------------推送、解压、重启容器 结束--------------"

        echo "=================================== 推送编译包并重启服务 结束 ==================================="
    }

    stage('自动化任务完成') {
        echo "=================================== 自动化任务完成 ==================================="
    }
}
